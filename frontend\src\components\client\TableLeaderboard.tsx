import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Trophy, 
  Users, 
  TrendingUp, 
  Star, 
  Crown,
  Medal,
  Award,
  ChevronUp,
  ChevronDown,
  X
} from 'lucide-react';

interface TableStats {
  tableNumber: string;
  totalSuggestions: number;
  totalVotes: number;
  averageScore: number;
  paidSuggestions: number;
  points: number;
  rank: number;
  isCurrentTable: boolean;
}

interface TableLeaderboardProps {
  restaurantId: string;
  currentTableNumber?: string;
  isVisible: boolean;
  onClose: () => void;
}

export const TableLeaderboard: React.FC<TableLeaderboardProps> = ({
  restaurantId,
  currentTableNumber,
  isVisible,
  onClose
}) => {
  const [tableStats, setTableStats] = useState<TableStats[]>([]);
  const [loading, setLoading] = useState(false);
  const [sortBy, setSortBy] = useState<'points' | 'suggestions' | 'votes'>('points');

  useEffect(() => {
    if (isVisible) {
      loadTableStats();
      
      // Atualizar a cada 30 segundos
      const interval = setInterval(loadTableStats, 30000);
      return () => clearInterval(interval);
    }
  }, [isVisible, restaurantId]);

  const loadTableStats = async () => {
    setLoading(true);
    try {
      const response = await fetch(`http://localhost:8001/api/v1/analytics/tables/${restaurantId}/leaderboard`);
      
      if (response.ok) {
        const data = await response.json();
        const stats = data.tables.map((table: any, index: number) => ({
          tableNumber: table.tableNumber || `Mesa ${index + 1}`,
          totalSuggestions: table.totalSuggestions || 0,
          totalVotes: table.totalVotes || 0,
          averageScore: table.averageScore || 0,
          paidSuggestions: table.paidSuggestions || 0,
          points: table.points || 0,
          rank: index + 1,
          isCurrentTable: table.tableNumber === currentTableNumber
        }));
        
        setTableStats(stats);
      }
    } catch (error) {
      console.error('Erro ao carregar estatísticas das mesas:', error);
      
      // Mock data para demonstração
      const mockStats: TableStats[] = [
        {
          tableNumber: 'Mesa 5',
          totalSuggestions: 8,
          totalVotes: 45,
          averageScore: 4.2,
          paidSuggestions: 3,
          points: 850,
          rank: 1,
          isCurrentTable: currentTableNumber === '5'
        },
        {
          tableNumber: 'Mesa 10',
          totalSuggestions: 6,
          totalVotes: 38,
          averageScore: 3.8,
          paidSuggestions: 2,
          points: 720,
          rank: 2,
          isCurrentTable: currentTableNumber === '10'
        },
        {
          tableNumber: 'Mesa 3',
          totalSuggestions: 5,
          totalVotes: 32,
          averageScore: 3.5,
          paidSuggestions: 1,
          points: 650,
          rank: 3,
          isCurrentTable: currentTableNumber === '3'
        },
        {
          tableNumber: 'Mesa 7',
          totalSuggestions: 4,
          totalVotes: 28,
          averageScore: 3.2,
          paidSuggestions: 1,
          points: 580,
          rank: 4,
          isCurrentTable: currentTableNumber === '7'
        },
        {
          tableNumber: 'Mesa 12',
          totalSuggestions: 3,
          totalVotes: 22,
          averageScore: 2.9,
          paidSuggestions: 0,
          points: 450,
          rank: 5,
          isCurrentTable: currentTableNumber === '12'
        }
      ];
      
      setTableStats(mockStats);
    } finally {
      setLoading(false);
    }
  };

  const sortedStats = [...tableStats].sort((a, b) => {
    switch (sortBy) {
      case 'suggestions':
        return b.totalSuggestions - a.totalSuggestions;
      case 'votes':
        return b.totalVotes - a.totalVotes;
      default:
        return b.points - a.points;
    }
  });

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="w-5 h-5 text-yellow-500" />;
      case 2:
        return <Medal className="w-5 h-5 text-gray-400" />;
      case 3:
        return <Award className="w-5 h-5 text-orange-600" />;
      default:
        return <Trophy className="w-5 h-5 text-gray-500" />;
    }
  };

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1:
        return 'from-yellow-500 to-yellow-600';
      case 2:
        return 'from-gray-400 to-gray-500';
      case 3:
        return 'from-orange-500 to-orange-600';
      default:
        return 'from-gray-600 to-gray-700';
    }
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 20 }}
        className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-md max-h-[80vh] overflow-hidden"
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-purple-600 to-pink-600 p-6 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Trophy className="w-6 h-6" />
              <div>
                <h2 className="text-xl font-bold">Ranking das Mesas</h2>
                <p className="text-purple-100 text-sm">Competição em tempo real</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-white/80 hover:text-white transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Filtros */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex space-x-2">
            {[
              { key: 'points', label: 'Pontos' },
              { key: 'suggestions', label: 'Sugestões' },
              { key: 'votes', label: 'Votos' }
            ].map((option) => (
              <button
                key={option.key}
                onClick={() => setSortBy(option.key as any)}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                  sortBy === option.key
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-purple-100 dark:hover:bg-gray-600'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>

        {/* Lista de Mesas */}
        <div className="p-4 max-h-96 overflow-y-auto">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="w-8 h-8 border-4 border-purple-600 border-t-transparent rounded-full animate-spin"></div>
              <span className="ml-3 text-gray-600 dark:text-gray-400">Carregando ranking...</span>
            </div>
          ) : (
            <div className="space-y-3">
              <AnimatePresence>
                {sortedStats.map((table, index) => (
                  <motion.div
                    key={table.tableNumber}
                    layout
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className={`p-4 rounded-lg border-2 transition-all ${
                      table.isCurrentTable
                        ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20'
                        : 'border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-full bg-gradient-to-r ${getRankColor(index + 1)}`}>
                          {getRankIcon(index + 1)}
                        </div>
                        <div>
                          <h3 className={`font-bold ${
                            table.isCurrentTable 
                              ? 'text-purple-700 dark:text-purple-300' 
                              : 'text-gray-900 dark:text-white'
                          }`}>
                            {table.tableNumber}
                            {table.isCurrentTable && (
                              <span className="ml-2 text-xs bg-purple-600 text-white px-2 py-1 rounded-full">
                                Você
                              </span>
                            )}
                          </h3>
                          <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                            <span>{table.points} pts</span>
                            <span>{table.totalSuggestions} sugestões</span>
                            <span>{table.totalVotes} votos</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="flex items-center space-x-1 text-yellow-500">
                          <Star className="w-4 h-4" />
                          <span className="font-medium">{table.averageScore.toFixed(1)}</span>
                        </div>
                        {table.paidSuggestions > 0 && (
                          <div className="text-xs text-green-600 dark:text-green-400">
                            {table.paidSuggestions} pagas
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Barra de progresso */}
                    <div className="mt-3">
                      <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full bg-gradient-to-r ${getRankColor(index + 1)} transition-all duration-500`}
                          style={{
                            width: `${Math.min((table.points / Math.max(...sortedStats.map(s => s.points))) * 100, 100)}%`
                          }}
                        />
                      </div>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 bg-gray-50 dark:bg-gray-700 text-center">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            🎵 Continue sugerindo e votando para subir no ranking!
          </p>
        </div>
      </motion.div>
    </div>
  );
};
