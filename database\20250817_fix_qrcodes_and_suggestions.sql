-- Ensure qr_codes table exists minimally (idempotent)
CREATE TABLE IF NOT EXISTS qr_codes (
	id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
	restaurant_id VARCHAR NOT NULL,
	code TEXT NOT NULL,
	created_at TIMESTAMP DEFAULT now()
);

-- Ensure FK to restaurants (if column exists)
DO $$
BEGIN
	IF EXISTS (
		SELECT 1 FROM information_schema.columns
		WHERE table_name='qr_codes' AND column_name='restaurant_id'
	) AND NOT EXISTS (
		SELECT 1 FROM information_schema.table_constraints 
		WHERE table_name='qr_codes' AND constraint_name='fk_qr_codes_restaurant'
	) THEN
		ALTER TABLE qr_codes
		ADD CONSTRAINT fk_qr_codes_restaurant
			FOREIGN KEY (restaurant_id) REFERENCES restaurants(id) ON DELETE CASCADE;
	END IF;
END $$;

-- Ensure suggestions.session_id FK to client_sessions if both exist
DO $$
BEGIN
	IF EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='session_id'
	) AND EXISTS (
		SELECT 1 FROM information_schema.tables WHERE table_name='client_sessions'
	) AND NOT EXISTS (
		SELECT 1 FROM information_schema.table_constraints 
		WHERE table_name='suggestions' AND constraint_name='fk_suggestions_session'
	) THEN
		ALTER TABLE suggestions
		ADD CONSTRAINT fk_suggestions_session
			FOREIGN KEY (session_id) REFERENCES client_sessions(id) ON DELETE SET NULL;
	END IF;
END $$;

