# Configurações de Produção
# Sistema de Playlist Interativa para Restaurantes

# ===== BANCO DE DADOS =====
DB_HOST=postgres
DB_PORT=5432
DB_NAME=restaurant_playlist
DB_USER=playlist_user
DB_PASSWORD=secure_database_password_change_me

# ===== REDIS =====
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=secure_redis_password_change_me

# ===== AUTENTICAÇÃO =====
JWT_SECRET=very_long_jwt_secret_key_change_in_production_minimum_32_chars
JWT_EXPIRES_IN=7d

# ===== YOUTUBE API =====
YOUTUBE_API_KEY=your_youtube_api_key_here

# ===== URLS =====
FRONTEND_URL=https://playlist.yourdomain.com
API_URL=https://api.yourdomain.com
WS_URL=https://api.yourdomain.com

# ===== CORS =====
CORS_ORIGIN=https://playlist.yourdomain.com

# ===== RATE LIMITING =====
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# ===== UPLOADS =====
MAX_FILE_SIZE=5242880
UPLOAD_PATH=/app/uploads

# ===== LOGS =====
LOG_LEVEL=info
LOG_FILE=/app/logs/app.log

# ===== MODERAÇÃO =====
AUTO_MODERATION_ENABLED=true
MAX_SUGGESTIONS_PER_SESSION=10
MAX_VOTES_PER_SESSION=50

# ===== CACHE =====
CACHE_TTL=300
CACHE_MAX_KEYS=1000

# ===== WEBSOCKET =====
WS_CORS_ORIGIN=https://playlist.yourdomain.com
WS_MAX_CONNECTIONS=1000

# ===== MONITORAMENTO =====
HEALTH_CHECK_INTERVAL=30000
METRICS_ENABLED=true

# ===== BACKUP =====
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# ===== SSL/HTTPS =====
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# ===== NOTIFICAÇÕES =====
EMAIL_ENABLED=false
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# ===== ANALYTICS =====
ANALYTICS_ENABLED=true
ANALYTICS_RETENTION_DAYS=90

# ===== Seed Demo/Admin (opcional para homologação) =====
# RUN_SEED=false
# ADMIN_EMAIL=<EMAIL>
# ADMIN_PASSWORD=admin123
# ADMIN_NAME=Administrador
# ADMIN_RESTAURANT_ID=demo-restaurant
