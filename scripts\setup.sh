#!/bin/bash

# Script de configuração inicial do Sistema de Playlist Interativa para Restaurantes
# Este script automatiza a configuração inicial do projeto

set -e  # Parar em caso de erro

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para imprimir mensagens coloridas
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Verificar se Docker está instalado
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker não está instalado. Por favor, instale o Docker primeiro."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose não está instalado. Por favor, instale o Docker Compose primeiro."
        exit 1
    fi
    
    print_message "Docker e Docker Compose encontrados ✓"
}

# Verificar se Node.js está instalado (para desenvolvimento)
check_node() {
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_message "Node.js encontrado: $NODE_VERSION ✓"
    else
        print_warning "Node.js não encontrado. Necessário apenas para desenvolvimento local."
    fi
}

# Criar arquivo .env se não existir
setup_env() {
    if [ ! -f .env ]; then
        print_message "Criando arquivo .env a partir do .env.example..."
        cp .env.example .env
        
        print_warning "IMPORTANTE: Configure as seguintes variáveis no arquivo .env:"
        echo "  - YOUTUBE_API_KEY (obrigatório)"
        echo "  - JWT_SECRET (recomendado alterar)"
        echo "  - Outras configurações conforme necessário"
        echo ""
        echo "Pressione Enter para continuar após configurar o .env..."
        read -r
    else
        print_message "Arquivo .env já existe ✓"
    fi
}

# Verificar se a chave do YouTube está configurada
check_youtube_api() {
    if [ -f .env ]; then
        if grep -q "YOUTUBE_API_KEY=your-youtube-api-key-here" .env || ! grep -q "YOUTUBE_API_KEY=" .env; then
            print_error "Chave da API do YouTube não configurada!"
            echo "Por favor, configure YOUTUBE_API_KEY no arquivo .env"
            echo "Obtenha sua chave em: https://console.developers.google.com/"
            exit 1
        else
            print_message "Chave da API do YouTube configurada ✓"
        fi
    fi
}

# Criar diretórios necessários
create_directories() {
    print_message "Criando diretórios necessários..."
    
    mkdir -p backend/logs
    mkdir -p backend/uploads
    mkdir -p nginx/ssl
    mkdir -p database/backups
    
    print_message "Diretórios criados ✓"
}

# Configurar permissões
setup_permissions() {
    print_message "Configurando permissões..."
    
    # Tornar scripts executáveis
    chmod +x scripts/*.sh
    
    # Configurar permissões para logs
    chmod 755 backend/logs
    
    print_message "Permissões configuradas ✓"
}

# Build das imagens Docker
build_images() {
    print_message "Fazendo build das imagens Docker..."
    
    docker-compose build --no-cache
    
    print_message "Build das imagens concluído ✓"
}

# Inicializar banco de dados
init_database() {
    print_message "Inicializando banco de dados..."
    
    # Iniciar apenas o PostgreSQL primeiro
    docker-compose up -d postgres
    
    # Aguardar o banco estar pronto
    print_message "Aguardando banco de dados ficar pronto..."
    sleep 10
    
    # Verificar se o banco está respondendo
    until docker-compose exec postgres pg_isready -U restaurant_user -d restaurant_playlist; do
        print_message "Aguardando banco de dados..."
        sleep 2
    done
    
    print_message "Banco de dados inicializado ✓"
}

# Inicializar Redis
init_redis() {
    print_message "Inicializando Redis..."
    
    docker-compose up -d redis
    
    # Aguardar Redis estar pronto
    sleep 5
    
    print_message "Redis inicializado ✓"
}

# Iniciar todos os serviços
start_services() {
    print_message "Iniciando todos os serviços..."
    
    docker-compose up -d
    
    # Aguardar serviços estarem prontos
    print_message "Aguardando serviços ficarem prontos..."
    sleep 15
    
    print_message "Todos os serviços iniciados ✓"
}

# Verificar saúde dos serviços
check_health() {
    print_message "Verificando saúde dos serviços..."
    
    # Verificar backend
    if curl -f http://localhost:5000/health &> /dev/null; then
        print_message "Backend: OK ✓"
    else
        print_warning "Backend: Não respondendo"
    fi
    
    # Verificar frontend
    if curl -f http://localhost:3000 &> /dev/null; then
        print_message "Frontend: OK ✓"
    else
        print_warning "Frontend: Não respondendo"
    fi
    
    # Verificar banco
    if docker-compose exec postgres pg_isready -U restaurant_user -d restaurant_playlist &> /dev/null; then
        print_message "PostgreSQL: OK ✓"
    else
        print_warning "PostgreSQL: Não respondendo"
    fi
    
    # Verificar Redis
    if docker-compose exec redis redis-cli ping &> /dev/null; then
        print_message "Redis: OK ✓"
    else
        print_warning "Redis: Não respondendo"
    fi
}

# Mostrar informações finais
show_final_info() {
    print_header "CONFIGURAÇÃO CONCLUÍDA!"
    
    echo ""
    echo "🎉 O Sistema de Playlist Interativa está pronto!"
    echo ""
    echo "📱 Acesse o sistema:"
    echo "   Frontend: http://localhost:3000"
    echo "   API: http://localhost:5000"
    echo "   Admin: http://localhost:3000/admin"
    echo ""
    echo "🔑 Login inicial:"
    echo "   Email: <EMAIL>"
    echo "   Senha: admin123"
    echo ""
    echo "🛠️ Ferramentas de desenvolvimento (se --dev foi usado):"
    echo "   Adminer: http://localhost:8080"
    echo "   Redis Commander: http://localhost:8081"
    echo ""
    echo "📚 Comandos úteis:"
    echo "   Ver logs: docker-compose logs -f"
    echo "   Parar: docker-compose down"
    echo "   Reiniciar: docker-compose restart"
    echo ""
    echo "⚠️  Lembre-se de:"
    echo "   - Configurar SSL para produção"
    echo "   - Alterar senhas padrão"
    echo "   - Configurar backup do banco"
    echo ""
}

# Função principal
main() {
    print_header "CONFIGURAÇÃO INICIAL"
    
    # Verificar argumentos
    DEV_MODE=false
    if [[ "$1" == "--dev" ]]; then
        DEV_MODE=true
        print_message "Modo de desenvolvimento ativado"
    fi
    
    # Executar verificações e configurações
    check_docker
    check_node
    setup_env
    check_youtube_api
    create_directories
    setup_permissions
    
    # Build e inicialização
    build_images
    init_database
    init_redis
    
    # Iniciar serviços
    if [ "$DEV_MODE" = true ]; then
        docker-compose --profile dev up -d
    else
        start_services
    fi
    
    # Verificações finais
    check_health
    show_final_info
}

# Executar função principal
main "$@"
