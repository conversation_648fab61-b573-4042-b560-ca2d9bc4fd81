import { Router, Request, Response } from "express";
import { YouTubeService } from "../services/YouTubeService";

const router = Router();

/**
 * GET /api/test-youtube/validate
 * Testar se a API Key do YouTube está funcionando
 */
router.get("/validate", async (req: Request, res: Response) => {
  try {
    const youtubeService = new YouTubeService();

    console.log("🔍 Testando API Key do YouTube...");

    // Fazer uma busca simples para testar
    const results = await youtubeService.searchVideos("test music", 1);

    if (results && results.videos && results.videos.length > 0) {
      res.json({
        success: true,
        message: "✅ API Key do YouTube está funcionando!",
        apiKey: process.env.YOUTUBE_API_KEY
          ? `${process.env.YOUTUBE_API_KEY.substring(0, 10)}...`
          : "Não configurada",
        testResult: {
          videosFound: results.videos.length,
          firstVideo: results.videos[0]
            ? {
                title: results.videos[0].title,
                artist: results.videos[0].artist,
                duration: results.videos[0].duration,
              }
            : null,
        },
      });
    } else {
      res.status(400).json({
        success: false,
        message: "❌ API Key não retornou resultados",
        apiKey: process.env.YOUTUBE_API_KEY
          ? `${process.env.YOUTUBE_API_KEY.substring(0, 10)}...`
          : "Não configurada",
      });
    }
  } catch (error: any) {
    console.error("❌ Erro ao testar YouTube API:", error);

    let errorMessage = "Erro desconhecido";
    let errorDetails = "";

    if (error.response?.status === 403) {
      errorMessage = "API Key inválida ou sem permissões";
      errorDetails =
        "Verifique se a API Key está correta e se a YouTube Data API v3 está ativada";
    } else if (error.response?.status === 400) {
      errorMessage = "Requisição inválida";
      errorDetails =
        error.response.data?.error?.message ||
        "Parâmetros da requisição incorretos";
    } else if (error.message.includes("YOUTUBE_API_KEY")) {
      errorMessage = "API Key não configurada";
      errorDetails = "Configure a variável YOUTUBE_API_KEY no arquivo .env";
    }

    res.status(500).json({
      success: false,
      message: `❌ ${errorMessage}`,
      details: errorDetails,
      apiKey: process.env.YOUTUBE_API_KEY
        ? `${process.env.YOUTUBE_API_KEY.substring(0, 10)}...`
        : "Não configurada",
      error: error.message,
    });
  }
});

/**
 * GET /api/test-youtube/search
 * Testar busca no YouTube
 */
router.get("/search", async (req: Request, res: Response) => {
  try {
    const { q = "música brasileira" } = req.query;

    if (typeof q !== "string") {
      return res.status(400).json({
        success: false,
        message: 'Parâmetro "q" deve ser uma string',
      });
    }

    const youtubeService = new YouTubeService();

    console.log(`🔍 Buscando no YouTube: "${q}"`);

    const searchResult = await youtubeService.searchVideos(q, 5);
    const results = searchResult.videos || [];

    res.json({
      success: true,
      message: `✅ Encontrados ${results.length} vídeos`,
      query: q,
      results: results.map((video) => ({
        id: video.youtubeVideoId,
        title: video.title,
        artist: video.artist,
        duration: video.duration,
        thumbnail: video.thumbnailUrl,
        url: `https://www.youtube.com/watch?v=${video.youtubeVideoId}`,
      })),
    });
  } catch (error: any) {
    console.error("❌ Erro na busca:", error);

    res.status(500).json({
      success: false,
      message: "❌ Erro na busca do YouTube",
      error: error.message,
    });
  }
});

/**
 * GET /api/test-youtube/config
 * Mostrar configuração atual
 */
router.get("/config", (req: Request, res: Response) => {
  res.json({
    success: true,
    config: {
      hasApiKey: !!process.env.YOUTUBE_API_KEY,
      apiKeyPreview: process.env.YOUTUBE_API_KEY
        ? `${process.env.YOUTUBE_API_KEY.substring(0, 10)}...`
        : "Não configurada",
      hasClientId: !!process.env.YOUTUBE_CLIENT_ID,
      hasClientSecret: !!process.env.YOUTUBE_CLIENT_SECRET,
      clientSecretPreview: process.env.YOUTUBE_CLIENT_SECRET
        ? `${process.env.YOUTUBE_CLIENT_SECRET.substring(0, 10)}...`
        : "Não configurada",
      quotaLimit: process.env.YOUTUBE_API_QUOTA_LIMIT || "10000",
    },
    instructions: {
      step1: "Vá para https://console.cloud.google.com/",
      step2: "Crie/selecione um projeto",
      step3: "Ative a YouTube Data API v3",
      step4: "Crie uma API Key",
      step5: "Configure YOUTUBE_API_KEY no arquivo .env",
      step6: "Teste em /api/test-youtube/validate",
    },
  });
});

export default router;
