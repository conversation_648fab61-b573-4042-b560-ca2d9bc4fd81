import { Router } from "express";
import { param, validationResult } from "../utils/validation";
import { playlistReorderService } from "../services/PlaylistReorderService";
import { optionalAuth } from "../middleware/auth";
import { asyncHandler } from "../middleware/errorHandler";
import { ValidationError } from "../utils/errors";

const router = Router();

/**
 * @swagger
 * /api/v1/playlist-reorder/status:
 *   get:
 *     summary: Obter status do serviço de reordenação automática
 *     tags: [Playlist Reorder]
 *     responses:
 *       200:
 *         description: Status do serviço
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 status:
 *                   type: object
 *                   properties:
 *                     isRunning:
 *                       type: boolean
 *                     nextExecution:
 *                       type: string
 *                       format: date-time
 *                     uptime:
 *                       type: number
 */
router.get(
  "/status",
  optionalAuth,
  asyncHandler(async (req, res) => {
    const status = playlistReorderService.getStatus();

    res.json({
      success: true,
      status: {
        isRunning: status.isRunning,
        nextExecution: status.nextExecution?.toISOString(),
        uptime: Math.round(status.uptime),
        message: status.isRunning
          ? "Serviço de reordenação automática ativo"
          : "Serviço de reordenação automática parado",
      },
    });
  })
);

/**
 * @swagger
 * /api/v1/playlist-reorder/manual/{restaurantId}:
 *   post:
 *     summary: Executar reordenação manual para um restaurante
 *     tags: [Playlist Reorder]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID do restaurante
 *     responses:
 *       200:
 *         description: Reordenação executada
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 result:
 *                   type: object
 *                   properties:
 *                     restaurantId:
 *                       type: string
 *                     playlistId:
 *                       type: string
 *                     tracksReordered:
 *                       type: number
 *                     message:
 *                       type: string
 *                     timestamp:
 *                       type: string
 *                       format: date-time
 */
router.post(
  "/manual/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    const result = await playlistReorderService.manualReorder(restaurantId);

    res.json({
      success: result.success,
      result: {
  status: result.status,
        restaurantId: result.restaurantId,
        playlistId: result.playlistId,
        tracksReordered: result.tracksReordered,
        message: result.message,
        timestamp: result.timestamp.toISOString(),
      },
    });
  })
);

/**
 * @swagger
 * /api/v1/playlist-reorder/start:
 *   post:
 *     summary: Iniciar serviço de reordenação automática
 *     tags: [Playlist Reorder]
 *     responses:
 *       200:
 *         description: Serviço iniciado
 */
router.post(
  "/start",
  optionalAuth,
  asyncHandler(async (req, res) => {
    playlistReorderService.startAutoReorder();

    res.json({
      success: true,
      message: "Serviço de reordenação automática iniciado",
    });
  })
);

/**
 * @swagger
 * /api/v1/playlist-reorder/stop:
 *   post:
 *     summary: Parar serviço de reordenação automática
 *     tags: [Playlist Reorder]
 *     responses:
 *       200:
 *         description: Serviço parado
 */
router.post(
  "/stop",
  optionalAuth,
  asyncHandler(async (req, res) => {
    playlistReorderService.stopAutoReorder();

    res.json({
      success: true,
      message: "Serviço de reordenação automática parado",
    });
  })
);

export default router;
