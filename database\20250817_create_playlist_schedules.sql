-- Create playlist_schedules table (idempotent)
CREATE TABLE IF NOT EXISTS playlist_schedules (
	id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
	restaurant_id VARCHAR NOT NULL,
	playlist_id UUID NOT NULL,
	is_active BOOLEAN DEFAULT TRUE,
	schedule JSONB NULL,
	created_at TIMESTAMP DEFAULT now(),
	updated_at TIMESTAMP DEFAULT now()
);

-- FKs if referenced tables exist
DO $$
BEGIN
	IF EXISTS (
		SELECT 1 FROM information_schema.tables WHERE table_name='restaurants'
	) AND NOT EXISTS (
		SELECT 1 FROM information_schema.table_constraints 
		WHERE table_name='playlist_schedules' AND constraint_name='fk_ps_restaurant'
	) THEN
		ALTER TABLE playlist_schedules
		ADD CONSTRAINT fk_ps_restaurant
			FOREIGN KEY (restaurant_id) REFERENCES restaurants(id) ON DELETE CASCADE;
	END IF;
	IF EXISTS (
		SELECT 1 FROM information_schema.tables WHERE table_name='playlists'
	) AND NOT EXISTS (
		SELECT 1 FROM information_schema.table_constraints 
		WHERE table_name='playlist_schedules' AND constraint_name='fk_ps_playlist'
	) THEN
		ALTER TABLE playlist_schedules
		ADD CONSTRAINT fk_ps_playlist
			FOREIGN KEY (playlist_id) REFERENCES playlists(id) ON DELETE CASCADE;
	END IF;
END $$;

