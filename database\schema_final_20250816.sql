--
-- PostgreSQL database dump
--

-- Dumped from database version 15.13
-- Dumped by pg_dump version 15.13

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: pg_trgm; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_trgm WITH SCHEMA public;


--
-- Name: EXTENSION pg_trgm; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION pg_trgm IS 'text similarity measurement and index searching based on trigrams';


--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: play_status; Type: TYPE; Schema: public; Owner: restaurant_user
--

CREATE TYPE public.play_status AS ENUM (
    'playing',
    'completed',
    'skipped',
    'interrupted',
    'error'
);


ALTER TYPE public.play_status OWNER TO restaurant_user;

--
-- Name: playlist_status; Type: TYPE; Schema: public; Owner: restaurant_user
--

CREATE TYPE public.playlist_status AS ENUM (
    'active',
    'inactive',
    'archived',
    'deleted'
);


ALTER TYPE public.playlist_status OWNER TO restaurant_user;

--
-- Name: playlist_type; Type: TYPE; Schema: public; Owner: restaurant_user
--

CREATE TYPE public.playlist_type AS ENUM (
    'custom',
    'youtube',
    'youtube_import',
    'auto_generated',
    'suggestions'
);


ALTER TYPE public.playlist_type OWNER TO restaurant_user;

--
-- Name: restaurant_status; Type: TYPE; Schema: public; Owner: restaurant_user
--

CREATE TYPE public.restaurant_status AS ENUM (
    'active',
    'inactive',
    'suspended',
    'trial'
);


ALTER TYPE public.restaurant_status OWNER TO restaurant_user;

--
-- Name: rule_action; Type: TYPE; Schema: public; Owner: restaurant_user
--

CREATE TYPE public.rule_action AS ENUM (
    'auto_reject',
    'flag_for_review',
    'require_approval',
    'auto_approve'
);


ALTER TYPE public.rule_action OWNER TO restaurant_user;

--
-- Name: rule_type; Type: TYPE; Schema: public; Owner: restaurant_user
--

CREATE TYPE public.rule_type AS ENUM (
    'blacklist_word',
    'blacklist_artist',
    'blacklist_channel',
    'genre_restriction',
    'duration_limit',
    'content_rating',
    'language_filter',
    'time_restriction'
);


ALTER TYPE public.rule_type OWNER TO restaurant_user;

--
-- Name: suggestion_source; Type: TYPE; Schema: public; Owner: restaurant_user
--

CREATE TYPE public.suggestion_source AS ENUM (
    'client',
    'admin',
    'auto',
    'import'
);


ALTER TYPE public.suggestion_source OWNER TO restaurant_user;

--
-- Name: suggestion_status; Type: TYPE; Schema: public; Owner: restaurant_user
--

CREATE TYPE public.suggestion_status AS ENUM (
    'pending',
    'approved',
    'rejected',
    'playing',
    'played',
    'skipped',
    'completed',
    'expired'
);


ALTER TYPE public.suggestion_status OWNER TO restaurant_user;

--
-- Name: user_role; Type: TYPE; Schema: public; Owner: restaurant_user
--

CREATE TYPE public.user_role AS ENUM (
    'super_admin',
    'admin',
    'moderator',
    'staff'
);


ALTER TYPE public.user_role OWNER TO restaurant_user;

--
-- Name: vote_type; Type: TYPE; Schema: public; Owner: restaurant_user
--

CREATE TYPE public.vote_type AS ENUM (
    'up',
    'down'
);


ALTER TYPE public.vote_type OWNER TO restaurant_user;

--
-- Name: gen_random_uuid(); Type: FUNCTION; Schema: public; Owner: restaurant_user
--

CREATE FUNCTION public.gen_random_uuid() RETURNS uuid
    LANGUAGE plpgsql
    AS $$
BEGIN
  RETURN uuid_generate_v4();
END;
$$;


ALTER FUNCTION public.gen_random_uuid() OWNER TO restaurant_user;

--
-- Name: get_genres_by_category(character varying); Type: FUNCTION; Schema: public; Owner: restaurant_user
--

CREATE FUNCTION public.get_genres_by_category(category_filter character varying DEFAULT NULL::character varying) RETURNS TABLE(id uuid, name character varying, "displayName" character varying, description character varying, category character varying, color character varying, icon character varying, priority integer, "isActive" boolean, "usageCount" integer)
    LANGUAGE plpgsql
    AS $$
BEGIN
  RETURN QUERY
  SELECT g.id, g.name, g."displayName", g.description, g.category, g.color, g.icon,
         g.priority, g."isActive", g."usageCount"
  FROM genres g
  WHERE (category_filter IS NULL OR g.category = category_filter)
    AND g."isActive" = TRUE
  ORDER BY g.priority ASC, g."displayName" ASC;
END;
$$;


ALTER FUNCTION public.get_genres_by_category(category_filter character varying) OWNER TO restaurant_user;

--
-- Name: increment_genre_usage(uuid); Type: FUNCTION; Schema: public; Owner: restaurant_user
--

CREATE FUNCTION public.increment_genre_usage(p_genre_id uuid) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
  UPDATE genres
  SET "usageCount" = "usageCount" + 1,
      "lastUsedAt" = NOW()
  WHERE id = p_genre_id;
END;
$$;


ALTER FUNCTION public.increment_genre_usage(p_genre_id uuid) OWNER TO restaurant_user;

--
-- Name: search_genres(character varying); Type: FUNCTION; Schema: public; Owner: restaurant_user
--

CREATE FUNCTION public.search_genres(search_text character varying) RETURNS TABLE(id uuid, name character varying, "displayName" character varying, description character varying, category character varying, color character varying, icon character varying, priority integer, "isActive" boolean, "usageCount" integer, relevance double precision)
    LANGUAGE plpgsql
    AS $$
BEGIN
  RETURN QUERY
  SELECT 
    g.id,
    g.name,
    g."displayName",
    g.description,
    g.category,
    g.color,
    g.icon,
    g.priority,
    g."isActive",
    g."usageCount",
    (
      CASE 
        WHEN LOWER(g."displayName") = LOWER(search_text) THEN 1.0
        WHEN LOWER(g.name) = LOWER(search_text) THEN 0.9
        WHEN LOWER(g."displayName") LIKE LOWER(search_text || '%') THEN 0.8
        WHEN LOWER(g.name) LIKE LOWER(search_text || '%') THEN 0.7
        WHEN LOWER(g."displayName") LIKE LOWER('%' || search_text || '%') THEN 0.6
        WHEN LOWER(g.name) LIKE LOWER('%' || search_text || '%') THEN 0.5
        WHEN LOWER(g.description) LIKE LOWER('%' || search_text || '%') THEN 0.3
        ELSE 0.1
      END
    ) AS relevance
  FROM genres g
  WHERE g."isActive" = TRUE
    AND (
      LOWER(g."displayName") LIKE LOWER('%' || search_text || '%') OR
      LOWER(g.name) LIKE LOWER('%' || search_text || '%') OR
      LOWER(g.description) LIKE LOWER('%' || search_text || '%')
    )
  ORDER BY relevance DESC, g.priority ASC, g."displayName" ASC;
END;
$$;


ALTER FUNCTION public.search_genres(search_text character varying) OWNER TO restaurant_user;

--
-- Name: update_suggestion_vote_counts(); Type: FUNCTION; Schema: public; Owner: restaurant_user
--

CREATE FUNCTION public.update_suggestion_vote_counts() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
  tgt UUID;
BEGIN
  IF (TG_OP = 'INSERT') THEN
    tgt := NEW.suggestion_id;
  ELSIF (TG_OP = 'UPDATE') THEN
    IF NEW.suggestion_id IS DISTINCT FROM OLD.suggestion_id THEN
      IF OLD.suggestion_id IS NOT NULL THEN
        UPDATE suggestions s SET
          upvotes    = (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = OLD.suggestion_id AND v.vote_type = 'up'),
          downvotes  = (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = OLD.suggestion_id AND v.vote_type = 'down'),
          vote_count = (
            (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = OLD.suggestion_id AND v.vote_type = 'up') -
            (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = OLD.suggestion_id AND v.vote_type = 'down')
          ),
          updated_at = NOW()
        WHERE s.id = OLD.suggestion_id;
      END IF;
      tgt := NEW.suggestion_id;
    ELSE
      tgt := NEW.suggestion_id;
    END IF;
  ELSIF (TG_OP = 'DELETE') THEN
    tgt := OLD.suggestion_id;
  END IF;

  IF tgt IS NOT NULL THEN
    UPDATE suggestions s SET
      upvotes    = (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = tgt AND v.vote_type = 'up'),
      downvotes  = (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = tgt AND v.vote_type = 'down'),
      vote_count = (
        (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = tgt AND v.vote_type = 'up') -
        (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = tgt AND v.vote_type = 'down')
      ),
      updated_at = NOW()
    WHERE s.id = tgt;
  END IF;

  RETURN NULL;
END;
$$;


ALTER FUNCTION public.update_suggestion_vote_counts() OWNER TO restaurant_user;

--
-- Name: update_updated_at(); Type: FUNCTION; Schema: public; Owner: restaurant_user
--

CREATE FUNCTION public.update_updated_at() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  NEW."updated_at" = NOW();
  RETURN NEW;
END; $$;


ALTER FUNCTION public.update_updated_at() OWNER TO restaurant_user;

--
-- Name: update_updatedat(); Type: FUNCTION; Schema: public; Owner: restaurant_user
--

CREATE FUNCTION public.update_updatedat() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  NEW."updatedAt" = NOW();
  RETURN NEW;
END; $$;


ALTER FUNCTION public.update_updatedat() OWNER TO restaurant_user;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: analytics_daily; Type: TABLE; Schema: public; Owner: restaurant_user
--

CREATE TABLE public.analytics_daily (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    date date NOT NULL,
    "totalSuggestions" integer DEFAULT 0,
    "approvedSuggestions" integer DEFAULT 0,
    "rejectedSuggestions" integer DEFAULT 0,
    "pendingSuggestions" integer DEFAULT 0,
    "totalVotes" integer DEFAULT 0,
    upvotes integer DEFAULT 0,
    downvotes integer DEFAULT 0,
    "uniqueSessions" integer DEFAULT 0,
    "totalPageViews" integer DEFAULT 0,
    "totalPlayTime" integer DEFAULT 0,
    "songsPlayed" integer DEFAULT 0,
    "songsSkipped" integer DEFAULT 0,
    "averageSessionDuration" double precision DEFAULT 0,
    "averageSongRating" double precision DEFAULT 0,
    "topGenres" json,
    "topArtists" json,
    "hourlyActivity" json,
    devicestats json,
    locationstats json,
    engagementstats json,
    moderationstats json,
    restaurant_id character varying(255) NOT NULL,
    "createdAt" timestamp without time zone DEFAULT now(),
    "updatedAt" timestamp without time zone DEFAULT now()
);


ALTER TABLE public.analytics_daily OWNER TO restaurant_user;

--
-- Name: client_sessions; Type: TABLE; Schema: public; Owner: restaurant_user
--

CREATE TABLE public.client_sessions (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    "sessionToken" character varying(255) NOT NULL,
    "ipAddress" inet,
    "userAgent" text,
    "deviceInfo" json,
    location json,
    "lastActivity" timestamp without time zone DEFAULT now(),
    "suggestionsCount" integer DEFAULT 0,
    "votesCount" integer DEFAULT 0,
    points integer DEFAULT 0,
    "pageViews" integer DEFAULT 0,
    "sessionDuration" integer DEFAULT 0,
    preferences json,
    "activityLog" json,
    tablenumber character varying(10),
    clientname character varying(100),
    "isActive" boolean DEFAULT true,
    restaurant_id character varying(255) NOT NULL,
    "createdAt" timestamp without time zone DEFAULT now(),
    "updatedAt" timestamp without time zone DEFAULT now()
);


ALTER TABLE public.client_sessions OWNER TO restaurant_user;

--
-- Name: competitive_votes; Type: TABLE; Schema: public; Owner: restaurant_user
--

CREATE TABLE public.competitive_votes (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    voting_session_id character varying(255) NOT NULL,
    suggestion_id uuid NOT NULL,
    voter_session_id uuid NOT NULL,
    voter_table_name character varying(100),
    rating integer NOT NULL,
    comment text,
    voter_ip character varying(100),
    voter_user_agent text,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    CONSTRAINT competitive_votes_rating_check CHECK (((rating >= 1) AND (rating <= 5)))
);


ALTER TABLE public.competitive_votes OWNER TO restaurant_user;

--
-- Name: genres; Type: TABLE; Schema: public; Owner: restaurant_user
--

CREATE TABLE public.genres (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name character varying(100) NOT NULL,
    "displayName" character varying(100) NOT NULL,
    description character varying(300),
    category character varying(50) DEFAULT 'music'::character varying,
    color character varying(20) DEFAULT '#3B82F6'::character varying,
    icon character varying(100),
    metadata json,
    priority integer DEFAULT 0,
    "isActive" boolean DEFAULT true,
    "isDefault" boolean DEFAULT false,
    "usageCount" integer DEFAULT 0,
    "lastUsedAt" timestamp without time zone,
    "createdAt" timestamp without time zone DEFAULT now(),
    "updatedAt" timestamp without time zone DEFAULT now()
);


ALTER TABLE public.genres OWNER TO restaurant_user;

--
-- Name: suggestion_genres; Type: TABLE; Schema: public; Owner: restaurant_user
--

CREATE TABLE public.suggestion_genres (
    suggestion_id uuid NOT NULL,
    genre_id uuid NOT NULL,
    "createdAt" timestamp without time zone DEFAULT now()
);


ALTER TABLE public.suggestion_genres OWNER TO restaurant_user;

--
-- Name: genre_stats; Type: VIEW; Schema: public; Owner: restaurant_user
--

CREATE VIEW public.genre_stats AS
 SELECT g.id,
    g.name,
    g."displayName",
    g.category,
    g."usageCount",
    g."lastUsedAt",
    count(sg.suggestion_id) AS suggestions_count,
    count(DISTINCT sg.suggestion_id) AS unique_suggestions
   FROM (public.genres g
     LEFT JOIN public.suggestion_genres sg ON ((g.id = sg.genre_id)))
  GROUP BY g.id, g.name, g."displayName", g.category, g."usageCount", g."lastUsedAt"
  ORDER BY g."usageCount" DESC, g."displayName";


ALTER TABLE public.genre_stats OWNER TO restaurant_user;

--
-- Name: lyrics; Type: TABLE; Schema: public; Owner: restaurant_user
--

CREATE TABLE public.lyrics (
    id character varying(255) NOT NULL,
    title character varying(300) NOT NULL,
    artist character varying(200) NOT NULL,
    album character varying(200),
    duration integer NOT NULL,
    language character varying(10) DEFAULT 'pt'::character varying,
    lines json NOT NULL,
    source character varying(50) NOT NULL,
    copyright text,
    is_explicit boolean DEFAULT false,
    has_timestamps boolean DEFAULT false,
    youtube_video_id character varying(100),
    metadata json,
    view_count integer DEFAULT 0,
    sing_along_count integer DEFAULT 0,
    last_used timestamp without time zone,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.lyrics OWNER TO restaurant_user;

--
-- Name: migrations; Type: TABLE; Schema: public; Owner: restaurant_user
--

CREATE TABLE public.migrations (
    id integer NOT NULL,
    "timestamp" bigint NOT NULL,
    name character varying NOT NULL
);


ALTER TABLE public.migrations OWNER TO restaurant_user;

--
-- Name: migrations_id_seq; Type: SEQUENCE; Schema: public; Owner: restaurant_user
--

CREATE SEQUENCE public.migrations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.migrations_id_seq OWNER TO restaurant_user;

--
-- Name: migrations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: restaurant_user
--

ALTER SEQUENCE public.migrations_id_seq OWNED BY public.migrations.id;


--
-- Name: moderation_rules; Type: TABLE; Schema: public; Owner: restaurant_user
--

CREATE TABLE public.moderation_rules (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    "ruleType" public.rule_type NOT NULL,
    action public.rule_action DEFAULT 'flag_for_review'::public.rule_action,
    "ruleValue" json NOT NULL,
    "isActive" boolean DEFAULT true,
    priority integer DEFAULT 0,
    "matchCount" integer DEFAULT 0,
    "lastMatchedAt" timestamp without time zone,
    restaurant_id character varying(255) NOT NULL,
    created_by uuid,
    "createdAt" timestamp without time zone DEFAULT now(),
    "updatedAt" timestamp without time zone DEFAULT now()
);


ALTER TABLE public.moderation_rules OWNER TO restaurant_user;

--
-- Name: payments; Type: TABLE; Schema: public; Owner: restaurant_user
--

CREATE TABLE public.payments (
    id character varying(255) NOT NULL,
    suggestion_id uuid NOT NULL,
    session_id uuid NOT NULL,
    amount integer NOT NULL,
    status character varying(50) DEFAULT 'pending'::character varying,
    status_detail character varying(255),
    payment_method character varying(50) DEFAULT 'pix'::character varying,
    external_reference character varying(255),
    qr_code text,
    qr_code_base64 text,
    ticket_url character varying(1000),
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now(),
    approved_at timestamp without time zone,
    expires_at timestamp without time zone,
    payer_email character varying(255),
    payer_name character varying(255),
    platform_fee integer DEFAULT 60,
    restaurant_amount integer DEFAULT 140,
    metadata json
);


ALTER TABLE public.payments OWNER TO restaurant_user;

--
-- Name: play_history; Type: TABLE; Schema: public; Owner: restaurant_user
--

CREATE TABLE public.play_history (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    "youtubeVideoId" character varying(100) NOT NULL,
    title character varying(300) NOT NULL,
    artist character varying(200),
    duration integer NOT NULL,
    "playDuration" integer NOT NULL,
    status public.play_status NOT NULL,
    "skipReason" text,
    "playedAt" timestamp without time zone NOT NULL,
    "endedAt" timestamp without time zone,
    "queuePosition" integer,
    "voteCount" integer DEFAULT 0,
    metadata json,
    restaurant_id character varying(255) NOT NULL,
    suggestion_id uuid,
    played_by uuid,
    "createdAt" timestamp without time zone DEFAULT now()
);


ALTER TABLE public.play_history OWNER TO restaurant_user;

--
-- Name: playlist_schedules; Type: TABLE; Schema: public; Owner: restaurant_user
--

CREATE TABLE public.playlist_schedules (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    restaurant_id character varying(255) NOT NULL,
    name character varying(100) NOT NULL,
    description text,
    "timeSlots" json NOT NULL,
    "isActive" boolean DEFAULT true,
    mode character varying(20) DEFAULT 'normal'::character varying,
    settings json,
    "createdAt" timestamp without time zone DEFAULT now(),
    "updatedAt" timestamp without time zone DEFAULT now(),
    CONSTRAINT playlist_schedules_mode_check CHECK (((mode)::text = ANY ((ARRAY['normal'::character varying, 'shuffle'::character varying, 'repeat'::character varying, 'smart'::character varying])::text[])))
);


ALTER TABLE public.playlist_schedules OWNER TO restaurant_user;

--
-- Name: playlist_tracks; Type: TABLE; Schema: public; Owner: restaurant_user
--

CREATE TABLE public.playlist_tracks (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    playlist_id uuid NOT NULL,
    title character varying(200) NOT NULL,
    artist character varying(200) NOT NULL,
    youtube_video_id character varying(50) NOT NULL,
    thumbnail_url character varying(500),
    duration integer DEFAULT 0,
    "position" integer DEFAULT 0,
    is_active boolean DEFAULT true,
    is_explicit boolean DEFAULT false,
    genre character varying(100),
    mood character varying(100),
    bpm integer,
    key character varying(20),
    energy double precision DEFAULT 0,
    valence double precision DEFAULT 0.5,
    danceability double precision DEFAULT 0.5,
    play_count integer DEFAULT 0,
    skip_count integer DEFAULT 0,
    upvotes integer DEFAULT 0,
    downvotes integer DEFAULT 0,
    average_play_duration double precision DEFAULT 0,
    completion_rate double precision DEFAULT 0,
    last_played_at timestamp without time zone,
    analytics json,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.playlist_tracks OWNER TO restaurant_user;

--
-- Name: playlists; Type: TABLE; Schema: public; Owner: restaurant_user
--

CREATE TABLE public.playlists (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name character varying(200) NOT NULL,
    description text,
    restaurant_id character varying(255) NOT NULL,
    type public.playlist_type DEFAULT 'custom'::public.playlist_type,
    status public.playlist_status DEFAULT 'active'::public.playlist_status,
    "youtubePlaylistId" character varying(255),
    "coverImage" character varying(255),
    "genreTags" json,
    "moodTags" json,
    "isDefault" boolean DEFAULT false,
    "isPublic" boolean DEFAULT true,
    "executionOrder" integer,
    "trackCount" integer DEFAULT 0,
    "totalDuration" integer DEFAULT 0,
    "playCount" integer DEFAULT 0,
    "averageRating" double precision DEFAULT 0,
    tracks json,
    settings json,
    schedule json,
    "createdAt" timestamp without time zone DEFAULT now(),
    "updatedAt" timestamp without time zone DEFAULT now()
);


ALTER TABLE public.playlists OWNER TO restaurant_user;

--
-- Name: qr_codes; Type: TABLE; Schema: public; Owner: restaurant_user
--

CREATE TABLE public.qr_codes (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    restaurant_id character varying(255) NOT NULL,
    type character varying(50) NOT NULL,
    name character varying(255) NOT NULL,
    table_number character varying(50),
    url character varying(1000) NOT NULL,
    qr_code_data text NOT NULL,
    is_active boolean DEFAULT true,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.qr_codes OWNER TO restaurant_user;

--
-- Name: restaurants; Type: TABLE; Schema: public; Owner: restaurant_user
--

CREATE TABLE public.restaurants (
    id character varying(255) NOT NULL,
    name character varying(200) NOT NULL,
    description text,
    logo character varying(255),
    phone character varying(50),
    email character varying(255),
    website character varying(255),
    address json,
    status public.restaurant_status DEFAULT 'trial'::public.restaurant_status,
    "youtubeChannelId" character varying(255),
    "youtubePremiumToken" text,
    "youtubeCredentials" json,
    settings json,
    "socialMedia" json,
    language character varying(10) DEFAULT 'pt-BR'::character varying,
    timezone character varying(50) DEFAULT 'America/Sao_Paulo'::character varying,
    "trialExpiresAt" timestamp without time zone,
    "lastActivityAt" timestamp without time zone,
    "isActive" boolean DEFAULT true,
    "businessHours" json,
    "createdAt" timestamp without time zone DEFAULT now(),
    "updatedAt" timestamp without time zone DEFAULT now()
);


ALTER TABLE public.restaurants OWNER TO restaurant_user;

--
-- Name: rewards; Type: TABLE; Schema: public; Owner: restaurant_user
--

CREATE TABLE public.rewards (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    restaurant_id character varying(255) NOT NULL,
    session_id uuid NOT NULL,
    client_name character varying(255),
    table_name character varying(100),
    type character varying(50) NOT NULL,
    title character varying(255) NOT NULL,
    description text NOT NULL,
    reward_data json NOT NULL,
    awarded_for character varying(50) NOT NULL,
    awarded_date timestamp without time zone NOT NULL,
    expires_at timestamp without time zone,
    status character varying(50) DEFAULT 'active'::character varying,
    used_at timestamp without time zone,
    usage_count integer DEFAULT 0,
    performance_data json,
    social_sharing json,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.rewards OWNER TO restaurant_user;

--
-- Name: suggestions; Type: TABLE; Schema: public; Owner: restaurant_user
--

CREATE TABLE public.suggestions (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    youtube_video_id character varying(50) NOT NULL,
    title character varying(300) NOT NULL,
    artist character varying(200),
    genre character varying(100),
    channel_name character varying(200),
    duration integer,
    thumbnail_url character varying(500),
    description text,
    status public.suggestion_status DEFAULT 'pending'::public.suggestion_status,
    source public.suggestion_source DEFAULT 'client'::public.suggestion_source,
    client_session_id character varying(255),
    client_ip inet,
    client_user_agent text,
    vote_count integer DEFAULT 0,
    upvotes integer DEFAULT 0,
    downvotes integer DEFAULT 0,
    moderated_at timestamp without time zone,
    moderation_reason text,
    played_at timestamp without time zone,
    play_duration integer,
    skip_reason text,
    metadata json,
    moderation_flags json,
    is_priority boolean DEFAULT false,
    is_paid boolean DEFAULT false,
    paid_at timestamp without time zone,
    payment_amount numeric(10,2),
    payment_id character varying(255),
    payment_status character varying(50),
    pix_code text,
    queue_position integer,
    client_name character varying(100),
    table_number integer,
    session_id character varying(255),
    completed_at timestamp without time zone,
    votes_count integer DEFAULT 0,
    rejection_reason text,
    restaurant_id character varying(255) NOT NULL,
    playlist_id uuid,
    moderated_by uuid,
    suggested_by uuid,
    created_at timestamp without time zone DEFAULT now(),
    updated_at timestamp without time zone DEFAULT now()
);


ALTER TABLE public.suggestions OWNER TO restaurant_user;

--
-- Name: users; Type: TABLE; Schema: public; Owner: restaurant_user
--

CREATE TABLE public.users (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    name character varying(255) NOT NULL,
    email character varying(255) NOT NULL,
    password character varying(255) NOT NULL,
    role public.user_role DEFAULT 'staff'::public.user_role,
    "isActive" boolean DEFAULT true,
    phone character varying(50),
    avatar character varying(255),
    "lastLoginAt" timestamp without time zone,
    "lastLoginIp" inet,
    preferences json,
    "emailVerifiedAt" timestamp without time zone,
    "emailVerificationToken" character varying(255),
    "passwordResetToken" character varying(255),
    "passwordResetExpiresAt" timestamp without time zone,
    restaurant_id character varying(255),
    "createdAt" timestamp without time zone DEFAULT now(),
    "updatedAt" timestamp without time zone DEFAULT now()
);


ALTER TABLE public.users OWNER TO restaurant_user;

--
-- Name: votes; Type: TABLE; Schema: public; Owner: restaurant_user
--

CREATE TABLE public.votes (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    vote_type public.vote_type NOT NULL,
    client_session_id character varying(255),
    session_id character varying(255),
    table_number integer,
    suggestion_id uuid,
    client_ip inet,
    client_user_agent text,
    user_id uuid,
    "createdAt" timestamp without time zone DEFAULT now()
);


ALTER TABLE public.votes OWNER TO restaurant_user;

--
-- Name: migrations id; Type: DEFAULT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.migrations ALTER COLUMN id SET DEFAULT nextval('public.migrations_id_seq'::regclass);


--
-- Name: migrations PK_8c82d7f526340ab734260ea46be; Type: CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.migrations
    ADD CONSTRAINT "PK_8c82d7f526340ab734260ea46be" PRIMARY KEY (id);


--
-- Name: analytics_daily analytics_daily_pkey; Type: CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.analytics_daily
    ADD CONSTRAINT analytics_daily_pkey PRIMARY KEY (id);


--
-- Name: client_sessions client_sessions_pkey; Type: CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.client_sessions
    ADD CONSTRAINT client_sessions_pkey PRIMARY KEY (id);


--
-- Name: client_sessions client_sessions_sessionToken_key; Type: CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.client_sessions
    ADD CONSTRAINT "client_sessions_sessionToken_key" UNIQUE ("sessionToken");


--
-- Name: competitive_votes competitive_votes_pkey; Type: CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.competitive_votes
    ADD CONSTRAINT competitive_votes_pkey PRIMARY KEY (id);


--
-- Name: genres genres_name_key; Type: CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.genres
    ADD CONSTRAINT genres_name_key UNIQUE (name);


--
-- Name: genres genres_pkey; Type: CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.genres
    ADD CONSTRAINT genres_pkey PRIMARY KEY (id);


--
-- Name: lyrics lyrics_pkey; Type: CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.lyrics
    ADD CONSTRAINT lyrics_pkey PRIMARY KEY (id);


--
-- Name: moderation_rules moderation_rules_pkey; Type: CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.moderation_rules
    ADD CONSTRAINT moderation_rules_pkey PRIMARY KEY (id);


--
-- Name: payments payments_pkey; Type: CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.payments
    ADD CONSTRAINT payments_pkey PRIMARY KEY (id);


--
-- Name: play_history play_history_pkey; Type: CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.play_history
    ADD CONSTRAINT play_history_pkey PRIMARY KEY (id);


--
-- Name: playlist_schedules playlist_schedules_pkey; Type: CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.playlist_schedules
    ADD CONSTRAINT playlist_schedules_pkey PRIMARY KEY (id);


--
-- Name: playlist_tracks playlist_tracks_pkey; Type: CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.playlist_tracks
    ADD CONSTRAINT playlist_tracks_pkey PRIMARY KEY (id);


--
-- Name: playlists playlists_pkey; Type: CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.playlists
    ADD CONSTRAINT playlists_pkey PRIMARY KEY (id);


--
-- Name: qr_codes qr_codes_pkey; Type: CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.qr_codes
    ADD CONSTRAINT qr_codes_pkey PRIMARY KEY (id);


--
-- Name: restaurants restaurants_pkey; Type: CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.restaurants
    ADD CONSTRAINT restaurants_pkey PRIMARY KEY (id);


--
-- Name: rewards rewards_pkey; Type: CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.rewards
    ADD CONSTRAINT rewards_pkey PRIMARY KEY (id);


--
-- Name: suggestion_genres suggestion_genres_pkey; Type: CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.suggestion_genres
    ADD CONSTRAINT suggestion_genres_pkey PRIMARY KEY (suggestion_id, genre_id);


--
-- Name: suggestions suggestions_pkey; Type: CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.suggestions
    ADD CONSTRAINT suggestions_pkey PRIMARY KEY (id);


--
-- Name: analytics_daily uq_analytics_daily; Type: CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.analytics_daily
    ADD CONSTRAINT uq_analytics_daily UNIQUE (restaurant_id, date);


--
-- Name: users users_email_key; Type: CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: votes votes_pkey; Type: CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.votes
    ADD CONSTRAINT votes_pkey PRIMARY KEY (id);


--
-- Name: idx_client_sessions_active; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_client_sessions_active ON public.client_sessions USING btree ("isActive");


--
-- Name: idx_client_sessions_restaurant; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_client_sessions_restaurant ON public.client_sessions USING btree (restaurant_id);


--
-- Name: idx_competitive_votes_created; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_competitive_votes_created ON public.competitive_votes USING btree (created_at);


--
-- Name: idx_competitive_votes_suggestion; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_competitive_votes_suggestion ON public.competitive_votes USING btree (suggestion_id);


--
-- Name: idx_competitive_votes_voter_session; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_competitive_votes_voter_session ON public.competitive_votes USING btree (voter_session_id);


--
-- Name: idx_competitive_votes_voting_session; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_competitive_votes_voting_session ON public.competitive_votes USING btree (voting_session_id);


--
-- Name: idx_genres_active; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_genres_active ON public.genres USING btree ("isActive");


--
-- Name: idx_genres_category; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_genres_category ON public.genres USING btree (category);


--
-- Name: idx_genres_priority; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_genres_priority ON public.genres USING btree (priority);


--
-- Name: idx_genres_usage; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_genres_usage ON public.genres USING btree ("usageCount");


--
-- Name: idx_lyrics_title_artist; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_lyrics_title_artist ON public.lyrics USING btree (title, artist);


--
-- Name: idx_lyrics_youtube; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_lyrics_youtube ON public.lyrics USING btree (youtube_video_id);


--
-- Name: idx_moderation_rules_active; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_moderation_rules_active ON public.moderation_rules USING btree ("isActive");


--
-- Name: idx_moderation_rules_restaurant_type; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_moderation_rules_restaurant_type ON public.moderation_rules USING btree (restaurant_id, "ruleType");


--
-- Name: idx_payments_status; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_payments_status ON public.payments USING btree (status);


--
-- Name: idx_payments_suggestion; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_payments_suggestion ON public.payments USING btree (suggestion_id);


--
-- Name: idx_play_history_restaurant; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_play_history_restaurant ON public.play_history USING btree (restaurant_id);


--
-- Name: idx_play_history_status; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_play_history_status ON public.play_history USING btree (status);


--
-- Name: idx_playlist_schedules_active; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_playlist_schedules_active ON public.playlist_schedules USING btree ("isActive");


--
-- Name: idx_playlist_schedules_restaurant; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_playlist_schedules_restaurant ON public.playlist_schedules USING btree (restaurant_id);


--
-- Name: idx_playlist_tracks_active; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_playlist_tracks_active ON public.playlist_tracks USING btree (is_active);


--
-- Name: idx_playlist_tracks_genre; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_playlist_tracks_genre ON public.playlist_tracks USING btree (genre);


--
-- Name: idx_playlist_tracks_mood; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_playlist_tracks_mood ON public.playlist_tracks USING btree (mood);


--
-- Name: idx_playlist_tracks_playlist; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_playlist_tracks_playlist ON public.playlist_tracks USING btree (playlist_id);


--
-- Name: idx_playlist_tracks_position; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_playlist_tracks_position ON public.playlist_tracks USING btree (playlist_id, "position");


--
-- Name: idx_playlist_tracks_youtube; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_playlist_tracks_youtube ON public.playlist_tracks USING btree (youtube_video_id);


--
-- Name: idx_playlists_default; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_playlists_default ON public.playlists USING btree ("isDefault");


--
-- Name: idx_playlists_execution_order_camel; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_playlists_execution_order_camel ON public.playlists USING btree (restaurant_id, "executionOrder");


--
-- Name: idx_playlists_restaurant; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_playlists_restaurant ON public.playlists USING btree (restaurant_id);


--
-- Name: idx_playlists_status; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_playlists_status ON public.playlists USING btree (status);


--
-- Name: idx_playlists_type; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_playlists_type ON public.playlists USING btree (type);


--
-- Name: idx_qr_codes_active; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_qr_codes_active ON public.qr_codes USING btree (is_active);


--
-- Name: idx_qr_codes_restaurant; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_qr_codes_restaurant ON public.qr_codes USING btree (restaurant_id);


--
-- Name: idx_restaurants_created; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_restaurants_created ON public.restaurants USING btree ("createdAt");


--
-- Name: idx_restaurants_status; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_restaurants_status ON public.restaurants USING btree (status);


--
-- Name: idx_rewards_awarded_date; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_rewards_awarded_date ON public.rewards USING btree (awarded_date);


--
-- Name: idx_rewards_restaurant; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_rewards_restaurant ON public.rewards USING btree (restaurant_id);


--
-- Name: idx_rewards_session; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_rewards_session ON public.rewards USING btree (session_id);


--
-- Name: idx_rewards_status; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_rewards_status ON public.rewards USING btree (status);


--
-- Name: idx_suggestion_genres_genre; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_suggestion_genres_genre ON public.suggestion_genres USING btree (genre_id);


--
-- Name: idx_suggestion_genres_suggestion; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_suggestion_genres_suggestion ON public.suggestion_genres USING btree (suggestion_id);


--
-- Name: idx_suggestions_artist_search; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_suggestions_artist_search ON public.suggestions USING gin (artist public.gin_trgm_ops);


--
-- Name: idx_suggestions_created; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_suggestions_created ON public.suggestions USING btree (created_at);


--
-- Name: idx_suggestions_playlist; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_suggestions_playlist ON public.suggestions USING btree (playlist_id);


--
-- Name: idx_suggestions_restaurant; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_suggestions_restaurant ON public.suggestions USING btree (restaurant_id);


--
-- Name: idx_suggestions_status; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_suggestions_status ON public.suggestions USING btree (status);


--
-- Name: idx_suggestions_title_search; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_suggestions_title_search ON public.suggestions USING gin (title public.gin_trgm_ops);


--
-- Name: idx_suggestions_vote_count; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_suggestions_vote_count ON public.suggestions USING btree (vote_count);


--
-- Name: idx_suggestions_youtube; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_suggestions_youtube ON public.suggestions USING btree (youtube_video_id);


--
-- Name: idx_users_active; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_users_active ON public.users USING btree ("isActive");


--
-- Name: idx_users_email; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_users_email ON public.users USING btree (email);


--
-- Name: idx_users_restaurant_role; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_users_restaurant_role ON public.users USING btree (restaurant_id, role);


--
-- Name: idx_users_role; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_users_role ON public.users USING btree (role);


--
-- Name: idx_votes_client_session_id; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_votes_client_session_id ON public.votes USING btree (client_session_id);


--
-- Name: idx_votes_created_at_camel; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_votes_created_at_camel ON public.votes USING btree ("createdAt");


--
-- Name: idx_votes_suggestion; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_votes_suggestion ON public.votes USING btree (suggestion_id);


--
-- Name: idx_votes_type; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_votes_type ON public.votes USING btree (vote_type);


--
-- Name: idx_votes_user; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE INDEX idx_votes_user ON public.votes USING btree (user_id);


--
-- Name: uq_vote_suggestion_client_session; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE UNIQUE INDEX uq_vote_suggestion_client_session ON public.votes USING btree (suggestion_id, client_session_id);


--
-- Name: uq_votes_suggestion_ip; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE UNIQUE INDEX uq_votes_suggestion_ip ON public.votes USING btree (suggestion_id, client_ip);


--
-- Name: uq_votes_suggestion_session; Type: INDEX; Schema: public; Owner: restaurant_user
--

CREATE UNIQUE INDEX uq_votes_suggestion_session ON public.votes USING btree (suggestion_id, session_id);


--
-- Name: analytics_daily trg_analytics_daily_updated; Type: TRIGGER; Schema: public; Owner: restaurant_user
--

CREATE TRIGGER trg_analytics_daily_updated BEFORE UPDATE ON public.analytics_daily FOR EACH ROW EXECUTE FUNCTION public.update_updatedat();


--
-- Name: client_sessions trg_client_sessions_updated; Type: TRIGGER; Schema: public; Owner: restaurant_user
--

CREATE TRIGGER trg_client_sessions_updated BEFORE UPDATE ON public.client_sessions FOR EACH ROW EXECUTE FUNCTION public.update_updatedat();


--
-- Name: competitive_votes trg_competitive_votes_updated; Type: TRIGGER; Schema: public; Owner: restaurant_user
--

CREATE TRIGGER trg_competitive_votes_updated BEFORE UPDATE ON public.competitive_votes FOR EACH ROW EXECUTE FUNCTION public.update_updated_at();


--
-- Name: genres trg_genres_updated; Type: TRIGGER; Schema: public; Owner: restaurant_user
--

CREATE TRIGGER trg_genres_updated BEFORE UPDATE ON public.genres FOR EACH ROW EXECUTE FUNCTION public.update_updatedat();


--
-- Name: lyrics trg_lyrics_updated; Type: TRIGGER; Schema: public; Owner: restaurant_user
--

CREATE TRIGGER trg_lyrics_updated BEFORE UPDATE ON public.lyrics FOR EACH ROW EXECUTE FUNCTION public.update_updated_at();


--
-- Name: moderation_rules trg_moderation_rules_updated; Type: TRIGGER; Schema: public; Owner: restaurant_user
--

CREATE TRIGGER trg_moderation_rules_updated BEFORE UPDATE ON public.moderation_rules FOR EACH ROW EXECUTE FUNCTION public.update_updatedat();


--
-- Name: payments trg_payments_updated; Type: TRIGGER; Schema: public; Owner: restaurant_user
--

CREATE TRIGGER trg_payments_updated BEFORE UPDATE ON public.payments FOR EACH ROW EXECUTE FUNCTION public.update_updated_at();


--
-- Name: playlist_schedules trg_playlist_schedules_updated; Type: TRIGGER; Schema: public; Owner: restaurant_user
--

CREATE TRIGGER trg_playlist_schedules_updated BEFORE UPDATE ON public.playlist_schedules FOR EACH ROW EXECUTE FUNCTION public.update_updatedat();


--
-- Name: playlist_tracks trg_playlist_tracks_updated; Type: TRIGGER; Schema: public; Owner: restaurant_user
--

CREATE TRIGGER trg_playlist_tracks_updated BEFORE UPDATE ON public.playlist_tracks FOR EACH ROW EXECUTE FUNCTION public.update_updated_at();


--
-- Name: playlists trg_playlists_updated; Type: TRIGGER; Schema: public; Owner: restaurant_user
--

CREATE TRIGGER trg_playlists_updated BEFORE UPDATE ON public.playlists FOR EACH ROW EXECUTE FUNCTION public.update_updatedat();


--
-- Name: qr_codes trg_qr_codes_updated; Type: TRIGGER; Schema: public; Owner: restaurant_user
--

CREATE TRIGGER trg_qr_codes_updated BEFORE UPDATE ON public.qr_codes FOR EACH ROW EXECUTE FUNCTION public.update_updated_at();


--
-- Name: restaurants trg_restaurants_updated; Type: TRIGGER; Schema: public; Owner: restaurant_user
--

CREATE TRIGGER trg_restaurants_updated BEFORE UPDATE ON public.restaurants FOR EACH ROW EXECUTE FUNCTION public.update_updatedat();


--
-- Name: rewards trg_rewards_updated; Type: TRIGGER; Schema: public; Owner: restaurant_user
--

CREATE TRIGGER trg_rewards_updated BEFORE UPDATE ON public.rewards FOR EACH ROW EXECUTE FUNCTION public.update_updated_at();


--
-- Name: suggestions trg_suggestions_updated; Type: TRIGGER; Schema: public; Owner: restaurant_user
--

CREATE TRIGGER trg_suggestions_updated BEFORE UPDATE ON public.suggestions FOR EACH ROW EXECUTE FUNCTION public.update_updated_at();


--
-- Name: votes trg_update_vote_counts; Type: TRIGGER; Schema: public; Owner: restaurant_user
--

CREATE TRIGGER trg_update_vote_counts AFTER INSERT OR DELETE OR UPDATE ON public.votes FOR EACH ROW EXECUTE FUNCTION public.update_suggestion_vote_counts();


--
-- Name: users trg_users_updated; Type: TRIGGER; Schema: public; Owner: restaurant_user
--

CREATE TRIGGER trg_users_updated BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION public.update_updatedat();


--
-- Name: analytics_daily analytics_daily_restaurant_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.analytics_daily
    ADD CONSTRAINT analytics_daily_restaurant_id_fkey FOREIGN KEY (restaurant_id) REFERENCES public.restaurants(id) ON DELETE CASCADE;


--
-- Name: client_sessions client_sessions_restaurant_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.client_sessions
    ADD CONSTRAINT client_sessions_restaurant_id_fkey FOREIGN KEY (restaurant_id) REFERENCES public.restaurants(id) ON DELETE CASCADE;


--
-- Name: competitive_votes competitive_votes_suggestion_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.competitive_votes
    ADD CONSTRAINT competitive_votes_suggestion_id_fkey FOREIGN KEY (suggestion_id) REFERENCES public.suggestions(id) ON DELETE CASCADE;


--
-- Name: moderation_rules moderation_rules_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.moderation_rules
    ADD CONSTRAINT moderation_rules_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: moderation_rules moderation_rules_restaurant_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.moderation_rules
    ADD CONSTRAINT moderation_rules_restaurant_id_fkey FOREIGN KEY (restaurant_id) REFERENCES public.restaurants(id) ON DELETE CASCADE;


--
-- Name: payments payments_suggestion_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.payments
    ADD CONSTRAINT payments_suggestion_id_fkey FOREIGN KEY (suggestion_id) REFERENCES public.suggestions(id) ON DELETE CASCADE;


--
-- Name: play_history play_history_played_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.play_history
    ADD CONSTRAINT play_history_played_by_fkey FOREIGN KEY (played_by) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: play_history play_history_restaurant_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.play_history
    ADD CONSTRAINT play_history_restaurant_id_fkey FOREIGN KEY (restaurant_id) REFERENCES public.restaurants(id) ON DELETE CASCADE;


--
-- Name: play_history play_history_suggestion_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.play_history
    ADD CONSTRAINT play_history_suggestion_id_fkey FOREIGN KEY (suggestion_id) REFERENCES public.suggestions(id) ON DELETE SET NULL;


--
-- Name: playlist_schedules playlist_schedules_restaurant_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.playlist_schedules
    ADD CONSTRAINT playlist_schedules_restaurant_id_fkey FOREIGN KEY (restaurant_id) REFERENCES public.restaurants(id) ON DELETE CASCADE;


--
-- Name: playlist_tracks playlist_tracks_playlist_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.playlist_tracks
    ADD CONSTRAINT playlist_tracks_playlist_id_fkey FOREIGN KEY (playlist_id) REFERENCES public.playlists(id) ON DELETE CASCADE;


--
-- Name: playlists playlists_restaurant_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.playlists
    ADD CONSTRAINT playlists_restaurant_id_fkey FOREIGN KEY (restaurant_id) REFERENCES public.restaurants(id) ON DELETE CASCADE;


--
-- Name: qr_codes qr_codes_restaurant_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.qr_codes
    ADD CONSTRAINT qr_codes_restaurant_id_fkey FOREIGN KEY (restaurant_id) REFERENCES public.restaurants(id) ON DELETE CASCADE;


--
-- Name: rewards rewards_restaurant_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.rewards
    ADD CONSTRAINT rewards_restaurant_id_fkey FOREIGN KEY (restaurant_id) REFERENCES public.restaurants(id) ON DELETE CASCADE;


--
-- Name: suggestion_genres suggestion_genres_genre_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.suggestion_genres
    ADD CONSTRAINT suggestion_genres_genre_id_fkey FOREIGN KEY (genre_id) REFERENCES public.genres(id) ON DELETE CASCADE;


--
-- Name: suggestion_genres suggestion_genres_suggestion_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.suggestion_genres
    ADD CONSTRAINT suggestion_genres_suggestion_id_fkey FOREIGN KEY (suggestion_id) REFERENCES public.suggestions(id) ON DELETE CASCADE;


--
-- Name: suggestions suggestions_moderated_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.suggestions
    ADD CONSTRAINT suggestions_moderated_by_fkey FOREIGN KEY (moderated_by) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: suggestions suggestions_playlist_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.suggestions
    ADD CONSTRAINT suggestions_playlist_id_fkey FOREIGN KEY (playlist_id) REFERENCES public.playlists(id) ON DELETE SET NULL;


--
-- Name: suggestions suggestions_restaurant_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.suggestions
    ADD CONSTRAINT suggestions_restaurant_id_fkey FOREIGN KEY (restaurant_id) REFERENCES public.restaurants(id) ON DELETE CASCADE;


--
-- Name: suggestions suggestions_suggested_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.suggestions
    ADD CONSTRAINT suggestions_suggested_by_fkey FOREIGN KEY (suggested_by) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: users users_restaurant_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_restaurant_id_fkey FOREIGN KEY (restaurant_id) REFERENCES public.restaurants(id) ON DELETE SET NULL;


--
-- Name: votes votes_suggestion_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.votes
    ADD CONSTRAINT votes_suggestion_id_fkey FOREIGN KEY (suggestion_id) REFERENCES public.suggestions(id) ON DELETE CASCADE;


--
-- Name: votes votes_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: restaurant_user
--

ALTER TABLE ONLY public.votes
    ADD CONSTRAINT votes_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- PostgreSQL database dump complete
--

