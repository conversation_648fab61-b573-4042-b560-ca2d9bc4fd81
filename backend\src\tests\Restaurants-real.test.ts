import express from 'express';
import request from 'supertest';
import jwt from 'jsonwebtoken';
import { TestDataSource, initializeTestDatabase, cleanTestData, closeTestDatabase } from '../config/test-database';
import { Restaurant } from '../models/Restaurant';
import restaurantsRouter from '../routes/restaurants';
// Mock da <PERSON> do YouTube (apenas para rota de playlist colaborativa)
jest.mock('googleapis', () => ({
  google: {
    youtube: jest.fn(() => ({
      playlists: {
        insert: jest.fn().mockResolvedValue({ data: { id: 'yt-playlist-mock' } })
      },
      playlistItems: {
        insert: jest.fn().mockResolvedValue({ data: { id: 'yt-item-mock' } }),
        list: jest.fn().mockResolvedValue({ data: { items: [] } }),
        update: jest.fn().mockResolvedValue({})
      }
    })),
    auth: {
      OAuth2: jest.fn().mockImplementation(() => ({ setCredentials: jest.fn() }))
    }
  }
}));
// Importar depois do mock
import collaborativePlaylistRoutes from '../routes/collaborativePlaylist';
import { User, UserRole } from '../models/User';
import { generateToken } from '../middleware/auth';

// Usaremos o banco real (Docker) via TestDataSource e redirecionaremos AppDataSource
jest.mock('../config/database', () => ({ AppDataSource: {} }));

describe('REST - Criação de Restaurantes (dados reais)', () => {
  const basePath = '/api/v1/restaurants';
  let app: express.Express;

  beforeAll(async () => {
    await initializeTestDatabase();

    // Montar app mínimo só com JSON e rota de restaurantes
    app = express();
    app.use(express.json());
    app.use(basePath, restaurantsRouter);
  app.use('/api/v1/collaborative-playlist', collaborativePlaylistRoutes);

  // Garantir JWT_SECRET para autenticação
  process.env.JWT_SECRET = process.env.JWT_SECRET || 'test-secret';
  });

  afterAll(async () => {
    await closeTestDatabase();
  });

  beforeEach(async () => {
    // Limpar dados
    await cleanTestData();

    // Redirecionar AppDataSource.getRepository para TestDataSource
    const { AppDataSource } = require('../config/database');
    AppDataSource.getRepository = jest.fn((entity: any) => TestDataSource.getRepository(entity));
  });

  it('deve criar um restaurante com id fornecido (slug estável) e validar no banco', async () => {
    const payload = {
      id: 'novo-restaurante',
      name: 'Novo Restaurante',
      description: 'Teste de criação real',
    };

    const res = await request(app).post(basePath).send(payload);
    expect(res.status).toBe(201);
    expect(res.body?.success).toBe(true);
    expect(res.body?.restaurant?.id).toBe(payload.id);
    expect(res.body?.restaurant?.name).toBe(payload.name);

    const repo = TestDataSource.getRepository(Restaurant);
    const saved = await repo.findOne({ where: { id: payload.id } });
    expect(saved).toBeTruthy();
    expect(saved?.name).toBe(payload.name);
    expect(saved?.isActive).toBe(true);
    expect(saved?.status).toBe('trial');
  });

  it('deve listar restaurantes ativos e conter o recém-criado', async () => {
    const repo = TestDataSource.getRepository(Restaurant);
    await repo.save(repo.create({ id: 'listar-rest', name: 'Listar Rest', isActive: true }));

    const res = await request(app).get(basePath);
    expect(res.status).toBe(200);
    const ids = (res.body?.restaurants || []).map((r: any) => r.id);
    expect(ids).toContain('listar-rest');
  });

  it('deve obter restaurante por id', async () => {
    const repo = TestDataSource.getRepository(Restaurant);
    await repo.save(repo.create({ id: 'get-rest', name: 'Get Rest', isActive: true }));

    const res = await request(app).get(`${basePath}/get-rest`);
    expect(res.status).toBe(200);
    expect(res.body?.restaurant?.id).toBe('get-rest');
  });

  it('deve criar playlist colaborativa após criar restaurante (fluxo inicial)', async () => {
    // 1) Criar restaurante
    const restRepo = TestDataSource.getRepository(Restaurant);
    const restaurant = restRepo.create({ id: 'rest-collab', name: 'Rest Collab', isActive: true });
    await restRepo.save(restaurant);

    // 2) Criar usuário e token
    const userRepo = TestDataSource.getRepository(User);
    const user = userRepo.create({
      name: 'Owner',
      email: `owner_${Date.now()}@test.com`,
      password: 'secret123',
      role: UserRole.ADMIN,
      isActive: true,
      restaurant
    });
    await userRepo.save(user);
    const token = generateToken(user);

    // 3) Criar playlist colaborativa via API
    const res = await request(app)
      .post(`/api/v1/collaborative-playlist/${restaurant.id}/create`)
      .set('Authorization', `Bearer ${token}`)
      .send({ playlistName: 'Playlist Principal', description: 'Colab test' });

    expect(res.status).toBe(201);
    expect(res.body?.success).toBe(true);
    expect(res.body?.data?.youtubePlaylistId || res.body?.youtubePlaylistId).toBeDefined();
  });

  it('deve retornar 409 ao tentar criar com id duplicado explícito', async () => {
    const repo = TestDataSource.getRepository(Restaurant);
    await repo.save(repo.create({ id: 'id-duplicado', name: 'Primeiro' }));

    const res = await request(app)
      .post(basePath)
      .send({ id: 'id-duplicado', name: 'Segundo' });

    expect(res.status).toBe(409);
    expect(res.body?.error).toBeDefined();
  });

  it('deve gerar slug a partir do nome quando id não for enviado e resolver conflito com sufixo -2', async () => {
    const repo = TestDataSource.getRepository(Restaurant);
    await repo.save(repo.create({ id: 'bar-do-joao', name: 'Bar do João' }));

    const res = await request(app)
      .post(basePath)
      .send({ name: 'Bar do João' });

    expect(res.status).toBe(201);
    expect(res.body?.success).toBe(true);
    // Deve criar bar-do-joao-2
    expect(res.body?.restaurant?.id).toBe('bar-do-joao-2');
  });

  it('deve aplicar defaults de language/timezone e settings', async () => {
    const res = await request(app)
      .post(basePath)
      .send({ name: 'Cafeteria Central' });

    expect(res.status).toBe(201);
  const r = res.body?.restaurant;
  // A resposta pública não expõe language/timezone; validar via banco
  const repo = TestDataSource.getRepository(Restaurant);
  const saved = await repo.findOne({ where: { id: r.id } });
  expect(saved?.language).toBe('pt-BR');
  expect(saved?.timezone).toBe('America/Sao_Paulo');
  // A resposta pública inclui parte de settings
  expect(r.settings).toBeDefined();
  });
});
