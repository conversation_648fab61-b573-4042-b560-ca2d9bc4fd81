$ErrorActionPreference = 'Stop'
$headers = @{
  'Content-Type' = 'application/json'
  'x-session-id' = 'test-session-001'
}
$bodyObj = @{ 
  restaurantId = 'demo-restaurant'
  song = @{ videoId = 'zqi6eTLw-mE'; title = 'Test Song'; artist = 'Test Artist' }
  isPriority = $false 
}
$body = $bodyObj | ConvertTo-Json -Depth 5
$response = Invoke-RestMethod -Uri 'http://localhost:8001/api/v1/playback-queue/add' -Method Post -Headers $headers -Body $body -ContentType 'application/json' -UseBasicParsing
$response | ConvertTo-Json -Depth 8
