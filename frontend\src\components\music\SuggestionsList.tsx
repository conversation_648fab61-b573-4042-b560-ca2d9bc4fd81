import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Music, Clock, Play, CheckCircle, XCircle, AlertCircle } from "lucide-react";

// Componentes
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import VoteButton from "@/components/ui/VoteButton";
import Button from "@/components/ui/Button";

// Tipos
import { Suggestion, PaginatedResponse } from "@/types";

interface SuggestionsListProps {
  suggestions: Suggestion[];
  isLoading: boolean;
  pagination?: PaginatedResponse<Suggestion>["pagination"];
  onVote?: (suggestionId: string, voteType: "up" | "down") => void;
  onLoadMore?: () => void;
}

const SuggestionsList: React.FC<SuggestionsListProps> = ({
  suggestions,
  isLoading,
  pagination,
  onVote,
  onLoadMore,
}) => {
  const [votingStates, setVotingStates] = useState<Record<string, boolean>>({});

  // Função para votar
  const handleVote = async (suggestionId: string, voteType: "up" | "down") => {
    if (votingStates[suggestionId] || !onVote) return;

    setVotingStates((prev) => ({ ...prev, [suggestionId]: true }));

    try {
      await onVote(suggestionId, voteType);
    } finally {
      setVotingStates((prev) => ({ ...prev, [suggestionId]: false }));
    }
  };

  // Função para obter configuração do status
  const getStatusConfig = (status: Suggestion["status"]) => {
    switch (status) {
      case "pending":
        return {
          icon: AlertCircle,
          label: "Pendente",
          color: "text-yellow-600 dark:text-yellow-400",
          bgColor: "bg-yellow-100 dark:bg-yellow-900/20",
        };
      case "playing":
        return {
          icon: Play,
          label: "Tocando",
          color: "text-blue-600 dark:text-blue-400",
          bgColor: "bg-blue-100 dark:bg-blue-900/20",
        };
      case "played":
        return {
          icon: CheckCircle,
          label: "Tocada",
          color: "text-gray-600 dark:text-gray-400",
          bgColor: "bg-gray-100 dark:bg-gray-900/20",
        };
      case "skipped":
        return {
          icon: XCircle,
          label: "Pulada",
          color: "text-red-600 dark:text-red-400",
          bgColor: "bg-red-100 dark:bg-red-900/20",
        };
      default:
        return {
          icon: AlertCircle,
          label: String(status),
          color: "text-gray-600 dark:text-gray-400",
          bgColor: "bg-gray-100 dark:bg-gray-900/20",
        };
    }
  };

  if (isLoading && suggestions.length === 0) {
    return (
      <div className="card p-8 text-center">
        <LoadingSpinner size="lg" />
        <p className="mt-4 text-gray-600 dark:text-gray-400">
          Carregando sugestões...
        </p>
      </div>
    );
  }

  if (suggestions.length === 0) {
    return (
      <div className="card p-8 text-center">
        <div className="w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
          <Music className="w-8 h-8 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Nenhuma sugestão encontrada
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Seja o primeiro a sugerir uma música!
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Lista de sugestões */}
      <div className="space-y-3">
        <AnimatePresence>
          {suggestions.map((suggestion, index) => {
            const statusConfig = getStatusConfig(suggestion.status);
            const StatusIcon = statusConfig.icon;

            return (
              <motion.div
                key={suggestion.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: index * 0.05 }}
                className="music-card"
              >
                <div className="flex space-x-4">
                  {/* Thumbnail */}
                  <div className="flex-shrink-0">
                    <div className="relative w-20 h-15 rounded-lg overflow-hidden bg-gray-200 dark:bg-gray-700">
                      <img
                        src={suggestion.thumbnailUrl}
                        alt={suggestion.title}
                        className="w-full h-full object-cover"
                        loading="lazy"
                      />

                      {/* Overlay de status */}
                      <div className="absolute top-1 right-1">
                        <div
                          className={`
                          w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0
                          ${statusConfig.bgColor}
                        `}
                        >
                          <StatusIcon
                            className={`w-3 h-3 flex-shrink-0 ${statusConfig.color}`}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Informações da música */}
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-gray-900 dark:text-white truncate">
                      {suggestion.title}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                      {suggestion.artist}
                    </p>

                    {/* Metadados */}
                    <div className="flex items-center space-x-3 mt-1 text-xs text-gray-500 dark:text-gray-500">
                      <div className="flex items-center space-x-1">
                        <Clock className="w-3 h-3 flex-shrink-0" />
                        <span>{suggestion.formattedDuration}</span>
                      </div>

                      {/* Status */}
                      <span
                        className={`
                        px-2 py-0.5 rounded-full text-xs font-medium
                        ${statusConfig.bgColor} ${statusConfig.color}
                      `}
                      >
                        {statusConfig.label}
                      </span>

                      {/* Posição na fila */}
                      {suggestion.queuePosition && (
                        <span className="text-xs text-gray-500">
                          #{suggestion.queuePosition} na fila
                        </span>
                      )}
                    </div>

                    {/* Tags */}
                    <div className="flex items-center space-x-2 mt-2">
                      {suggestion.isExplicit && (
                        <span className="px-1.5 py-0.5 bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 rounded text-xs">
                          Explícito
                        </span>
                      )}

                      {suggestion.isLive && (
                        <span className="px-1.5 py-0.5 bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 rounded text-xs">
                          Ao vivo
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Votos e ações */}
                  <div className="flex items-center space-x-3">
                    {/* Contador de votos */}
                    <div className="text-center">
                      <div
                        className={`text-lg font-bold ${
                          suggestion.voteCount > 0
                            ? "text-green-600 dark:text-green-400"
                            : suggestion.voteCount < 0
                            ? "text-red-600 dark:text-red-400"
                            : "text-gray-600 dark:text-gray-400"
                        }`}
                      >
                        {suggestion.voteCount > 0 ? "+" : ""}
                        {suggestion.voteCount}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-500">
                        votos
                      </div>
                    </div>

                    {/* Botões de voto */}
                    {suggestion.canBeVoted && (
                      <div className="flex flex-col space-y-1">
                        <VoteButton
                          type="up"
                          onClick={() => handleVote(suggestion.id, "up")}
                          disabled={votingStates[suggestion.id]}
                          count={suggestion.upvotes}
                          size="sm"
                          variant="minimal"
                        />

                        <VoteButton
                          type="down"
                          onClick={() => handleVote(suggestion.id, "down")}
                          disabled={votingStates[suggestion.id]}
                          count={suggestion.downvotes}
                          size="sm"
                          variant="minimal"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>

      {/* Paginação */}
      {pagination && pagination.hasNext && (
        <div className="text-center pt-4">
          <Button
            onClick={onLoadMore}
            disabled={isLoading}
            variant="outline"
            className="w-full sm:w-auto"
          >
            {isLoading ? (
              <>
                <LoadingSpinner size="sm" />
                Carregando...
              </>
            ) : (
              `Carregar mais (${
                pagination.total - suggestions.length
              } restantes)`
            )}
          </Button>
        </div>
      )}

      {/* Informações da paginação */}
      {pagination && (
        <div className="text-center text-sm text-gray-500 dark:text-gray-400">
          Mostrando {suggestions.length} de {pagination.total} sugestões
        </div>
      )}
    </div>
  );
};

export default SuggestionsList;
