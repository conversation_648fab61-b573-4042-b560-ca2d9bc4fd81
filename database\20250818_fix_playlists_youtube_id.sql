-- Idempotent fix: alinhar identificadores de YouTube (snake/camel) e índices

-- Se houver tabela suggestions com "youtubeVideoId", garantir espelho snake e índice
DO $$
BEGIN
	IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name='suggestions') THEN
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='youtube_video_id')
			 AND EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='youtubeVideoId') THEN
			EXECUTE 'ALTER TABLE suggestions ADD COLUMN youtube_video_id VARCHAR(100)';
			EXECUTE 'UPDATE suggestions SET youtube_video_id = "youtubeVideoId" WHERE youtube_video_id IS NULL';
		END IF;
		CREATE INDEX IF NOT EXISTS idx_suggestions_youtube_snake ON suggestions(youtube_video_id);
	END IF;
END $$;

-- Garantir índices de YouTube para playlist_tracks e play_history
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_youtube ON playlist_tracks(youtube_video_id);
DO $$
BEGIN
	IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name='play_history') THEN
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='play_history' AND column_name='youtubeVideoId') THEN
			-- criar coluna camelCase para compat (alguns serviços consultam play_history.youtubeVideoId)
			EXECUTE 'ALTER TABLE play_history ADD COLUMN "youtubeVideoId" VARCHAR(50)';
		END IF;
		CREATE INDEX IF NOT EXISTS idx_play_history_youtube ON play_history("youtubeVideoId");
	END IF;
END $$;
