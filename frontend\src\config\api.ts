// Configuração da API
export const API_CONFIG = {
  // Use variável de ambiente ou fallback para desenvolvimento local
  BASE_URL: import.meta.env.VITE_API_URL || "http://localhost:8001",
  API_VERSION: "v1",
  get API_BASE() {
    return `${this.BASE_URL}/api/${this.API_VERSION}`;
  },

  // Endpoints específicos
  ENDPOINTS: {
    RESTAURANTS: "/restaurants",
    SUGGESTIONS: "/suggestions",
    PLAYBACK: "/playback",
    ANALYTICS: "/analytics",
    NOTIFICATIONS: "/notifications",
    GENRES: "/genres",
  },
};

// Helper para construir URLs da API
export const buildApiUrl = (
  endpoint: string,
  params?: Record<string, string>
) => {
  try {
    const envBase = (import.meta as any).env?.VITE_API_URL as string | undefined;
    const base = (envBase && envBase.trim() !== "" ? envBase : "http://localhost:8001").replace(/\/$/, "");
    // Constrói URL absoluta sempre (evita cair em /api no host 8000 quando não há proxy)
    const url = new URL(`/api/${API_CONFIG.API_VERSION}${endpoint}`, base);

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value && value.trim() !== "") {
          url.searchParams.append(key, value);
        }
      });
    }

    return url.toString();
  } catch (error) {
    console.error("❌ Erro ao construir URL da API:", error, {
      endpoint,
      params,
    });
    // Fallback
    return `/api/v1${endpoint}`;
  }
};

// Headers com autenticação opcional
export const getAuthHeaders = (
  contentType: string = "application/json"
): Record<string, string> => {
  const token =
    typeof localStorage !== "undefined"
      ? localStorage.getItem("authToken")
      : null;
  const headers: Record<string, string> = {};
  if (contentType) headers["Content-Type"] = contentType;
  if (token) headers["Authorization"] = `Bearer ${token}`;
  return headers;
};
