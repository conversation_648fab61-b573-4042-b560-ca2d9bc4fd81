-- Seed demo restaurant if missing (idempotent)
INSERT INTO restaurants (
    id, name, description, phone, email, website, address, status,
    settings, language, timezone, "isActive", "subscriptionPlan",
    "maxUsers", "maxPlaylists", "maxSuggestions", "storageLimit",
    "apiUsage", metadata, "createdAt", "updatedAt", logo,
    "youtubeChannelId", "socialMedia", "businessHours"
)
SELECT
    'demo-restaurant',
    'Restaurante Demo',
    'Restaurante de demonstração para testes do sistema de playlist colaborativa',
    '+55 11 99999-9999',
    '<EMAIL>',
    'https://demo-restaurante.com',
    '{"street": "Rua Demo, 123", "city": "São Paulo", "state": "SP", "zipCode": "01234-567", "country": "Brasil"}'::json,
    'active',
    '{"allowSuggestions": true, "moderationEnabled": false, "maxSuggestionsPerUser": 5, "votingEnabled": true, "pixPayments": true, "autoPlay": true, "playlist": {"defaultVolume": 70}}'::json,
    'pt-BR',
    'America/Sao_Paulo',
    true,
    'premium',
    50,
    10,
    1000,
    ***********,
    '{"requests": 0, "lastReset": "2024-01-01T00:00:00Z"}'::json,
    '{"demo": true, "setupComplete": true, "paymentEnabled": true}'::json,
    NOW(),
    NOW(),
    'https://placehold.co/200x200/6366f1/ffffff?text=DEMO',
    'UCdemoChannelId123',
    '{"instagram": "@demo_restaurant", "facebook": "DemoRestaurant"}'::json,
    '{"monday": {"open": "11:00", "close": "23:00"}, "tuesday": {"open": "11:00", "close": "23:00"}, "wednesday": {"open": "11:00", "close": "23:00"}, "thursday": {"open": "11:00", "close": "23:00"}, "friday": {"open": "11:00", "close": "00:00"}, "saturday": {"open": "11:00", "close": "00:00"}, "sunday": {"open": "11:00", "close": "22:00"}}'::json
WHERE NOT EXISTS (
    SELECT 1 FROM restaurants WHERE id = 'demo-restaurant'
);

-- Criar playlist demo se não existir
INSERT INTO playlists (
    id, name, description, type, status, "youtubePlaylistId",
    "isDefault", "isPublic", restaurant_id, execution_order,
    is_active, "createdAt", "updatedAt"
)
SELECT
    'demo-playlist-1',
    'Playlist Demo Principal',
    'Playlist principal do restaurante demo com músicas populares',
    'youtube',
    'active',
    'PLdemoPlaylistId123',
    true,
    true,
    'demo-restaurant',
    1,
    true,
    NOW(),
    NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM playlists WHERE id = 'demo-playlist-1'
);

-- Criar usuário admin demo se não existir
INSERT INTO users (
    id, email, name, role, "isActive", restaurant_id,
    "createdAt", "updatedAt"
)
SELECT
    'demo-admin-user',
    '<EMAIL>',
    'Admin Demo',
    'admin',
    true,
    'demo-restaurant',
    NOW(),
    NOW()
WHERE NOT EXISTS (
    SELECT 1 FROM users WHERE id = 'demo-admin-user'
);

