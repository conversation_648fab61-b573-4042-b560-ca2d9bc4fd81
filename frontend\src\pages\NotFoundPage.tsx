import React from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { Home, ArrowLeft, Search, Music } from 'lucide-react';
import Button from '@/components/ui/Button';

const NotFoundPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center px-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center max-w-md mx-auto"
      >
        {/* Ilustração 404 */}
        <motion.div
          initial={{ scale: 0.8 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <div className="relative">
            {/* Número 404 */}
            <div className="text-8xl md:text-9xl font-bold text-gray-200 dark:text-gray-700 select-none">
              404
            </div>
            
            {/* Ícone de música sobreposto */}
            <div className="absolute inset-0 flex items-center justify-center">
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                className="w-16 h-16 bg-primary-500 rounded-full flex items-center justify-center shadow-lg"
              >
                <Music className="w-8 h-8 text-white" />
              </motion.div>
            </div>
          </div>
        </motion.div>

        {/* Título e descrição */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
          className="mb-8"
        >
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Página Não Encontrada
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mb-2">
            Ops! A página que você está procurando não existe ou foi movida.
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Que tal voltar e descobrir uma nova música?
          </p>
        </motion.div>

        {/* Ações */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="space-y-4"
        >
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button
              onClick={() => navigate('/')}
              icon={<Home className="w-4 h-4" />}
              size="lg"
            >
              Página Inicial
            </Button>
            
            <Button
              onClick={() => navigate(-1)}
              variant="outline"
              icon={<ArrowLeft className="w-4 h-4" />}
              size="lg"
            >
              Voltar
            </Button>
          </div>

          {/* Link para busca */}
          <div className="pt-4">
            <button
              onClick={() => navigate('/')}
              className="inline-flex items-center space-x-2 text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors"
            >
              <Search className="w-4 h-4" />
              <span className="text-sm">Procurar restaurantes</span>
            </button>
          </div>
        </motion.div>

        {/* Sugestões úteis */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
          className="mt-12 p-4 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-200 dark:border-gray-700"
        >
          <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
            Você pode estar procurando por:
          </h3>
          <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
            <li>• Página inicial do sistema</li>
            <li>• Painel administrativo (/admin)</li>
            <li>• Página de um restaurante específico</li>
          </ul>
        </motion.div>

        {/* Elementos decorativos */}
        <div className="absolute inset-0 -z-10 overflow-hidden">
          <motion.div
            animate={{
              x: [0, 100, 0],
              y: [0, -100, 0],
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: "linear"
            }}
            className="absolute top-1/4 left-1/4 w-32 h-32 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-10"
          />
          <motion.div
            animate={{
              x: [0, -100, 0],
              y: [0, 100, 0],
            }}
            transition={{
              duration: 15,
              repeat: Infinity,
              ease: "linear"
            }}
            className="absolute bottom-1/4 right-1/4 w-32 h-32 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-10"
          />
        </div>
      </motion.div>
    </div>
  );
};

export default NotFoundPage;
