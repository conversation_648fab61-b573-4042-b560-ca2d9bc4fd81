-- Ensure playlists table has expected columns and case
ALTER TABLE IF EXISTS playlists
	ADD COLUMN IF NOT EXISTS "youtubePlaylistId" VARCHAR NULL,
	ADD COLUMN IF NOT EXISTS tracks <PERSON><PERSON><PERSON> NULL,
	ADD COLUMN IF NOT EXISTS "isDefault" BOOLEAN DEFAULT FALSE,
	ADD COLUMN IF NOT EXISTS "isPublic" BOOLEAN DEFAULT TRUE,
	ADD COLUMN IF NOT EXISTS execution_order INTEGER NULL;

-- Backfill execution_order if missing using created_at or "createdAt" (conforme existir)
DO $$
BEGIN
	IF EXISTS (
		SELECT 1 FROM information_schema.columns 
		WHERE table_name='playlists' AND column_name='created_at'
	) THEN
		UPDATE playlists
		SET execution_order = sub.rn
		FROM (
			SELECT id, ROW_NUMBER() OVER (ORDER BY COALESCE(created_at, now())) AS rn
			FROM playlists
		) sub
		WHERE playlists.id = sub.id AND playlists.execution_order IS NULL;
	ELSE
		UPDATE playlists
		SET execution_order = sub.rn
		FROM (
			SELECT id, ROW_NUMBER() OVER (ORDER BY COALESCE("createdAt", now())) AS rn
			FROM playlists
		) sub
		WHERE playlists.id = sub.id AND playlists.execution_order IS NULL;
	END IF;
END $$;

