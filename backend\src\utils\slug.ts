// Utilitário simples para gerar/normalizar slugs estáveis
// - caracteres minúsculos a-z, números e hífen
// - remove acentos e espaços
export function toSlug(input: string): string {
  if (!input) return "";
  const normalized = input
    .toString()
    .normalize("NFD")
    .replace(/\p{Diacritic}/gu, "") // remove acentos
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9\s-]/g, "") // remove caracteres inválidos
    .replace(/[\s_]+/g, "-") // espaços/underscores -> hífen
    .replace(/-+/g, "-") // colapsa hífens
    .replace(/^-|-$/g, ""); // trim hífens
  return normalized || "restaurante";
}

export function isValidSlug(slug: string): boolean {
  return /^[a-z0-9-]{3,}$/.test(slug);
}
