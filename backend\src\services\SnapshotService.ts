import { Repository } from "typeorm";
import { AppDataSource } from "../config/database";
import { Suggestion } from "../models/Suggestion";
import { Payment } from "../models/Payment";
import { PlaylistTrack } from "../models/PlaylistTrack";
import { WebSocketService } from "./WebSocketService";

export interface RankingSnapshot {
  restaurantId: string;
  nextTrack: {
    id: string;
    youtubeVideoId: string;
    title: string;
    artist: string;
    score: number;
    isPaid: boolean;
    paymentAmount?: number;
  } | null;
  paidQueue: Array<{
    id: string;
    youtubeVideoId: string;
    title: string;
    artist: string;
    paymentAmount: number;
    voteCount: number;
  }>;
  freeQueue: Array<{
    id: string;
    youtubeVideoId: string;
    title: string;
    artist: string;
    voteCount: number;
  }>;
  timestamp: Date;
}

export class SnapshotService {
  private suggestionRepository: Repository<Suggestion>;
  private paymentRepository: Repository<Payment>;
  private playlistTrackRepository: Repository<PlaylistTrack>;
  private webSocketService?: WebSocketService;
  private snapshots: Map<string, RankingSnapshot> = new Map();

  constructor() {
    this.suggestionRepository = AppDataSource.getRepository(Suggestion);
    this.paymentRepository = AppDataSource.getRepository(Payment);
    this.playlistTrackRepository = AppDataSource.getRepository(PlaylistTrack);
  this.webSocketService = WebSocketService.getInstance();
  }

  async generateSnapshot(restaurantId: string): Promise<RankingSnapshot> {
    try {
      // Get all active suggestions with votes and payments
      const suggestions = await this.suggestionRepository
        .createQueryBuilder("suggestion")
        .leftJoin("payments", "payment", "payment.suggestion_id = suggestion.id AND payment.status = 'paid'")
        .addSelect([
          "COALESCE(SUM(payment.amount), 0) as payment_amount",
          "COUNT(CASE WHEN payment.id IS NOT NULL THEN 1 END) > 0 as is_paid"
        ])
        .where("suggestion.restaurant_id = :restaurantId", { restaurantId })
        .andWhere("suggestion.status IN ('pending', 'approved')")
        .groupBy("suggestion.id")
        .orderBy("is_paid", "DESC")
        .addOrderBy("payment_amount", "DESC")
  .addOrderBy("suggestion.vote_count", "DESC")
  .addOrderBy("COALESCE(suggestion.\"createdAt\", suggestion.created_at)", "ASC")
        .getRawAndEntities();

      const paidQueue = [];
      const freeQueue = [];
      let nextTrack = null;

      for (const suggestion of suggestions.entities) {
        const raw = suggestions.raw.find(r => r.suggestion_id === suggestion.id);
        const paymentAmount = parseFloat(raw?.payment_amount || '0');
        const isPaid = raw?.is_paid === true || paymentAmount > 0;

        const item = {
          id: suggestion.id,
          youtubeVideoId: suggestion.youtubeVideoId,
          title: suggestion.title,
          artist: suggestion.artist || 'Artista Desconhecido',
          voteCount: suggestion.voteCount || 0,
          score: this.calculateScore(suggestion, paymentAmount),
          isPaid,
          paymentAmount
        };

        if (isPaid) {
          paidQueue.push({
            ...item,
            paymentAmount
          });
        } else {
          freeQueue.push(item);
        }

        // First item (highest priority) becomes next track
        if (!nextTrack) {
          nextTrack = item;
        }
      }

      // If no suggestions with votes, get from base playlist
      if (!nextTrack) {
        const baseTrack = await this.playlistTrackRepository
          .createQueryBuilder("track")
          .innerJoin("track.playlist", "playlist")
          .where("playlist.restaurant_id = :restaurantId", { restaurantId })
          .andWhere("track.is_active = true")
          .orderBy("track.position", "ASC")
          .getOne();

        if (baseTrack) {
          nextTrack = {
            id: baseTrack.id,
            youtubeVideoId: baseTrack.youtubeVideoId,
            title: baseTrack.title,
            artist: baseTrack.artist,
            score: 0,
            isPaid: false
          };
        }
      }

      const snapshot: RankingSnapshot = {
        restaurantId,
        nextTrack,
        paidQueue,
        freeQueue,
        timestamp: new Date()
      };

      // Store snapshot
      this.snapshots.set(restaurantId, snapshot);

  // Broadcast to connected clients (se disponível)
  this.webSocketService?.broadcastToRestaurant(restaurantId, 'ranking-snapshot', {
        nextTrack: snapshot.nextTrack,
        paidQueue: snapshot.paidQueue.slice(0, 10), // Limit for performance
        freeQueue: snapshot.freeQueue.slice(0, 20),
        timestamp: snapshot.timestamp
      });

      console.log(`📸 Generated snapshot for restaurant ${restaurantId}:`, {
        nextTrack: nextTrack?.title || 'None',
        paidCount: paidQueue.length,
        freeCount: freeQueue.length
      });

      return snapshot;
    } catch (error) {
      console.error(`Error generating snapshot for restaurant ${restaurantId}:`, error);
      throw error;
    }
  }

  private calculateScore(suggestion: Suggestion, paymentAmount: number): number {
    // Paid suggestions get massive boost
    if (paymentAmount > 0) {
      return 10000 + paymentAmount * 100 + (suggestion.voteCount || 0);
    }

    // Free suggestions based on votes with time decay
    const baseScore = suggestion.voteCount || 0;
    // createdAt pode ser nulo em dados legados
    const created = (suggestion as any).createdAt ?? (suggestion as any).updatedAt ?? new Date();
    let createdMs: number;
    try {
      const t = new Date(created as any).getTime();
      createdMs = isFinite(t) ? t : Date.now();
    } catch {
      createdMs = Date.now();
    }
    const hoursOld = (Date.now() - createdMs) / (1000 * 60 * 60);
    const timeDecay = Math.exp(-hoursOld / 24); // Decay over 24 hours
    
    return baseScore * timeDecay;
  }

  getSnapshot(restaurantId: string): RankingSnapshot | null {
    return this.snapshots.get(restaurantId) || null;
  }

  async startPeriodicSnapshots(): Promise<void> {
    console.log('🔄 Starting periodic snapshot generation (every 5 minutes)');
    
    setInterval(async () => {
      try {
        // Get all active restaurants
        const restaurants = await AppDataSource.query(`
          SELECT DISTINCT r.id
          FROM restaurants r
          INNER JOIN suggestions s ON s.restaurant_id = r.id
          WHERE r."isActive" = true
          AND s.status IN ('pending', 'approved')
          AND COALESCE(s."createdAt", s.created_at) > NOW() - INTERVAL '24 hours'
        `);

        for (const restaurant of restaurants) {
          await this.generateSnapshot(restaurant.id);
        }
      } catch (error) {
        console.error('Error in periodic snapshot generation:', error);
      }
    }, 5 * 60 * 1000); // 5 minutes
  }

  async generateSnapshotForAllActiveRestaurants(): Promise<void> {
    try {
      const restaurants = await AppDataSource.query(`
        SELECT DISTINCT r.id
        FROM restaurants r
        WHERE r."isActive" = true
      `);

      for (const restaurant of restaurants) {
        await this.generateSnapshot(restaurant.id);
      }
    } catch (error) {
      console.error('Error generating snapshots for all restaurants:', error);
    }
  }
}

// Singleton instance
export const snapshotService = new SnapshotService();
