import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  ThumbsUp, 
  ThumbsDown, 
  TrendingUp, 
  Users, 
  Clock,
  Zap,
  Trophy,
  Target
} from "lucide-react";
import { toast } from "react-hot-toast";
import VoteButton from "@/components/ui/VoteButton";

interface VotingDemoProps {
  restaurantId?: string;
}

interface MockSuggestion {
  id: string;
  title: string;
  artist: string;
  upvotes: number;
  downvotes: number;
  voteCount: number;
  position: number;
  canBeVoted: boolean;
  thumbnailUrl: string;
}

const VotingDemo: React.FC<VotingDemoProps> = ({ 
  restaurantId = "demo-restaurant" 
}) => {
  const [suggestions, setSuggestions] = useState<MockSuggestion[]>([
    {
      id: "1a0b82df-583a-42ba-a101-2a3e869e3f1f",
      title: "<PERSON> ft. <PERSON>",
      artist: "<PERSON>",
      upvotes: 8,
      downvotes: 1,
      voteCount: 7,
      position: 1,
      canBeVoted: true,
      thumbnailUrl: "https://img.youtube.com/vi/kJQP7kiw5Fk/mqdefault.jpg"
    },
    {
      id: "2b1c93ef-694b-53cb-b212-3b4f970f4g2g",
      title: "Ed Sheeran - Shape of You",
      artist: "Ed Sheeran",
      upvotes: 6,
      downvotes: 2,
      voteCount: 4,
      position: 2,
      canBeVoted: true,
      thumbnailUrl: "https://img.youtube.com/vi/JGwWNGJdvx8/mqdefault.jpg"
    },
    {
      id: "3c2d04fg-705c-64dc-c323-4c5g081g5h3h",
      title: "The Weeknd - Blinding Lights",
      artist: "The Weeknd",
      upvotes: 5,
      downvotes: 1,
      voteCount: 4,
      position: 3,
      canBeVoted: true,
      thumbnailUrl: "https://img.youtube.com/vi/4NRXx6U8ABQ/mqdefault.jpg"
    }
  ]);

  const [votingStates, setVotingStates] = useState<Record<string, boolean>>({});
  const [sessionId] = useState(() => 
    crypto.randomUUID ? crypto.randomUUID() : 
    'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    })
  );

  // Simular atualizações em tempo real
  useEffect(() => {
    const interval = setInterval(() => {
      // Simular votos aleatórios de outros usuários
      if (Math.random() > 0.7) {
        setSuggestions(prev => {
          const updated = [...prev];
          const randomIndex = Math.floor(Math.random() * updated.length);
          const isUpvote = Math.random() > 0.3;
          
          if (isUpvote) {
            updated[randomIndex].upvotes++;
          } else {
            updated[randomIndex].downvotes++;
          }
          
          updated[randomIndex].voteCount = 
            updated[randomIndex].upvotes - updated[randomIndex].downvotes;
          
          // Reordenar por voteCount
          updated.sort((a, b) => b.voteCount - a.voteCount);
          updated.forEach((item, index) => {
            item.position = index + 1;
          });
          
          return updated;
        });
      }
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const handleVote = async (suggestionId: string, voteType: "up" | "down") => {
    if (votingStates[suggestionId]) return;

    setVotingStates(prev => ({ ...prev, [suggestionId]: true }));

    try {
      // Simular chamada da API
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setSuggestions(prev => {
        const updated = [...prev];
        const index = updated.findIndex(s => s.id === suggestionId);
        
        if (index !== -1) {
          if (voteType === "up") {
            updated[index].upvotes++;
          } else {
            updated[index].downvotes++;
          }
          
          updated[index].voteCount = 
            updated[index].upvotes - updated[index].downvotes;
          
          // Reordenar por voteCount
          updated.sort((a, b) => b.voteCount - a.voteCount);
          updated.forEach((item, idx) => {
            item.position = idx + 1;
          });
        }
        
        return updated;
      });

      toast.success(
        `Voto ${voteType === "up" ? "positivo" : "negativo"} registrado!`,
        {
          icon: voteType === "up" ? "👍" : "👎",
          duration: 2000,
        }
      );
    } catch (error) {
      toast.error("Erro ao votar. Tente novamente.");
    } finally {
      setVotingStates(prev => ({ ...prev, [suggestionId]: false }));
    }
  };

  const totalVotes = suggestions.reduce((sum, s) => sum + s.upvotes + s.downvotes, 0);

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-center space-x-3"
        >
          <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full">
            <Trophy className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Sistema de Votação em Tempo Real
          </h1>
        </motion.div>
        
        <div className="flex items-center justify-center space-x-6 text-sm text-gray-600 dark:text-gray-400">
          <div className="flex items-center space-x-2">
            <Users className="w-4 h-4" />
            <span>Sessão: {sessionId.slice(0, 8)}...</span>
          </div>
          <div className="flex items-center space-x-2">
            <Target className="w-4 h-4" />
            <span>{totalVotes} votos totais</span>
          </div>
          <div className="flex items-center space-x-2">
            <Zap className="w-4 h-4 text-green-500" />
            <span>Tempo real ativo</span>
          </div>
        </div>
      </div>

      {/* Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 text-white"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-100 text-sm">Total Upvotes</p>
              <p className="text-2xl font-bold">
                {suggestions.reduce((sum, s) => sum + s.upvotes, 0)}
              </p>
            </div>
            <ThumbsUp className="w-8 h-8 text-green-200" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.1 }}
          className="bg-gradient-to-r from-red-500 to-red-600 rounded-lg p-4 text-white"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-red-100 text-sm">Total Downvotes</p>
              <p className="text-2xl font-bold">
                {suggestions.reduce((sum, s) => sum + s.downvotes, 0)}
              </p>
            </div>
            <ThumbsDown className="w-8 h-8 text-red-200" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.2 }}
          className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-4 text-white"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-100 text-sm">Engajamento</p>
              <p className="text-2xl font-bold">
                {Math.round((totalVotes / suggestions.length) * 10) / 10}
              </p>
            </div>
            <TrendingUp className="w-8 h-8 text-purple-200" />
          </div>
        </motion.div>
      </div>

      {/* Lista de Sugestões */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
          <Clock className="w-5 h-5" />
          <span>Fila de Reprodução - Vote para Reordenar</span>
        </h2>

        <AnimatePresence>
          {suggestions.map((suggestion, index) => (
            <motion.div
              key={suggestion.id}
              layout
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 border border-gray-200 dark:border-gray-700"
            >
              <div className="flex items-center space-x-4">
                {/* Posição */}
                <div className={`
                  flex items-center justify-center w-8 h-8 rounded-full text-sm font-bold
                  ${index === 0 ? 'bg-yellow-500 text-white' : 
                    index === 1 ? 'bg-gray-400 text-white' :
                    index === 2 ? 'bg-orange-600 text-white' :
                    'bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300'}
                `}>
                  {suggestion.position}
                </div>

                {/* Thumbnail */}
                <img
                  src={suggestion.thumbnailUrl}
                  alt={suggestion.title}
                  className="w-16 h-12 rounded object-cover"
                />

                {/* Info */}
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900 dark:text-white">
                    {suggestion.title}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {suggestion.artist}
                  </p>
                  <div className="flex items-center space-x-4 mt-1">
                    <span className="text-xs text-gray-500">
                      Score: {suggestion.voteCount}
                    </span>
                    <span className="text-xs text-green-600">
                      +{suggestion.upvotes}
                    </span>
                    <span className="text-xs text-red-600">
                      -{suggestion.downvotes}
                    </span>
                  </div>
                </div>

                {/* Botões de Voto */}
                {suggestion.canBeVoted && (
                  <div className="flex flex-col space-y-2">
                    <VoteButton
                      type="up"
                      onClick={() => handleVote(suggestion.id, "up")}
                      disabled={votingStates[suggestion.id]}
                      count={suggestion.upvotes}
                      size="sm"
                    />
                    <VoteButton
                      type="down"
                      onClick={() => handleVote(suggestion.id, "down")}
                      disabled={votingStates[suggestion.id]}
                      count={suggestion.downvotes}
                      size="sm"
                    />
                  </div>
                )}
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Instruções */}
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
        <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
          💡 Como Funciona o Sistema de Votação:
        </h3>
        <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
          <li>• Vote positivo (👍) para subir uma música na fila</li>
          <li>• Vote negativo (👎) para descer uma música na fila</li>
          <li>• As posições são atualizadas em tempo real</li>
          <li>• Cada sessão pode votar uma vez por música</li>
          <li>• O sistema previne spam e votos duplicados</li>
        </ul>
      </div>
    </div>
  );
};

export default VotingDemo;
