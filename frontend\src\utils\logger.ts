// Logger leve com níveis e ambiente
const isDev = import.meta?.env?.DEV ?? false;

type Level = 'debug' | 'info' | 'warn' | 'error';

function write(level: Level, ...args: any[]) {
  // Em dev, loga tudo; em prod, suprime debug
  if (!isDev && level === 'debug') return;
  const prefix = `[WS:${level.toUpperCase()}]`;
  // eslint-disable-next-line no-console
  (console as any)[level === 'debug' ? 'log' : level]?.(prefix, ...args);
}

export const logger = {
  debug: (...args: any[]) => write('debug', ...args),
  info: (...args: any[]) => write('info', ...args),
  warn: (...args: any[]) => write('warn', ...args),
  error: (...args: any[]) => write('error', ...args),
};
