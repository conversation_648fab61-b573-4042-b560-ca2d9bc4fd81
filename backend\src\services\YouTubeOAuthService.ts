import { google } from "googleapis";
import { OAuth2Client } from "google-auth-library";
import { AppDataSource } from "../config/database";
import { Restaurant } from "../models/Restaurant";

interface YouTubeCredentials {
  access_token: string;
  refresh_token: string;
  scope: string;
  token_type: string;
  expiry_date: number;
}

interface PlaylistItem {
  videoId: string;
  position: number;
}

export class YouTubeOAuthService {
  private oauth2Client: OAuth2Client;
  private youtube: any;
  private restaurantRepository = AppDataSource.getRepository(Restaurant);
  // Limitar chamadas consecutivas de update para evitar rate limit
  private reorderDelayMs = 150; // ~6-7 ops/seg

  constructor() {
    const clientId = process.env.YOUTUBE_CLIENT_ID;
    const clientSecret = process.env.YOUTUBE_CLIENT_SECRET;

    if (!clientId || clientId === 'your_client_id_here' || !clientSecret) {
      console.warn("⚠️ YouTube OAuth não configurado. Reordenação de playlists desabilitada.");
      // Usar API key apenas para leitura
      this.youtube = google.youtube({
        version: "v3",
        auth: process.env.YOUTUBE_API_KEY,
      });
      return;
    }

    this.oauth2Client = new google.auth.OAuth2(
      clientId,
      clientSecret,
      process.env.YOUTUBE_REDIRECT_URI ||
        "http://localhost:8001/auth/youtube/callback"
    );

    this.youtube = google.youtube({
      version: "v3",
      auth: this.oauth2Client,
    });
  }

  /**
   * Gerar URL de autorização OAuth
   */
  generateAuthUrl(restaurantId: string): string {
    const scopes = [
      "https://www.googleapis.com/auth/youtube",
      "https://www.googleapis.com/auth/youtube.force-ssl",
    ];

    return this.oauth2Client.generateAuthUrl({
      access_type: "offline",
      scope: scopes,
      state: restaurantId, // Passar restaurantId no state
      prompt: "consent", // Força novo refresh_token
    });
  }

  /**
   * Trocar código de autorização por tokens
   */
  async exchangeCodeForTokens(
    code: string,
    restaurantId: string
  ): Promise<YouTubeCredentials> {
    try {
      const response = await this.oauth2Client.getToken(code);
      const tokens = response.tokens;

      if (!tokens.access_token || !tokens.refresh_token) {
        throw new Error("Tokens inválidos recebidos do YouTube");
      }

      const credentials: YouTubeCredentials = {
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token!,
        scope: tokens.scope || "",
        token_type: tokens.token_type || "Bearer",
        expiry_date: tokens.expiry_date || Date.now() + 3600000,
      };

      // Salvar credenciais no restaurante
      await this.saveCredentials(restaurantId, credentials);

      console.log(
        `✅ Credenciais YouTube salvas para restaurante: ${restaurantId}`
      );
      return credentials;
    } catch (error) {
      console.error("Erro ao trocar código por tokens:", error);
      throw new Error("Falha na autenticação com YouTube");
    }
  }

  /**
   * Salvar credenciais do YouTube no restaurante
   */
  private async saveCredentials(
    restaurantId: string,
    credentials: YouTubeCredentials
  ): Promise<void> {
    const restaurant = await this.restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      throw new Error("Restaurante não encontrado");
    }

    // Salvar credenciais de forma segura (criptografadas em produção)
    restaurant.youtubeCredentials = {
      access_token: credentials.access_token,
      refresh_token: credentials.refresh_token,
      expiry_date: credentials.expiry_date,
      scope: credentials.scope,
    };

    await this.restaurantRepository.save(restaurant);
  }

  /**
   * Carregar e configurar credenciais para um restaurante
   */
  private async loadCredentials(restaurantId: string): Promise<boolean> {
    // Se OAuth não estiver configurado, não há credenciais para carregar
    if (!this.oauth2Client) {
      return false;
    }
    const restaurant = await this.restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant?.youtubeCredentials) {
      return false;
    }

    const credentials = restaurant.youtubeCredentials;

    // Verificar se o token expirou
    if (credentials.expiry_date && credentials.expiry_date < Date.now()) {
      // Tentar renovar o token
      try {
        this.oauth2Client.setCredentials({
          refresh_token: credentials.refresh_token,
        });

        const { credentials: newCredentials } =
          await this.oauth2Client.refreshAccessToken();

        // Atualizar credenciais
        await this.saveCredentials(restaurantId, {
          access_token: newCredentials.access_token!,
          refresh_token: credentials.refresh_token,
          expiry_date: newCredentials.expiry_date!,
          scope: credentials.scope,
          token_type: "Bearer",
        });

        console.log(`🔄 Token renovado para restaurante: ${restaurantId}`);
      } catch (error) {
        console.error("Erro ao renovar token:", error);
        return false;
      }
    }

    // Configurar credenciais no cliente OAuth
    this.oauth2Client.setCredentials({
      access_token: credentials.access_token,
      refresh_token: credentials.refresh_token,
      expiry_date: credentials.expiry_date,
    });

    return true;
  }

  /**
   * Criar nova playlist no YouTube
   */
  async createPlaylist(
    restaurantId: string,
    title: string,
    description: string
  ): Promise<string> {
    const hasCredentials = await this.loadCredentials(restaurantId);
    if (!hasCredentials) {
      throw new Error("Restaurante não autenticado com YouTube");
    }

    try {
      const response = await this.youtube.playlists.insert({
        part: ["snippet", "status"],
        requestBody: {
          snippet: {
            title,
            description,
            defaultLanguage: "pt-BR",
          },
          status: {
            privacyStatus: "public", // ou 'unlisted' para não listada
          },
        },
      });

      const playlistId = response.data.id;
      console.log(`✅ Playlist criada no YouTube: ${playlistId}`);
      return playlistId;
    } catch (error) {
      console.error("Erro ao criar playlist:", error);
      throw new Error("Falha ao criar playlist no YouTube");
    }
  }

  /**
   * Reordenar playlist baseado em votações
   */
  async reorderPlaylist(
    restaurantId: string,
    playlistId: string,
    newOrder: PlaylistItem[]
  ): Promise<boolean> {
    // Verificar se OAuth está configurado
    if (!this.oauth2Client) {
      console.warn("⚠️ YouTube OAuth não configurado. Reordenação ignorada.");
      return true; // Retornar true para não quebrar o fluxo
    }

    const hasCredentials = await this.loadCredentials(restaurantId);
    if (!hasCredentials) {
      console.error("❌ Credenciais OAuth não encontradas para:", restaurantId);
      console.log("💡 Para habilitar reordenação automática:");
      console.log(`   1. Acesse: http://localhost:8001/auth/youtube?restaurantId=${restaurantId}`);
      console.log("   2. Autorize o acesso à sua conta YouTube");
      console.log("   3. As playlists serão reordenadas automaticamente");
      return false;
    }

    try {
      // Obter todos os itens atuais da playlist (com paginação)
      const currentItems = await this.getPlaylistItems(playlistId);

      if (!Array.isArray(newOrder) || newOrder.length === 0) {
        console.warn("reorderPlaylist chamado com newOrder vazio");
        return true;
      }

      // Normalizar e validar newOrder
      const unique = new Map<string, number>();
      for (const it of newOrder) {
        if (!it?.videoId || typeof it.position !== "number") continue;
        unique.set(it.videoId, Math.max(0, Math.floor(it.position)));
      }

      // Montar lista de atualizações necessárias
      const updates = currentItems
        .map((ci) => {
          const target = unique.get(ci.videoId);
          return target !== undefined && target !== ci.position
            ? { id: ci.id, videoId: ci.videoId, from: ci.position, to: target }
            : null;
        })
        .filter(Boolean) as Array<{ id: string; videoId: string; from: number; to: number }>;

      if (updates.length === 0) {
        return true; // nada a fazer
      }

      // Ordenar por posição alvo para reduzir colisões
      updates.sort((a, b) => a.to - b.to);

      const sleep = (ms: number) => new Promise((r) => setTimeout(r, ms));

      // Retry simples com até 3 tentativas por item
      const updateWithRetry = async (u: { id: string; videoId: string; to: number }) => {
        let lastErr: any;
        for (let attempt = 0; attempt < 3; attempt++) {
          try {
            await this.youtube.playlistItems.update({
              part: ["snippet"],
              requestBody: {
                id: u.id,
                snippet: {
                  playlistId,
                  resourceId: {
                    kind: "youtube#video",
                    videoId: u.videoId,
                  },
                  position: u.to,
                },
              },
            });
            return;
          } catch (e) {
            lastErr = e;
            // backoff exponencial pequeno
            await sleep(200 * Math.pow(2, attempt));
          }
        }
        throw lastErr;
      };

      for (const u of updates) {
        await updateWithRetry(u);
        await sleep(this.reorderDelayMs);
      }

      console.log(
        `🔄 Playlist reordenada: ${playlistId} (${updates.length}/${newOrder.length} atualizações)`
      );
      return true;
    } catch (error) {
      console.error("Erro ao reordenar playlist:", error);
      return false;
    }
  }

  /**
   * Obter itens da playlist
   */
  private async getPlaylistItems(playlistId: string): Promise<any[]> {
    const all: any[] = [];
    try {
      let pageToken: string | undefined = undefined;
      do {
        const response = await this.youtube.playlistItems.list({
          part: ["snippet", "contentDetails"],
          playlistId,
          maxResults: 50,
          pageToken,
        });
        const items = response.data.items || [];
        for (let i = 0; i < items.length; i++) {
          const item = items[i];
          all.push({
            id: item.id,
            videoId: item.contentDetails.videoId,
            position: item.snippet.position ?? all.length,
            title: item.snippet.title,
          });
        }
        pageToken = response.data.nextPageToken;
      } while (pageToken);
    } catch (error) {
      console.error("Erro ao obter itens da playlist:", error);
    }
    return all;
  }

  /**
   * Adicionar vídeo à playlist
   */
  async addVideoToPlaylist(
    restaurantId: string,
    playlistId: string,
    videoId: string,
    position?: number
  ): Promise<boolean> {
    const hasCredentials = await this.loadCredentials(restaurantId);
    if (!hasCredentials) {
      throw new Error("Restaurante não autenticado com YouTube");
    }

    try {
      await this.youtube.playlistItems.insert({
        part: ["snippet"],
        requestBody: {
          snippet: {
            playlistId,
            resourceId: {
              kind: "youtube#video",
              videoId,
            },
            position,
          },
        },
      });

      console.log(`➕ Vídeo adicionado à playlist: ${videoId}`);
      return true;
    } catch (error) {
      console.error("Erro ao adicionar vídeo:", error);
      return false;
    }
  }

  /**
   * Remover vídeo da playlist
   */
  async removeVideoFromPlaylist(
    restaurantId: string,
    playlistItemId: string
  ): Promise<boolean> {
    const hasCredentials = await this.loadCredentials(restaurantId);
    if (!hasCredentials) {
      throw new Error("Restaurante não autenticado com YouTube");
    }

    try {
      await this.youtube.playlistItems.delete({
        id: playlistItemId,
      });

      console.log(`➖ Vídeo removido da playlist: ${playlistItemId}`);
      return true;
    } catch (error) {
      console.error("Erro ao remover vídeo:", error);
      return false;
    }
  }

  /**
   * Verificar se restaurante está autenticado
   */
  async isAuthenticated(restaurantId: string): Promise<boolean> {
    return await this.loadCredentials(restaurantId);
  }
}

export const youtubeOAuthService = new YouTubeOAuthService();
