import React, { useState, useEffect, useCallback, memo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useParams } from "react-router-dom";
import {
  BarChart3,
  CheckCircle,
  XCircle,
  Eye,
  Trash2,
  RefreshCw,
  Activity,
  Clock,
} from "lucide-react";
import { toast } from "react-hot-toast";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { buildApiUrl } from "../../config/api";

enum Performance {
  Excellent = "excellent",
  Good = "good",
  Average = "average",
  Poor = "poor",
  Terrible = "terrible",
}

enum Recommendation {
  Keep = "keep",
  Monitor = "monitor",
  Remove = "remove",
  Blacklist = "blacklist",
}

enum HealthRating {
  Excellent = "excellent",
  Good = "good",
  NeedsAttention = "needs_attention",
  Critical = "critical",
}

interface TrackAnalytics {
  id: string;
  title: string;
  artist: string;
  youtubeVideoId: string;
  totalVotes: number;
  upvotes: number;
  downvotes: number;
  score: number;
  negativeVoteRatio: number;
  positiveVoteRatio: number;
  playCount: number;
  skipCount: number;
  completionRate: number;
  averagePlayDuration: number;
  lastPlayed?: string;
  suggestedCount: number;
  performance: Performance;
  recommendation: Recommendation;
}

interface PlaylistHealth {
  totalTracks: number;
  excellentTracks: number;
  goodTracks: number;
  averageTracks: number;
  poorTracks: number;
  terribleTracks: number;
  overallScore: number;
  healthRating: HealthRating;
  recommendations: string[];
}

interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

interface AdvancedPlaylistAnalyticsProps {
  restaurantId?: string;
}

const AdvancedPlaylistAnalyticsComponent: React.FC<
  AdvancedPlaylistAnalyticsProps
> = ({ restaurantId }) => {
  const { restaurantId: urlRestaurantId } = useParams<{
    restaurantId: string;
  }>();
  const finalRestaurantId = restaurantId || urlRestaurantId;

  // Validar que temos um restaurantId
  if (!finalRestaurantId) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600">Erro</h1>
          <p className="text-gray-600 mt-2">ID do restaurante não fornecido</p>
        </div>
      </div>
    );
  }

  const [trackAnalytics, setTrackAnalytics] = useState<TrackAnalytics[]>([]);
  const [playlistHealth, setPlaylistHealth] = useState<PlaylistHealth | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<Performance | "all">("all");
  const [sortBy, setSortBy] = useState<
    "negativeVoteRatio" | "score" | "playCount"
  >("negativeVoteRatio");

  const loadAnalytics = useCallback(async () => {
    if (!finalRestaurantId) {
      toast.error("Restaurant ID is required");
      return;
    }

    setLoading(true);
    setError(null);
    try {
      const [tracksResponse, healthResponse] = await Promise.all([
        fetch(buildApiUrl(`/analytics/tracks/${finalRestaurantId}`)),
        fetch(buildApiUrl(`/analytics/playlist-health/${finalRestaurantId}`)),
      ]);

      if (!tracksResponse.ok || !healthResponse.ok) {
        throw new Error("Erro ao carregar dados do servidor");
      }

      const [tracksData, healthData] = await Promise.all([
        tracksResponse.json() as Promise<
          ApiResponse<{ trackAnalytics: TrackAnalytics[] }>
        >,
        healthResponse.json() as Promise<
          ApiResponse<{ playlistHealth: PlaylistHealth }>
        >,
      ]);

      if (tracksData.success) {
        setTrackAnalytics(tracksData.data.trackAnalytics || []);
      } else {
        throw new Error(
          tracksData.message || "Erro ao carregar dados de faixas"
        );
      }

      if (healthData.success) {
        setPlaylistHealth(healthData.data.playlistHealth || null);
      } else {
        throw new Error(
          healthData.message || "Erro ao carregar saúde da playlist"
        );
      }
    } catch (error) {
      console.error("Erro ao carregar analytics:", error);
      setError("Não foi possível carregar os dados de analytics");
      toast.error("Erro ao carregar analytics");
    } finally {
      setLoading(false);
    }
  }, [finalRestaurantId]);

  useEffect(() => {
    loadAnalytics();
  }, [loadAnalytics]);

  const getPerformanceColor = useCallback(
    (performance: Performance): string => {
      switch (performance) {
        case Performance.Excellent:
          return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";
        case Performance.Good:
          return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400";
        case Performance.Average:
          return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400";
        case Performance.Poor:
          return "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400";
        case Performance.Terrible:
          return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";
        default:
          return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400";
      }
    },
    []
  );

  const getRecommendationIcon = useCallback(
    (recommendation: Recommendation) => {
      switch (recommendation) {
        case Recommendation.Keep:
          return (
            <CheckCircle
              className="w-4 h-4 text-green-600"
              aria-hidden="true"
            />
          );
        case Recommendation.Monitor:
          return <Eye className="w-4 h-4 text-yellow-600" aria-hidden="true" />;
        case Recommendation.Remove:
          return (
            <XCircle className="w-4 h-4 text-red-600" aria-hidden="true" />
          );
        case Recommendation.Blacklist:
          return <Trash2 className="w-4 h-4 text-red-800" aria-hidden="true" />;
        default:
          return (
            <CheckCircle className="w-4 h-4 text-gray-600" aria-hidden="true" />
          );
      }
    },
    []
  );

  const getHealthColor = useCallback((rating: HealthRating): string => {
    switch (rating) {
      case HealthRating.Excellent:
        return "text-green-600";
      case HealthRating.Good:
        return "text-blue-600";
      case HealthRating.NeedsAttention:
        return "text-yellow-600";
      case HealthRating.Critical:
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  }, []);

  const getPerformanceLabel = useCallback(
    (performance: Performance): string => {
      switch (performance) {
        case Performance.Excellent:
          return "Excelente";
        case Performance.Good:
          return "Boa";
        case Performance.Average:
          return "Média";
        case Performance.Poor:
          return "Ruim";
        case Performance.Terrible:
          return "Terrível";
        default:
          return "";
      }
    },
    []
  );

  const getRecommendationLabel = useCallback(
    (recommendation: Recommendation): string => {
      switch (recommendation) {
        case Recommendation.Keep:
          return "Manter";
        case Recommendation.Monitor:
          return "Monitorar";
        case Recommendation.Remove:
          return "Remover";
        case Recommendation.Blacklist:
          return "Blacklist";
        default:
          return "";
      }
    },
    []
  );

  const formatDate = useCallback((dateString?: string) => {
    if (!dateString) return "Nunca";
    try {
      return format(new Date(dateString), "dd/MM/yyyy HH:mm", { locale: ptBR });
    } catch {
      return "Data inválida";
    }
  }, []);

  const filteredTracks = trackAnalytics
    .filter((track) => filter === "all" || track.performance === filter)
    .sort((a, b) => {
      switch (sortBy) {
        case "negativeVoteRatio":
          return b.negativeVoteRatio - a.negativeVoteRatio;
        case "score":
          return b.score - a.score;
        case "playCount":
          return b.playCount - a.playCount;
        default:
          return 0;
      }
    });

  if (loading) {
    return (
      <div
        className="flex justify-center items-center h-64"
        aria-live="polite"
        aria-busy="true"
      >
        <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
        <span className="sr-only">Carregando analytics</span>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-4 sm:p-6 space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
            Analytics Avançado da Playlist
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Análise detalhada de desempenho e recomendações para otimização
          </p>
        </div>
        <button
          onClick={loadAnalytics}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
          aria-label="Atualizar analytics"
        >
          <RefreshCw className="w-4 h-4" aria-hidden="true" />
          <span>Atualizar</span>
        </button>
      </div>

      {playlistHealth && (
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 sm:p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-4">
            <h3 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white">
              Saúde da Playlist
            </h3>
            <div className="flex items-center space-x-2">
              <Activity
                className={`w-5 h-5 ${getHealthColor(
                  playlistHealth.healthRating
                )}`}
                aria-hidden="true"
              />
              <span
                className={`text-sm font-medium ${getHealthColor(
                  playlistHealth.healthRating
                )}`}
              >
                {playlistHealth.healthRating.charAt(0).toUpperCase() +
                  playlistHealth.healthRating.slice(1).replace("_", " ")}
              </span>
            </div>
          </div>
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4 mb-4">
            {[
              {
                label: "Excelentes",
                value: playlistHealth.excellentTracks,
                color: "green",
              },
              {
                label: "Boas",
                value: playlistHealth.goodTracks,
                color: "blue",
              },
              {
                label: "Médias",
                value: playlistHealth.averageTracks,
                color: "yellow",
              },
              {
                label: "Ruins",
                value: playlistHealth.poorTracks,
                color: "orange",
              },
              {
                label: "Terríveis",
                value: playlistHealth.terribleTracks,
                color: "red",
              },
              {
                label: "Score Geral",
                value: playlistHealth.overallScore,
                color: "gray",
              },
            ].map((item) => (
              <div key={item.label} className="text-center">
                <div
                  className={`text-xl sm:text-2xl font-bold text-${item.color}-600 dark:text-${item.color}-400`}
                >
                  {item.label === "Score Geral"
                    ? `${item.value}/100`
                    : item.value}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {item.label}
                </div>
              </div>
            ))}
          </div>
          {playlistHealth.recommendations.length > 0 && (
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-900 dark:text-blue-300 mb-2">
                Recomendações:
              </h4>
              <ul className="space-y-1">
                {playlistHealth.recommendations.map((rec, index) => (
                  <li
                    key={index}
                    className="text-sm text-blue-800 dark:text-blue-400"
                  >
                    • {rec}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 sm:p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <label
              htmlFor="performance-filter"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >
              Filtrar por Performance
            </label>
            <select
              id="performance-filter"
              value={filter}
              onChange={(e) => setFilter(e.target.value as Performance | "all")}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              aria-label="Filtrar por performance"
            >
              <option value="all">Todas</option>
              {Object.values(Performance).map((perf) => (
                <option key={perf} value={perf}>
                  {perf.charAt(0).toUpperCase() + perf.slice(1)}
                </option>
              ))}
            </select>
          </div>
          <div className="flex-1">
            <label
              htmlFor="sort-by"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >
              Ordenar por
            </label>
            <select
              id="sort-by"
              value={sortBy}
              onChange={(e) =>
                setSortBy(
                  e.target.value as "negativeVoteRatio" | "score" | "playCount"
                )
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              aria-label="Ordenar por"
            >
              <option value="negativeVoteRatio">Taxa de Rejeição</option>
              <option value="score">Score de Votos</option>
              <option value="playCount">Vezes Tocada</option>
            </select>
          </div>
        </div>
      </div>

      <div
        className="space-y-4"
        role="region"
        aria-label="Lista de músicas analisadas"
      >
        {filteredTracks.length > 0 ? (
          <AnimatePresence>
            {filteredTracks.map((track) => (
              <motion.div
                key={track.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="bg-white dark:bg-gray-800 rounded-lg p-4 sm:p-6 shadow-sm border border-gray-200 dark:border-gray-700"
              >
                <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex flex-wrap items-center gap-3 mb-2">
                      <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white truncate">
                        {track.title}
                      </h3>
                      <span
                        className={`px-2 py-1 rounded-full text-xs font-medium ${getPerformanceColor(
                          track.performance
                        )}`}
                        title={`Performance: ${getPerformanceLabel(
                          track.performance
                        )}`}
                      >
                        {getPerformanceLabel(track.performance)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 truncate">
                      {track.artist}
                    </p>
                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-4">
                      <div className="text-center">
                        <div className="text-lg font-bold text-red-600 dark:text-red-400">
                          {(track.negativeVoteRatio * 100).toFixed(1)}%
                        </div>
                        <div className="text-xs text-gray-600 dark:text-gray-400">
                          Taxa Rejeição
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                          {track.score}
                        </div>
                        <div className="text-xs text-gray-600 dark:text-gray-400">
                          Score
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-purple-600 dark:text-purple-400">
                          {track.playCount}
                        </div>
                        <div className="text-xs text-gray-600 dark:text-gray-400">
                          Reproduções
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-green-600 dark:text-green-400">
                          {(track.completionRate * 100).toFixed(1)}%
                        </div>
                        <div className="text-xs text-gray-600 dark:text-gray-400">
                          Conclusão
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-wrap items-center gap-3 sm:gap-4 text-sm text-gray-600 dark:text-gray-400">
                      <div className="flex items-center space-x-1">
                        <CheckCircle
                          className="w-4 h-4 text-green-600"
                          aria-hidden="true"
                        />
                        <span>{track.upvotes} positivos</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <XCircle
                          className="w-4 h-4 text-red-600"
                          aria-hidden="true"
                        />
                        <span>{track.downvotes} negativos</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="w-4 h-4" aria-hidden="true" />
                        <span>{track.skipCount} pulos</span>
                      </div>
                      {track.lastPlayed && (
                        <div className="flex items-center space-x-1">
                          <Clock className="w-4 h-4" aria-hidden="true" />
                          <span>
                            Última reprodução: {formatDate(track.lastPlayed)}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getRecommendationIcon(track.recommendation)}
                    <span
                      className="text-sm font-medium text-gray-700 dark:text-gray-300"
                      title={`Recomendação: ${getRecommendationLabel(
                        track.recommendation
                      )}`}
                    >
                      {getRecommendationLabel(track.recommendation)}
                    </span>
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        ) : (
          <div
            className="text-center py-12 bg-gray-50 dark:bg-gray-800/50 rounded-lg"
            role="status"
          >
            <BarChart3
              className="w-12 h-12 text-gray-400 mx-auto mb-4"
              aria-hidden="true"
            />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Nenhuma análise encontrada
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Não há dados suficientes para análise ou o filtro não retornou
              resultados
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

const AdvancedPlaylistAnalytics = memo(AdvancedPlaylistAnalyticsComponent);
AdvancedPlaylistAnalytics.displayName = "AdvancedPlaylistAnalytics";

export default AdvancedPlaylistAnalytics;
