import { Router } from "express";
import { body, param, query, validationResult } from "../utils/validation";
import { lyricsService } from "../services/LyricsService";
import asyncHandler from "../middleware/asyncHandler";
import { optionalAuth } from "../middleware/auth";
import { ValidationError, NotFoundError } from "../utils/errors";

const router = Router();

/**
 * @swagger
 * /api/v1/lyrics/search:
 *   get:
 *     summary: Buscar letras por título e artista
 *     tags: [Lyrics]
 *     parameters:
 *       - in: query
 *         name: title
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: artist
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: youtubeVideoId
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Letras encontradas
 *       404:
 *         description: Letras não encontradas
 */
router.get(
  "/search",
  [
    query("title").notEmpty().withMessage("T<PERSON>tulo é obrigatório"),
    query("artist").notEmpty().withMessage("Artista é obrigatório"),
    query("youtubeVideoId").optional().isString(),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { title, artist, youtubeVideoId } = req.query;

    const lyrics = await lyricsService.getLyrics(
      title as string,
      artist as string,
      youtubeVideoId as string
    );

    if (!lyrics) {
      throw new NotFoundError("Letras não encontradas para esta música");
    }

    res.json({
      success: true,
      lyrics: lyrics,
    });
  })
);

/**
 * @swagger
 * /api/v1/lyrics/youtube/{videoId}:
 *   get:
 *     summary: Buscar letras por ID do YouTube
 *     tags: [Lyrics]
 *     parameters:
 *       - in: path
 *         name: videoId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Letras encontradas
 *       404:
 *         description: Letras não encontradas
 */
router.get(
  "/youtube/:videoId",
  [param("videoId").notEmpty().withMessage("ID do vídeo é obrigatório")],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { videoId } = req.params;

    const lyrics = await lyricsService.getLyricsByYouTubeId(videoId);

    if (!lyrics) {
      throw new NotFoundError("Letras não encontradas para este vídeo");
    }

    res.json({
      success: true,
      lyrics: lyrics,
    });
  })
);

/**
 * @swagger
 * /api/v1/lyrics/karaoke:
 *   get:
 *     summary: Obter letras para karaokê com sincronização
 *     tags: [Lyrics]
 *     parameters:
 *       - in: query
 *         name: title
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: artist
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: currentTime
 *         schema:
 *           type: number
 *         description: Tempo atual da música em segundos
 *     responses:
 *       200:
 *         description: Letras para karaokê
 */
router.get(
  "/karaoke",
  [
    query("title").notEmpty().withMessage("Título é obrigatório"),
    query("artist").notEmpty().withMessage("Artista é obrigatório"),
    query("currentTime").optional().isFloat({ min: 0 }),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { title, artist, currentTime } = req.query;

    const lyrics = await lyricsService.getLyrics(
      title as string,
      artist as string
    );

    if (!lyrics) {
      throw new NotFoundError("Letras não encontradas para esta música");
    }

    // Se tem tempo atual, retornar informações de sincronização
    let karaokeData = lyrics;
    let currentLine = null;
    let nextLine = null;
    let progress = 0;

    if (currentTime !== undefined) {
      const time = parseFloat(currentTime as string);
      const lineData = lyricsService.getCurrentLine(lyrics, time);

      currentLine = lineData.current;
      nextLine = lineData.next;

      // Calcular progresso na linha atual
      if (currentLine && nextLine) {
        const lineDuration = nextLine.time - currentLine.time;
        const elapsed = time - currentLine.time;
        progress = Math.min(1, Math.max(0, elapsed / lineDuration));
      }
    }

    res.json({
      success: true,
      lyrics: karaokeData,
      karaoke: {
        currentTime: currentTime ? parseFloat(currentTime as string) : null,
        currentLine,
        nextLine,
        progress,
        hasTimestamps: lyrics.hasTimestamps,
      },
    });
  })
);

/**
 * @swagger
 * /api/v1/lyrics/add:
 *   post:
 *     summary: Adicionar letras manualmente
 *     tags: [Lyrics]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - artist
 *               - lines
 *             properties:
 *               title:
 *                 type: string
 *               artist:
 *                 type: string
 *               lines:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     time:
 *                       type: number
 *                     text:
 *                       type: string
 *                     duration:
 *                       type: number
 *               youtubeVideoId:
 *                 type: string
 *               album:
 *                 type: string
 *               language:
 *                 type: string
 *     responses:
 *       201:
 *         description: Letras adicionadas com sucesso
 */
router.post(
  "/add",
  [
    body("title").notEmpty().withMessage("Título é obrigatório"),
    body("artist").notEmpty().withMessage("Artista é obrigatório"),
    body("lines").isArray().withMessage("Linhas devem ser um array"),
    body("lines.*.time")
      .isFloat({ min: 0 })
      .withMessage("Tempo deve ser um número positivo"),
    body("lines.*.text").notEmpty().withMessage("Texto da linha é obrigatório"),
    body("youtubeVideoId").optional().isString(),
    body("album").optional().isString(),
    body("language").optional().isString(),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { title, artist, lines, youtubeVideoId, album, language } = req.body;

    const lyrics = await lyricsService.addManualLyrics({
      title,
      artist,
      lines,
      youtubeVideoId,
      album,
      language,
    });

    res.status(201).json({
      success: true,
      message: "Letras adicionadas com sucesso",
      lyrics,
    });
  })
);

/**
 * @swagger
 * /api/v1/lyrics/sync/{lyricsId}:
 *   get:
 *     summary: Obter dados de sincronização para uma música
 *     tags: [Lyrics]
 *     parameters:
 *       - in: path
 *         name: lyricsId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: time
 *         required: true
 *         schema:
 *           type: number
 *         description: Tempo atual em segundos
 *     responses:
 *       200:
 *         description: Dados de sincronização
 */
router.get(
  "/sync/:lyricsId",
  [
    param("lyricsId").notEmpty().withMessage("ID das letras é obrigatório"),
    query("time")
      .isFloat({ min: 0 })
      .withMessage("Tempo deve ser um número positivo"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { lyricsId } = req.params;
    const { time } = req.query;

    // Por simplicidade, vamos buscar as letras pelo título/artista
    // Em uma implementação real, você teria um endpoint específico para buscar por ID

    res.json({
      success: true,
      message: "Endpoint de sincronização - implementar busca por ID",
      lyricsId,
      time: parseFloat(time as string),
    });
  })
);

/**
 * @swagger
 * /api/v1/lyrics/test:
 *   get:
 *     summary: Obter letras de teste para desenvolvimento
 *     tags: [Lyrics]
 *     parameters:
 *       - in: query
 *         name: title
 *         schema:
 *           type: string
 *           default: "Teste"
 *       - in: query
 *         name: artist
 *         schema:
 *           type: string
 *           default: "Artista Teste"
 *     responses:
 *       200:
 *         description: Letras de teste
 */
router.get(
  "/test",
  [query("title").optional().isString(), query("artist").optional().isString()],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const { title = "Música de Teste", artist = "Artista Teste" } = req.query;

    // Criar letras de teste
    const testLyrics = {
      id: `test_${Date.now()}`,
      title: title as string,
      artist: artist as string,
      duration: 120,
      language: "pt",
      lines: [
        { time: 0, text: "🎵 Início da música", duration: 5 },
        { time: 5, text: "Esta é uma música de teste", duration: 5 },
        { time: 10, text: "Para demonstrar o karaokê", duration: 5 },
        { time: 15, text: "Cante junto com a gente!", duration: 5 },
        { time: 20, text: "La la la la la", duration: 10 },
        { time: 30, text: "Na na na na na", duration: 10 },
        { time: 40, text: "Refrão chegando agora", duration: 5 },
        { time: 45, text: "Todos juntos, vamos cantar", duration: 10 },
        { time: 55, text: "Esta é a nossa canção", duration: 10 },
        { time: 65, text: "De teste e diversão", duration: 10 },
        { time: 75, text: "🎤 Solo instrumental", duration: 20 },
        { time: 95, text: "Voltando com a letra", duration: 5 },
        { time: 100, text: "Para finalizar em grande estilo", duration: 10 },
        { time: 110, text: "🎶 Fim da música de teste", duration: 10 },
      ],
      source: "manual",
      isExplicit: false,
      hasTimestamps: true,
    };

    res.json({
      success: true,
      lyrics: testLyrics,
      note: "Estas são letras de teste para desenvolvimento",
    });
  })
);

/**
 * @swagger
 * /api/v1/lyrics/clear-cache:
 *   post:
 *     summary: Limpar cache de letras
 *     tags: [Lyrics]
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *               artist:
 *                 type: string
 *     responses:
 *       200:
 *         description: Cache limpo
 */
router.post(
  "/clear-cache",
  [body("title").optional().isString(), body("artist").optional().isString()],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const { title, artist } = req.body;

    await lyricsService.clearLyricsCache(title, artist);

    res.json({
      success: true,
      message:
        title && artist
          ? `Cache limpo para "${title}" - "${artist}"`
          : "Cache geral de letras limpo",
    });
  })
);

export default router;
