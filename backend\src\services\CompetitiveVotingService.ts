import { AppDataSource } from "../config/database";
import { CompetitiveVote } from "../models/CompetitiveVote";
import { Suggestion } from "../models/Suggestion";
import { ClientSession } from "../models/ClientSession";
import { redisClient } from "../config/redis";
import { notificationService } from "./NotificationService";
import { NotificationType, NotificationPriority } from "./NotificationService";
import { logger } from "../utils/logger";
import { Between } from "typeorm";

export interface VotingSession {
  id: string;
  suggestionId: string;
  restaurantId: string;
  performerSessionId: string;
  performerTableName?: string;
  performerClientName?: string;
  songTitle: string;
  artist: string;
  startTime: Date;
  endTime: Date;
  isActive: boolean;
  totalVotes: number;
  averageRating: number;
  ratings: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
}

export interface VoteData {
  sessionId: string;
  suggestionId: string;
  voterSessionId: string;
  voterTableName?: string;
  rating: 1 | 2 | 3 | 4 | 5;
  comment?: string;
}

export interface LeaderboardEntry {
  sessionId: string;
  clientName: string;
  tableName: string;
  songTitle: string;
  artist: string;
  averageRating: number;
  totalVotes: number;
  performanceTime: Date;
  rank: number;
}

class CompetitiveVotingService {
  private static instance: CompetitiveVotingService;
  private voteRepository = AppDataSource.getRepository(CompetitiveVote);
  private suggestionRepository = AppDataSource.getRepository(Suggestion);
  private sessionRepository = AppDataSource.getRepository(ClientSession);

  static getInstance(): CompetitiveVotingService {
    if (!CompetitiveVotingService.instance) {
      CompetitiveVotingService.instance = new CompetitiveVotingService();
    }
    return CompetitiveVotingService.instance;
  }

  async startVotingSession(
    suggestionId: string
  ): Promise<VotingSession | null> {
    try {
      const suggestion = await this.suggestionRepository.findOne({
        where: { id: suggestionId },
        relations: ["restaurant", "clientSession"],
      });

      if (
        !suggestion ||
        !suggestion.isPaid ||
        !suggestion.restaurant ||
        !suggestion.clientSession
      ) {
        return null;
      }

      const sessionId = `voting_${suggestionId}_${Date.now()}`;
      const startTime = new Date();
      const endTime = new Date(
        startTime.getTime() + (suggestion.duration || 180) * 1000
      );

      const votingSession: VotingSession = {
        id: sessionId,
        suggestionId,
        restaurantId: suggestion.restaurant.id,
        performerSessionId: suggestion.clientSession.id,
        performerTableName: suggestion.clientSession.tableNumber,
        performerClientName: suggestion.clientSession.clientName,
        songTitle: suggestion.title,
        artist: suggestion.artist || "Artista Desconhecido",
        startTime,
        endTime,
        isActive: true,
        totalVotes: 0,
        averageRating: 0,
        ratings: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
      };

      const ttl =
        Math.ceil((endTime.getTime() - startTime.getTime()) / 1000) + 60;
      await redisClient
        .getClient()
        .setEx(
          `voting_session:${sessionId}`,
          ttl,
          JSON.stringify(votingSession)
        );

      await notificationService.sendToRestaurant(suggestion.restaurant.id, {
        type: NotificationType.VOTE,
        title: "🎤 Votação Ativa!",
        message: `Vote na performance de "${suggestion.title}" pela mesa ${
          suggestion.clientSession.tableNumber || "?"
        }`,
        priority: NotificationPriority.HIGH,
        category: "competitive_voting",
        data: {
          votingSessionId: sessionId,
          suggestionId,
          songTitle: suggestion.title,
          artist: suggestion.artist,
          performerName: suggestion.clientSession.clientName,
          performerTable: suggestion.clientSession.tableNumber,
          endTime: endTime.toISOString(),
        },
        autoClose: true,
        duration: 5000,
      });

      setTimeout(() => {
        this.endVotingSession(sessionId);
      }, ttl * 1000);

      return votingSession;
    } catch (error) {
      logger.error("Erro ao iniciar sessão de votação:", error);
      return null;
    }
  }

  async submitVote(voteData: VoteData): Promise<{
    success: boolean;
    message: string;
    votingSession?: VotingSession;
  }> {
    try {
      const votingSession = await this.getVotingSession(voteData.sessionId);

      if (!votingSession) {
        return { success: false, message: "Sessão de votação não encontrada" };
      }

      if (!votingSession.isActive || new Date() > votingSession.endTime) {
        return {
          success: false,
          message: "Votação encerrada para esta música",
        };
      }

      const existingVote = await this.voteRepository.findOne({
        where: {
          votingSessionId: voteData.sessionId,
          voterSessionId: voteData.voterSessionId,
        },
      });

      if (existingVote) {
        return { success: false, message: "Você já votou nesta performance" };
      }

      if (voteData.voterSessionId === votingSession.performerSessionId) {
        return {
          success: false,
          message: "Você não pode votar na sua própria performance",
        };
      }

      const vote = this.voteRepository.create({
        votingSessionId: voteData.sessionId,
        suggestionId: voteData.suggestionId,
        voterSessionId: voteData.voterSessionId,
        voterTableName: voteData.voterTableName,
        rating: voteData.rating,
        comment: voteData.comment,
        createdAt: new Date(),
      });

      await this.voteRepository.save(vote);

      const updatedSession = await this.updateVotingSessionStats(
        voteData.sessionId
      );

      if (updatedSession) {
        await notificationService.sendToUser(votingSession.performerSessionId, {
          type: NotificationType.SUCCESS,
          title: "🌟 Novo Voto!",
          message: `Alguém votou ${voteData.rating} estrelas na sua performance!`,
          priority: NotificationPriority.NORMAL,
          category: "competitive_voting",
          data: {
            rating: voteData.rating,
            totalVotes: updatedSession.totalVotes,
            averageRating: updatedSession.averageRating,
          },
          autoClose: true,
          duration: 3000,
        });

        await notificationService.sendToRestaurant(votingSession.restaurantId, {
          type: NotificationType.VOTE,
          title: "Votação Atualizada",
          message: `Nova avaliação: ${voteData.rating} estrelas`,
          priority: NotificationPriority.LOW,
          category: "competitive_voting",
          data: {
            votingSessionId: voteData.sessionId,
            newRating: voteData.rating,
            updatedStats: updatedSession,
          },
          autoClose: true,
          duration: 3000,
        });
      }

      return {
        success: true,
        message: "Voto registrado com sucesso!",
        votingSession: updatedSession || undefined,
      };
    } catch (error) {
      logger.error("Erro ao submeter voto:", error);
      return { success: false, message: "Erro interno do servidor" };
    }
  }

  async getVotingSession(sessionId: string): Promise<VotingSession | null> {
    try {
      const cached = await redisClient
        .getClient()
        .get(`voting_session:${sessionId}`);

      if (cached) {
        return JSON.parse(cached) as VotingSession;
      }

      return null;
    } catch (error) {
      logger.error("Erro ao obter sessão de votação:", error);
      return null;
    }
  }

  private async updateVotingSessionStats(
    sessionId: string
  ): Promise<VotingSession | null> {
    try {
      const votingSession = await this.getVotingSession(sessionId);

      if (!votingSession) return null;

      const votes = await this.voteRepository.find({
        where: { votingSessionId: sessionId },
      });

      const totalVotes = votes.length;
      const ratings = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
      let totalRating = 0;

      votes.forEach((vote) => {
        ratings[vote.rating]++;
        totalRating += vote.rating;
      });

      const averageRating = totalVotes > 0 ? totalRating / totalVotes : 0;

      const updatedSession: VotingSession = {
        ...votingSession,
        totalVotes,
        averageRating: Math.round(averageRating * 100) / 100,
        ratings,
      };

      const ttl = Math.ceil(
        (votingSession.endTime.getTime() - Date.now()) / 1000
      );
      if (ttl > 0) {
        await redisClient
          .getClient()
          .setEx(
            `voting_session:${sessionId}`,
            ttl,
            JSON.stringify(updatedSession)
          );
      }

      return updatedSession;
    } catch (error) {
      logger.error("Erro ao atualizar estatísticas:", error);
      return null;
    }
  }

  async endVotingSession(sessionId: string): Promise<void> {
    try {
      const votingSession = await this.getVotingSession(sessionId);

      if (!votingSession) return;

      const finalSession: VotingSession = {
        ...votingSession,
        isActive: false,
      };

      await redisClient
        .getClient()
        .setEx(
          `voting_result:${sessionId}`,
          86400,
          JSON.stringify(finalSession)
        );

      await redisClient.getClient().del(`voting_session:${sessionId}`);

      await notificationService.sendToRestaurant(votingSession.restaurantId, {
        type: NotificationType.INFO,
        title: "🏁 Votação Encerrada",
        message: `Performance de "${votingSession.songTitle}" recebeu ${finalSession.totalVotes} votos (${finalSession.averageRating}⭐)`,
        priority: NotificationPriority.NORMAL,
        category: "competitive_voting",
        data: {
          votingSessionId: sessionId,
          finalStats: finalSession,
        },
        autoClose: true,
        duration: 5000,
      });

      await notificationService.sendToUser(votingSession.performerSessionId, {
        type: NotificationType.SUCCESS,
        title: "🎉 Resultado Final!",
        message: `Sua performance recebeu ${finalSession.totalVotes} votos com média de ${finalSession.averageRating} estrelas!`,
        priority: NotificationPriority.HIGH,
        category: "competitive_voting",
        data: {
          finalStats: finalSession,
          rank: await this.calculateRank(
            votingSession.restaurantId,
            finalSession.averageRating
          ),
        },
        autoClose: true,
        duration: 5000,
      });
    } catch (error) {
      logger.error("Erro ao encerrar sessão de votação:", error);
    }
  }

  async getActiveVotingSessions(
    restaurantId: string
  ): Promise<VotingSession[]> {
    try {
      const keys = await redisClient
        .getClient()
        .keys(`voting_session:${restaurantId}:*`);
      const sessions: VotingSession[] = [];

      for (const key of keys) {
        const session = await redisClient.getClient().get(key);
        if (session) {
          const parsedSession = JSON.parse(session) as VotingSession;
          if (
            parsedSession.isActive &&
            parsedSession.restaurantId === restaurantId
          ) {
            sessions.push(parsedSession);
          }
        }
      }

      return sessions;
    } catch (error) {
      logger.error("Erro ao obter sessões ativas:", error);
      return [];
    }
  }

  async getDailyLeaderboard(
    restaurantId: string,
    date?: Date
  ): Promise<LeaderboardEntry[]> {
    try {
      const targetDate = date || new Date();
      const startOfDay = new Date(targetDate);
      startOfDay.setHours(0, 0, 0, 0);
      const endOfDay = new Date(targetDate);
      endOfDay.setHours(23, 59, 59, 999);

      const votes = await this.voteRepository
        .createQueryBuilder("vote")
        .leftJoinAndSelect("vote.suggestion", "suggestion")
        .leftJoinAndSelect("suggestion.clientSession", "clientSession")
        .leftJoinAndSelect("suggestion.restaurant", "restaurant")
        .where("restaurant.id = :restaurantId", { restaurantId })
        .andWhere("vote.createdAt BETWEEN :startOfDay AND :endOfDay", {
          startOfDay,
          endOfDay,
        })
        .getMany();

      const performanceMap = new Map<
        string,
        {
          sessionId: string;
          clientName: string;
          tableName: string;
          songTitle: string;
          artist: string;
          votes: number[];
          performanceTime: Date;
        }
      >();

      votes.forEach((vote) => {
        const key = `${vote.suggestion.clientSessionId}_${vote.suggestionId}`;
        if (!performanceMap.has(key)) {
          performanceMap.set(key, {
            sessionId: vote.suggestion.clientSessionId,
            clientName: vote.suggestion.clientSession?.clientName || "Cliente",
            tableName: vote.suggestion.clientSession?.tableNumber || "Mesa ?",
            songTitle: vote.suggestion.title,
            artist: vote.suggestion.artist || "Artista Desconhecido",
            votes: [],
            performanceTime: vote.suggestion.playedAt || vote.createdAt,
          });
        }
        performanceMap.get(key)!.votes.push(vote.rating);
      });

      const leaderboard: LeaderboardEntry[] = Array.from(
        performanceMap.values()
      )
        .map((performance) => ({
          sessionId: performance.sessionId,
          clientName: performance.clientName,
          tableName: performance.tableName,
          songTitle: performance.songTitle,
          artist: performance.artist,
          averageRating:
            performance.votes.length > 0
              ? Math.round(
                  (performance.votes.reduce((sum, rating) => sum + rating, 0) /
                    performance.votes.length) *
                    100
                ) / 100
              : 0,
          totalVotes: performance.votes.length,
          performanceTime: performance.performanceTime,
          rank: 0,
        }))
        .sort((a, b) => {
          if (b.averageRating !== a.averageRating) {
            return b.averageRating - a.averageRating;
          }
          return b.totalVotes - a.totalVotes;
        })
        .map((entry, index) => ({
          ...entry,
          rank: index + 1,
        }));

      return leaderboard;
    } catch (error) {
      logger.error("Erro ao obter leaderboard:", error);
      return [];
    }
  }

  private async calculateRank(
    restaurantId: string,
    averageRating: number
  ): Promise<number> {
    try {
      const leaderboard = await this.getDailyLeaderboard(restaurantId);
      const betterPerformances = leaderboard.filter(
        (entry) => entry.averageRating > averageRating
      );
      return betterPerformances.length + 1;
    } catch (error) {
      logger.error("Erro ao calcular rank:", error);
      return 1;
    }
  }

  async getVotingStats(
    restaurantId: string,
    period: string = "1d"
  ): Promise<{
    totalSessions: number;
    totalVotes: number;
    averageRating: number;
    participationRate: number;
    topPerformers: LeaderboardEntry[];
    votingTrends: Array<{ hour: number; votes: number; averageRating: number }>;
  }> {
    try {
      const endDate = new Date();
      const startDate = new Date();

      switch (period) {
        case "1d":
          startDate.setDate(endDate.getDate() - 1);
          break;
        case "7d":
          startDate.setDate(endDate.getDate() - 7);
          break;
        case "30d":
          startDate.setDate(endDate.getDate() - 30);
          break;
        default:
          startDate.setDate(endDate.getDate() - 1);
      }

      const votes = await this.voteRepository
        .createQueryBuilder("vote")
        .leftJoinAndSelect("vote.suggestion", "suggestion")
        .leftJoinAndSelect("suggestion.restaurant", "restaurant")
        .where("restaurant.id = :restaurantId", { restaurantId })
        .andWhere("vote.createdAt BETWEEN :startDate AND :endDate", {
          startDate,
          endDate,
        })
        .getMany();

      const totalVotes = votes.length;
      const uniqueSessions = new Set(
        votes.map((v) => `${v.suggestionId}_${v.votingSessionId}`)
      ).size;
      const averageRating =
        totalVotes > 0
          ? Math.round(
              (votes.reduce((sum, vote) => sum + vote.rating, 0) / totalVotes) *
                100
            ) / 100
          : 0;

      const totalSuggestions = await this.suggestionRepository.count({
        where: {
          restaurant: { id: restaurantId },
          isPaid: true,
          createdAt: Between(startDate, endDate),
        },
      });

      const participationRate =
        totalSuggestions > 0
          ? Math.round((uniqueSessions / totalSuggestions) * 100 * 100) / 100
          : 0;

      const topPerformers = await this.getDailyLeaderboard(restaurantId);

      const votingTrends = Array.from({ length: 24 }, (_, hour) => {
        const hourVotes = votes.filter(
          (vote) => vote.createdAt.getHours() === hour
        );
        return {
          hour,
          votes: hourVotes.length,
          averageRating:
            hourVotes.length > 0
              ? Math.round(
                  (hourVotes.reduce((sum, vote) => sum + vote.rating, 0) /
                    hourVotes.length) *
                    100
                ) / 100
              : 0,
        };
      });

      return {
        totalSessions: uniqueSessions,
        totalVotes,
        averageRating,
        participationRate,
        topPerformers: topPerformers.slice(0, 10),
        votingTrends,
      };
    } catch (error) {
      logger.error("Erro ao obter estatísticas de votação:", error);
      return {
        totalSessions: 0,
        totalVotes: 0,
        averageRating: 0,
        participationRate: 0,
        topPerformers: [],
        votingTrends: [],
      };
    }
  }
}

export const competitiveVotingService = CompetitiveVotingService.getInstance();
export default CompetitiveVotingService;
