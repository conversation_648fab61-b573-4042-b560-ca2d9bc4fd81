import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Gift,
  Trophy,
  Star,
  Clock,
  CreditCard,
  Music,
  Zap,
  Share2,
  CheckCircle,
  XCircle,
  Calendar,
  Award,
  Loader2,
  X,
  <PERSON><PERSON><PERSON><PERSON>gle,
} from "lucide-react";
import { toast } from "react-hot-toast";
import { API_CONFIG } from "@/config/api";

interface PerformanceData {
  songTitle?: string;
  artist?: string;
  averageRating?: number;
  totalVotes?: number;
  rank?: number;
  competitionDate?: string;
}

interface Reward {
  id: string;
  type: "discount" | "free_song" | "priority_queue" | "badge" | "custom";
  title: string;
  description: string;
  icon: string;
  formattedValue: string;
  awardedFor:
    | "daily_winner"
    | "weekly_winner"
    | "monthly_winner"
    | "best_performance"
    | "most_votes"
    | "participation";
  awardedDate: string;
  expiresAt?: string;
  status: "active" | "used" | "expired" | "cancelled";
  isActive: boolean;
  canBeUsed: boolean;
  usageCount: number;
  usageLimit?: number;
  performanceData?: PerformanceData;
}

interface RewardsPanelProps {
  sessionId: string;
  restaurantId: string;
  isOpen: boolean;
  onClose: () => void;
}

const RewardStatus: React.FC<{ status: string; isActive: boolean }> = ({ status, isActive }) => {
  const getStatusDetails = () => {
    if (isActive) return { label: "Ativo", className: "bg-green-100 text-green-800" };
    
    switch (status) {
      case "used": return { label: "Usado", className: "bg-gray-100 text-gray-800" };
      case "expired": return { label: "Expirado", className: "bg-red-100 text-red-800" };
      case "cancelled": return { label: "Cancelado", className: "bg-red-100 text-red-800" };
      default: return { label: status, className: "bg-gray-100 text-gray-800" };
    }
  };

  const { label, className } = getStatusDetails();
  
  return (
    <div className={`text-xs px-2 py-1 rounded-full ${className}`}>
      {label}
    </div>
  );
};

const RewardCard: React.FC<{
  reward: Reward;
  onUse: (id: string) => void;
  onShare: (reward: Reward) => void;
}> = ({ reward, onUse, onShare }) => {
  const [isUsingReward, setIsUsingReward] = useState(false);
  
  const IconComponent = useMemo(() => {
    const icons = {
      discount: CreditCard,
      free_song: Music,
      priority_queue: Zap,
      badge: Award,
      custom: Gift,
    };
    return icons[reward.type] || Gift;
  }, [reward.type]);
  
  const colorClass = useMemo(() => {
    const colors = {
      discount: "text-green-600 bg-green-100 dark:bg-green-900/20 dark:text-green-400",
      free_song: "text-blue-600 bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400",
      priority_queue: "text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400",
      badge: "text-purple-600 bg-purple-100 dark:bg-purple-900/20 dark:text-purple-400",
      custom: "text-pink-600 bg-pink-100 dark:bg-pink-900/20 dark:text-pink-400",
    };
    return colors[reward.type] || "text-gray-600 bg-gray-100 dark:bg-gray-800 dark:text-gray-400";
  }, [reward.type]);
  
  const awardedForLabel = useMemo(() => {
    const labels = {
      daily_winner: "Campeão do Dia",
      weekly_winner: "Campeão da Semana",
      monthly_winner: "Campeão do Mês",
      best_performance: "Melhor Performance",
      most_votes: "Mais Votado",
      participation: "Participação",
    };
    return labels[reward.awardedFor] || reward.awardedFor;
  }, [reward.awardedFor]);
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };
  
  const getDaysUntilExpiry = (expiresAt: string) => {
    const now = new Date();
    const expiry = new Date(expiresAt);
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };
  
  const daysUntilExpiry = reward.expiresAt ? getDaysUntilExpiry(reward.expiresAt) : null;
  
  const handleUseReward = () => {
    if (window.confirm(`Tem certeza que deseja usar este prêmio: ${reward.title}?`)) {
      setIsUsingReward(true);
      onUse(reward.id);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`p-4 rounded-xl border-2 transition-all ${
        reward.isActive
          ? "border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/10"
          : reward.status === "used"
          ? "border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800/30"
          : "border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/10"
      }`}
    >
      <div className="flex flex-col sm:flex-row sm:items-start justify-between gap-4">
        <div className="flex items-start space-x-4">
          <div className={`p-3 rounded-full ${colorClass}`}>
            <IconComponent className="w-6 h-6" />
          </div>

          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-1">
              <h3 className="font-bold text-gray-900 dark:text-white">
                {reward.title}
              </h3>
              {reward.performanceData?.rank &&
                reward.performanceData.rank <= 3 && (
                  <div className="flex items-center space-x-1">
                    {reward.performanceData.rank === 1 && (
                      <span role="img" aria-label="Medalha de ouro">🥇</span>
                    )}
                    {reward.performanceData.rank === 2 && (
                      <span role="img" aria-label="Medalha de prata">🥈</span>
                    )}
                    {reward.performanceData.rank === 3 && (
                      <span role="img" aria-label="Medalha de bronze">🥉</span>
                    )}
                  </div>
                )}
            </div>

            <p className="text-gray-600 dark:text-gray-300 text-sm mb-2">
              {reward.description}
            </p>

            <div className="flex flex-wrap items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
              <div className="flex items-center space-x-1">
                <Calendar className="w-3 h-3" aria-hidden="true" />
                <span>{formatDate(reward.awardedDate)}</span>
              </div>

              <div className="flex items-center space-x-1">
                <Star className="w-3 h-3" aria-hidden="true" />
                <span>{awardedForLabel}</span>
              </div>

              {reward.performanceData?.songTitle && (
                <div className="flex items-center space-x-1">
                  <Music className="w-3 h-3" aria-hidden="true" />
                  <span title={`${reward.performanceData.songTitle} - ${reward.performanceData.artist || ''}`}>
                    {reward.performanceData.songTitle}
                  </span>
                </div>
              )}
            </div>

            {reward.performanceData?.averageRating && (
              <div className="mt-2 flex items-center space-x-2">
                <div className="flex items-center space-x-1">
                  <Star className="w-4 h-4 text-yellow-500" aria-hidden="true" />
                  <span className="text-sm font-medium">
                    {reward.performanceData.averageRating.toFixed(1)}
                  </span>
                </div>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  ({reward.performanceData.totalVotes} votos)
                </span>
              </div>
            )}

            {daysUntilExpiry !== null && (
              <div className="mt-2">
                {daysUntilExpiry > 0 ? (
                  <div className="flex items-center space-x-1 text-xs text-orange-600 dark:text-orange-400">
                    <Clock className="w-3 h-3" aria-hidden="true" />
                    <span>
                      Expira em {daysUntilExpiry} {daysUntilExpiry === 1 ? 'dia' : 'dias'}
                    </span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-1 text-xs text-red-600 dark:text-red-400">
                    <XCircle className="w-3 h-3" aria-hidden="true" />
                    <span>Expirado</span>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        <div className="flex flex-col items-end space-y-2">
          <div className="text-right">
            <div className="font-bold text-lg text-purple-600 dark:text-purple-400">
              {reward.formattedValue}
            </div>
            <RewardStatus status={reward.status} isActive={reward.isActive} />
          </div>

          <div className="flex space-x-2">
            <button
              onClick={() => onShare(reward)}
              className="p-2 text-blue-600 hover:bg-blue-100 dark:text-blue-400 dark:hover:bg-blue-900/20 rounded-full transition-colors"
              title="Compartilhar"
              aria-label="Compartilhar prêmio"
            >
              <Share2 className="w-4 h-4" />
            </button>

            {reward.canBeUsed && (
              <button
                onClick={handleUseReward}
                disabled={isUsingReward}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
                aria-label="Usar prêmio"
              >
                {isUsingReward ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" aria-hidden="true" />
                    <span>Processando...</span>
                  </>
                ) : (
                  <>
                    <CheckCircle className="w-4 h-4" aria-hidden="true" />
                    <span>Usar Prêmio</span>
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

const RewardsPanel: React.FC<RewardsPanelProps> = ({
  sessionId,
  restaurantId,
  isOpen,
  onClose,
}) => {
  const [rewards, setRewards] = useState<Reward[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeRewards, setActiveRewards] = useState(0);

  const loadRewards = useCallback(async () => {
    if (!sessionId) return;
    
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(
  `${API_CONFIG.BASE_URL}/rewards/client/${sessionId}`,
        {
          headers: {
            "Content-Type": "application/json",
            "X-Restaurant-ID": restaurantId
          },
          credentials: "include" // For CSRF protection with cookies
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Error ${response.status}: Failed to load rewards`);
      }

      const data = await response.json();
      setRewards(data.rewards || []);
      setActiveRewards(data.active || 0);
    } catch (error) {
      console.error("Error loading rewards:", error);
      setError(error instanceof Error ? error.message : "Failed to load rewards");
      toast.error("Erro ao carregar prêmios. Tente novamente.");
    } finally {
      setLoading(false);
    }
  }, [sessionId, restaurantId]);

  useEffect(() => {
    if (isOpen && sessionId) {
      loadRewards();
    }
  }, [isOpen, sessionId, loadRewards]);

  const useReward = useCallback(async (rewardId: string) => {
    if (!sessionId) return;
    
    try {
      const response = await fetch(
  `${API_CONFIG.BASE_URL}/rewards/use/${rewardId}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-Restaurant-ID": restaurantId
          },
          credentials: "include", // For CSRF protection with cookies
          body: JSON.stringify({ sessionId }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Error ${response.status}: Failed to use reward`);
      }

      const data = await response.json();
      
      // Reload rewards list to reflect changes
      await loadRewards();
      
      // Show success notification
      toast.success(data.message || "Prêmio utilizado com sucesso!");
    } catch (error) {
      console.error("Error using reward:", error);
      toast.error(error instanceof Error ? error.message : "Erro ao usar prêmio");
    }
  }, [sessionId, restaurantId, loadRewards]);

  const shareReward = useCallback((reward: Reward) => {
    const shareText = `🏆 Ganhei um prêmio! ${reward.title} - ${reward.formattedValue}`;

    if (navigator.share) {
      navigator.share({
        title: "Meu Prêmio!",
        text: shareText,
        url: window.location.href,
      }).catch(err => {
        console.error("Error sharing:", err);
        // Fallback to clipboard
        copyToClipboard(shareText);
      });
    } else {
      // Fallback to clipboard
      copyToClipboard(shareText);
    }
  }, []);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
      .then(() => toast.success("Texto copiado para a área de transferência!"))
      .catch(() => toast.error("Não foi possível copiar o texto"));
  };

  // Early return if not open
  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
        role="dialog"
        aria-modal="true"
        aria-labelledby="rewards-title"
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-purple-600 to-blue-600 p-6 text-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Trophy className="w-8 h-8" aria-hidden="true" />
                <div>
                  <h2 id="rewards-title" className="text-2xl font-bold">Meus Prêmios</h2>
                  <p className="text-purple-100">
                    {activeRewards} prêmio{activeRewards !== 1 ? 's' : ''} ativo{activeRewards !== 1 ? 's' : ''} • {rewards.length} total
                  </p>
                </div>
              </div>

              <button
                onClick={onClose}
                className="p-2 hover:bg-white/20 rounded-full transition-colors"
                aria-label="Fechar painel de prêmios"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 max-h-[60vh] overflow-y-auto">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="w-8 h-8 text-purple-600 animate-spin" aria-hidden="true" />
                <span className="ml-3 text-gray-600 dark:text-gray-300">
                  Carregando prêmios...
                </span>
              </div>
            ) : error ? (
              <div className="text-center py-8 px-4">
                <AlertTriangle className="w-12 h-12 text-orange-500 mx-auto mb-3" aria-hidden="true" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Erro ao carregar prêmios
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">{error}</p>
                <button
                  onClick={loadRewards}
                  className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  Tentar novamente
                </button>
              </div>
            ) : rewards.length === 0 ? (
              <div className="text-center py-12">
                <Gift className="w-16 h-16 text-gray-400 dark:text-gray-500 mx-auto mb-4" aria-hidden="true" />
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  Nenhum prêmio ainda
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Participe das competições para ganhar prêmios incríveis!
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {rewards.map((reward) => (
                  <RewardCard 
                    key={reward.id} 
                    reward={reward} 
                    onUse={useReward} 
                    onShare={shareReward} 
                  />
                ))}
              </div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default RewardsPanel;