import express from 'express';
import request from 'supertest';
import { v4 as uuid } from 'uuid';
import { TestDataSource, initializeTestDatabase, cleanTestData, closeTestDatabase } from '../config/test-database';
import { Restaurant } from '../models/Restaurant';
import { User, UserRole } from '../models/User';
import { generateToken } from '../middleware/auth';
import restaurantsRouter from '../routes/restaurants';
import collaborativePlaylistRoutes from '../routes/collaborativePlaylist';
import genresRoutes from '../routes/genres';

// Mock YouTube API
jest.mock('googleapis', () => ({
  google: {
    youtube: jest.fn(() => ({
      playlists: {
        insert: jest.fn().mockResolvedValue({ data: { id: 'yt-playlist-mock' } })
      },
      playlistItems: {
        insert: jest.fn().mockResolvedValue({ data: { id: 'yt-item-mock' } }),
        list: jest.fn().mockResolvedValue({ data: { items: [] } }),
        update: jest.fn().mockResolvedValue({})
      }
    })),
    auth: { OAuth2: jest.fn().mockImplementation(() => ({ setCredentials: jest.fn() })) }
  }
}));

// Mock YouTubeService.getVideoInfo para add-tracks e ranking
jest.mock('../services/YouTubeService', () => ({
  YouTubeService: jest.fn().mockImplementation(() => ({
    getVideoInfo: jest.fn(async (videoId: string) => ({
      youtubeVideoId: videoId,
      title: `Title ${videoId}`,
      artist: `Artist ${videoId}`,
      channelName: 'Channel',
      duration: 180,
      thumbnailUrl: `https://i.ytimg.com/vi/${videoId}/mqdefault.jpg`,
      description: 'desc',
      metadata: { genre: ['rock'] }
    }))
  }))
}));

// Mock WebSocketService para não abrir timers
jest.mock('../services/WebSocketService', () => ({
  WebSocketService: { getInstance: () => ({
    emitToRestaurant: jest.fn(),
    emitToUser: jest.fn(),
    broadcast: jest.fn(),
    broadcastToRestaurant: jest.fn()
  }) }
}));

// Redirecionar AppDataSource para TestDataSource
jest.mock('../config/database', () => ({ AppDataSource: {} }));


describe('Fluxo colaborativo real: add-tracks, voto normal/supervoto, stats/ranking + gêneros', () => {
  let app: express.Express;
  const baseCollab = '/api/v1/collaborative-playlist';
  const baseRestaurants = '/api/v1/restaurants';
  const baseGenres = '/api/v1/genres';

  beforeAll(async () => {
    await initializeTestDatabase();
    process.env.JWT_SECRET = process.env.JWT_SECRET || 'test-secret';

    app = express();
    app.use(express.json());
    app.use(baseRestaurants, restaurantsRouter);
    app.use(baseCollab, collaborativePlaylistRoutes);
    app.use(baseGenres, genresRoutes);
  });

  afterAll(async () => {
    await closeTestDatabase();
  });

  beforeEach(async () => {
    await cleanTestData();
    const { AppDataSource } = require('../config/database');
  AppDataSource.getRepository = jest.fn((e: any) => TestDataSource.getRepository(e));
  AppDataSource.isInitialized = true;
  });

  it('deve criar restaurante, playlist, adicionar faixas, votar normal e supervoto, e verificar stats/ranking', async () => {
    // Criar restaurante e usuário
    const restRepo = TestDataSource.getRepository(Restaurant);
    const restaurant = restRepo.create({ id: 'rest-flow', name: 'Rest Flow', isActive: true });
    await restRepo.save(restaurant);

    const userRepo = TestDataSource.getRepository(User);
    const user = userRepo.create({
      name: 'Owner', email: `owner_${Date.now()}@test.com`, password: 'x', role: UserRole.ADMIN, isActive: true, restaurant
    });
    await userRepo.save(user);
    const token = generateToken(user);

    // Criar playlist colaborativa
    const createRes = await request(app)
      .post(`${baseCollab}/${restaurant.id}/create`)
      .set('Authorization', `Bearer ${token}`)
      .send({ playlistName: 'Principal' });
    expect(createRes.status).toBe(201);
    const playlistId = createRes.body?.data?.playlistId || createRes.body?.playlistId;
    expect(playlistId).toBeDefined();

    // Adicionar faixas
    const addRes = await request(app)
      .post(`${baseCollab}/${restaurant.id}/add-tracks`)
      .set('Authorization', `Bearer ${token}`)
      .send({ videoIds: ['AAA111', 'BBB222', 'CCC333'] });
    expect(addRes.status).toBe(200);
    expect(addRes.body?.data?.addedCount).toBe(3);

    // Voto normal (gratuito)
    const voteRes = await request(app)
      .post(`${baseCollab}/${restaurant.id}/vote`)
      .send({ youtubeVideoId: 'AAA111', tableNumber: 5, clientSessionId: uuid() });
    expect(voteRes.status).toBe(200);
    expect(voteRes.body?.data?.voteWeight).toBe(1);

    // Supervoto (pago)
    const superRes = await request(app)
      .post(`${baseCollab}/${restaurant.id}/supervote`)
      .send({ youtubeVideoId: 'BBB222', paymentAmount: 20, paymentId: 'pix_123', clientSessionId: uuid() });
    expect(superRes.status).toBe(200);
    expect(superRes.body?.data?.voteWeight).toBe(8);

    // Stats
    const statsRes = await request(app).get(`${baseCollab}/${restaurant.id}/stats`);
    expect(statsRes.status).toBe(200);
    expect(statsRes.body?.data?.totalVotes).toBeGreaterThanOrEqual(9);

    // Ranking
    const rankRes = await request(app).get(`${baseCollab}/${restaurant.id}/ranking?limit=10`);
    expect(rankRes.status).toBe(200);
    const items = rankRes.body?.data || [];
    expect(items.length).toBeGreaterThan(0);
    const top = items[0];
    expect(['BBB222','AAA111','CCC333']).toContain(top.youtubeVideoId);
  });

  it('deve semear gêneros padrão, listar e buscar por ID', async () => {
    // Seed
    const seedRes = await request(app).post(`${baseGenres}/seed`).send({});
    expect(seedRes.status).toBe(200);
    // Listar
    const listRes = await request(app).get(baseGenres);
    expect(listRes.status).toBe(200);
    expect(listRes.body?.total).toBeGreaterThan(0);

    // Pegar um id qualquer da categoria music
    const music = listRes.body?.genres?.music || [];
    if (music.length > 0) {
      const id = music[0].id;
      const getRes = await request(app).get(`${baseGenres}/${id}`);
      expect(getRes.status).toBe(200);
      expect(getRes.body?.success).toBe(true);
    }
  });
});
