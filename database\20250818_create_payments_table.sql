-- Criação idempotente da tabela payments usada por PIX/Mercado Pago

CREATE TABLE IF NOT EXISTS payments (
  id VARCHAR PRIMARY KEY,
  suggestion_id UUID NOT NULL,
  session_id UUID NOT NULL,
  amount INTEGER NOT NULL,
  status VARCHAR NOT NULL DEFAULT 'pending',
  status_detail VARCHAR NULL,
  payment_method VARCHAR NOT NULL DEFAULT 'pix',
  external_reference VARCHAR NULL,
  qr_code TEXT NULL,
  qr_code_base64 TEXT NULL,
  ticket_url VARCHAR NULL,
  created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
  approved_at TIMESTAMP WITHOUT TIME ZONE NULL,
  expires_at TIMESTAMP WITHOUT TIME ZONE NULL,
  payer_email VARCHAR NULL,
  payer_name VARCHAR NULL,
  platform_fee INTEGER NOT NULL DEFAULT 60,
  restaurant_amount INTEGER NOT NULL DEFAULT 140,
  metadata JSON NULL
);

-- <PERSON><PERSON> e índices
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_type = 'FOREIGN KEY' AND constraint_name = 'fk_payments_suggestion'
  ) THEN
    ALTER TABLE payments
      ADD CONSTRAINT fk_payments_suggestion FOREIGN KEY (suggestion_id)
      REFERENCES suggestions(id) ON DELETE CASCADE;
  END IF;
END $$;

CREATE INDEX IF NOT EXISTS idx_payments_suggestion ON payments(suggestion_id);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
