{"version": 3, "file": "MusicPlayer-bc235a29.js", "sources": ["../../src/components/restaurant/MusicPlayer.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport { apiService } from \"../../services/api\";\r\nimport { useRestaurantContext } from \"./RestaurantDashboard\";\r\nimport { buildApiUrl } from \"../../config/api\";\r\nimport {\r\n  Play,\r\n  Pause,\r\n  Square,\r\n  Volume2,\r\n  VolumeX,\r\n  Music,\r\n  SkipBack,\r\n  SkipForward,\r\n  Shuffle,\r\n  Repeat,\r\n  Heart,\r\n  Share2,\r\n  Download,\r\n  Settings,\r\n  Maximize2,\r\n  Minimize2,\r\n  Search,\r\n  RefreshCw,\r\n  List,\r\n  X,\r\n  Youtube,\r\n  ExternalLink,\r\n  AlertCircle,\r\n  Plus,\r\n} from \"lucide-react\";\r\nimport { toast } from \"react-hot-toast\";\r\nimport YouTube from \"react-youtube\";\r\n\r\n// Interfaces locais para o player\r\ninterface PlaylistTrack {\r\n  youtubeVideoId: string;\r\n  title: string;\r\n  artist: string;\r\n  duration: number;\r\n  thumbnailUrl: string;\r\n  addedAt: string;\r\n  position: number;\r\n}\r\n\r\ninterface PlaylistData {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  thumbnail: string;\r\n  tracks: PlaylistTrack[];\r\n  totalDuration: number;\r\n  videoCount: number;\r\n  isActive: boolean;\r\n}\r\n\r\nconst ENABLE_MOCKS = import.meta.env.VITE_ENABLE_MOCKS === 'true';\r\n\r\n// Componente principal\r\nconst MusicPlayer: React.FC = () => {\r\n  const { restaurantId } = useRestaurantContext();\r\n\r\n  // Estado do player\r\n  const [isPlaying, setIsPlaying] = useState(false);\r\n  const [volume, setVolume] = useState(0.7);\r\n  const [isMuted, setIsMuted] = useState(false);\r\n  const [currentTime, setCurrentTime] = useState(0);\r\n  const [isVisible, setIsVisible] = useState(true);\r\n  const [isShuffled, setIsShuffled] = useState(false);\r\n  const [repeatMode, setRepeatMode] = useState<\"none\" | \"one\" | \"all\">(\"none\");\r\n  const [isLiked, setIsLiked] = useState(false);\r\n  const [isFullscreen, setIsFullscreen] = useState(false);\r\n  const [showPlaylist, setShowPlaylist] = useState(false);\r\n\r\n  // YouTube e Playlist\r\n  const [playlists, setPlaylists] = useState<PlaylistData[]>([]);\r\n  const [currentPlaylist, setCurrentPlaylist] = useState<PlaylistData | null>(\r\n    null\r\n  );\r\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [isSearching, setIsSearching] = useState(false);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [player, setPlayer] = useState<any>(null);\r\n\r\n  // Referência para o player do YouTube\r\n  const youtubePlayerRef = useRef<any>(null);\r\n\r\n  // Obtém o vídeo atual\r\n  const currentVideo = currentPlaylist?.tracks[currentVideoIndex] || {\r\n    youtubeVideoId: \"\",\r\n    title: \"Nenhuma música selecionada\",\r\n    artist: \"Selecione uma playlist\",\r\n    thumbnailUrl: \"\",\r\n    duration: 0,\r\n    addedAt: \"\",\r\n    position: 0,\r\n  };\r\n\r\n  // Opções para o player do YouTube\r\n  const youtubeOpts = {\r\n    height: \"0\",\r\n    width: \"0\",\r\n    playerVars: {\r\n      autoplay: isPlaying ? 1 : 0,\r\n      controls: 0,\r\n      disablekb: 1,\r\n      enablejsapi: 1,\r\n      iv_load_policy: 3,\r\n      modestbranding: 1,\r\n      rel: 0,\r\n      showinfo: 0,\r\n      fs: 0, // Desabilitar fullscreen\r\n      cc_load_policy: 0, // Desabilitar legendas\r\n      playsinline: 1, // Reproduzir inline no mobile\r\n  // Para iframes do YouTube, o host deve ser youtube.com para evitar erros de postMessage\r\n  origin: window.location.origin,\r\n  host: \"https://www.youtube.com\",\r\n    },\r\n  };\r\n\r\n  // Função para filtrar playlists locais\r\n  const searchLocalPlaylists = useCallback(\r\n    async (query: string) => {\r\n      if (!query.trim()) {\r\n        // Se a busca estiver vazia, mostrar todas as playlists carregadas\r\n        setIsSearching(false);\r\n        return;\r\n      }\r\n\r\n      setIsSearching(true);\r\n\r\n      try {\r\n        // Filtrar playlists locais por nome ou descrição\r\n        const filtered = playlists.filter(\r\n          (playlist) =>\r\n            playlist.name.toLowerCase().includes(query.toLowerCase()) ||\r\n            playlist.description.toLowerCase().includes(query.toLowerCase()) ||\r\n            playlist.tracks.some(\r\n              (track) =>\r\n                track.title.toLowerCase().includes(query.toLowerCase()) ||\r\n                track.artist.toLowerCase().includes(query.toLowerCase())\r\n            )\r\n        );\r\n\r\n        setPlaylists(filtered);\r\n\r\n        if (filtered.length > 0) {\r\n          toast.success(\r\n            `Encontradas ${filtered.length} playlist(s) com \"${query}\"`\r\n          );\r\n        } else {\r\n          toast(`Nenhuma playlist encontrada com \"${query}\"`, { icon: \"ℹ️\" });\r\n        }\r\n      } catch (err: any) {\r\n        console.error(\"Erro ao filtrar playlists:\", err);\r\n        toast.error(\"Erro ao filtrar playlists\");\r\n      } finally {\r\n        setIsSearching(false);\r\n      }\r\n    },\r\n    [playlists]\r\n  );\r\n\r\n  // Converter duração ISO 8601 para segundos\r\n  const convertISO8601ToSeconds = (duration: string): number => {\r\n    const match = duration.match(/PT(\\d+H)?(\\d+M)?(\\d+S)?/);\r\n\r\n    const hours = match?.[1] ? parseInt(match[1]) : 0;\r\n    const minutes = match?.[2] ? parseInt(match[2]) : 0;\r\n    const seconds = match?.[3] ? parseInt(match[3]) : 0;\r\n\r\n    return hours * 3600 + minutes * 60 + seconds;\r\n  };\r\n\r\n  // Carregar playlists reais do restaurante\r\n  const loadRealPlaylists = useCallback(async () => {\r\n    if (!restaurantId) {\r\n      toast.error(\"ID do restaurante não encontrado\");\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      console.log(`🔄 Carregando playlists para restaurante: ${restaurantId}`);\r\n\r\n      // Usar buildApiUrl para consistência\r\n      const url = buildApiUrl(`/playlists/restaurant/${restaurantId}`);\r\n      const response = await fetch(url, {\r\n        method: \"GET\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Erro ao carregar playlists: ${response.status}`);\r\n      }\r\n\r\n      const data = await response.json();\r\n      const foundPlaylists = data.playlists || [];\r\n\r\n      console.log(`✅ Encontradas ${foundPlaylists.length} playlists`);\r\n      console.log(`📊 Dados das playlists:`, foundPlaylists);\r\n\r\n      // Debug detalhado de cada playlist\r\n      foundPlaylists.forEach((playlist: any, index: number) => {\r\n        console.log(`📋 Playlist ${index + 1}:`, {\r\n          id: playlist.id,\r\n          name: playlist.name,\r\n          isActive: playlist.isActive,\r\n          videoCount: playlist.videoCount,\r\n        });\r\n      });\r\n\r\n      // Filtrar apenas playlists ativas e carregar seus tracks\r\n      const activePlaylistsBasic = foundPlaylists.filter((p: any) => {\r\n        if (!p.isActive) {\r\n          console.log(\r\n            `⚠️ Playlist ${p.name} filtrada: não está ativa (isActive=${p.isActive})`\r\n          );\r\n          return false;\r\n        }\r\n        return true;\r\n      });\r\n\r\n      console.log(\r\n        `🔄 Carregando tracks para ${activePlaylistsBasic.length} playlists ativas...`\r\n      );\r\n\r\n      // Carregar tracks para cada playlist ativa (otimizado)\r\n      const formattedPlaylists: PlaylistData[] = [];\r\n\r\n      for (const playlistBasic of activePlaylistsBasic) {\r\n        try {\r\n          console.log(\r\n            `🎵 Carregando tracks da playlist: ${playlistBasic.name} (${playlistBasic.id})`\r\n          );\r\n\r\n          // Carregar apenas as primeiras 10 tracks por playlist para otimizar\r\n          const playlistResponse = await fetch(\r\n            buildApiUrl(`/playlists/${playlistBasic.id}?limit=10&offset=0`),\r\n            {\r\n              method: \"GET\",\r\n              headers: {\r\n                \"Content-Type\": \"application/json\",\r\n              },\r\n            }\r\n          );\r\n\r\n          if (!playlistResponse.ok) {\r\n            console.log(\r\n              `⚠️ Erro ao carregar tracks da playlist ${playlistBasic.name}: ${playlistResponse.status}`\r\n            );\r\n            continue;\r\n          }\r\n\r\n          const playlistData = await playlistResponse.json();\r\n\r\n          if (\r\n            !playlistData.tracks ||\r\n            !Array.isArray(playlistData.tracks) ||\r\n            playlistData.tracks.length === 0\r\n          ) {\r\n            console.log(\r\n              `⚠️ Playlist ${playlistBasic.name} não tem tracks válidos`\r\n            );\r\n            continue;\r\n          }\r\n\r\n          console.log(\r\n            `✅ Playlist ${playlistBasic.name} carregada com ${playlistData.tracks.length} tracks (primeiros 10)`\r\n          );\r\n\r\n          const formattedPlaylist: PlaylistData = {\r\n            id: playlistData.id,\r\n            name: playlistData.name,\r\n            description: playlistData.description || \"\",\r\n            thumbnail: playlistData.thumbnail || \"\",\r\n            tracks: playlistData.tracks.map((track: any) => ({\r\n              youtubeVideoId: track.youtubeVideoId,\r\n              title: track.title,\r\n              artist: track.artist,\r\n              duration: track.duration,\r\n              thumbnailUrl: track.thumbnailUrl || \"\",\r\n              addedAt: track.addedAt,\r\n              position: track.position,\r\n            })),\r\n            totalDuration: playlistData.totalDuration || 0,\r\n            videoCount: playlistData.videoCount || playlistData.tracks.length,\r\n            isActive: playlistData.isActive,\r\n          };\r\n\r\n          formattedPlaylists.push(formattedPlaylist);\r\n        } catch (err) {\r\n          console.error(\r\n            `❌ Erro ao carregar tracks da playlist ${playlistBasic.name}:`,\r\n            err\r\n          );\r\n        }\r\n      }\r\n\r\n      setPlaylists(formattedPlaylists);\r\n\r\n      if (formattedPlaylists.length > 0) {\r\n        if (!currentPlaylist) {\r\n          setCurrentPlaylist(formattedPlaylists[0]);\r\n          setCurrentVideoIndex(0);\r\n        }\r\n        toast.success(`Carregadas ${formattedPlaylists.length} playlists`);\r\n      } else {\r\n        const enableMocks = import.meta.env.VITE_ENABLE_MOCKS === 'true';\r\n        if (enableMocks) {\r\n          console.log(\r\n            \"⚠️ Nenhuma playlist válida encontrada, carregando dados de exemplo (VITE_ENABLE_MOCKS=true)\"\r\n          );\r\n          loadExamplePlaylists();\r\n          toast(\r\n            \"Carregadas playlists de exemplo (nenhuma playlist com músicas encontrada)\",\r\n            { icon: \"ℹ️\", duration: 4000 }\r\n          );\r\n        } else {\r\n          console.warn(\"⚠️ Nenhuma playlist com músicas encontrada e mocks desativados (VITE_ENABLE_MOCKS=false)\");\r\n          toast(\"Nenhuma playlist com músicas encontrada\", { icon: \"⚠️\" });\r\n        }\r\n      }\r\n    } catch (err: any) {\r\n      console.error(\"❌ Erro ao carregar playlists:\", err);\r\n      setError(err.message || \"Erro ao carregar playlists\");\r\n      toast.error(\"Erro ao carregar playlists do restaurante\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [restaurantId]); // Removendo currentPlaylist para evitar loops\r\n\r\n  // Carregar playlist específica com suas tracks\r\n  const loadPlaylistTracks = useCallback(async (playlistId: string) => {\r\n    setLoading(true);\r\n\r\n    try {\r\n      console.log(`🔄 Carregando tracks da playlist: ${playlistId}`);\r\n\r\n      const url = buildApiUrl(`/playlists/${playlistId}`);\r\n      const response = await fetch(url, {\r\n        method: \"GET\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Erro ao carregar playlist: ${response.status}`);\r\n      }\r\n\r\n      const playlistData = await response.json();\r\n\r\n      if (playlistData.tracks && playlistData.tracks.length > 0) {\r\n        const formattedPlaylist: PlaylistData = {\r\n          id: playlistData.id,\r\n          name: playlistData.name,\r\n          description: playlistData.description || \"\",\r\n          thumbnail: playlistData.thumbnail || \"\",\r\n          tracks: playlistData.tracks.map((track: any) => ({\r\n            youtubeVideoId: track.youtubeVideoId,\r\n            title: track.title,\r\n            artist: track.artist,\r\n            duration: track.duration,\r\n            thumbnailUrl: track.thumbnailUrl || \"\",\r\n            addedAt: track.addedAt,\r\n            position: track.position,\r\n          })),\r\n          totalDuration: playlistData.totalDuration || 0,\r\n          videoCount: playlistData.videoCount || playlistData.tracks.length,\r\n          isActive: playlistData.isActive,\r\n        };\r\n\r\n        setCurrentPlaylist(formattedPlaylist);\r\n        setCurrentVideoIndex(0);\r\n\r\n        toast.success(`Playlist \"${playlistData.name}\" carregada`);\r\n      } else {\r\n        toast.error(\"Playlist não possui músicas\");\r\n      }\r\n    } catch (err: any) {\r\n      console.error(\"❌ Erro ao carregar playlist:\", err);\r\n      toast.error(\"Erro ao carregar playlist\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Função para carregar mais tracks de uma playlist (lazy loading)\r\n  const loadMoreTracksForPlaylist = useCallback(\r\n    async (playlistId: string, offset: number = 10, limit: number = 20) => {\r\n      try {\r\n        console.log(\r\n          `🔄 Carregando mais tracks da playlist: ${playlistId} (offset: ${offset})`\r\n        );\r\n\r\n        const response = await fetch(\r\n          buildApiUrl(\r\n            `/playlists/${playlistId}?limit=${limit}&offset=${offset}`\r\n          ),\r\n          {\r\n            method: \"GET\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n            },\r\n          }\r\n        );\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`Erro ao carregar mais tracks: ${response.status}`);\r\n        }\r\n\r\n        const playlistData = await response.json();\r\n\r\n        if (playlistData.tracks && playlistData.tracks.length > 0) {\r\n          // Atualizar a playlist atual com as novas tracks\r\n          setCurrentPlaylist((prev) => {\r\n            if (!prev || prev.id !== playlistId) return prev;\r\n\r\n            const newTracks = playlistData.tracks.map((track: any) => ({\r\n              youtubeVideoId: track.youtubeVideoId,\r\n              title: track.title,\r\n              artist: track.artist,\r\n              duration: track.duration,\r\n              thumbnailUrl: track.thumbnailUrl || \"\",\r\n              addedAt: track.addedAt,\r\n              position: track.position,\r\n            }));\r\n\r\n            return {\r\n              ...prev,\r\n              tracks: [...prev.tracks, ...newTracks],\r\n            };\r\n          });\r\n\r\n          // Atualizar também na lista de playlists\r\n          setPlaylists((prev) =>\r\n            prev.map((playlist) =>\r\n              playlist.id === playlistId\r\n                ? {\r\n                    ...playlist,\r\n                    tracks: [\r\n                      ...playlist.tracks,\r\n                      ...playlistData.tracks.map((track: any) => ({\r\n                        youtubeVideoId: track.youtubeVideoId,\r\n                        title: track.title,\r\n                        artist: track.artist,\r\n                        duration: track.duration,\r\n                        thumbnailUrl: track.thumbnailUrl || \"\",\r\n                        addedAt: track.addedAt,\r\n                        position: track.position,\r\n                      })),\r\n                    ],\r\n                  }\r\n                : playlist\r\n            )\r\n          );\r\n\r\n          console.log(\r\n            `✅ Carregadas mais ${playlistData.tracks.length} tracks`\r\n          );\r\n          toast.success(\r\n            `Carregadas mais ${playlistData.tracks.length} músicas`\r\n          );\r\n        }\r\n      } catch (err: any) {\r\n        console.error(\"❌ Erro ao carregar mais tracks:\", err);\r\n        toast.error(\"Erro ao carregar mais músicas\");\r\n      }\r\n    },\r\n    []\r\n  );\r\n\r\n  // Inicializar com playlists reais\r\n  useEffect(() => {\r\n    console.log(`🎵 MusicPlayer: restaurantId = ${restaurantId}`);\r\n    if (restaurantId) {\r\n      console.log(\r\n        `🎵 MusicPlayer: Iniciando carregamento de playlists para ${restaurantId}`\r\n      );\r\n      loadRealPlaylists();\r\n    } else if (ENABLE_MOCKS) {\r\n      console.log(`🎵 MusicPlayer: restaurantId não disponível, carregando mocks`);\r\n      loadExamplePlaylists();\r\n    } else {\r\n      console.log(`🎵 MusicPlayer: restaurantId não disponível, mocks desabilitados`);\r\n    }\r\n  }, [restaurantId, loadRealPlaylists]);\r\n\r\n  // Carregar playlists de exemplo para desenvolvimento/demo\r\n  const loadExamplePlaylists = useCallback(() => {\r\n    const examplePlaylists: PlaylistData[] = [\r\n      {\r\n        id: \"example-playlist-1\",\r\n        name: \"Playlist Demo - Ambiente\",\r\n        description: \"Playlist de exemplo para demonstração\",\r\n        thumbnail:\r\n          \"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='300'%3E%3Crect width='100%25' height='100%25' fill='%238B5CF6'/%3E%3Ctext x='50%25' y='50%25' dominant-baseline='middle' text-anchor='middle' fill='white' font-size='48'%3E♪%3C/text%3E%3C/svg%3E\",\r\n        tracks: [\r\n          {\r\n            youtubeVideoId: \"dQw4w9WgXcQ\",\r\n            title: \"Never Gonna Give You Up\",\r\n            artist: \"Rick Astley\",\r\n            duration: 213,\r\n            thumbnailUrl:\r\n              \"https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg\",\r\n            addedAt: new Date().toISOString(),\r\n            position: 1,\r\n          },\r\n          {\r\n            youtubeVideoId: \"9bZkp7q19f0\",\r\n            title: \"Gangnam Style\",\r\n            artist: \"PSY\",\r\n            duration: 253,\r\n            thumbnailUrl:\r\n              \"https://img.youtube.com/vi/9bZkp7q19f0/mqdefault.jpg\",\r\n            addedAt: new Date().toISOString(),\r\n            position: 2,\r\n          },\r\n          {\r\n            youtubeVideoId: \"kJQP7kiw5Fk\",\r\n            title: \"Despacito\",\r\n            artist: \"Luis Fonsi ft. Daddy Yankee\",\r\n            duration: 281,\r\n            thumbnailUrl:\r\n              \"https://img.youtube.com/vi/kJQP7kiw5Fk/mqdefault.jpg\",\r\n            addedAt: new Date().toISOString(),\r\n            position: 3,\r\n          },\r\n        ],\r\n        totalDuration: 747,\r\n        videoCount: 3,\r\n        isActive: true,\r\n      },\r\n      {\r\n        id: \"example-playlist-2\",\r\n        name: \"Playlist Demo - Animada\",\r\n        description: \"Músicas mais animadas para o ambiente\",\r\n        thumbnail:\r\n          \"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='300'%3E%3Crect width='100%25' height='100%25' fill='%23E74C3C'/%3E%3Ctext x='50%25' y='50%25' dominant-baseline='middle' text-anchor='middle' fill='white' font-size='48'%3E♫%3C/text%3E%3C/svg%3E\",\r\n        tracks: [\r\n          {\r\n            youtubeVideoId: \"fJ9rUzIMcZQ\",\r\n            title: \"Bohemian Rhapsody\",\r\n            artist: \"Queen\",\r\n            duration: 355,\r\n            thumbnailUrl:\r\n              \"https://img.youtube.com/vi/fJ9rUzIMcZQ/mqdefault.jpg\",\r\n            addedAt: new Date().toISOString(),\r\n            position: 1,\r\n          },\r\n          {\r\n            youtubeVideoId: \"hTWKbfoikeg\",\r\n            title: \"Smells Like Teen Spirit\",\r\n            artist: \"Nirvana\",\r\n            duration: 301,\r\n            thumbnailUrl:\r\n              \"https://img.youtube.com/vi/hTWKbfoikeg/mqdefault.jpg\",\r\n            addedAt: new Date().toISOString(),\r\n            position: 2,\r\n          },\r\n        ],\r\n        totalDuration: 656,\r\n        videoCount: 2,\r\n        isActive: true,\r\n      },\r\n    ];\r\n\r\n    setPlaylists(examplePlaylists);\r\n\r\n    if (!currentPlaylist) {\r\n      setCurrentPlaylist(examplePlaylists[0]);\r\n      setCurrentVideoIndex(0);\r\n    }\r\n\r\n    console.log(\"✅ Playlists de exemplo carregadas:\", examplePlaylists.length);\r\n  }, [currentPlaylist]);\r\n\r\n  // Gerenciar eventos do player do YouTube\r\n  const onPlayerReady = (event: any) => {\r\n    setPlayer(event.target);\r\n    // Definir volume inicial\r\n    event.target.setVolume(volume * 100);\r\n  };\r\n\r\n  const onPlayerStateChange = (event: any) => {\r\n    // -1 (não iniciado), 0 (terminado), 1 (reproduzindo), 2 (pausado), 3 (buffer), 5 (vídeo sugerido)\r\n    switch (event.data) {\r\n      case 0: // Terminado\r\n        if (repeatMode === \"one\") {\r\n          event.target.playVideo();\r\n        } else if (\r\n          repeatMode === \"all\" ||\r\n          currentVideoIndex < (currentPlaylist?.tracks.length || 0) - 1\r\n        ) {\r\n          handleNextSong();\r\n        } else {\r\n          setIsPlaying(false);\r\n          setCurrentTime(0);\r\n        }\r\n        break;\r\n      case 1: // Reproduzindo\r\n        setIsPlaying(true);\r\n        break;\r\n      case 2: // Pausado\r\n        setIsPlaying(false);\r\n        break;\r\n    }\r\n  };\r\n\r\n  // Configurar listener para postMessage do YouTube\r\n  useEffect(() => {\r\n    const handlePostMessage = (event: MessageEvent) => {\r\n      // Permitir mensagens do YouTube\r\n      if (\r\n        event.origin === \"https://www.youtube.com\" ||\r\n        event.origin === \"https://www.youtube-nocookie.com\"\r\n      ) {\r\n        // Mensagens do YouTube são permitidas - não fazer nada\r\n        return;\r\n      }\r\n      \r\n      // Log outros origins para debug (apenas em desenvolvimento)\r\n      if (process.env.NODE_ENV === \"development\" && event.origin !== window.location.origin) {\r\n        console.log(\"PostMessage de origin não conhecido:\", event.origin, event.data);\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"message\", handlePostMessage);\r\n    return () => window.removeEventListener(\"message\", handlePostMessage);\r\n  }, []);\r\n\r\n  // Atualizar tempo atual\r\n  useEffect(() => {\r\n    if (isPlaying && player) {\r\n      const interval = setInterval(() => {\r\n        const currentTime = player.getCurrentTime();\r\n        setCurrentTime(currentTime);\r\n      }, 1000);\r\n      return () => clearInterval(interval);\r\n    }\r\n  }, [isPlaying, player]);\r\n\r\n  // Controles do player\r\n  const togglePlay = () => {\r\n    if (player) {\r\n      if (isPlaying) {\r\n        player.pauseVideo();\r\n      } else {\r\n        player.playVideo();\r\n      }\r\n      toast.success(isPlaying ? \"Pausado\" : \"Reproduzindo\");\r\n    }\r\n  };\r\n\r\n  const stopPlayback = () => {\r\n    if (player) {\r\n      player.stopVideo();\r\n      setIsPlaying(false);\r\n      setCurrentTime(0);\r\n      toast.success(\"Parado\");\r\n    }\r\n  };\r\n\r\n  const toggleMute = () => {\r\n    if (player) {\r\n      if (isMuted) {\r\n        player.unMute();\r\n        player.setVolume(volume * 100);\r\n      } else {\r\n        player.mute();\r\n      }\r\n      setIsMuted(!isMuted);\r\n    }\r\n  };\r\n\r\n  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const newVolume = parseFloat(e.target.value);\r\n    setVolume(newVolume);\r\n\r\n    if (player) {\r\n      player.setVolume(newVolume * 100);\r\n      if (newVolume === 0) {\r\n        player.mute();\r\n        setIsMuted(true);\r\n      } else if (isMuted) {\r\n        player.unMute();\r\n        setIsMuted(false);\r\n      }\r\n    }\r\n  };\r\n\r\n  const formatTime = (time: number) => {\r\n    const minutes = Math.floor(time / 60);\r\n    const seconds = Math.floor(time % 60);\r\n    return `${minutes}:${seconds.toString().padStart(2, \"0\")}`;\r\n  };\r\n\r\n  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (player && currentVideo.duration > 0) {\r\n      const seekTime = parseFloat(e.target.value) * currentVideo.duration;\r\n      player.seekTo(seekTime);\r\n      setCurrentTime(seekTime);\r\n    }\r\n  };\r\n\r\n  const skipBack = () => {\r\n    if (player) {\r\n      const newTime = Math.max(0, currentTime - 10);\r\n      player.seekTo(newTime);\r\n      setCurrentTime(newTime);\r\n      toast.success(\"Voltou 10 segundos\");\r\n    }\r\n  };\r\n\r\n  const skipForward = () => {\r\n    if (player) {\r\n      const newTime = Math.min(currentVideo.duration, currentTime + 10);\r\n      player.seekTo(newTime);\r\n      setCurrentTime(newTime);\r\n      toast.success(\"Avançou 10 segundos\");\r\n    }\r\n  };\r\n\r\n  const toggleShuffle = () => {\r\n    setIsShuffled(!isShuffled);\r\n    toast.success(isShuffled ? \"Aleatório desativado\" : \"Aleatório ativado\");\r\n  };\r\n\r\n  const toggleRepeat = () => {\r\n    const modes = [\"none\", \"one\", \"all\"] as const;\r\n    const currentIndex = modes.indexOf(repeatMode);\r\n    const nextMode = modes[(currentIndex + 1) % modes.length];\r\n    setRepeatMode(nextMode);\r\n\r\n    const messages = {\r\n      none: \"Repetição desativada\",\r\n      one: \"Repetir uma música\",\r\n      all: \"Repetir todas\",\r\n    };\r\n    toast.success(messages[nextMode]);\r\n  };\r\n\r\n  const toggleLike = () => {\r\n    setIsLiked(!isLiked);\r\n    toast.success(\r\n      isLiked ? \"Removido dos favoritos\" : \"Adicionado aos favoritos\"\r\n    );\r\n  };\r\n\r\n  const toggleFullscreen = () => {\r\n    setIsFullscreen(!isFullscreen);\r\n  };\r\n\r\n  // Navegação entre músicas\r\n  const handlePreviousSong = () => {\r\n    if (!currentPlaylist) return;\r\n\r\n    if (isShuffled) {\r\n      const randomIndex = Math.floor(\r\n        Math.random() * currentPlaylist.tracks.length\r\n      );\r\n      setCurrentVideoIndex(randomIndex);\r\n    } else {\r\n      setCurrentVideoIndex((prev) =>\r\n        prev > 0 ? prev - 1 : currentPlaylist.tracks.length - 1\r\n      );\r\n    }\r\n\r\n    // Reiniciar o player com a nova música\r\n    setCurrentTime(0);\r\n    if (isPlaying && player) {\r\n      // Pequeno timeout para garantir que o estado foi atualizado\r\n      setTimeout(() => player.playVideo(), 100);\r\n    }\r\n\r\n    toast.success(\"Música anterior\");\r\n  };\r\n\r\n  const handleNextSong = () => {\r\n    if (!currentPlaylist) return;\r\n\r\n    const nextIndex = isShuffled\r\n      ? Math.floor(Math.random() * currentPlaylist.tracks.length)\r\n      : currentVideoIndex < currentPlaylist.tracks.length - 1\r\n      ? currentVideoIndex + 1\r\n      : 0;\r\n\r\n    // Se estamos próximos do fim da playlist (últimas 5 músicas) e ainda há mais tracks para carregar\r\n    if (\r\n      !isShuffled &&\r\n      nextIndex >= currentPlaylist.tracks.length - 5 &&\r\n      currentPlaylist.tracks.length < (currentPlaylist.videoCount || 50)\r\n    ) {\r\n      console.log(\"🔄 Próximo do fim da playlist, carregando mais tracks...\");\r\n      loadMoreTracksForPlaylist(\r\n        currentPlaylist.id,\r\n        currentPlaylist.tracks.length\r\n      );\r\n    }\r\n\r\n    setCurrentVideoIndex(nextIndex);\r\n\r\n    // Reiniciar o player com a nova música\r\n    setCurrentTime(0);\r\n    if (isPlaying && player) {\r\n      // Pequeno timeout para garantir que o estado foi atualizado\r\n      setTimeout(() => player.playVideo(), 100);\r\n    }\r\n\r\n    toast.success(\"Próxima música\");\r\n  };\r\n\r\n  // Selecionar playlist\r\n  const handleSelectPlaylist = (playlist: PlaylistData) => {\r\n    setCurrentPlaylist(playlist);\r\n    setCurrentVideoIndex(0);\r\n    setCurrentTime(0);\r\n    setShowPlaylist(false);\r\n\r\n    // Iniciar reprodução da nova playlist\r\n    if (player && isPlaying) {\r\n      // Pequeno timeout para garantir que o estado foi atualizado\r\n      setTimeout(() => player.playVideo(), 100);\r\n    }\r\n\r\n    toast.success(`Playlist \"${playlist.name}\" selecionada`);\r\n  };\r\n\r\n  // Selecionar música específica\r\n  const handleSelectSong = (index: number) => {\r\n    setCurrentVideoIndex(index);\r\n    setCurrentTime(0);\r\n\r\n    // Iniciar reprodução da nova música\r\n    if (player) {\r\n      if (isPlaying) {\r\n        // Pequeno timeout para garantir que o estado foi atualizado\r\n        setTimeout(() => player.playVideo(), 100);\r\n      }\r\n    }\r\n\r\n    toast.success(\r\n      `Música \"${currentPlaylist?.tracks[index].title}\" selecionada`\r\n    );\r\n  };\r\n\r\n  // Componente não visível (simulação de erro)\r\n  if (!isVisible) {\r\n    return (\r\n      <div className=\"text-center py-8\">\r\n        <AlertCircle className=\"w-12 h-12 text-red-500 mx-auto mb-4\" />\r\n        <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-4\">\r\n          Player não disponível\r\n        </h3>\r\n        <button\r\n          onClick={() => setIsVisible(true)}\r\n          className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\r\n        >\r\n          Recarregar Player\r\n        </button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      className={`space-y-6 ${\r\n        isFullscreen ? \"fixed inset-0 z-50 bg-black bg-opacity-95 p-8\" : \"\"\r\n      }`}\r\n    >\r\n      <div\r\n        className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden ${\r\n          isFullscreen ? \"h-full flex flex-col\" : \"\"\r\n        }`}\r\n      >\r\n        {/* Header */}\r\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\">\r\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-2\">\r\n            <Music className=\"w-6 h-6 text-purple-600\" />\r\n            Player de Música\r\n          </h2>\r\n          <div className=\"flex items-center gap-2\">\r\n            <button\r\n              onClick={() => setShowPlaylist(!showPlaylist)}\r\n              className=\"p-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors\"\r\n              aria-label=\"Mostrar playlist\"\r\n              title=\"Mostrar playlist\"\r\n            >\r\n              <List className=\"w-5 h-5\" />\r\n            </button>\r\n            <button\r\n              onClick={toggleFullscreen}\r\n              className=\"p-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors\"\r\n              aria-label={isFullscreen ? \"Sair da tela cheia\" : \"Tela cheia\"}\r\n              title={isFullscreen ? \"Sair da tela cheia\" : \"Tela cheia\"}\r\n            >\r\n              {isFullscreen ? (\r\n                <Minimize2 className=\"w-5 h-5\" />\r\n              ) : (\r\n                <Maximize2 className=\"w-5 h-5\" />\r\n              )}\r\n            </button>\r\n            <button\r\n              onClick={() => setIsVisible(false)}\r\n              className=\"text-red-600 hover:text-red-700 text-sm px-3 py-1 rounded-md border border-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors\"\r\n              aria-label=\"Simular erro\"\r\n            >\r\n              Simular Erro\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Layout com dois painéis */}\r\n        <div className={`flex ${isFullscreen ? \"flex-1\" : \"\"}`}>\r\n          {/* Painel principal do player */}\r\n          <div\r\n            className={`${showPlaylist ? \"w-2/3\" : \"w-full\"} ${\r\n              isFullscreen ? \"flex flex-col\" : \"\"\r\n            }`}\r\n          >\r\n            {/* Main Player Area */}\r\n            <div className={`${isFullscreen ? \"flex-1 flex flex-col\" : \"p-6\"}`}>\r\n              {/* YouTube Player (invisível) */}\r\n              <div className=\"hidden\">\r\n                <YouTube\r\n                  videoId={currentVideo.youtubeVideoId}\r\n                  opts={youtubeOpts}\r\n                  onReady={onPlayerReady}\r\n                  onStateChange={onPlayerStateChange}\r\n                  ref={youtubePlayerRef}\r\n                />\r\n              </div>\r\n\r\n              {/* Album Art & Song Info */}\r\n              <div\r\n                className={`bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700 rounded-xl p-8 text-white mb-6 ${\r\n                  isFullscreen ? \"flex-1 flex items-center justify-center\" : \"\"\r\n                }`}\r\n              >\r\n                <div\r\n                  className={`${\r\n                    isFullscreen\r\n                      ? \"text-center max-w-2xl\"\r\n                      : \"flex items-center space-x-6\"\r\n                  }`}\r\n                >\r\n                  <div\r\n                    className={`${\r\n                      isFullscreen ? \"w-80 h-80 mx-auto mb-8\" : \"w-24 h-24\"\r\n                    } bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm border border-white/30 shadow-2xl`}\r\n                  >\r\n                    <Music\r\n                      className={`${\r\n                        isFullscreen ? \"w-32 h-32\" : \"w-12 h-12\"\r\n                      } text-white/80`}\r\n                    />\r\n                  </div>\r\n                  <div\r\n                    className={`flex-1 ${isFullscreen ? \"text-center\" : \"\"}`}\r\n                  >\r\n                    <h3\r\n                      className={`${\r\n                        isFullscreen ? \"text-4xl mb-2\" : \"text-xl\"\r\n                      } font-bold`}\r\n                    >\r\n                      {currentVideo.title}\r\n                    </h3>\r\n                    <p\r\n                      className={`${\r\n                        isFullscreen\r\n                          ? \"text-2xl text-purple-200 mb-1\"\r\n                          : \"text-lg text-blue-200\"\r\n                      }`}\r\n                    >\r\n                      {currentVideo.artist}\r\n                    </p>\r\n                    <p\r\n                      className={`${\r\n                        isFullscreen\r\n                          ? \"text-lg text-purple-300\"\r\n                          : \"text-sm text-blue-300\"\r\n                      }`}\r\n                    >\r\n                      {currentPlaylist?.name || \"Nenhuma playlist selecionada\"}\r\n                    </p>\r\n\r\n                    {/* YouTube badge */}\r\n                    <div\r\n                      className={`flex items-center justify-center gap-1 mt-2 ${\r\n                        isFullscreen ? \"mt-4\" : \"\"\r\n                      }`}\r\n                    >\r\n                      <Youtube className=\"w-4 h-4 text-red-400\" />\r\n                      <span className=\"text-xs text-white/70\">YouTube</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Progress Bar */}\r\n              <div className=\"mb-6 px-2\">\r\n                <div className=\"flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-2\">\r\n                  <span>{formatTime(currentTime)}</span>\r\n                  <span>{formatTime(currentVideo.duration)}</span>\r\n                </div>\r\n                <input\r\n                  type=\"range\"\r\n                  min=\"0\"\r\n                  max=\"1\"\r\n                  step=\"0.001\"\r\n                  value={\r\n                    currentVideo.duration > 0\r\n                      ? currentTime / currentVideo.duration\r\n                      : 0\r\n                  }\r\n                  onChange={handleSeek}\r\n                  className=\"w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full appearance-none cursor-pointer\"\r\n                  style={{\r\n                    background: `linear-gradient(to right, #8B5CF6 0%, #3B82F6 ${\r\n                      (currentTime / Math.max(1, currentVideo.duration)) * 100\r\n                    }%, #E5E7EB ${\r\n                      (currentTime / Math.max(1, currentVideo.duration)) * 100\r\n                    }%, #E5E7EB 100%)`,\r\n                  }}\r\n                />\r\n              </div>\r\n\r\n              {/* Main Controls */}\r\n              <div className=\"flex items-center justify-center space-x-6 mb-6\">\r\n                <button\r\n                  onClick={toggleShuffle}\r\n                  className={`p-3 rounded-full transition-all ${\r\n                    isShuffled\r\n                      ? \"bg-purple-600 text-white shadow-lg\"\r\n                      : \"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600\"\r\n                  }`}\r\n                  title={\r\n                    isShuffled\r\n                      ? \"Desativar modo aleatório\"\r\n                      : \"Ativar modo aleatório\"\r\n                  }\r\n                >\r\n                  <Shuffle className=\"w-5 h-5\" />\r\n                </button>\r\n\r\n                <button\r\n                  onClick={handlePreviousSong}\r\n                  className=\"p-3 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-full transition-colors\"\r\n                  title=\"Música anterior\"\r\n                >\r\n                  <SkipBack className=\"w-6 h-6\" />\r\n                </button>\r\n\r\n                <button\r\n                  onClick={togglePlay}\r\n                  className=\"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-full p-4 transition-all transform hover:scale-105 shadow-lg\"\r\n                  title={isPlaying ? \"Pausar\" : \"Reproduzir\"}\r\n                >\r\n                  {isPlaying ? (\r\n                    <Pause className=\"w-8 h-8\" />\r\n                  ) : (\r\n                    <Play className=\"w-8 h-8 ml-1\" />\r\n                  )}\r\n                </button>\r\n\r\n                <button\r\n                  onClick={handleNextSong}\r\n                  className=\"p-3 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-full transition-colors\"\r\n                  title=\"Próxima música\"\r\n                >\r\n                  <SkipForward className=\"w-6 h-6\" />\r\n                </button>\r\n\r\n                <button\r\n                  onClick={toggleRepeat}\r\n                  className={`p-3 rounded-full transition-all relative ${\r\n                    repeatMode !== \"none\"\r\n                      ? \"bg-purple-600 text-white shadow-lg\"\r\n                      : \"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600\"\r\n                  }`}\r\n                  title={\r\n                    repeatMode === \"none\"\r\n                      ? \"Ativar repetição\"\r\n                      : repeatMode === \"one\"\r\n                      ? \"Repetir todas as músicas\"\r\n                      : \"Desativar repetição\"\r\n                  }\r\n                >\r\n                  <Repeat className=\"w-5 h-5\" />\r\n                  {repeatMode === \"one\" && (\r\n                    <span className=\"absolute -top-1 -right-1 w-4 h-4 bg-purple-400 rounded-full text-xs flex items-center justify-center text-white\">\r\n                      1\r\n                    </span>\r\n                  )}\r\n                </button>\r\n              </div>\r\n\r\n              {/* Secondary Controls */}\r\n              <div className=\"flex items-center justify-between px-2 mb-6\">\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <button\r\n                    onClick={toggleLike}\r\n                    className={`p-2 rounded-full transition-all ${\r\n                      isLiked\r\n                        ? \"text-red-500 bg-red-50 dark:bg-red-900/20\"\r\n                        : \"text-gray-400 hover:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                    }`}\r\n                    title={\r\n                      isLiked\r\n                        ? \"Remover dos favoritos\"\r\n                        : \"Adicionar aos favoritos\"\r\n                    }\r\n                  >\r\n                    <Heart\r\n                      className={`w-5 h-5 ${isLiked ? \"fill-current\" : \"\"}`}\r\n                    />\r\n                  </button>\r\n                  <button\r\n                    className=\"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors\"\r\n                    title=\"Compartilhar\"\r\n                  >\r\n                    <Share2 className=\"w-5 h-5\" />\r\n                  </button>\r\n                  <a\r\n                    href={`https://www.youtube.com/watch?v=${currentVideo.youtubeVideoId}`}\r\n                    target=\"_blank\"\r\n                    rel=\"noopener noreferrer\"\r\n                    className=\"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors\"\r\n                    title=\"Abrir no YouTube\"\r\n                  >\r\n                    <ExternalLink className=\"w-5 h-5\" />\r\n                  </a>\r\n                </div>\r\n\r\n                {/* Volume Control */}\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <button\r\n                    onClick={toggleMute}\r\n                    className=\"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors\"\r\n                    title={isMuted ? \"Ativar som\" : \"Desativar som\"}\r\n                  >\r\n                    {isMuted || volume === 0 ? (\r\n                      <VolumeX className=\"w-5 h-5\" />\r\n                    ) : (\r\n                      <Volume2 className=\"w-5 h-5\" />\r\n                    )}\r\n                  </button>\r\n                  <input\r\n                    type=\"range\"\r\n                    min=\"0\"\r\n                    max=\"1\"\r\n                    step=\"0.01\"\r\n                    value={isMuted ? 0 : volume}\r\n                    onChange={handleVolumeChange}\r\n                    className=\"w-24 h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer\"\r\n                    style={{\r\n                      background: `linear-gradient(to right, #8B5CF6 0%, #3B82F6 ${\r\n                        (isMuted ? 0 : volume) * 100\r\n                      }%, #E5E7EB ${\r\n                        (isMuted ? 0 : volume) * 100\r\n                      }%, #E5E7EB 100%)`,\r\n                    }}\r\n                    title={`Volume: ${Math.round(\r\n                      (isMuted ? 0 : volume) * 100\r\n                    )}%`}\r\n                  />\r\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400 w-8 text-right\">\r\n                    {Math.round((isMuted ? 0 : volume) * 100)}%\r\n                  </span>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Search Bar - Always visible in small screen, only when not showing playlist in larger screens */}\r\n              <div\r\n                className={`mt-6 p-2 ${\r\n                  showPlaylist && !isFullscreen ? \"hidden md:block\" : \"\"\r\n                }`}\r\n              >\r\n                <div className=\"relative\">\r\n                  <input\r\n                    type=\"text\"\r\n                    placeholder=\"Buscar playlist...\"\r\n                    value={searchQuery}\r\n                    onChange={(e) => setSearchQuery(e.target.value)}\r\n                    onKeyDown={(e) => {\r\n                      if (e.key === \"Enter\") {\r\n                        setIsSearching(true);\r\n                        searchLocalPlaylists(searchQuery);\r\n                      }\r\n                    }}\r\n                    className=\"w-full px-4 py-2 pl-10 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                  />\r\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" />\r\n                  <button\r\n                    onClick={() => {\r\n                      setIsSearching(true);\r\n                      searchLocalPlaylists(searchQuery);\r\n                    }}\r\n                    disabled={isSearching || !searchQuery.trim()}\r\n                    className=\"absolute right-2 top-1/2 transform -translate-y-1/2 px-2 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                  >\r\n                    {isSearching ? (\r\n                      <RefreshCw className=\"w-4 h-4 animate-spin\" />\r\n                    ) : (\r\n                      \"Buscar\"\r\n                    )}\r\n                  </button>\r\n                </div>\r\n\r\n                {error && (\r\n                  <div className=\"mt-2 text-sm text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 p-2 rounded\">\r\n                    <div className=\"flex items-center gap-1\">\r\n                      <AlertCircle className=\"w-4 h-4\" />\r\n                      <span>{error}</span>\r\n                    </div>\r\n                    <button\r\n                      onClick={loadRealPlaylists}\r\n                      className=\"mt-1 text-blue-600 dark:text-blue-400 hover:underline\"\r\n                    >\r\n                      Tentar novamente\r\n                    </button>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Painel da playlist */}\r\n          <AnimatePresence>\r\n            {showPlaylist && (\r\n              <motion.div\r\n                initial={{ width: 0, opacity: 0 }}\r\n                animate={{ width: \"33.333333%\", opacity: 1 }}\r\n                exit={{ width: 0, opacity: 0 }}\r\n                transition={{ duration: 0.3 }}\r\n                className=\"border-l border-gray-200 dark:border-gray-700 h-full overflow-hidden\"\r\n              >\r\n                <div className=\"p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between\">\r\n                  <h3 className=\"font-medium text-gray-900 dark:text-white\">\r\n                    Playlists\r\n                  </h3>\r\n                  <button\r\n                    onClick={() => setShowPlaylist(false)}\r\n                    className=\"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\r\n                  >\r\n                    <X className=\"w-5 h-5\" />\r\n                  </button>\r\n                </div>\r\n\r\n                <div\r\n                  className=\"h-full overflow-y-auto\"\r\n                  style={{ maxHeight: \"calc(100vh - 250px)\" }}\r\n                >\r\n                  {/* Lista de playlists */}\r\n                  <div className=\"space-y-2 p-2\">\r\n                    {playlists.map((playlist) => (\r\n                      <div\r\n                        key={playlist.id}\r\n                        onClick={() => handleSelectPlaylist(playlist)}\r\n                        className={`flex items-center p-2 rounded-lg cursor-pointer ${\r\n                          currentPlaylist?.id === playlist.id\r\n                            ? \"bg-purple-100 dark:bg-purple-900/30\"\r\n                            : \"hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        }`}\r\n                      >\r\n                        <div className=\"w-12 h-12 rounded overflow-hidden mr-3 flex-shrink-0\">\r\n                          <img\r\n                            src={playlist.thumbnail}\r\n                            alt={playlist.name}\r\n                            className=\"w-full h-full object-cover\"\r\n                            onError={(e) => {\r\n                              e.currentTarget.src =\r\n                                \"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='300'%3E%3Crect width='100%25' height='100%25' fill='%238B5CF6'/%3E%3Ctext x='50%25' y='50%25' dominant-baseline='middle' text-anchor='middle' fill='white' font-size='48'%3E♪%3C/text%3E%3C/svg%3E\";\r\n                            }}\r\n                          />\r\n                        </div>\r\n                        <div className=\"flex-1 min-w-0\">\r\n                          <h4\r\n                            className={`text-sm font-medium truncate ${\r\n                              currentPlaylist?.id === playlist.id\r\n                                ? \"text-purple-800 dark:text-purple-300\"\r\n                                : \"text-gray-900 dark:text-white\"\r\n                            }`}\r\n                          >\r\n                            {playlist.name}\r\n                          </h4>\r\n                          <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\r\n                            {playlist.tracks.length} músicas\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                    ))}\r\n\r\n                    {playlists.length === 0 && !loading && (\r\n                      <div className=\"text-center py-8 text-gray-500 dark:text-gray-400\">\r\n                        <Music className=\"w-12 h-12 mx-auto mb-2 opacity-50\" />\r\n                        <p>Nenhuma playlist encontrada</p>\r\n                        <button\r\n                          onClick={loadRealPlaylists}\r\n                          className=\"mt-2 text-blue-600 dark:text-blue-400 hover:underline text-sm\"\r\n                        >\r\n                          Recarregar playlists\r\n                        </button>\r\n                      </div>\r\n                    )}\r\n\r\n                    {loading && (\r\n                      <div className=\"text-center py-8\">\r\n                        <RefreshCw className=\"w-8 h-8 animate-spin mx-auto mb-2 text-purple-600\" />\r\n                        <p className=\"text-gray-600 dark:text-gray-400\">\r\n                          Buscando playlists...\r\n                        </p>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n\r\n                  {/* Músicas da playlist atual */}\r\n                  {currentPlaylist && (\r\n                    <div className=\"mt-4 p-2\">\r\n                      <h3 className=\"font-medium text-gray-900 dark:text-white mb-2 px-2\">\r\n                        Músicas de {currentPlaylist.name}\r\n                      </h3>\r\n                      <div className=\"space-y-1\">\r\n                        {currentPlaylist.tracks.map((video, index) => (\r\n                          <div\r\n                            key={video.youtubeVideoId}\r\n                            onClick={() => handleSelectSong(index)}\r\n                            className={`flex items-center p-2 rounded-lg cursor-pointer ${\r\n                              currentVideoIndex === index\r\n                                ? \"bg-blue-100 dark:bg-blue-900/30\"\r\n                                : \"hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                            }`}\r\n                          >\r\n                            <div className=\"mr-3 text-xs text-gray-500 dark:text-gray-400 w-5 text-center\">\r\n                              {index + 1}\r\n                            </div>\r\n                            <div className=\"w-8 h-8 rounded bg-gray-600 mr-2 flex-shrink-0 flex items-center justify-center\">\r\n                              <span className=\"text-xs\">🎵</span>\r\n                            </div>\r\n                            <div className=\"flex-1 min-w-0\">\r\n                              <h4\r\n                                className={`text-xs font-medium truncate ${\r\n                                  currentVideoIndex === index\r\n                                    ? \"text-blue-800 dark:text-blue-300\"\r\n                                    : \"text-gray-900 dark:text-white\"\r\n                                }`}\r\n                              >\r\n                                {video.title}\r\n                              </h4>\r\n                              <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\r\n                                {video.artist}\r\n                              </p>\r\n                            </div>\r\n                            <div className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                              {formatTime(video.duration)}\r\n                            </div>\r\n                          </div>\r\n                        ))}\r\n\r\n                        {/* Botão para carregar mais tracks */}\r\n                        {currentPlaylist.tracks.length <\r\n                          (currentPlaylist.videoCount || 50) && (\r\n                          <div className=\"text-center py-4\">\r\n                            <button\r\n                              onClick={() =>\r\n                                loadMoreTracksForPlaylist(\r\n                                  currentPlaylist.id,\r\n                                  currentPlaylist.tracks.length\r\n                                )\r\n                              }\r\n                              className=\"px-4 py-2 text-sm bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2 mx-auto\"\r\n                            >\r\n                              <Plus className=\"w-4 h-4\" />\r\n                              Carregar mais músicas (\r\n                              {currentPlaylist.videoCount -\r\n                                currentPlaylist.tracks.length}{\" \"}\r\n                              restantes)\r\n                            </button>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </motion.div>\r\n            )}\r\n          </AnimatePresence>\r\n        </div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n};\r\n\r\nexport default MusicPlayer;\r\n"], "names": ["ENABLE_MOCKS", "MusicPlayer", "restaurantId", "useRestaurantContext", "isPlaying", "setIsPlaying", "useState", "volume", "setVolume", "isMuted", "setIsMuted", "currentTime", "setCurrentTime", "isVisible", "setIsVisible", "isShuffled", "setIsShuffled", "repeatMode", "setRepeatMode", "isLiked", "setIsLiked", "isFullscreen", "setIsFullscreen", "showPlaylist", "setShowPlaylist", "playlists", "setPlaylists", "currentPlaylist", "setCurrentPlaylist", "currentVideoIndex", "setCurrentVideoIndex", "searchQuery", "setSearch<PERSON>uery", "isSearching", "setIsSearching", "loading", "setLoading", "error", "setError", "player", "setPlayer", "youtubePlayerRef", "useRef", "currentVideo", "youtubeOpts", "searchLocalPlaylists", "useCallback", "query", "filtered", "playlist", "track", "toast", "err", "loadRealPlaylists", "url", "buildApiUrl", "response", "foundPlaylists", "index", "activePlaylistsBasic", "p", "formattedPlaylists", "playlistBasic", "playlistResponse", "playlistData", "formattedPlaylist", "loadExamplePlaylists", "playlistId", "loadMoreTracksForPlaylist", "offset", "limit", "prev", "newTracks", "useEffect", "examplePlaylists", "onPlayerReady", "event", "onPlayerStateChange", "handleNextSong", "handlePostMessage", "interval", "togglePlay", "toggleMute", "handleVolumeChange", "e", "newVolume", "formatTime", "time", "minutes", "seconds", "handleSeek", "seekTime", "toggleShuffle", "toggleRepeat", "modes", "currentIndex", "nextMode", "messages", "toggleLike", "toggleFullscreen", "handlePreviousSong", "randomIndex", "nextIndex", "handleSelectPlaylist", "handleSelectSong", "jsx", "motion", "jsxs", "Music", "List", "Minimize2", "Maximize2", "YouTube", "Youtube", "Shuffle", "SkipBack", "Pause", "Play", "SkipForward", "Repeat", "Heart", "Share2", "ExternalLink", "VolumeX", "Volume2", "Search", "RefreshCw", "AlertCircle", "AnimatePresence", "X", "video", "Plus"], "mappings": "6XAyDA,MAAMA,GAA+B,CAAA,EAAA,oBAAsB,OAGrDC,GAAwB,IAAM,CAC5B,KAAA,CAAE,aAAAC,GAAiBC,KAGnB,CAACC,EAAWC,CAAY,EAAIC,WAAS,EAAK,EAC1C,CAACC,EAAQC,EAAS,EAAIF,WAAS,EAAG,EAClC,CAACG,EAASC,CAAU,EAAIJ,WAAS,EAAK,EACtC,CAACK,EAAaC,CAAc,EAAIN,WAAS,CAAC,EAC1C,CAACO,GAAWC,CAAY,EAAIR,WAAS,EAAI,EACzC,CAACS,EAAYC,EAAa,EAAIV,WAAS,EAAK,EAC5C,CAACW,EAAYC,EAAa,EAAIZ,WAAiC,MAAM,EACrE,CAACa,EAASC,EAAU,EAAId,WAAS,EAAK,EACtC,CAACe,EAAcC,EAAe,EAAIhB,WAAS,EAAK,EAChD,CAACiB,EAAcC,CAAe,EAAIlB,WAAS,EAAK,EAGhD,CAACmB,EAAWC,CAAY,EAAIpB,EAAA,SAAyB,CAAE,CAAA,EACvD,CAACqB,EAAiBC,CAAkB,EAAItB,EAAA,SAC5C,IAAA,EAEI,CAACuB,EAAmBC,CAAoB,EAAIxB,WAAS,CAAC,EACtD,CAACyB,EAAaC,EAAc,EAAI1B,WAAS,EAAE,EAC3C,CAAC2B,EAAaC,CAAc,EAAI5B,WAAS,EAAK,EAC9C,CAAC6B,EAASC,CAAU,EAAI9B,WAAS,EAAK,EACtC,CAAC+B,EAAOC,CAAQ,EAAIhC,WAAwB,IAAI,EAChD,CAACiC,EAAQC,EAAS,EAAIlC,WAAc,IAAI,EAGxCmC,GAAmBC,SAAY,IAAI,EAGnCC,GAAehB,GAAA,YAAAA,EAAiB,OAAOE,KAAsB,CACjE,eAAgB,GAChB,MAAO,6BACP,OAAQ,yBACR,aAAc,GACd,SAAU,EACV,QAAS,GACT,SAAU,CAAA,EAINe,GAAc,CAClB,OAAQ,IACR,MAAO,IACP,WAAY,CACV,SAAUxC,EAAY,EAAI,EAC1B,SAAU,EACV,UAAW,EACX,YAAa,EACb,eAAgB,EAChB,eAAgB,EAChB,IAAK,EACL,SAAU,EACV,GAAI,EACJ,eAAgB,EAChB,YAAa,EAEjB,OAAQ,OAAO,SAAS,OACxB,KAAM,yBACJ,CAAA,EAIIyC,EAAuBC,EAAA,YAC3B,MAAOC,GAAkB,CACnB,GAAA,CAACA,EAAM,OAAQ,CAEjBb,EAAe,EAAK,EACpB,MACF,CAEAA,EAAe,EAAI,EAEf,GAAA,CAEF,MAAMc,EAAWvB,EAAU,OACxBwB,GACCA,EAAS,KAAK,YAAc,EAAA,SAASF,EAAM,aAAa,GACxDE,EAAS,YAAY,YAAc,EAAA,SAASF,EAAM,aAAa,GAC/DE,EAAS,OAAO,KACbC,GACCA,EAAM,MAAM,YAAY,EAAE,SAASH,EAAM,YAAa,CAAA,GACtDG,EAAM,OAAO,YAAA,EAAc,SAASH,EAAM,aAAa,CAC3D,CAAA,EAGJrB,EAAasB,CAAQ,EAEjBA,EAAS,OAAS,EACdG,EAAA,QACJ,eAAeH,EAAS,MAAM,qBAAqBD,CAAK,GAAA,EAG1DI,EAAM,oCAAoCJ,CAAK,IAAK,CAAE,KAAM,KAAM,QAE7DK,EAAU,CACT,QAAA,MAAM,6BAA8BA,CAAG,EAC/CD,EAAM,MAAM,2BAA2B,CAAA,QACvC,CACAjB,EAAe,EAAK,CACtB,CACF,EACA,CAACT,CAAS,CAAA,EAeN4B,EAAoBP,EAAAA,YAAY,SAAY,CAChD,GAAI,CAAC5C,EAAc,CACjBiD,EAAM,MAAM,kCAAkC,EAC9C,MACF,CAEAf,EAAW,EAAI,EACfE,EAAS,IAAI,EAET,GAAA,CACM,QAAA,IAAI,6CAA6CpC,CAAY,EAAE,EAGvE,MAAMoD,EAAMC,EAAY,yBAAyBrD,CAAY,EAAE,EACzDsD,EAAW,MAAM,MAAMF,EAAK,CAChC,OAAQ,MACR,QAAS,CACP,eAAgB,kBAClB,CAAA,CACD,EAEG,GAAA,CAACE,EAAS,GACZ,MAAM,IAAI,MAAM,+BAA+BA,EAAS,MAAM,EAAE,EAI5D,MAAAC,GADO,MAAMD,EAAS,QACA,WAAa,GAEzC,QAAQ,IAAI,iBAAiBC,EAAe,MAAM,YAAY,EACtD,QAAA,IAAI,0BAA2BA,CAAc,EAGtCA,EAAA,QAAQ,CAACR,EAAeS,IAAkB,CACvD,QAAQ,IAAI,eAAeA,EAAQ,CAAC,IAAK,CACvC,GAAIT,EAAS,GACb,KAAMA,EAAS,KACf,SAAUA,EAAS,SACnB,WAAYA,EAAS,UAAA,CACtB,CAAA,CACF,EAGD,MAAMU,EAAuBF,EAAe,OAAQG,GAC7CA,EAAE,SAMA,IALG,QAAA,IACN,eAAeA,EAAE,IAAI,uCAAuCA,EAAE,QAAQ,GAAA,EAEjE,GAGV,EAEO,QAAA,IACN,6BAA6BD,EAAqB,MAAM,sBAAA,EAI1D,MAAME,EAAqC,CAAA,EAE3C,UAAWC,KAAiBH,EACtB,GAAA,CACM,QAAA,IACN,qCAAqCG,EAAc,IAAI,KAAKA,EAAc,EAAE,GAAA,EAI9E,MAAMC,EAAmB,MAAM,MAC7BR,EAAY,cAAcO,EAAc,EAAE,oBAAoB,EAC9D,CACE,OAAQ,MACR,QAAS,CACP,eAAgB,kBAClB,CACF,CAAA,EAGE,GAAA,CAACC,EAAiB,GAAI,CAChB,QAAA,IACN,0CAA0CD,EAAc,IAAI,KAAKC,EAAiB,MAAM,EAAA,EAE1F,QACF,CAEM,MAAAC,EAAe,MAAMD,EAAiB,OAE5C,GACE,CAACC,EAAa,QACd,CAAC,MAAM,QAAQA,EAAa,MAAM,GAClCA,EAAa,OAAO,SAAW,EAC/B,CACQ,QAAA,IACN,eAAeF,EAAc,IAAI,yBAAA,EAEnC,QACF,CAEQ,QAAA,IACN,cAAcA,EAAc,IAAI,kBAAkBE,EAAa,OAAO,MAAM,wBAAA,EAG9E,MAAMC,GAAkC,CACtC,GAAID,EAAa,GACjB,KAAMA,EAAa,KACnB,YAAaA,EAAa,aAAe,GACzC,UAAWA,EAAa,WAAa,GACrC,OAAQA,EAAa,OAAO,IAAKd,IAAgB,CAC/C,eAAgBA,EAAM,eACtB,MAAOA,EAAM,MACb,OAAQA,EAAM,OACd,SAAUA,EAAM,SAChB,aAAcA,EAAM,cAAgB,GACpC,QAASA,EAAM,QACf,SAAUA,EAAM,QAAA,EAChB,EACF,cAAec,EAAa,eAAiB,EAC7C,WAAYA,EAAa,YAAcA,EAAa,OAAO,OAC3D,SAAUA,EAAa,QAAA,EAGzBH,EAAmB,KAAKI,EAAiB,QAClCb,EAAK,CACJ,QAAA,MACN,yCAAyCU,EAAc,IAAI,IAC3DV,CAAA,CAEJ,CAGF1B,EAAamC,CAAkB,EAE3BA,EAAmB,OAAS,GACzBlC,IACgBC,EAAAiC,EAAmB,CAAC,CAAC,EACxC/B,EAAqB,CAAC,GAExBqB,EAAM,QAAQ,cAAcU,EAAmB,MAAM,YAAY,GAE7C,GAAgB,oBAAsB,QAEhD,QAAA,IACN,6FAAA,EAEmBK,IACrBf,EACE,4EACA,CAAE,KAAM,KAAM,SAAU,GAAK,CAAA,IAG/B,QAAQ,KAAK,0FAA0F,EACvGA,EAAM,0CAA2C,CAAE,KAAM,IAAM,CAAA,SAG5DC,EAAU,CACT,QAAA,MAAM,gCAAiCA,CAAG,EACzCd,EAAAc,EAAI,SAAW,4BAA4B,EACpDD,EAAM,MAAM,2CAA2C,CAAA,QACvD,CACAf,EAAW,EAAK,CAClB,CAAA,EACC,CAAClC,CAAY,CAAC,EAGU4C,EAAY,YAAA,MAAOqB,GAAuB,CACnE/B,EAAW,EAAI,EAEX,GAAA,CACM,QAAA,IAAI,qCAAqC+B,CAAU,EAAE,EAE7D,MAAMb,EAAMC,EAAY,cAAcY,CAAU,EAAE,EAC5CX,EAAW,MAAM,MAAMF,EAAK,CAChC,OAAQ,MACR,QAAS,CACP,eAAgB,kBAClB,CAAA,CACD,EAEG,GAAA,CAACE,EAAS,GACZ,MAAM,IAAI,MAAM,8BAA8BA,EAAS,MAAM,EAAE,EAG3D,MAAAQ,EAAe,MAAMR,EAAS,OAEpC,GAAIQ,EAAa,QAAUA,EAAa,OAAO,OAAS,EAAG,CACzD,MAAMC,EAAkC,CACtC,GAAID,EAAa,GACjB,KAAMA,EAAa,KACnB,YAAaA,EAAa,aAAe,GACzC,UAAWA,EAAa,WAAa,GACrC,OAAQA,EAAa,OAAO,IAAKd,IAAgB,CAC/C,eAAgBA,EAAM,eACtB,MAAOA,EAAM,MACb,OAAQA,EAAM,OACd,SAAUA,EAAM,SAChB,aAAcA,EAAM,cAAgB,GACpC,QAASA,EAAM,QACf,SAAUA,EAAM,QAAA,EAChB,EACF,cAAec,EAAa,eAAiB,EAC7C,WAAYA,EAAa,YAAcA,EAAa,OAAO,OAC3D,SAAUA,EAAa,QAAA,EAGzBpC,EAAmBqC,CAAiB,EACpCnC,EAAqB,CAAC,EAEtBqB,EAAM,QAAQ,aAAaa,EAAa,IAAI,aAAa,CAAA,MAEzDb,EAAM,MAAM,6BAA6B,QAEpCC,EAAU,CACT,QAAA,MAAM,+BAAgCA,CAAG,EACjDD,EAAM,MAAM,2BAA2B,CAAA,QACvC,CACAf,EAAW,EAAK,CAClB,CACF,EAAG,EAAE,EAGL,MAAMgC,EAA4BtB,EAAA,YAChC,MAAOqB,EAAoBE,EAAiB,GAAIC,EAAgB,KAAO,CACjE,GAAA,CACM,QAAA,IACN,0CAA0CH,CAAU,aAAaE,CAAM,GAAA,EAGzE,MAAMb,EAAW,MAAM,MACrBD,EACE,cAAcY,CAAU,UAAUG,CAAK,WAAWD,CAAM,EAC1D,EACA,CACE,OAAQ,MACR,QAAS,CACP,eAAgB,kBAClB,CACF,CAAA,EAGE,GAAA,CAACb,EAAS,GACZ,MAAM,IAAI,MAAM,iCAAiCA,EAAS,MAAM,EAAE,EAG9D,MAAAQ,EAAe,MAAMR,EAAS,OAEhCQ,EAAa,QAAUA,EAAa,OAAO,OAAS,IAEtDpC,EAAoB2C,GAAS,CACvB,GAAA,CAACA,GAAQA,EAAK,KAAOJ,EAAmB,OAAAI,EAE5C,MAAMC,EAAYR,EAAa,OAAO,IAAKd,IAAgB,CACzD,eAAgBA,EAAM,eACtB,MAAOA,EAAM,MACb,OAAQA,EAAM,OACd,SAAUA,EAAM,SAChB,aAAcA,EAAM,cAAgB,GACpC,QAASA,EAAM,QACf,SAAUA,EAAM,QAChB,EAAA,EAEK,MAAA,CACL,GAAGqB,EACH,OAAQ,CAAC,GAAGA,EAAK,OAAQ,GAAGC,CAAS,CAAA,CACvC,CACD,EAGD9C,EAAc6C,GACZA,EAAK,IAAKtB,GACRA,EAAS,KAAOkB,EACZ,CACE,GAAGlB,EACH,OAAQ,CACN,GAAGA,EAAS,OACZ,GAAGe,EAAa,OAAO,IAAKd,IAAgB,CAC1C,eAAgBA,EAAM,eACtB,MAAOA,EAAM,MACb,OAAQA,EAAM,OACd,SAAUA,EAAM,SAChB,aAAcA,EAAM,cAAgB,GACpC,QAASA,EAAM,QACf,SAAUA,EAAM,QAAA,EAChB,CACJ,CAAA,EAEFD,CACN,CAAA,EAGM,QAAA,IACN,qBAAqBe,EAAa,OAAO,MAAM,SAAA,EAE3Cb,EAAA,QACJ,mBAAmBa,EAAa,OAAO,MAAM,UAAA,SAG1CZ,EAAU,CACT,QAAA,MAAM,kCAAmCA,CAAG,EACpDD,EAAM,MAAM,+BAA+B,CAC7C,CACF,EACA,CAAC,CAAA,EAIHsB,EAAAA,UAAU,IAAM,CACN,QAAA,IAAI,kCAAkCvE,CAAY,EAAE,EACxDA,GACM,QAAA,IACN,4DAA4DA,CAAY,EAAA,EAExDmD,KACTrD,IACT,QAAQ,IAAI,+DAA+D,EACtDkE,KAErB,QAAQ,IAAI,kEAAkE,CAChF,EACC,CAAChE,EAAcmD,CAAiB,CAAC,EAG9B,MAAAa,EAAuBpB,EAAAA,YAAY,IAAM,CAC7C,MAAM4B,EAAmC,CACvC,CACE,GAAI,qBACJ,KAAM,2BACN,YAAa,wCACb,UACE,sRACF,OAAQ,CACN,CACE,eAAgB,cAChB,MAAO,0BACP,OAAQ,cACR,SAAU,IACV,aACE,uDACF,QAAS,IAAI,KAAK,EAAE,YAAY,EAChC,SAAU,CACZ,EACA,CACE,eAAgB,cAChB,MAAO,gBACP,OAAQ,MACR,SAAU,IACV,aACE,uDACF,QAAS,IAAI,KAAK,EAAE,YAAY,EAChC,SAAU,CACZ,EACA,CACE,eAAgB,cAChB,MAAO,YACP,OAAQ,8BACR,SAAU,IACV,aACE,uDACF,QAAS,IAAI,KAAK,EAAE,YAAY,EAChC,SAAU,CACZ,CACF,EACA,cAAe,IACf,WAAY,EACZ,SAAU,EACZ,EACA,CACE,GAAI,qBACJ,KAAM,0BACN,YAAa,wCACb,UACE,sRACF,OAAQ,CACN,CACE,eAAgB,cAChB,MAAO,oBACP,OAAQ,QACR,SAAU,IACV,aACE,uDACF,QAAS,IAAI,KAAK,EAAE,YAAY,EAChC,SAAU,CACZ,EACA,CACE,eAAgB,cAChB,MAAO,0BACP,OAAQ,UACR,SAAU,IACV,aACE,uDACF,QAAS,IAAI,KAAK,EAAE,YAAY,EAChC,SAAU,CACZ,CACF,EACA,cAAe,IACf,WAAY,EACZ,SAAU,EACZ,CAAA,EAGFhD,EAAagD,CAAgB,EAExB/C,IACgBC,EAAA8C,EAAiB,CAAC,CAAC,EACtC5C,EAAqB,CAAC,GAGhB,QAAA,IAAI,qCAAsC4C,EAAiB,MAAM,CAAA,EACxE,CAAC/C,CAAe,CAAC,EAGdgD,GAAiBC,GAAe,CACpCpC,GAAUoC,EAAM,MAAM,EAEhBA,EAAA,OAAO,UAAUrE,EAAS,GAAG,CAAA,EAG/BsE,GAAuBD,GAAe,CAE1C,OAAQA,EAAM,KAAM,CAClB,IAAK,GACC3D,IAAe,MACjB2D,EAAM,OAAO,YAEb3D,IAAe,OACfY,IAAqBF,GAAA,YAAAA,EAAiB,OAAO,SAAU,GAAK,EAE7CmD,KAEfzE,EAAa,EAAK,EAClBO,EAAe,CAAC,GAElB,MACF,IAAK,GACHP,EAAa,EAAI,EACjB,MACF,IAAK,GACHA,EAAa,EAAK,EAClB,KACJ,CAAA,EAIFoE,EAAAA,UAAU,IAAM,CACR,MAAAM,EAAqBH,GAAwB,CAG/CA,EAAM,SAAW,2BACjBA,EAAM,MASR,EAGK,cAAA,iBAAiB,UAAWG,CAAiB,EAC7C,IAAM,OAAO,oBAAoB,UAAWA,CAAiB,CACtE,EAAG,CAAE,CAAA,EAGLN,EAAAA,UAAU,IAAM,CACd,GAAIrE,GAAamC,EAAQ,CACjB,MAAAyC,EAAW,YAAY,IAAM,CAC3BrE,MAAAA,EAAc4B,EAAO,iBAC3B3B,EAAeD,CAAW,GACzB,GAAI,EACA,MAAA,IAAM,cAAcqE,CAAQ,CACrC,CAAA,EACC,CAAC5E,EAAWmC,CAAM,CAAC,EAGtB,MAAM0C,GAAa,IAAM,CACnB1C,IACEnC,EACFmC,EAAO,WAAW,EAElBA,EAAO,UAAU,EAEbY,EAAA,QAAQ/C,EAAY,UAAY,cAAc,EACtD,EAYI8E,GAAa,IAAM,CACnB3C,IACE9B,GACF8B,EAAO,OAAO,EACPA,EAAA,UAAUhC,EAAS,GAAG,GAE7BgC,EAAO,KAAK,EAEd7B,EAAW,CAACD,CAAO,EACrB,EAGI0E,GAAsBC,GAA2C,CACrE,MAAMC,EAAY,WAAWD,EAAE,OAAO,KAAK,EAC3C5E,GAAU6E,CAAS,EAEf9C,IACKA,EAAA,UAAU8C,EAAY,GAAG,EAC5BA,IAAc,GAChB9C,EAAO,KAAK,EACZ7B,EAAW,EAAI,GACND,IACT8B,EAAO,OAAO,EACd7B,EAAW,EAAK,GAEpB,EAGI4E,EAAcC,GAAiB,CACnC,MAAMC,EAAU,KAAK,MAAMD,EAAO,EAAE,EAC9BE,EAAU,KAAK,MAAMF,EAAO,EAAE,EAC7B,MAAA,GAAGC,CAAO,IAAIC,EAAQ,WAAW,SAAS,EAAG,GAAG,CAAC,EAAA,EAGpDC,GAAcN,GAA2C,CACzD,GAAA7C,GAAUI,EAAa,SAAW,EAAG,CACvC,MAAMgD,EAAW,WAAWP,EAAE,OAAO,KAAK,EAAIzC,EAAa,SAC3DJ,EAAO,OAAOoD,CAAQ,EACtB/E,EAAe+E,CAAQ,CACzB,CAAA,EAqBIC,GAAgB,IAAM,CAC1B5E,GAAc,CAACD,CAAU,EACnBoC,EAAA,QAAQpC,EAAa,uBAAyB,mBAAmB,CAAA,EAGnE8E,GAAe,IAAM,CACzB,MAAMC,EAAQ,CAAC,OAAQ,MAAO,KAAK,EAC7BC,EAAeD,EAAM,QAAQ7E,CAAU,EACvC+E,EAAWF,GAAOC,EAAe,GAAKD,EAAM,MAAM,EACxD5E,GAAc8E,CAAQ,EAEtB,MAAMC,EAAW,CACf,KAAM,uBACN,IAAK,qBACL,IAAK,eAAA,EAED9C,EAAA,QAAQ8C,EAASD,CAAQ,CAAC,CAAA,EAG5BE,GAAa,IAAM,CACvB9E,GAAW,CAACD,CAAO,EACbgC,EAAA,QACJhC,EAAU,yBAA2B,0BAAA,CACvC,EAGIgF,GAAmB,IAAM,CAC7B7E,GAAgB,CAACD,CAAY,CAAA,EAIzB+E,GAAqB,IAAM,CAC/B,GAAKzE,EAEL,IAAIZ,EAAY,CACd,MAAMsF,EAAc,KAAK,MACvB,KAAK,OAAA,EAAW1E,EAAgB,OAAO,MAAA,EAEzCG,EAAqBuE,CAAW,CAAA,MAEhCvE,EAAsByC,GACpBA,EAAO,EAAIA,EAAO,EAAI5C,EAAgB,OAAO,OAAS,CAAA,EAK1Df,EAAe,CAAC,EACZR,GAAamC,GAEf,WAAW,IAAMA,EAAO,UAAU,EAAG,GAAG,EAG1CY,EAAM,QAAQ,iBAAiB,EAAA,EAG3B2B,EAAiB,IAAM,CAC3B,GAAI,CAACnD,EAAiB,OAEtB,MAAM2E,EAAYvF,EACd,KAAK,MAAM,KAAK,SAAWY,EAAgB,OAAO,MAAM,EACxDE,EAAoBF,EAAgB,OAAO,OAAS,EACpDE,EAAoB,EACpB,EAIF,CAACd,GACDuF,GAAa3E,EAAgB,OAAO,OAAS,GAC7CA,EAAgB,OAAO,QAAUA,EAAgB,YAAc,MAE/D,QAAQ,IAAI,0DAA0D,EACtEyC,EACEzC,EAAgB,GAChBA,EAAgB,OAAO,MAAA,GAI3BG,EAAqBwE,CAAS,EAG9B1F,EAAe,CAAC,EACZR,GAAamC,GAEf,WAAW,IAAMA,EAAO,UAAU,EAAG,GAAG,EAG1CY,EAAM,QAAQ,gBAAgB,CAAA,EAI1BoD,GAAwBtD,GAA2B,CACvDrB,EAAmBqB,CAAQ,EAC3BnB,EAAqB,CAAC,EACtBlB,EAAe,CAAC,EAChBY,EAAgB,EAAK,EAGjBe,GAAUnC,GAEZ,WAAW,IAAMmC,EAAO,UAAU,EAAG,GAAG,EAG1CY,EAAM,QAAQ,aAAaF,EAAS,IAAI,eAAe,CAAA,EAInDuD,GAAoB9C,GAAkB,CAC1C5B,EAAqB4B,CAAK,EAC1B9C,EAAe,CAAC,EAGZ2B,GACEnC,GAEF,WAAW,IAAMmC,EAAO,UAAU,EAAG,GAAG,EAItCY,EAAA,QACJ,WAAWxB,GAAA,YAAAA,EAAiB,OAAO+B,GAAO,KAAK,eAAA,CACjD,EAIF,OAAK7C,GAkBH4F,EAAA,IAACC,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,UAAW,aACTrF,EAAe,gDAAkD,EACnE,GAEA,SAAAsF,EAAA,KAAC,MAAA,CACC,UAAW,8GACTtF,EAAe,uBAAyB,EAC1C,GAGA,SAAA,CAACsF,EAAAA,KAAA,MAAA,CAAI,UAAU,sFACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,2EACZ,SAAA,CAACF,EAAAA,IAAAG,EAAA,CAAM,UAAU,yBAA0B,CAAA,EAAE,kBAAA,EAE/C,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAAAF,EAAA,IAAC,SAAA,CACC,QAAS,IAAMjF,EAAgB,CAACD,CAAY,EAC5C,UAAU,4JACV,aAAW,mBACX,MAAM,mBAEN,SAAAkF,EAAAA,IAACI,GAAK,CAAA,UAAU,SAAU,CAAA,CAAA,CAC5B,EACAJ,EAAA,IAAC,SAAA,CACC,QAASN,GACT,UAAU,4JACV,aAAY9E,EAAe,qBAAuB,aAClD,MAAOA,EAAe,qBAAuB,aAE5C,SAAAA,QACEyF,GAAU,CAAA,UAAU,UAAU,EAE/BL,EAAA,IAACM,GAAU,CAAA,UAAU,SAAU,CAAA,CAAA,CAEnC,EACAN,EAAA,IAAC,SAAA,CACC,QAAS,IAAM3F,EAAa,EAAK,EACjC,UAAU,gJACV,aAAW,eACZ,SAAA,cAAA,CAED,CAAA,EACF,CAAA,EACF,SAGC,MAAI,CAAA,UAAW,QAAQO,EAAe,SAAW,EAAE,GAElD,SAAA,CAAAoF,EAAA,IAAC,MAAA,CACC,UAAW,GAAGlF,EAAe,QAAU,QAAQ,IAC7CF,EAAe,gBAAkB,EACnC,GAGA,gBAAC,MAAI,CAAA,UAAW,GAAGA,EAAe,uBAAyB,KAAK,GAE9D,SAAA,CAACoF,EAAAA,IAAA,MAAA,CAAI,UAAU,SACb,SAAAA,EAAA,IAACO,GAAA,CACC,QAASrE,EAAa,eACtB,KAAMC,GACN,QAAS+B,GACT,cAAeE,GACf,IAAKpC,EAAA,CAAA,EAET,EAGAgE,EAAA,IAAC,MAAA,CACC,UAAW,+FACTpF,EAAe,0CAA4C,EAC7D,GAEA,SAAAsF,EAAA,KAAC,MAAA,CACC,UAAW,GACTtF,EACI,wBACA,6BACN,GAEA,SAAA,CAAAoF,EAAA,IAAC,MAAA,CACC,UAAW,GACTpF,EAAe,yBAA2B,WAC5C,8GAEA,SAAAoF,EAAA,IAACG,EAAA,CACC,UAAW,GACTvF,EAAe,YAAc,WAC/B,gBAAA,CACF,CAAA,CACF,EACAsF,EAAA,KAAC,MAAA,CACC,UAAW,UAAUtF,EAAe,cAAgB,EAAE,GAEtD,SAAA,CAAAoF,EAAA,IAAC,KAAA,CACC,UAAW,GACTpF,EAAe,gBAAkB,SACnC,aAEC,SAAasB,EAAA,KAAA,CAChB,EACA8D,EAAA,IAAC,IAAA,CACC,UAAW,GACTpF,EACI,gCACA,uBACN,GAEC,SAAasB,EAAA,MAAA,CAChB,EACA8D,EAAA,IAAC,IAAA,CACC,UAAW,GACTpF,EACI,0BACA,uBACN,GAEC,2BAAiB,OAAQ,8BAAA,CAC5B,EAGAsF,EAAA,KAAC,MAAA,CACC,UAAW,+CACTtF,EAAe,OAAS,EAC1B,GAEA,SAAA,CAACoF,EAAAA,IAAAQ,GAAA,CAAQ,UAAU,sBAAuB,CAAA,EACzCR,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAAO,UAAA,CAAA,CAAA,CACjD,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CACF,EAGAE,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,kFACb,SAAA,CAACF,EAAA,IAAA,OAAA,CAAM,SAAWnB,EAAA3E,CAAW,CAAE,CAAA,EAC9B8F,EAAA,IAAA,OAAA,CAAM,SAAWnB,EAAA3C,EAAa,QAAQ,EAAE,CAAA,EAC3C,EACA8D,EAAA,IAAC,QAAA,CACC,KAAK,QACL,IAAI,IACJ,IAAI,IACJ,KAAK,QACL,MACE9D,EAAa,SAAW,EACpBhC,EAAcgC,EAAa,SAC3B,EAEN,SAAU+C,GACV,UAAU,sFACV,MAAO,CACL,WAAY,iDACT/E,EAAc,KAAK,IAAI,EAAGgC,EAAa,QAAQ,EAAK,GACvD,cACGhC,EAAc,KAAK,IAAI,EAAGgC,EAAa,QAAQ,EAAK,GACvD,kBACF,CAAA,CACF,CAAA,EACF,EAGAgE,EAAAA,KAAC,MAAI,CAAA,UAAU,kDACb,SAAA,CAAAF,EAAA,IAAC,SAAA,CACC,QAASb,GACT,UAAW,mCACT7E,EACI,qCACA,wGACN,GACA,MACEA,EACI,2BACA,wBAGN,SAAA0F,EAAAA,IAACS,GAAQ,CAAA,UAAU,SAAU,CAAA,CAAA,CAC/B,EAEAT,EAAA,IAAC,SAAA,CACC,QAASL,GACT,UAAU,4IACV,MAAM,kBAEN,SAAAK,EAAAA,IAACU,GAAS,CAAA,UAAU,SAAU,CAAA,CAAA,CAChC,EAEAV,EAAA,IAAC,SAAA,CACC,QAASxB,GACT,UAAU,sKACV,MAAO7E,EAAY,SAAW,aAE7B,SAAAA,QACEgH,GAAM,CAAA,UAAU,UAAU,EAE3BX,EAAA,IAACY,GAAK,CAAA,UAAU,cAAe,CAAA,CAAA,CAEnC,EAEAZ,EAAA,IAAC,SAAA,CACC,QAAS3B,EACT,UAAU,4IACV,MAAM,iBAEN,SAAA2B,EAAAA,IAACa,GAAY,CAAA,UAAU,SAAU,CAAA,CAAA,CACnC,EAEAX,EAAA,KAAC,SAAA,CACC,QAASd,GACT,UAAW,4CACT5E,IAAe,OACX,qCACA,wGACN,GACA,MACEA,IAAe,OACX,mBACAA,IAAe,MACf,2BACA,sBAGN,SAAA,CAACwF,EAAAA,IAAAc,GAAA,CAAO,UAAU,SAAU,CAAA,EAC3BtG,IAAe,OACdwF,EAAAA,IAAC,OAAK,CAAA,UAAU,kHAAkH,SAElI,IAAA,CAAA,CAAA,CAEJ,CAAA,EACF,EAGAE,EAAAA,KAAC,MAAI,CAAA,UAAU,8CACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAAAF,EAAA,IAAC,SAAA,CACC,QAASP,GACT,UAAW,mCACT/E,EACI,4CACA,2EACN,GACA,MACEA,EACI,wBACA,0BAGN,SAAAsF,EAAA,IAACe,GAAA,CACC,UAAW,WAAWrG,EAAU,eAAiB,EAAE,EAAA,CACrD,CAAA,CACF,EACAsF,EAAA,IAAC,SAAA,CACC,UAAU,yIACV,MAAM,eAEN,SAAAA,EAAAA,IAACgB,GAAO,CAAA,UAAU,SAAU,CAAA,CAAA,CAC9B,EACAhB,EAAA,IAAC,IAAA,CACC,KAAM,mCAAmC9D,EAAa,cAAc,GACpE,OAAO,SACP,IAAI,sBACJ,UAAU,yIACV,MAAM,mBAEN,SAAA8D,EAAAA,IAACiB,GAAa,CAAA,UAAU,SAAU,CAAA,CAAA,CACpC,CAAA,EACF,EAGAf,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAAAF,EAAA,IAAC,SAAA,CACC,QAASvB,GACT,UAAU,4JACV,MAAOzE,EAAU,aAAe,gBAE/B,SAAAA,GAAWF,IAAW,EACpBkG,EAAA,IAAAkB,GAAA,CAAQ,UAAU,SAAU,CAAA,EAE7BlB,EAAAA,IAACmB,GAAQ,CAAA,UAAU,SAAU,CAAA,CAAA,CAEjC,EACAnB,EAAA,IAAC,QAAA,CACC,KAAK,QACL,IAAI,IACJ,IAAI,IACJ,KAAK,OACL,MAAOhG,EAAU,EAAIF,EACrB,SAAU4E,GACV,UAAU,kFACV,MAAO,CACL,WAAY,kDACT1E,EAAU,EAAIF,GAAU,GAC3B,eACGE,EAAU,EAAIF,GAAU,GAC3B,kBACF,EACA,MAAO,WAAW,KAAK,OACpBE,EAAU,EAAIF,GAAU,GAC1B,CAAA,GAAA,CACH,EACAoG,EAAAA,KAAC,OAAK,CAAA,UAAU,0DACb,SAAA,CAAA,KAAK,OAAOlG,EAAU,EAAIF,GAAU,GAAG,EAAE,GAAA,EAC5C,CAAA,EACF,CAAA,EACF,EAGAoG,EAAA,KAAC,MAAA,CACC,UAAW,YACTpF,GAAgB,CAACF,EAAe,kBAAoB,EACtD,GAEA,SAAA,CAACsF,EAAAA,KAAA,MAAA,CAAI,UAAU,WACb,SAAA,CAAAF,EAAA,IAAC,QAAA,CACC,KAAK,OACL,YAAY,qBACZ,MAAO1E,EACP,SAAWqD,GAAMpD,GAAeoD,EAAE,OAAO,KAAK,EAC9C,UAAYA,GAAM,CACZA,EAAE,MAAQ,UACZlD,EAAe,EAAI,EACnBW,EAAqBd,CAAW,EAEpC,EACA,UAAU,iMAAA,CACZ,EACA0E,EAAAA,IAACoB,GAAO,CAAA,UAAU,0EAA2E,CAAA,EAC7FpB,EAAA,IAAC,SAAA,CACC,QAAS,IAAM,CACbvE,EAAe,EAAI,EACnBW,EAAqBd,CAAW,CAClC,EACA,SAAUE,GAAe,CAACF,EAAY,KAAK,EAC3C,UAAU,oKAET,SACCE,EAAAwE,MAACqB,EAAU,CAAA,UAAU,sBAAuB,CAAA,EAE5C,QAAA,CAEJ,CAAA,EACF,EAECzF,GACCsE,EAAA,KAAC,MAAI,CAAA,UAAU,uFACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,0BACb,SAAA,CAACF,EAAAA,IAAAsB,EAAA,CAAY,UAAU,SAAU,CAAA,EACjCtB,EAAAA,IAAC,QAAM,SAAMpE,CAAA,CAAA,CAAA,EACf,EACAoE,EAAA,IAAC,SAAA,CACC,QAASpD,EACT,UAAU,wDACX,SAAA,kBAAA,CAED,CAAA,EACF,CAAA,CAAA,CAEJ,CAAA,EACF,CAAA,CACF,EAGAoD,EAAAA,IAACuB,IACE,SACCzG,GAAAoF,EAAA,KAACD,EAAO,IAAP,CACC,QAAS,CAAE,MAAO,EAAG,QAAS,CAAE,EAChC,QAAS,CAAE,MAAO,aAAc,QAAS,CAAE,EAC3C,KAAM,CAAE,MAAO,EAAG,QAAS,CAAE,EAC7B,WAAY,CAAE,SAAU,EAAI,EAC5B,UAAU,uEAEV,SAAA,CAACC,EAAAA,KAAA,MAAA,CAAI,UAAU,sFACb,SAAA,CAACF,EAAA,IAAA,KAAA,CAAG,UAAU,4CAA4C,SAE1D,YAAA,EACAA,EAAA,IAAC,SAAA,CACC,QAAS,IAAMjF,EAAgB,EAAK,EACpC,UAAU,gFAEV,SAAAiF,EAAAA,IAACwB,GAAE,CAAA,UAAU,SAAU,CAAA,CAAA,CACzB,CAAA,EACF,EAEAtB,EAAA,KAAC,MAAA,CACC,UAAU,yBACV,MAAO,CAAE,UAAW,qBAAsB,EAG1C,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,gBACZ,SAAA,CAAUlF,EAAA,IAAKwB,GACd0D,EAAA,KAAC,MAAA,CAEC,QAAS,IAAMJ,GAAqBtD,CAAQ,EAC5C,UAAW,oDACTtB,GAAA,YAAAA,EAAiB,MAAOsB,EAAS,GAC7B,sCACA,0CACN,GAEA,SAAA,CAACwD,EAAAA,IAAA,MAAA,CAAI,UAAU,uDACb,SAAAA,EAAA,IAAC,MAAA,CACC,IAAKxD,EAAS,UACd,IAAKA,EAAS,KACd,UAAU,6BACV,QAAUmC,GAAM,CACdA,EAAE,cAAc,IACd,qRACJ,CAAA,CAAA,EAEJ,EACAuB,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAF,EAAA,IAAC,KAAA,CACC,UAAW,iCACT9E,GAAA,YAAAA,EAAiB,MAAOsB,EAAS,GAC7B,uCACA,+BACN,GAEC,SAASA,EAAA,IAAA,CACZ,EACA0D,EAAAA,KAAC,IAAE,CAAA,UAAU,oDACV,SAAA,CAAA1D,EAAS,OAAO,OAAO,UAAA,EAC1B,CAAA,EACF,CAAA,CAAA,EAhCKA,EAAS,EAAA,CAkCjB,EAEAxB,EAAU,SAAW,GAAK,CAACU,GACzBwE,OAAA,MAAA,CAAI,UAAU,oDACb,SAAA,CAACF,EAAAA,IAAAG,EAAA,CAAM,UAAU,mCAAoC,CAAA,EACrDH,EAAAA,IAAC,KAAE,SAA2B,6BAAA,CAAA,EAC9BA,EAAA,IAAC,SAAA,CACC,QAASpD,EACT,UAAU,gEACX,SAAA,sBAAA,CAED,CAAA,EACF,EAGDlB,GACCwE,EAAA,KAAC,MAAI,CAAA,UAAU,mBACb,SAAA,CAACF,EAAAA,IAAAqB,EAAA,CAAU,UAAU,mDAAoD,CAAA,EACxErB,EAAA,IAAA,IAAA,CAAE,UAAU,mCAAmC,SAEhD,wBAAA,CAAA,EACF,CAAA,EAEJ,EAGC9E,GACCgF,EAAA,KAAC,MAAI,CAAA,UAAU,WACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,sDAAsD,SAAA,CAAA,cACtDhF,EAAgB,IAAA,EAC9B,EACAgF,EAAAA,KAAC,MAAI,CAAA,UAAU,YACZ,SAAA,CAAAhF,EAAgB,OAAO,IAAI,CAACuG,EAAOxE,IAClCiD,EAAA,KAAC,MAAA,CAEC,QAAS,IAAMH,GAAiB9C,CAAK,EACrC,UAAW,mDACT7B,IAAsB6B,EAClB,kCACA,0CACN,GAEA,SAAA,CAAA+C,EAAA,IAAC,MAAI,CAAA,UAAU,gEACZ,SAAA/C,EAAQ,EACX,EACA+C,EAAAA,IAAC,OAAI,UAAU,kFACb,eAAC,OAAK,CAAA,UAAU,UAAU,SAAA,IAAA,CAAE,CAC9B,CAAA,EACAE,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAF,EAAA,IAAC,KAAA,CACC,UAAW,gCACT5E,IAAsB6B,EAClB,mCACA,+BACN,GAEC,SAAMwE,EAAA,KAAA,CACT,EACCzB,EAAA,IAAA,IAAA,CAAE,UAAU,oDACV,WAAM,OACT,CAAA,EACF,QACC,MAAI,CAAA,UAAU,2CACZ,SAAWnB,EAAA4C,EAAM,QAAQ,EAC5B,CAAA,CAAA,EA9BKA,EAAM,cAAA,CAgCd,EAGAvG,EAAgB,OAAO,QACrBA,EAAgB,YAAc,KAC/B8E,EAAA,IAAC,MAAI,CAAA,UAAU,mBACb,SAAAE,EAAA,KAAC,SAAA,CACC,QAAS,IACPvC,EACEzC,EAAgB,GAChBA,EAAgB,OAAO,MACzB,EAEF,UAAU,8HAEV,SAAA,CAAC8E,EAAAA,IAAA0B,GAAA,CAAK,UAAU,SAAU,CAAA,EAAE,0BAE3BxG,EAAgB,WACfA,EAAgB,OAAO,OAAQ,IAAI,YAAA,CAAA,CAAA,EAGzC,CAAA,EAEJ,CAAA,EACF,CAAA,CAAA,CAEJ,CAAA,CAAA,CAAA,EAGN,CAAA,EACF,CAAA,CAAA,CACF,CAAA,CAAA,EA7hBAgF,EAAA,KAAC,MAAI,CAAA,UAAU,mBACb,SAAA,CAACF,EAAAA,IAAAsB,EAAA,CAAY,UAAU,qCAAsC,CAAA,EAC5DtB,EAAA,IAAA,KAAA,CAAG,UAAU,2DAA2D,SAEzE,wBAAA,EACAA,EAAA,IAAC,SAAA,CACC,QAAS,IAAM3F,EAAa,EAAI,EAChC,UAAU,wIACX,SAAA,mBAAA,CAED,CACF,CAAA,CAAA,CAqhBN"}