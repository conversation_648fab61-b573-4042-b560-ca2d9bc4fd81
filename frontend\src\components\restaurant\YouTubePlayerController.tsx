import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Play, Pause, SkipForward, Volume2, Alert<PERSON>ircle, Wifi, WifiOff } from 'lucide-react';
import { toast } from 'react-hot-toast';

// Tipos para o sistema de filas
interface Song {
  id: string;
  videoId: string;
  title: string;
  artist: string;
  duration?: number;
  upvotes: number;
  downvotes: number;
  isPaid?: boolean;
  paymentId?: string;
  addedAt: Date;
}

interface QueueState {
  priorityQueue: Song[];
  normalQueue: Song[];
  currentSong: Song | null;
  isPlaying: boolean;
  isLoading: boolean;
}

// Declaração global para YouTube API
declare global {
  interface Window {
    YT: any;
    onYouTubeIframeAPIReady: () => void;
  }
}

const YouTubePlayerController: React.FC = () => {
  const playerRef = useRef<any>(null);
  const [player, setPlayer] = useState<any>(null);
  const [isAPIReady, setIsAPIReady] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'online' | 'offline'>('online');
  const [errorCount, setErrorCount] = useState(0);
  const ERROR_THRESHOLD = 3;

  const [queueState, setQueueState] = useState<QueueState>({
    priorityQueue: [],
    normalQueue: [],
    currentSong: null,
    isPlaying: false,
    isLoading: false,
  });

  // Carregar YouTube API
  useEffect(() => {
    if (!window.YT) {
      const tag = document.createElement('script');
      tag.src = 'https://www.youtube.com/iframe_api';
      const firstScriptTag = document.getElementsByTagName('script')[0];
      firstScriptTag.parentNode?.insertBefore(tag, firstScriptTag);

      window.onYouTubeIframeAPIReady = () => {
        setIsAPIReady(true);
        console.log('🎵 YouTube API carregada com sucesso');
      };
    } else {
      setIsAPIReady(true);
    }
  }, []);

  // Inicializar player quando API estiver pronta
  useEffect(() => {
    if (isAPIReady && !player) {
      initializePlayer();
    }
  }, [isAPIReady]);

  // Monitorar conexão
  useEffect(() => {
    const handleOnline = () => {
      setConnectionStatus('online');
      console.log('🌐 Conexão restaurada');
      toast.success('Conexão restaurada');
    };

    const handleOffline = () => {
      setConnectionStatus('offline');
      console.log('🌐 Conexão perdida');
      toast.error('Conexão perdida - modo offline ativado');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const initializePlayer = useCallback(() => {
    try {
      const newPlayer = new window.YT.Player('youtube-player', {
        height: '315',
        width: '560',
        videoId: '', // Será definido quando houver música na fila
        playerVars: {
          autoplay: 1,
          controls: 0, // Remover controles padrão
          disablekb: 1,
          fs: 0,
          iv_load_policy: 3,
          modestbranding: 1,
          playsinline: 1,
          rel: 0,
        },
        events: {
          onReady: onPlayerReady,
          onStateChange: onPlayerStateChange,
          onError: onPlayerError,
        },
      });

      setPlayer(newPlayer);
      console.log('🎵 Player inicializado com sucesso');
    } catch (error) {
      console.error('❌ Erro ao inicializar player:', error);
      handlePlayerError('Falha na inicialização do player');
    }
  }, []);

  const onPlayerReady = useCallback((event: any) => {
    console.log('🎵 Player pronto para uso');
    toast.success('Player de música carregado');
    
    // Carregar primeira música se houver fila
    playNextInQueue();
  }, []);

  const onPlayerStateChange = useCallback((event: any) => {
    const state = event.data;
    
    switch (state) {
      case window.YT.PlayerState.PLAYING:
        setQueueState(prev => ({ ...prev, isPlaying: true, isLoading: false }));
        console.log('▶️ Música iniciada');
        break;
        
      case window.YT.PlayerState.PAUSED:
        setQueueState(prev => ({ ...prev, isPlaying: false }));
        console.log('⏸️ Música pausada');
        break;
        
      case window.YT.PlayerState.ENDED:
        console.log('🔚 Música finalizada - tocando próxima');
        setTimeout(() => playNextInQueue(), 1000);
        break;
        
      case window.YT.PlayerState.BUFFERING:
        setQueueState(prev => ({ ...prev, isLoading: true }));
        console.log('⏳ Carregando música...');
        break;
        
      case window.YT.PlayerState.CUED:
        setQueueState(prev => ({ ...prev, isLoading: false }));
        break;
    }
  }, []);

  const onPlayerError = useCallback((event: any) => {
    const errorCode = event.data;
    let errorMessage = 'Erro desconhecido no player';
    
    switch (errorCode) {
      case 2:
        errorMessage = 'ID do vídeo inválido';
        break;
      case 5:
        errorMessage = 'Erro de reprodução HTML5';
        break;
      case 100:
        errorMessage = 'Vídeo não encontrado ou removido';
        break;
      case 101:
      case 150:
        errorMessage = 'Vídeo não pode ser reproduzido (restrições)';
        break;
    }
    
    console.error(`❌ Erro no player (${errorCode}): ${errorMessage}`);
    handlePlayerError(errorMessage);
    
    // Tentar próxima música após erro
    setTimeout(() => playNextInQueue(), 2000);
  }, []);

  const handlePlayerError = useCallback((message: string) => {
    setErrorCount(prev => prev + 1);
    toast.error(`Erro: ${message}`);
    
    if (errorCount >= ERROR_THRESHOLD) {
      toast.error('Múltiplos erros detectados - verificando sistema');
      // Aqui poderia notificar staff ou ativar modo de emergência
      console.log('🚨 Threshold de erros atingido - ação necessária');
    }
  }, [errorCount]);

  const getNextSong = useCallback((): Song | null => {
    // Prioridade: fila paga > fila normal
    if (queueState.priorityQueue.length > 0) {
      return queueState.priorityQueue[0];
    }
    if (queueState.normalQueue.length > 0) {
      return queueState.normalQueue[0];
    }
    return null;
  }, [queueState.priorityQueue, queueState.normalQueue]);

  const playNextInQueue = useCallback(async () => {
    if (!player) return;
    
    const nextSong = getNextSong();
    if (!nextSong) {
      console.log('📭 Fila vazia - aguardando novas músicas');
      setQueueState(prev => ({ ...prev, currentSong: null, isPlaying: false }));
      return;
    }

    try {
      // Verificar se vídeo pode ser reproduzido
      const canPlay = await checkVideoPlayability(nextSong.videoId);
      if (!canPlay) {
        console.log(`⚠️ Vídeo ${nextSong.title} não pode ser reproduzido - pulando`);
        removeFromQueue(nextSong);
        setTimeout(() => playNextInQueue(), 500);
        return;
      }

      // Carregar e reproduzir
      player.loadVideoById(nextSong.videoId);
      
      // Atualizar estado
      setQueueState(prev => ({
        ...prev,
        currentSong: nextSong,
        isLoading: true,
      }));

      // Remover da fila
      removeFromQueue(nextSong);
      
      console.log(`🎵 Tocando: ${nextSong.title} - ${nextSong.artist}`);
      toast.success(`Tocando: ${nextSong.title}`);
      
    } catch (error) {
      console.error('❌ Erro ao tocar próxima música:', error);
      handlePlayerError('Falha ao carregar música');
    }
  }, [player, getNextSong]);

  const checkVideoPlayability = async (videoId: string): Promise<boolean> => {
    try {
      // Verificação básica - em produção, usar YouTube Data API
      const response = await fetch(`https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v=${videoId}&format=json`);
      return response.ok;
    } catch (error) {
      console.warn('⚠️ Não foi possível verificar vídeo:', error);
      return true; // Assumir que pode reproduzir se não conseguir verificar
    }
  };

  const removeFromQueue = useCallback((song: Song) => {
    setQueueState(prev => ({
      ...prev,
      priorityQueue: prev.priorityQueue.filter(s => s.id !== song.id),
      normalQueue: prev.normalQueue.filter(s => s.id !== song.id),
    }));
  }, []);

  const addToQueue = useCallback((song: Song, isPaid: boolean = false) => {
    setQueueState(prev => ({
      ...prev,
      [isPaid ? 'priorityQueue' : 'normalQueue']: [
        ...prev[isPaid ? 'priorityQueue' : 'normalQueue'],
        { ...song, isPaid, addedAt: new Date() }
      ]
    }));
    
    console.log(`➕ Música adicionada à fila ${isPaid ? 'prioritária' : 'normal'}: ${song.title}`);
    toast.success(`Música adicionada à fila ${isPaid ? 'prioritária' : 'normal'}`);
    
    // Se não há música tocando, iniciar reprodução
    if (!queueState.currentSong && !queueState.isPlaying) {
      setTimeout(() => playNextInQueue(), 1000);
    }
  }, [queueState.currentSong, queueState.isPlaying, playNextInQueue]);

  const togglePlayPause = useCallback(() => {
    if (!player) return;
    
    if (queueState.isPlaying) {
      player.pauseVideo();
    } else {
      player.playVideo();
    }
  }, [player, queueState.isPlaying]);

  const skipToNext = useCallback(() => {
    console.log('⏭️ Pulando para próxima música');
    playNextInQueue();
  }, [playNextInQueue]);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
      {/* Header com status */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Player de Música
        </h3>
        <div className="flex items-center space-x-3">
          {connectionStatus === 'online' ? (
            <div className="flex items-center space-x-1 text-green-600">
              <Wifi className="w-4 h-4" />
              <span className="text-xs">Online</span>
            </div>
          ) : (
            <div className="flex items-center space-x-1 text-red-600">
              <WifiOff className="w-4 h-4" />
              <span className="text-xs">Offline</span>
            </div>
          )}
          
          {errorCount > 0 && (
            <div className="flex items-center space-x-1 text-yellow-600">
              <AlertCircle className="w-4 h-4" />
              <span className="text-xs">{errorCount} erros</span>
            </div>
          )}
        </div>
      </div>

      {/* Player do YouTube */}
      <div className="mb-4">
        <div id="youtube-player" className="w-full rounded-lg overflow-hidden"></div>
      </div>

      {/* Controles */}
      <div className="flex items-center justify-center space-x-4 mb-6">
        <button
          onClick={togglePlayPause}
          disabled={!player || !queueState.currentSong}
          className="w-12 h-12 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-full flex items-center justify-center transition-colors"
        >
          {queueState.isLoading ? (
            <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          ) : queueState.isPlaying ? (
            <Pause className="w-5 h-5" />
          ) : (
            <Play className="w-5 h-5" />
          )}
        </button>
        
        <button
          onClick={skipToNext}
          disabled={!player}
          className="w-10 h-10 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white rounded-full flex items-center justify-center transition-colors"
        >
          <SkipForward className="w-4 h-4" />
        </button>
        
        <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
          <Volume2 className="w-4 h-4" />
          <span className="text-sm">Volume: Auto</span>
        </div>
      </div>

      {/* Música atual */}
      {queueState.currentSong && (
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-4 mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <div className="flex-1">
              <p className="font-medium text-gray-900 dark:text-white">
                {queueState.currentSong.title}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {queueState.currentSong.artist}
              </p>
            </div>
            {queueState.currentSong.isPaid && (
              <div className="bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded text-xs font-medium">
                Prioritária
              </div>
            )}
          </div>
        </div>
      )}

      {/* Filas */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Fila Prioritária */}
        <div>
          <h4 className="font-medium text-gray-900 dark:text-white mb-2 flex items-center">
            🔥 Fila Prioritária ({queueState.priorityQueue.length})
          </h4>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {queueState.priorityQueue.length > 0 ? (
              queueState.priorityQueue.slice(0, 5).map((song, index) => (
                <div key={song.id} className="bg-yellow-50 dark:bg-yellow-900/20 rounded p-2">
                  <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {index + 1}. {song.title}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400 truncate">
                    {song.artist}
                  </p>
                </div>
              ))
            ) : (
              <p className="text-sm text-gray-500 dark:text-gray-400 text-center py-2">
                Nenhuma música paga na fila
              </p>
            )}
          </div>
        </div>

        {/* Fila Normal */}
        <div>
          <h4 className="font-medium text-gray-900 dark:text-white mb-2 flex items-center">
            📋 Fila Normal ({queueState.normalQueue.length})
          </h4>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {queueState.normalQueue.length > 0 ? (
              queueState.normalQueue.slice(0, 5).map((song, index) => (
                <div key={song.id} className="bg-gray-50 dark:bg-gray-700 rounded p-2">
                  <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {index + 1}. {song.title}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400 truncate">
                    {song.artist} • {song.upvotes - song.downvotes} votos
                  </p>
                </div>
              ))
            ) : (
              <p className="text-sm text-gray-500 dark:text-gray-400 text-center py-2">
                Nenhuma música na fila normal
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default YouTubePlayerController;
