# ============================================
# SCRIPT DE SETUP COMPLETO DO AMBIENTE
# Inicializa o ambiente Docker com todas as tabelas atualizadas
# ============================================

Write-Host "🚀 Iniciando setup completo do ambiente..." -ForegroundColor Cyan

# Parar containers existentes
Write-Host "🛑 Parando containers existentes..." -ForegroundColor Yellow
docker-compose down

# Remover volumes para garantir inicialização limpa
Write-Host "🗑️ Removendo volumes antigos..." -ForegroundColor Yellow
docker volume rm youtube_postgres_data -f 2>$null
docker volume rm youtube_redis_data -f 2>$null

# Construir e iniciar containers
Write-Host "🏗️ Construindo e iniciando containers..." -ForegroundColor Cyan
docker-compose up -d --build

# Aguardar containers ficarem prontos
Write-Host "⏳ Aguardando containers ficarem prontos..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Verificar se o banco está respondendo
Write-Host "🔍 Verificando conectividade do banco..." -ForegroundColor Cyan
$maxAttempts = 10
$attempt = 0
do {
    $attempt++
    Write-Host "Tentativa $attempt de $maxAttempts..." -ForegroundColor Yellow
    $healthCheck = docker exec restaurant-playlist-db pg_isready -U restaurant_user -d restaurant_playlist 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Banco de dados está pronto!" -ForegroundColor Green
        break
    }
    Start-Sleep -Seconds 5
} while ($attempt -lt $maxAttempts)

if ($attempt -eq $maxAttempts) {
    Write-Host "❌ Timeout aguardando banco de dados ficar pronto" -ForegroundColor Red
    exit 1
}

# Verificar se as tabelas foram criadas corretamente
Write-Host "📊 Verificando estrutura das tabelas..." -ForegroundColor Cyan
$tablesCount = docker exec restaurant-playlist-db psql -U restaurant_user -d restaurant_playlist -tAc "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';"
Write-Host "✅ Total de tabelas criadas: $tablesCount" -ForegroundColor Green

# Verificar se a coluna execution_order existe
$columnExists = docker exec restaurant-playlist-db psql -U restaurant_user -d restaurant_playlist -tAc "SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='execution_order';"
if ($columnExists -eq "1") {
    Write-Host "✅ Coluna execution_order criada com sucesso" -ForegroundColor Green
} else {
    Write-Host "❌ Erro: Coluna execution_order não foi criada" -ForegroundColor Red
    exit 1
}

# Verificar se os índices foram criados
$indexCount = docker exec restaurant-playlist-db psql -U restaurant_user -d restaurant_playlist -tAc "SELECT COUNT(*) FROM pg_indexes WHERE tablename = 'playlists';"
Write-Host "✅ Total de índices na tabela playlists: $indexCount" -ForegroundColor Green

# Verificar health dos containers
Write-Host "🏥 Verificando health dos containers..." -ForegroundColor Cyan
docker ps --format "table {{.Names}}\t{{.Status}}"

Write-Host ""
Write-Host "🎉 Setup completo do ambiente concluído!" -ForegroundColor Green
Write-Host "🌐 Serviços disponíveis:" -ForegroundColor Yellow
Write-Host "   Frontend: http://localhost:8000" -ForegroundColor Cyan
Write-Host "   Backend:  http://localhost:8001" -ForegroundColor Cyan
Write-Host "   Banco:    localhost:8002" -ForegroundColor Cyan
Write-Host "   Redis:    localhost:8003" -ForegroundColor Cyan
Write-Host ""
Write-Host "📝 Para verificar logs:" -ForegroundColor Yellow
Write-Host "   docker-compose logs -f" -ForegroundColor Cyan
