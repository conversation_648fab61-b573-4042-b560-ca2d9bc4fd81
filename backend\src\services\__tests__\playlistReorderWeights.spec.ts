import { AppDataSource } from "../../config/database";
import { playlistReorderService } from "../PlaylistReorderService";

// Mocks dos serviços externos
jest.mock("../YouTubeOAuthService", () => ({
  youtubeOAuthService: {
    isAuthenticated: jest.fn().mockResolvedValue(true),
    reorderPlaylist: jest.fn().mockResolvedValue(true),
  },
}));

jest.mock("../CollaborativePlaylistService", () => ({
  collaborativePlaylistService: {
    getVotingRanking: jest.fn().mockResolvedValue({
      success: true,
      message: "3 músicas no ranking",
      data: [
        // Esperado: A(50 -> 20 votos) > B(20 -> 8 votos) > C(normal -> 1 voto)
        {
          youtubeVideoId: "videoA",
          title: "A",
          artist: "",
          voteCount: 20,
          superVoteCount: 1,
          normalVoteCount: 0,
          totalRevenue: 50,
          isPaid: true,
          paymentAmount: 50,
        },
        {
          youtubeVideoId: "videoB",
          title: "B",
          artist: "",
          voteCount: 8,
          superVoteCount: 1,
          normalVoteCount: 0,
          totalRevenue: 20,
          isPaid: true,
          paymentAmount: 20,
        },
        {
          youtubeVideoId: "videoC",
          title: "C",
          artist: "",
          voteCount: 1,
          superVoteCount: 0,
          normalVoteCount: 1,
          totalRevenue: 0,
          isPaid: false,
          paymentAmount: 0,
        },
      ],
    }),
  },
}));

// Tipos simples para tracks usados no serviço
type Track = { youtubeVideoId: string; position: number; title?: string };

describe("PlaylistReorderService - pesos influenciam reordenação", () => {
  const playlistRepoMock: any = {
    findOne: jest.fn(),
    save: jest.fn(),
    update: jest.fn(),
  };
  const scheduleRepoMock: any = {
    find: jest.fn().mockResolvedValue([]),
  };

  beforeAll(() => {
    // Configurar repositórios mockados
    // Observação: a implementação interna do serviço só usa métodos específicos
    // que cobrimos aqui.
    // @ts-ignore - jest mock
    (AppDataSource.getRepository as any).mockImplementation((model: any) => {
      const name = model?.name || "";
      if (name === "PlaylistSchedule") return scheduleRepoMock;
      if (name === "Playlist") return playlistRepoMock;
      // Retornar mocks neutros para outros
      return {
        find: jest.fn(),
        findOne: jest.fn(),
        save: jest.fn(),
        update: jest.fn(),
      };
    });
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("deve posicionar 50(20 votos) > 20(8 votos) > normal(1 voto) na playlist", async () => {
    // Playlist ativa com 3 faixas desordenadas
    const tracks: Track[] = [
      { youtubeVideoId: "videoC", position: 2, title: "C" },
      { youtubeVideoId: "videoA", position: 0, title: "A" },
      { youtubeVideoId: "videoB", position: 1, title: "B" },
    ];

    const activePlaylist = {
      id: "pl1",
      name: "Demo Playlist",
      status: "active",
      youtubePlaylistId: "yt_pl_1",
      tracks: tracks,
    } as any;

    playlistRepoMock.findOne.mockResolvedValue(activePlaylist);
    playlistRepoMock.save.mockImplementation(async (pl: any) => pl);

    const result = await playlistReorderService.manualReorder("demo-restaurant");

    expect(result.success).toBe(true);
    expect(result.tracksReordered).toBe(3);

    // Verificar chamada ao reordenamento do YouTube com a ordem esperada
    const { youtubeOAuthService } = require("../YouTubeOAuthService");
    expect(youtubeOAuthService.reorderPlaylist).toHaveBeenCalledTimes(1);
    const callArgs = (youtubeOAuthService.reorderPlaylist as jest.Mock).mock
      .calls[0];
    const newOrder = callArgs[2];

    // Ordem esperada: A (50->20) na pos 0, B (20->8) pos 1, C (normal->1) pos 2
    expect(newOrder).toEqual([
      expect.objectContaining({ videoId: "videoA", position: 0 }),
      expect.objectContaining({ videoId: "videoB", position: 1 }),
      expect.objectContaining({ videoId: "videoC", position: 2 }),
    ]);

    // Verificar atualização local das posições na playlist salva
    expect(playlistRepoMock.save).toHaveBeenCalledTimes(1);
    const savedPlaylist = (playlistRepoMock.save as jest.Mock).mock.calls[0][0];
    const savedOrder = (savedPlaylist.tracks as Track[]).map((t) => ({
      id: t.youtubeVideoId,
      position: t.position,
    }));
    expect(savedOrder).toEqual([
      { id: "videoA", position: 0 },
      { id: "videoB", position: 1 },
      { id: "videoC", position: 2 },
    ]);
  });
});
