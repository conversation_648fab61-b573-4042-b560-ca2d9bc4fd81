-- ============================================
-- SISTEMA DE PLAYLIST DE RESTAURANTE - DADOS INICIAIS
-- ============================================

-- Inserir restaurante demo
INSERT INTO restaurants (id, name, description, status, settings, language, timezone, "isActive", "createdAt", "updatedAt") VALUES
('demo-restaurant', 'Restaurante Demo', 'Restaurante de demonstração do sistema de playlist interativa', 'trial', 
'{"moderation": {"autoApprove": false, "requireApproval": true, "maxSuggestionsPerUser": 3, "maxSuggestionsPerHour": 10, "allowExplicitContent": false, "allowLiveVideos": true, "minVideoDuration": 30, "maxVideoDuration": 600}, "playlist": {"maxQueueSize": 50, "allowDuplicates": false, "shuffleMode": false, "repeatMode": "none", "crossfadeDuration": 3, "defaultVolume": 70}, "interface": {"theme": "light", "primaryColor": "#3B82F6", "secondaryColor": "#10B981", "showVoteCount": true, "showQueuePosition": true, "allowAnonymousSuggestions": true, "requireSessionId": true}}', 
'pt-BR', 'America/Sao_Paulo', true, NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Inserir usuário administrador (senha: admin123)
INSERT INTO users (id, name, email, password, role, "isActive", "emailVerifiedAt", "createdAt", "updatedAt") VALUES
(gen_random_uuid(), 'Administrador', '<EMAIL>', '$2b$12$LQv3c1yqBwEHFl5ePEjNNONciJ0MGhppMn5rjJ9TndsqKJbvHEubS', 'admin', true, NOW(), NOW(), NOW())
ON CONFLICT (email) DO NOTHING;

-- Inserir usuário do restaurante (senha: restaurant123)
INSERT INTO users (id, name, email, password, role, "isActive", "emailVerifiedAt", "createdAt", "updatedAt") VALUES
(gen_random_uuid(), 'Gerente do Restaurante', '<EMAIL>', '$2b$12$LQv3c1yqBwEHFl5ePEjNNONciJ0MGhppMn5rjJ9TndsqKJbvHEubS', 'admin', true, NOW(), NOW(), NOW())
ON CONFLICT (email) DO NOTHING;

-- Playlist will be created when restaurant starts creating content

-- Inserir algumas sugestões de exemplo
INSERT INTO suggestions (
    id, "youtubeVideoId", title, artist, "channelName", duration,
    "thumbnailUrl", status, "voteCount", upvotes, downvotes, "queuePosition",
    restaurant_id, "createdAt", "updatedAt", metadata
) VALUES
(gen_random_uuid(), 'dQw4w9WgXcQ', 'Never Gonna Give You Up (Official Video)', 'Rick Astley', 'Rick Astley', 214,
'https://i.ytimg.com/vi/dQw4w9WgXcQ/hqdefault.jpg', 'approved', 5, 5, 0, 1,
'demo-restaurant', NOW(), NOW(), '{"language": "en"}'),

(gen_random_uuid(), '9bZkp7q19f0', 'PSY - GANGNAM STYLE (강남스타일) M/V', 'PSY', 'officialpsy', 253,
'https://i.ytimg.com/vi/9bZkp7q19f0/hqdefault.jpg', 'approved', 3, 3, 0, 2,
'demo-restaurant', NOW(), NOW(), '{"language": "ko"}'),

(gen_random_uuid(), 'kJQP7kiw5Fk', 'Luis Fonsi - Despacito ft. Daddy Yankee', 'Luis Fonsi', 'LuisFonsiVEVO', 282,
'https://i.ytimg.com/vi/kJQP7kiw5Fk/hqdefault.jpg', 'approved', 7, 7, 0, 3,
'demo-restaurant', NOW(), NOW(), '{"language": "es"}');

-- Inserir sessões de cliente de exemplo
INSERT INTO client_sessions (
    id, "sessionToken", "ipAddress", "userAgent", "deviceInfo", location, 
    "lastActivity", "suggestionsCount", "votesCount", "pageViews", "sessionDuration", 
    preferences, "activityLog", "isActive", restaurant_id, "createdAt", "updatedAt",
    tablenumber, clientname
) VALUES
(gen_random_uuid(), '550e8400-e29b-41d4-a716-************', '*************', 'Mozilla/5.0', 
'{"type": "mobile", "os": "iOS", "browser": "Safari"}', '{"latitude": -23.5505, "longitude": -46.6333}', 
NOW(), 2, 3, 5, 300, '{"favoriteGenres": ["pop", "rock"], "language": "pt-BR"}', 
'[{"action": "suggestion_made", "timestamp": "2025-07-31T01:00:00.000Z"}, {"action": "vote_cast", "timestamp": "2025-07-31T01:05:00.000Z"}]', 
true, 'demo-restaurant', NOW(), NOW(), '1', 'João Silva'),

(gen_random_uuid(), '550e8400-e29b-41d4-a716-************', '*************', 'Mozilla/5.0',
'{"type": "mobile", "os": "Android", "browser": "Chrome"}', '{"latitude": -23.5505, "longitude": -46.6333}',
NOW(), 1, 2, 3, 180, '{"favoriteGenres": ["pop", "electronic"], "language": "pt-BR"}',
'[{"action": "vote_cast", "timestamp": "2025-07-31T01:10:00.000Z"}]',
true, 'demo-restaurant', NOW(), NOW(), '2', 'Maria Santos');

-- Mensagem de sucesso
SELECT 'Dados iniciais inseridos com sucesso!' as status;
