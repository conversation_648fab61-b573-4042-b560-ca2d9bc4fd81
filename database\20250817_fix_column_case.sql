-- Normalize common camelCase to snake_case or expected casing where safe

-- playlists: rename executionOrder -> execution_order if exists
DO $$
BEGIN
	IF EXISTS (
		SELECT 1 FROM information_schema.columns
		WHERE table_name='playlists' AND column_name='executionOrder'
	) THEN
		ALTER TABLE playlists RENAME COLUMN "executionOrder" TO execution_order;
	END IF;
END $$;

-- restaurants: rename isActive casing if mis-cased
DO $$
BEGIN
	IF EXISTS (
		SELECT 1 FROM information_schema.columns
		WHERE table_name='restaurants' AND column_name='isactive'
	) THEN
		ALTER TABLE restaurants RENAME COLUMN isactive TO "isActive";
	END IF;
END $$;

