#!/usr/bin/env node

// Script para testar se o backend está funcionando
const http = require("http");

const testBackend = () => {
  console.log("🔍 Testando backend...");

  const options = {
    hostname: "localhost",
    port: 8001,
    path: "/api/v1/health",
    method: "GET",
  };

  const req = http.request(options, (res) => {
    console.log(`✅ Backend Status: ${res.statusCode}`);
    console.log(`📍 Headers:`, res.headers);

    let data = "";
    res.on("data", (chunk) => {
      data += chunk;
    });

    res.on("end", () => {
      try {
        const parsed = JSON.parse(data);
        console.log("📋 Response:", parsed);
      } catch (e) {
        console.log("📋 Response (raw):", data);
      }
    });
  });

  req.on("error", (e) => {
    console.error(`❌ Erro de conexão: ${e.message}`);
    console.log("💡 Verifique se o backend está rodando na porta 8001");
  });

  req.end();
};

// Também testar endpoint de sugestões
const testSuggestions = () => {
  console.log("🔍 Testando endpoint de sugestões...");

  const options = {
    hostname: "localhost",
    port: 8001,
    path: "/api/v1/suggestions/demo-restaurant",
    method: "GET",
  };

  const req = http.request(options, (res) => {
    console.log(`✅ Sugestões Status: ${res.statusCode}`);

    let data = "";
    res.on("data", (chunk) => {
      data += chunk;
    });

    res.on("end", () => {
      try {
        const parsed = JSON.parse(data);
        console.log("📋 Sugestões Response:", parsed);
      } catch (e) {
        console.log("📋 Sugestões Response (raw):", data);
      }
    });
  });

  req.on("error", (e) => {
    console.error(`❌ Erro em sugestões: ${e.message}`);
  });

  req.end();
};

// Executar testes
testBackend();
setTimeout(testSuggestions, 1000);
