import { PlaybackService, PlaybackError, PlaybackAction, PlaybackTransitionMode, ITrack, IPlaybackState } from '../services/PlaybackService';
import { TestDataSource, initializeTestDatabase, cleanTestData, closeTestDatabase } from '../config/test-database';
import { Restaurant, RestaurantStatus } from '../models/Restaurant';
import { Suggestion, SuggestionStatus } from '../models/Suggestion';
import { PlayHistory } from '../models/PlayHistory';
import { Playlist } from '../models/Playlist';
import { Repository } from 'typeorm';

// Mock apenas do WebSocket e Analytics para simplificar
jest.mock('../services/WebSocketService', () => ({
  WebSocketService: {
    getInstance: () => ({
      emitToRestaurant: jest.fn(),
      emitToUser: jest.fn(),
      broadcast: jest.fn()
    })
  }
}));

jest.mock('../services/PlaylistAnalyticsService', () => ({
  PlaylistAnalyticsService: {
    getInstance: () => ({
      recordPlay: jest.fn(),
      recordSkip: jest.fn(),
      updateStats: jest.fn()
    })
  }
}));

// Mock AppDataSource para usar TestDataSource
jest.mock('../config/database', () => ({
  AppDataSource: {}
}));

describe('PlaybackService com dados reais', () => {
  let playbackService: PlaybackService;
  let restaurantRepository: Repository<Restaurant>;
  let suggestionRepository: Repository<Suggestion>;
  let testRestaurant: Restaurant;

  const mockTrack: ITrack = {
    id: 'track-123',
    title: 'Test Song',
    artist: 'Test Artist',
    youtubeVideoId: 'test-video-id',
    duration: 180,
    thumbnailUrl: 'https://test.com/thumb.jpg',
    upvotes: 10,
    downvotes: 2,
    score: 8,
    createdAt: new Date(),
    metadata: {
      genre: 'rock',
      mood: 'energetic',
      language: 'en'
    }
  };

  beforeAll(async () => {
    await initializeTestDatabase();
    restaurantRepository = TestDataSource.getRepository(Restaurant);
    suggestionRepository = TestDataSource.getRepository(Suggestion);
  });

  afterAll(async () => {
    await closeTestDatabase();
  });

  beforeEach(async () => {
    jest.clearAllMocks();
    
    // Limpar dados antes de cada teste
    await cleanTestData();
    
    // Reset singleton instance
    (PlaybackService as any).instance = undefined;
    
    // Mock AppDataSource.getRepository para retornar nossos repositórios de teste
    const { AppDataSource } = require('../config/database');
    AppDataSource.getRepository = jest.fn((entity: any) => {
      if (entity.name === 'Restaurant') return restaurantRepository;
      if (entity.name === 'Suggestion') return suggestionRepository;
      return TestDataSource.getRepository(entity);
    });
    
    // Criar restaurante de teste
    testRestaurant = restaurantRepository.create({
      id: 'test-restaurant-' + Date.now(),
      name: 'Test Restaurant',
      email: '<EMAIL>',
      status: RestaurantStatus.ACTIVE
    });
    await restaurantRepository.save(testRestaurant);
    
    // Criar instância do serviço
    playbackService = PlaybackService.getInstance();
  });
    mockRestaurantRepository = {
      findOne: jest.fn(),
      find: jest.fn(),
      save: jest.fn()
    };

    mockSuggestionRepository = {
      findOne: jest.fn(),
      find: jest.fn(),
      save: jest.fn(),
      createQueryBuilder: jest.fn()
    };

    mockPlayHistoryRepository = {
      create: jest.fn(),
      save: jest.fn(),
      find: jest.fn()
    };

    mockPlaylistRepository = {
      findOne: jest.fn(),
      find: jest.fn()
    };

    (AppDataSource.getRepository as jest.Mock)
      .mockReturnValueOnce(mockSuggestionRepository)
      .mockReturnValueOnce(mockPlayHistoryRepository)
      .mockReturnValueOnce(mockRestaurantRepository)
      .mockReturnValueOnce(mockPlaylistRepository);

    // Setup service mocks
    mockWsService = {
      notifyPlaybackUpdate: jest.fn(),
      notifyPlaybackStart: jest.fn(),
      notifyPlaybackEnd: jest.fn(),
      notifyQueueUpdate: jest.fn()
    } as any;

    mockAnalyticsService = {
      trackPlayback: jest.fn(),
      trackSkip: jest.fn(),
      updatePlaybackStats: jest.fn()
    } as any;

    (WebSocketService.getInstance as jest.Mock).mockReturnValue(mockWsService);
    (PlaylistAnalyticsService.getInstance as jest.Mock).mockReturnValue(mockAnalyticsService);

    playbackService = PlaybackService.getInstance();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Inicialização', () => {
    it('deve ser singleton', () => {
      const instance1 = PlaybackService.getInstance();
      const instance2 = PlaybackService.getInstance();
      
      expect(instance1).toBe(instance2);
    });

    it('deve inicializar estado de reprodução para restaurante válido', async () => {
      mockRestaurantRepository.findOne.mockResolvedValue(mockRestaurant);

      const state = await playbackService.initializePlayback('restaurant-123');

      expect(state).toEqual(
        expect.objectContaining({
          currentTrack: null,
          isPlaying: false,
          queue: [],
          priorityQueue: [],
          normalQueue: [],
          history: [],
          transitionMode: PlaybackTransitionMode.AUTOMATIC,
          isTransitioning: false,
          lastActivity: expect.any(Date)
        })
      );
    });

    it('deve lançar erro para restaurante inexistente', async () => {
      mockRestaurantRepository.findOne.mockResolvedValue(null);

      await expect(
        playbackService.initializePlayback('invalid-restaurant')
      ).rejects.toThrow(PlaybackError);

      await expect(
        playbackService.initializePlayback('invalid-restaurant')
      ).rejects.toMatchObject({
        code: 'RESTAURANT_NOT_FOUND',
        statusCode: 404
      });
    });
  });

  describe('Controle de Reprodução', () => {
    beforeEach(async () => {
      mockRestaurantRepository.findOne.mockResolvedValue(mockRestaurant);
      await playbackService.initializePlayback('restaurant-123');
    });

    it('deve reproduzir uma faixa com sucesso', async () => {
      mockSuggestionRepository.findOne.mockResolvedValue({
        id: 'suggestion-123',
        ...mockTrack
      });

      await playbackService.playTrack('restaurant-123', 'track-123');

      expect(mockWsService.notifyPlaybackStart).toHaveBeenCalledWith(
        'restaurant-123',
        expect.objectContaining({
          id: 'track-123',
          title: 'Test Song'
        })
      );
    });

    it('deve pausar reprodução', async () => {
      // Primeiro iniciar reprodução
      mockSuggestionRepository.findOne.mockResolvedValue({
        id: 'suggestion-123',
        ...mockTrack
      });
      
      await playbackService.playTrack('restaurant-123', 'track-123');
      await playbackService.pausePlayback('restaurant-123');

      const state = await playbackService.getPlaybackState('restaurant-123');
      expect(state?.isPlaying).toBe(false);
    });

    it('deve pular para próxima música', async () => {
      // Setup fila com múltiplas músicas
      const queue = [mockTrack, { ...mockTrack, id: 'track-456', title: 'Next Song' }];
      
      mockSuggestionRepository.find.mockResolvedValue(queue);
      
      await playbackService.skipToNext('restaurant-123');

      expect(mockWsService.notifyPlaybackUpdate).toHaveBeenCalled();
    });

    it('deve validar parâmetros de entrada', async () => {
      await expect(
        playbackService.playTrack('', 'track-123')
      ).rejects.toThrow(PlaybackError);

      await expect(
        playbackService.playTrack('restaurant-123', '')
      ).rejects.toThrow(PlaybackError);
    });
  });

  describe('Gerenciamento de Fila', () => {
    beforeEach(async () => {
      mockRestaurantRepository.findOne.mockResolvedValue(mockRestaurant);
      await playbackService.initializePlayback('restaurant-123');
    });

    it('deve adicionar música à fila prioritária', async () => {
      const priorityTrack = { ...mockTrack, isPaid: true };
      
      await playbackService.addToQueue('restaurant-123', priorityTrack, true);

      const state = await playbackService.getPlaybackState('restaurant-123');
      expect(state?.priorityQueue).toHaveLength(1);
      expect(state?.priorityQueue[0]).toEqual(
        expect.objectContaining({
          id: 'track-123',
          title: 'Test Song'
        })
      );
    });

    it('deve adicionar música à fila normal', async () => {
      await playbackService.addToQueue('restaurant-123', mockTrack, false);

      const state = await playbackService.getPlaybackState('restaurant-123');
      expect(state?.normalQueue).toHaveLength(1);
    });

    it('deve priorizar fila paga sobre fila normal', async () => {
      const normalTrack = { ...mockTrack, id: 'normal-track' };
      const priorityTrack = { ...mockTrack, id: 'priority-track' };

      // Adicionar música normal primeiro
      await playbackService.addToQueue('restaurant-123', normalTrack, false);
      
      // Adicionar música prioritária depois
      await playbackService.addToQueue('restaurant-123', priorityTrack, true);

      const nextTrack = await playbackService.getNextTrack('restaurant-123');
      expect(nextTrack?.id).toBe('priority-track');
    });

    it('deve remover música da fila', async () => {
      await playbackService.addToQueue('restaurant-123', mockTrack, false);
      await playbackService.removeFromQueue('restaurant-123', 'track-123');

      const state = await playbackService.getPlaybackState('restaurant-123');
      expect(state?.normalQueue).toHaveLength(0);
    });
  });

  describe('Sistema de Locks', () => {
    it('deve prevenir race conditions em operações concorrentes', async () => {
      mockRestaurantRepository.findOne.mockResolvedValue(mockRestaurant);
      
      // Executar múltiplas operações simultaneamente
      const promises = Array.from({ length: 5 }, (_, i) => 
        playbackService.initializePlayback(`restaurant-${i}`)
      );

      const results = await Promise.all(promises);
      
      // Todos devem ter sucesso sem conflitos
      expect(results).toHaveLength(5);
      results.forEach(state => {
        expect(state).toEqual(
          expect.objectContaining({
            isPlaying: false,
            queue: []
          })
        );
      });
    });

    it('deve liberar locks automaticamente após timeout', async () => {
      // Este teste verifica se o sistema de timeout funciona
      // Em um cenário real, seria difícil de testar sem manipular o tempo
      expect(true).toBe(true); // Placeholder - implementar com jest.useFakeTimers se necessário
    });
  });

  describe('Tratamento de Erros', () => {
    it('deve lançar PlaybackError com código específico', () => {
      const error = new PlaybackError('Test error', 'TEST_ERROR', 400, true, 'restaurant-123', 'track-123');

      expect(error).toBeInstanceOf(PlaybackError);
      expect(error.code).toBe('TEST_ERROR');
      expect(error.statusCode).toBe(400);
      expect(error.restaurantId).toBe('restaurant-123');
      expect(error.trackId).toBe('track-123');
    });

    it('deve lidar com falhas de WebSocket graciosamente', async () => {
      mockRestaurantRepository.findOne.mockResolvedValue(mockRestaurant);
      mockWsService.notifyPlaybackUpdate.mockRejectedValue(new Error('WebSocket error'));

      // Não deve lançar erro mesmo se WebSocket falhar
      await expect(
        playbackService.initializePlayback('restaurant-123')
      ).resolves.not.toThrow();
    });
  });

  describe('Limpeza de Estados', () => {
    it('deve limpar estados inativos automaticamente', async () => {
      mockRestaurantRepository.findOne.mockResolvedValue(mockRestaurant);
      
      await playbackService.initializePlayback('restaurant-123');
      
      // Simular passagem de tempo (seria melhor com jest.useFakeTimers)
      const state = await playbackService.getPlaybackState('restaurant-123');
      if (state) {
        state.lastActivity = new Date(Date.now() - 35 * 60 * 1000); // 35 minutos atrás
      }

      // Executar limpeza manualmente
      (playbackService as any).cleanupInactiveStates();

      // Estado deve ter sido removido
      const cleanedState = await playbackService.getPlaybackState('restaurant-123');
      expect(cleanedState).toBeNull();
    });
  });
});
