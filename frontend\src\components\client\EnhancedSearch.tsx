import React, { useState, useEffect, useRef, useCallback, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Search,
  X,
  TrendingUp,
  History,
  Clock,
  Music,
  Play,
  Plus,
  Loader2,
  Filter,
  Share2,
  Heart,
  Star,
  Volume2,
  AlertCircle,
} from "lucide-react";
import { toast } from "react-hot-toast";
import { apiService } from "../../services/api";

interface Song {
  id: string;
  title: string;
  artist: string;
  duration: number;
  formattedDuration: string;
  thumbnailUrl: string;
  channelName?: string;
  viewCount?: number;
  publishedAt?: string;
  genre?: string;
  mood?: string;
}

interface SearchSuggestion {
  id: string;
  query: string;
  type: "history" | "trending" | "suggestion" | "genre" | "artist";
  count?: number;
  icon?: React.ComponentType<any>;
}

interface ApiResponse {
  success: boolean;
  results?: Song[];
  error?: string;
  total?: number;
}

interface FilterOption {
  id: string;
  name: string;
  color: string;
  category: "genre" | "mood" | "era";
}

interface EnhancedSearchProps {
  onSearch: (query: string, filters?: string[]) => void;
  onSongSelect: (song: Song) => void;
  onSuggestionCreated?: () => void;
  restaurantId: string;
  placeholder?: string;
  showFilters?: boolean;
  initialFilters?: string[];
}

const EnhancedSearch: React.FC<EnhancedSearchProps> = ({
  onSearch,
  onSongSelect,
  onSuggestionCreated,
  restaurantId,
  placeholder = "Buscar música, artista ou álbum...",
  showFilters = true,
  initialFilters = [],
}) => {
  const [query, setQuery] = useState("");
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [searchResults, setSearchResults] = useState<Song[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [activeFilters, setActiveFilters] = useState<string[]>(initialFilters);
  const [showFiltersPanel, setShowFiltersPanel] = useState(false);

  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);

  const debounceRef = useRef<NodeJS.Timeout | null>(null);

  // Carregar histórico de busca
  useEffect(() => {
    try {
      const history = localStorage.getItem(`searchHistory_${restaurantId}`);
      if (history) {
        setSearchHistory(JSON.parse(history));
      }
    } catch (error) {
      console.error("Erro ao carregar histórico de busca:", error);
    }
  }, [restaurantId]);

  // Salvar no histórico
  const saveToHistory = useCallback(
    (searchQuery: string) => {
      if (!searchQuery.trim()) return;
      
      try {
        const newHistory = [
          searchQuery,
          ...searchHistory.filter((h) => h !== searchQuery),
        ].slice(0, 10);
        
        setSearchHistory(newHistory);
        localStorage.setItem(
          `searchHistory_${restaurantId}`,
          JSON.stringify(newHistory)
        );
      } catch (error) {
        console.error("Erro ao salvar histórico de busca:", error);
      }
    },
    [searchHistory, restaurantId]
  );

  // Lista de filtros disponíveis
  const filters = useMemo<FilterOption[]>(() => [
    { id: "rock", name: "Rock", color: "bg-red-100 text-red-800", category: "genre" },
    { id: "pop", name: "Pop", color: "bg-pink-100 text-pink-800", category: "genre" },
    { id: "sertanejo", name: "Sertanejo", color: "bg-yellow-100 text-yellow-800", category: "genre" },
    { id: "mpb", name: "MPB", color: "bg-green-100 text-green-800", category: "genre" },
    { id: "funk", name: "Funk", color: "bg-purple-100 text-purple-800", category: "genre" },
    { id: "eletronica", name: "Eletrônica", color: "bg-blue-100 text-blue-800", category: "genre" },
    { id: "happy", name: "Animado", color: "bg-amber-100 text-amber-800", category: "mood" },
    { id: "calm", name: "Calmo", color: "bg-teal-100 text-teal-800", category: "mood" },
  ], []);

  // Gerar sugestões com base na query atual ou histórico
  const generateSuggestions = useCallback(
    (searchQuery: string) => {
      const suggestionsList: SearchSuggestion[] = [];

      if (!searchQuery.trim()) {
        // Mostrar histórico quando não há busca
        searchHistory.slice(0, 5).forEach((item) => {
          suggestionsList.push({
            id: `history-${item}`,
            query: item,
            type: "history",
            icon: History,
          });
        });

        // Adicionar sugestões trending
        const trending = [
          "Sertanejo",
          "Rock Nacional",
          "Pop Internacional",
          "MPB",
          "Funk",
        ];
        trending.forEach((item) => {
          suggestionsList.push({
            id: `trending-${item}`,
            query: item,
            type: "trending",
            icon: TrendingUp,
          });
        });
      } else {
        // Sugestões baseadas na busca
        const genres = [
          "Rock",
          "Pop",
          "Sertanejo",
          "MPB",
          "Funk",
          "Eletrônica",
          "Jazz",
          "Clássica",
          "Hip Hop",
          "Reggae",
        ];
        const artists = [
          "Marília Mendonça",
          "Gusttavo Lima",
          "Anitta",
          "Caetano Veloso",
          "Legião Urbana",
          "Jorge & Mateus",
          "Henrique & Juliano",
          "Luan Santana",
          "Ed Sheeran",
          "Lady Gaga",
        ];

        // Filtrar gêneros por query
        genres.forEach((genre) => {
          if (genre.toLowerCase().includes(searchQuery.toLowerCase())) {
            suggestionsList.push({
              id: `genre-${genre}`,
              query: genre,
              type: "genre",
              icon: Music,
            });
          }
        });

        // Filtrar artistas por query
        artists.forEach((artist) => {
          if (artist.toLowerCase().includes(searchQuery.toLowerCase())) {
            suggestionsList.push({
              id: `artist-${artist}`,
              query: artist,
              type: "artist",
              icon: Star,
            });
          }
        });

        // Adicionar a query atual como sugestão se não estiver vazia
        if (
          searchQuery.trim() &&
          !suggestionsList.some(
            (s) => s.query.toLowerCase() === searchQuery.toLowerCase()
          )
        ) {
          suggestionsList.push({
            id: `suggestion-${searchQuery}`,
            query: searchQuery,
            type: "suggestion",
            icon: Search,
          });
        }
      }

      setSuggestions(suggestionsList.slice(0, 8));
    },
    [searchHistory]
  );

  // Função para lidar com erros na busca
  const handleSearchError = useCallback((errorMessage: string) => {
    setError(errorMessage);
    setLoading(false);
    toast.error(errorMessage);
    console.error(`Erro na busca: ${errorMessage}`);
  }, []);

  // Buscar músicas na API
  const searchSongs = useCallback(
    async (searchQuery: string) => {
      if (!searchQuery.trim()) return;

      try {
        setLoading(true);
        setError(null);
        setShowResults(true);

        // Construir params da busca
        const queryParams = new URLSearchParams();
        queryParams.append('q', searchQuery);
        
        // Adicionar filtros ativos à query
        if (activeFilters.length > 0) {
          const genreFilters = activeFilters.filter(id => 
            filters.find(f => f.id === id && f.category === 'genre')?.id
          );
          
          const moodFilters = activeFilters.filter(id => 
            filters.find(f => f.id === id && f.category === 'mood')?.id
          );
          
          if (genreFilters.length > 0) {
            queryParams.append('genres', genreFilters.join(','));
          }
          
          if (moodFilters.length > 0) {
            queryParams.append('moods', moodFilters.join(','));
          }
        }

        // Realizar a busca na API
        try {
          const response = await apiService.client.get<ApiResponse>(
            `/restaurants/${restaurantId}/playlist?${queryParams.toString()}`
          );
          
          if (!response.data.success) {
            throw new Error(response.data.error || "Erro desconhecido na busca");
          }
          
          setSearchResults(response.data.results || []);
          
          // Notificar componente pai sobre a busca
          onSearch(searchQuery, activeFilters);
          
          // Salvar no histórico apenas buscas bem-sucedidas
          saveToHistory(searchQuery);
        } catch (apiError) {
          console.warn("Erro na API, usando resultados de fallback:", apiError);
          
          // Fallback para resultados mock
          const mockResults: Song[] = [
            {
              id: "dQw4w9WgXcQ",
              title: "Never Gonna Give You Up",
              artist: "Rick Astley",
              duration: 213,
              formattedDuration: "3:33",
              thumbnailUrl: "https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg",
              genre: "Pop",
              mood: "Happy",
            },
            {
              id: "kJQP7kiw5Fk",
              title: "Despacito",
              artist: "Luis Fonsi ft. Daddy Yankee",
              duration: 281,
              formattedDuration: "4:41",
              thumbnailUrl: "https://img.youtube.com/vi/kJQP7kiw5Fk/mqdefault.jpg",
              genre: "Latin Pop",
              mood: "Energetic",
            },
          ].filter(
            (song) =>
              song.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
              song.artist.toLowerCase().includes(searchQuery.toLowerCase())
          ).filter(song => {
            if (activeFilters.length === 0) return true;
            return activeFilters.some(filter => 
              song.genre?.toLowerCase() === filter.toLowerCase() || song.mood?.toLowerCase() === filter.toLowerCase()
            );
          });

          setSearchResults(mockResults);
          
          saveToHistory(searchQuery);
          onSearch(searchQuery, activeFilters);
        }
      } catch (error) {
        handleSearchError(error instanceof Error ? error.message : "Erro desconhecido na busca");
      } finally {
        setLoading(false);
      }
    },
    [restaurantId, saveToHistory, onSearch, activeFilters, filters, handleSearchError]
  );

  // Lidar com clique fora
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        inputRef.current && 
        !inputRef.current.contains(event.target as Node) &&
        suggestionsRef.current && 
        !suggestionsRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Handlers
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    setSelectedIndex(-1);

    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    debounceRef.current = setTimeout(() => {
      generateSuggestions(value);
      setShowSuggestions(true);
    }, 300);
  };

  const handleSearch = (searchQuery: string = query) => {
    if (!searchQuery.trim()) return;

    setShowSuggestions(false);
    searchSongs(searchQuery);
  };

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setQuery(suggestion.query);
    setShowSuggestions(false);
    handleSearch(suggestion.query);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "ArrowDown") {
      e.preventDefault();
      setSelectedIndex((prev) => Math.min(prev + 1, suggestions.length - 1));
    } else if (e.key === "ArrowUp") {
      e.preventDefault();
      setSelectedIndex((prev) => Math.max(prev - 1, -1));
    } else if (e.key === "Enter") {
      e.preventDefault();
      if (selectedIndex >= 0 && suggestions[selectedIndex]) {
        handleSuggestionClick(suggestions[selectedIndex]);
      } else {
        handleSearch();
      }
    } else if (e.key === "Escape") {
      e.preventDefault();
      if (showSuggestions) {
        setShowSuggestions(false);
      } else if (showResults) {
        setShowResults(false);
      }
    }
  };

  const clearSearch = () => {
    setQuery("");
    setSearchResults([]);
    setShowResults(false);
    setShowSuggestions(false);
    setError(null);
    inputRef.current?.focus();
  };

  const toggleFilter = useCallback((filterId: string) => {
    setActiveFilters(prev => 
      prev.includes(filterId)
        ? prev.filter(f => f !== filterId)
        : [...prev, filterId]
    );
  }, []);

  const handleSongAction = useCallback(
    (song: Song, action: "suggest" | "preview" | "share") => {
      switch (action) {
        case "suggest":
          onSongSelect(song);
          toast.success(`"${song.title}" sugerida com sucesso!`);
          onSuggestionCreated?.();
          break;
          
        case "preview":
          toast("Preview em desenvolvimento", {
            icon: <Volume2 className="w-4 h-4" />,
          });
          break;
          
        case "share":
          const shareUrl = `https://youtube.com/watch?v=${song.id}`;
          if (navigator.share) {
            navigator.share({
              title: song.title,
              text: `Confira "${song.title}" de ${song.artist}`,
              url: shareUrl,
            }).catch(console.error);
          } else {
            navigator.clipboard.writeText(shareUrl)
              .then(() => toast.success("Link copiado!"))
              .catch(() => toast.error("Erro ao copiar link"));
          }
          break;
      }
    },
    [onSongSelect, onSuggestionCreated]
  );

  // Aplicar filtros quando mudam
  useEffect(() => {
    if (query && showResults) {
      searchSongs(query);
    }
  }, [activeFilters]);

  // Limpar debounce ao desmontar
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, []);

  return (
    <div className="relative w-full max-w-3xl mx-auto px-4 py-2 space-y-4">
      {/* Barra de busca */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" aria-hidden="true" />
        
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onFocus={() => {
            generateSuggestions(query);
            setShowSuggestions(true);
          }}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className="w-full pl-10 pr-20 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm text-base"
          aria-label="Buscar músicas"
          aria-controls={showSuggestions ? "search-suggestions" : undefined}
          aria-expanded={showSuggestions}
          aria-autocomplete="list"
          autoComplete="off"
          role="combobox"
        />

        <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center space-x-2">
          {showFilters && (
            <button
              onClick={() => setShowFiltersPanel(!showFiltersPanel)}
              className={`p-2 rounded-lg transition-colors relative ${
                activeFilters.length > 0
                  ? "bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400"
                  : "text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              }`}
              aria-label="Filtros"
              aria-expanded={showFiltersPanel}
              aria-controls="filters-panel"
            >
              <Filter className="w-5 h-5" />
              {activeFilters.length > 0 && (
                <span className="absolute top-0 right-0 bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center translate-x-1/2 -translate-y-1/2">
                  {activeFilters.length}
                </span>
              )}
            </button>
          )}

          {query && (
            <button
              onClick={clearSearch}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg transition-colors"
              aria-label="Limpar busca"
            >
              <X className="w-5 h-5" />
            </button>
          )}

          {loading && (
            <Loader2 className="w-5 h-5 text-blue-500 animate-spin" aria-hidden="true" />
          )}
        </div>
      </div>

      {/* Painel de filtros - Horizontal scroll for mobile */}
      <AnimatePresence>
        {showFiltersPanel && (
          <motion.div
            id="filters-panel"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="overflow-hidden"
          >
            <div className="flex overflow-x-auto space-x-2 pb-2 -mx-4 px-4 scrollbar-hide">
              {filters.map((filter) => (
                <button
                  key={filter.id}
                  onClick={() => toggleFilter(filter.id)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all whitespace-nowrap flex-shrink-0 ${
                    activeFilters.includes(filter.id)
                      ? `${filter.color} dark:bg-opacity-20`
                      : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                  }`}
                  aria-pressed={activeFilters.includes(filter.id)}
                >
                  {filter.name}
                </button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Sugestões de busca */}
      <AnimatePresence>
        {showSuggestions && suggestions.length > 0 && (
          <motion.div
            ref={suggestionsRef}
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 z-40 max-h-96 overflow-y-auto"
            role="listbox"
            aria-label="Sugestões de busca"
          >
            {suggestions.map((suggestion, index) => {
              const Icon = suggestion.icon || Music;
              return (
                <button
                  key={suggestion.id}
                  onClick={() => handleSuggestionClick(suggestion)}
                  onMouseEnter={() => setSelectedIndex(index)}
                  className={`w-full text-left cursor-pointer px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center space-x-3 transition-colors ${
                    index === selectedIndex ? "bg-blue-50 dark:bg-blue-900/20" : ""
                  } ${index < suggestions.length - 1 ? "border-b border-gray-100 dark:border-gray-700" : ""}`}
                  role="option"
                  aria-selected={index === selectedIndex}
                >
                  <Icon className="w-5 h-5 text-gray-400 flex-shrink-0" aria-hidden="true" />
                  <span className="text-gray-900 dark:text-white truncate flex-1">{suggestion.query}</span>
                  {suggestion.type === "history" && (
                    <Clock className="w-4 h-4 text-gray-400 flex-shrink-0" aria-hidden="true" />
                  )}
                  {suggestion.type === "trending" && (
                    <TrendingUp className="w-4 h-4 text-green-500 flex-shrink-0" aria-hidden="true" />
                  )}
                </button>
              );
            })}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Resultados da busca */}
      <AnimatePresence>
        {showResults && (
          <motion.div
            ref={resultsRef}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="space-y-4"
            role="region"
            aria-label="Resultados da busca"
          >
            {error ? (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-6 text-center space-y-3">
                <AlertCircle className="w-12 h-12 text-red-500 mx-auto" aria-hidden="true" />
                <h3 className="text-red-800 dark:text-red-400 font-medium text-lg">Erro na busca</h3>
                <p className="text-red-600 dark:text-red-300 text-sm">{error}</p>
                <button
                  onClick={() => handleSearch(query)}
                  className="px-6 py-3 bg-red-600 text-white rounded-lg text-base hover:bg-red-700 transition-colors w-full max-w-xs mx-auto"
                >
                  Tentar novamente
                </button>
              </div>
            ) : searchResults.length > 0 ? (
              searchResults.map((song) => (
                <motion.div
                  key={song.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700"
                  role="article"
                >
                  <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
                    <img
                      src={song.thumbnailUrl}
                      alt={`Capa de ${song.title}`}
                      className="w-full h-32 sm:w-24 sm:h-16 rounded-lg object-cover"
                      loading="lazy"
                    />

                    <div className="flex-1 space-y-1">
                      <h3 className="font-medium text-gray-900 dark:text-white text-base truncate">{song.title}</h3>
                      <p className="text-gray-600 dark:text-gray-400 text-sm truncate">{song.artist}</p>
                      <div className="flex items-center flex-wrap gap-2">
                        <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                          <Clock className="w-3 h-3 mr-1" aria-hidden="true" />
                          {song.formattedDuration}
                        </span>
                        {song.genre && (
                          <span className="text-xs bg-gray-100 dark:bg-gray-700 px-2 py-0.5 rounded">
                            {song.genre}
                          </span>
                        )}
                        {song.mood && (
                          <span className="text-xs bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 px-2 py-0.5 rounded">
                            {song.mood}
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="flex space-x-2 pt-2 sm:pt-0">
                      <button
                        onClick={() => handleSongAction(song, "preview")}
                        className="p-3 text-gray-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                        title="Ouvir prévia"
                        aria-label={`Ouvir prévia de ${song.title}`}
                      >
                        <Volume2 className="w-5 h-5" aria-hidden="true" />
                      </button>

                      <button
                        onClick={() => handleSongAction(song, "share")}
                        className="p-3 text-gray-400 hover:text-green-500 dark:hover:text-green-400 transition-colors rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                        title="Compartilhar"
                        aria-label={`Compartilhar ${song.title}`}
                      >
                        <Share2 className="w-5 h-5" aria-hidden="true" />
                      </button>

                      <button
                        onClick={() => handleSongAction(song, "suggest")}
                        className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all flex items-center space-x-2 text-base"
                        aria-label={`Sugerir ${song.title}`}
                      >
                        <Plus className="w-5 h-5" aria-hidden="true" />
                        <span>Sugerir</span>
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))
            ) : (
              <div className="text-center py-12 text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 space-y-3">
                <Music className="w-16 h-16 mx-auto opacity-50" aria-hidden="true" />
                <p className="font-medium text-lg">Nenhuma música encontrada</p>
                <p className="text-sm">
                  {activeFilters.length > 0 
                    ? "Tente remover alguns filtros ou buscar por outro termo"
                    : "Tente buscar por outro termo"}
                </p>
                {activeFilters.length > 0 && (
                  <button
                    onClick={() => setActiveFilters([])}
                    className="px-6 py-3 bg-blue-600 text-white rounded-lg text-base hover:bg-blue-700 transition-colors w-full max-w-xs mx-auto"
                  >
                    Limpar filtros
                  </button>
                )}
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default EnhancedSearch;