-- Exemplo de seed manual (execute conscientemente em ambientes de dev)

-- Usuário admin local (não sobrescreve se já existir)
INSERT INTO users (id, email, password, role, created_at, updated_at)
SELECT gen_random_uuid(), 'admin@local', '$2b$10$1m0n9bCw3JrRzX0sZf9oD.oY5h8WZ9GJQeU6s0YF0sN8tZ3nJbK2G', 'admin', NOW(), NOW()
WHERE NOT EXISTS (SELECT 1 FROM users WHERE email='admin@local');

-- Restaurante demo (não duplica)
INSERT INTO restaurants (id, name, slug, "isActive")
SELECT gen_random_uuid(), 'Demo', 'demo', true
WHERE NOT EXISTS (SELECT 1 FROM restaurants WHERE slug='demo');

