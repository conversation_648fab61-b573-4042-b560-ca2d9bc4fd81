import { Router } from "express";
import { body, param, validationResult } from "../utils/validation";
import { AppDataSource } from "../config/database";
import { PlaylistSchedule } from "../models/PlaylistSchedule";
import { Restaurant } from "../models/Restaurant";
import asyncHandler from "../middleware/asyncHandler";
import { ValidationError, NotFoundError } from "../utils/errors";

const router = Router();

/**
 * @route GET /api/v1/schedules/:restaurantId
 * @desc Obter horários de funcionamento do restaurante
 */
router.get(
  "/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    // Verificar se o restaurante existe
    const restaurantRepository = AppDataSource.getRepository(Restaurant);
    const restaurant = await restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      throw new NotFoundError("Restaurante não encontrado");
    }

    // Buscar horários
    const scheduleRepository = AppDataSource.getRepository(PlaylistSchedule);
    const schedules = await scheduleRepository.find({
      where: { restaurant: { id: restaurantId } },
      order: { createdAt: "DESC" },
    });

    res.json({
      success: true,
      schedules: schedules.map((schedule) => ({
        id: schedule.id,
        name: schedule.name,
        description: schedule.description,
        timeSlots: schedule.timeSlots,
        isActive: schedule.isActive,
        mode: schedule.mode,
        settings: schedule.settings,
        createdAt: schedule.createdAt,
        updatedAt: schedule.updatedAt,
      })),
    });
  })
);

/**
 * @route POST /api/v1/schedules/:restaurantId
 * @desc Criar novo horário de funcionamento
 */
router.post(
  "/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("name").notEmpty().withMessage("Nome do horário é obrigatório"),
    body("timeSlots").isArray().withMessage("Horários devem ser um array"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const {
      name,
      description,
      timeSlots,
      mode = "normal",
      settings = {},
    } = req.body;

    // Verificar se o restaurante existe
    const restaurantRepository = AppDataSource.getRepository(Restaurant);
    const restaurant = await restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      throw new NotFoundError("Restaurante não encontrado");
    }

    // Criar novo horário
    const scheduleRepository = AppDataSource.getRepository(PlaylistSchedule);
    const schedule = scheduleRepository.create({
      restaurant,
      name,
      description,
      timeSlots,
      mode,
      settings,
      isActive: true,
    });

    await scheduleRepository.save(schedule);

    res.json({
      success: true,
      message: "Horário criado com sucesso",
      schedule: {
        id: schedule.id,
        name: schedule.name,
        description: schedule.description,
        timeSlots: schedule.timeSlots,
        isActive: schedule.isActive,
        mode: schedule.mode,
        settings: schedule.settings,
        createdAt: schedule.createdAt,
        updatedAt: schedule.updatedAt,
      },
    });
  })
);

/**
 * @route PUT /api/v1/schedules/:restaurantId/:scheduleId
 * @desc Atualizar horário de funcionamento
 */
router.put(
  "/:restaurantId/:scheduleId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    param("scheduleId")
      .isUUID()
      .withMessage("ID do horário deve ser um UUID válido"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { restaurantId, scheduleId } = req.params;
    const { name, description, timeSlots, mode, settings, isActive } = req.body;

    const scheduleRepository = AppDataSource.getRepository(PlaylistSchedule);
    const schedule = await scheduleRepository.findOne({
      where: {
        id: scheduleId,
        restaurant: { id: restaurantId },
      },
    });

    if (!schedule) {
      throw new NotFoundError("Horário não encontrado");
    }

    // Atualizar campos
    if (name !== undefined) schedule.name = name;
    if (description !== undefined) schedule.description = description;
    if (timeSlots !== undefined) schedule.timeSlots = timeSlots;
    if (mode !== undefined) schedule.mode = mode;
    if (settings !== undefined) schedule.settings = settings;
    if (isActive !== undefined) schedule.isActive = isActive;

    await scheduleRepository.save(schedule);

    res.json({
      success: true,
      message: "Horário atualizado com sucesso",
      schedule: {
        id: schedule.id,
        name: schedule.name,
        description: schedule.description,
        timeSlots: schedule.timeSlots,
        isActive: schedule.isActive,
        mode: schedule.mode,
        settings: schedule.settings,
        createdAt: schedule.createdAt,
        updatedAt: schedule.updatedAt,
      },
    });
  })
);

/**
 * @route DELETE /api/v1/schedules/:restaurantId/:scheduleId
 * @desc Deletar horário de funcionamento
 */
router.delete(
  "/:restaurantId/:scheduleId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    param("scheduleId")
      .isUUID()
      .withMessage("ID do horário deve ser um UUID válido"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { restaurantId, scheduleId } = req.params;

    const scheduleRepository = AppDataSource.getRepository(PlaylistSchedule);
    const schedule = await scheduleRepository.findOne({
      where: {
        id: scheduleId,
        restaurant: { id: restaurantId },
      },
    });

    if (!schedule) {
      throw new NotFoundError("Horário não encontrado");
    }

    await scheduleRepository.remove(schedule);

    res.json({
      success: true,
      message: "Horário deletado com sucesso",
    });
  })
);

/**
 * @route GET /api/v1/schedules/:restaurantId/current
 * @desc Obter horário atual ativo
 */
router.get(
  "/:restaurantId/current",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    const scheduleRepository = AppDataSource.getRepository(PlaylistSchedule);
    const now = new Date();
    const currentHour = now.getHours();
    const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.

    // Buscar horários ativos
    const schedules = await scheduleRepository.find({
      where: {
        restaurant: { id: restaurantId },
        isActive: true,
      },
    });

    // Encontrar horário atual
    const currentSchedule = schedules.find((schedule) => {
      if (!schedule.timeSlots || !Array.isArray(schedule.timeSlots))
        return false;

      return schedule.timeSlots.some((slot: any) => {
        if (!slot.days || !slot.startTime || !slot.endTime) return false;

        // Verificar se hoje está nos dias ativos
        if (!slot.days.includes(currentDay)) return false;

        // Verificar horário
        const [startHour] = slot.startTime.split(":").map(Number);
        const [endHour] = slot.endTime.split(":").map(Number);

        return currentHour >= startHour && currentHour < endHour;
      });
    });

    res.json({
      success: true,
      currentSchedule: currentSchedule
        ? {
            id: currentSchedule.id,
            name: currentSchedule.name,
            description: currentSchedule.description,
            mode: currentSchedule.mode,
            settings: currentSchedule.settings,
          }
        : null,
      isOpen: !!currentSchedule,
      currentTime: now.toISOString(),
    });
  })
);

export default router;
