import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Building2,
  DollarSign,
  Calendar,
  Download,
  Filter,
  RefreshCw,
  Eye,
  Activity,
  PieChart,
  LineChart
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import apiService from '@/services/api';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

interface GlobalAnalyticsData {
  totalRestaurants: number;
  activeRestaurants: number;
  totalRevenue: number;
  monthlyRevenue: number;
  totalPayments: number;
  totalVotes: number;
  topRestaurants: Array<{
    id: string;
    name: string;
    revenue: number;
  transactions?: number;
  }>;
  revenueByMonth: Array<{
    month: string;
    revenue: number;
    payments: number;
  }>;
}

const GlobalAnalytics: React.FC = () => {
  const [analyticsData, setAnalyticsData] = useState<GlobalAnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d');
  const [selectedView, setSelectedView] = useState<'overview' | 'revenue' | 'restaurants'>('overview');

  useEffect(() => {
    loadGlobalAnalytics();
  }, [timeRange]);

  const loadGlobalAnalytics = async () => {
    try {
      setLoading(true);
      const { data } = await apiService.client.get('/admin/analytics');
      if (data?.success && data.analytics?.overview) {
        const ov = data.analytics.overview;
        const top = data.analytics.topRestaurants || [];
        setAnalyticsData({
          totalRestaurants: ov.totalRestaurants ?? 0,
          activeRestaurants: ov.activeRestaurants ?? 0,
          totalRevenue: ov.totalRevenue ?? 0,
          monthlyRevenue: ov.monthlyGrowth ?? 0,
          totalPayments: ov.totalPayments ?? 0,
          totalVotes: ov.totalVotes ?? 0,
          topRestaurants: top.map((r: any) => ({
            id: r.id,
            name: r.name,
            revenue: r.revenue ?? 0,
            transactions: r.transactions ?? r.payments ?? 0,
          })),
          revenueByMonth: [],
        });
      } else {
        throw new Error('Resposta inválida do servidor');
      }
    } catch (error) {
      console.error('Erro ao carregar analytics globais:', error);
      toast.error('Erro ao carregar dados de analytics');
    } finally {
      setLoading(false);
    }
  };

  const exportData = async () => {
    try {
      toast.success('Exportando dados...');
      // TODO: Implementar exportação
    } catch (error) {
      toast.error('Erro ao exportar dados');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Nenhum dado disponível</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Analytics Globais
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Métricas consolidadas de todos os restaurantes
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* Time Range Selector */}
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
          >
            <option value="7d">Últimos 7 dias</option>
            <option value="30d">Últimos 30 dias</option>
            <option value="90d">Últimos 90 dias</option>
          </select>
          
          <button
            onClick={exportData}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            title="Exportar dados de analytics"
          >
            <Download className="w-4 h-4" />
            <span>Exportar</span>
          </button>
          
          <button
            onClick={loadGlobalAnalytics}
            disabled={loading}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Recarregar dados de analytics"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span>Recarregar</span>
          </button>
          
          <button
            onClick={() => setSelectedView('revenue')}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            <PieChart className="w-4 h-4" />
            <span>Relatório</span>
          </button>
        </div>
      </div>

      {/* View Selector */}
      <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
        {[
          { key: 'overview', label: 'Visão Geral', icon: BarChart3 },
          { key: 'revenue', label: 'Receitas', icon: DollarSign },
          { key: 'restaurants', label: 'Restaurantes', icon: Building2 },
        ].map((view) => (
          <button
            key={view.key}
            onClick={() => setSelectedView(view.key as any)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              selectedView === view.key
                ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            <view.icon className="w-4 h-4" />
            <span>{view.label}</span>
          </button>
        ))}
      </div>

      {/* Overview Stats */}
      {selectedView === 'overview' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[
            {
              title: 'Total de Restaurantes',
              value: analyticsData.totalRestaurants,
              subtitle: `${analyticsData.activeRestaurants} ativos`,
              icon: Building2,
              color: 'text-blue-600',
              bgColor: 'bg-blue-100 dark:bg-blue-900/20',
            },
            {
              title: 'Receita Total',
              value: `R$ ${analyticsData.totalRevenue.toFixed(2)}`,
              subtitle: `+R$ ${analyticsData.monthlyRevenue.toFixed(2)} este mês`,
              icon: DollarSign,
              color: 'text-green-600',
              bgColor: 'bg-green-100 dark:bg-green-900/20',
            },
            {
              title: 'Pagamentos (Supervoto)',
              value: analyticsData.totalPayments.toLocaleString(),
              subtitle: 'Últimos 30 dias',
              icon: DollarSign,
              color: 'text-purple-600',
              bgColor: 'bg-purple-100 dark:bg-purple-900/20',
            },
            {
              title: 'Votos Totais',
              value: analyticsData.totalVotes.toLocaleString(),
              subtitle: 'Engajamento na plataforma',
              icon: Activity,
              color: 'text-yellow-600',
              bgColor: 'bg-yellow-100 dark:bg-yellow-900/20',
            },
          ].map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="card p-6"
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                    {stat.title}
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {stat.value}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {stat.subtitle}
                  </p>
                </div>
                <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                  <stat.icon className={`w-6 h-6 ${stat.color}`} />
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {/* Top Restaurants */}
      {selectedView === 'restaurants' && (
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Top Restaurantes por Receita
          </h3>
          <div className="space-y-4">
            {analyticsData.topRestaurants.map((restaurant, index) => (
              <div
                key={restaurant.id}
                className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg"
              >
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-8 h-8 bg-blue-100 dark:bg-blue-900/20 text-blue-600 rounded-full text-sm font-bold">
                    {index + 1}
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      {restaurant.name}
                    </h4>
                    {/* Informação opcional de transações, quando disponível */}
                    {typeof restaurant.transactions === 'number' && (
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {restaurant.transactions} transações
                      </p>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900 dark:text-white">
                    R$ {restaurant.revenue.toFixed(2)}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    receita
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Revenue View */}
      {selectedView === 'revenue' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Receita por Mês
            </h3>
            <div className="space-y-3">
              {analyticsData.revenueByMonth.map((month) => (
                <div key={month.month} className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">{month.month}</span>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900 dark:text-white">
                      R$ {month.revenue.toFixed(2)}
                    </p>
                    <p className="text-sm text-gray-500">{month.payments} pagamentos</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
          {/* Card secundário opcional poderá exibir outros relatórios no futuro */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Atividade de Pagamentos
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Em breve: distribuição por método de pagamento, horários de pico e mais.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default GlobalAnalytics;
