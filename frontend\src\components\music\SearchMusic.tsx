import React, { useState, useCallback, useRef } from "react";
import { useQuery, useMutation } from "react-query";
import { motion, AnimatePresence } from "framer-motion";
import {
  Search,
  Plus,
  Play,
  Clock,
  Eye,
  AlertCircle,
  AlertTriangle,
} from "lucide-react";
import { toast } from "react-hot-toast";

// Componentes
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import Button from "@/components/ui/Button";

// Hooks e serviços
import { apiService, apiQueries } from "@/services/api";
import { useAppStore } from "@/store";

// Tipos
import { VideoInfo, SearchResult } from "@/types";

interface SearchMusicProps {
  restaurantId: string;
  onSuggestionCreated?: () => void;
}

const SearchMusic: React.FC<SearchMusicProps> = ({
  restaurantId,
  onSuggestionCreated,
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  // const [selectedVideo, setSelectedVideo] = useState<VideoInfo | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  // const [showHistory, setShowHistory] = useState(false);
  const [useYouTubeAPI, setUseYouTubeAPI] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);

  const { setLoading } = useAppStore();

  // Query para busca no YouTube
  const {
    data: searchResults,
    isLoading: isSearching,
    error: searchError,
    refetch: performSearch,
  } = useQuery<SearchResult>({
    ...apiQueries.youtubeSearch(
      searchQuery,
      restaurantId,
      10,
      undefined,
      useYouTubeAPI
    ),
    enabled: false, // Busca manual
  });

  // Mutation para criar sugestão
  const createSuggestionMutation = useMutation(
    (videoId: string) =>
      apiService.createSuggestion({
        youtubeVideoId: videoId,
        restaurantId,
      }),
    {
      onSuccess: () => {
        toast.success("Música sugerida com sucesso!");
        setSearchQuery("");
        onSuggestionCreated?.();
        // A API retorna { suggestion }; moderação automática pode não estar disponível.
        // Detalhes adicionais chegam via WebSocket (evento "new-suggestion").
      },
      onError: (error: any) => {
        const message =
          error.response?.data?.message || "Erro ao sugerir música";
        toast.error(message);
      },
      onSettled: () => {
        setIsSubmitting(false);
        setLoading("suggestions", false);
      },
    }
  );

  // Carregar histórico de busca do localStorage
  React.useEffect(() => {
    const savedHistory = localStorage.getItem("searchHistory");
    if (savedHistory) {
      setSearchHistory(JSON.parse(savedHistory));
    }
  }, []);

  // Salvar no histórico
  const addToHistory = useCallback(
    (query: string) => {
      const newHistory = [
        query,
        ...searchHistory.filter((h) => h !== query),
      ].slice(0, 10);
      setSearchHistory(newHistory);
      localStorage.setItem("searchHistory", JSON.stringify(newHistory));
    },
    [searchHistory]
  );

  // Função para realizar busca
  const handleSearch = useCallback(async () => {
    if (!searchQuery.trim() || searchQuery.length < 2) {
      toast.error("Digite pelo menos 2 caracteres para buscar");
      return;
    }

  addToHistory(searchQuery.trim());

    setLoading("search", true);
    try {
      await performSearch();
    } catch (error) {
      toast.error("Erro ao buscar músicas");
    } finally {
      setLoading("search", false);
    }
  }, [searchQuery, performSearch, setLoading, addToHistory]);

  // Função para sugerir música
  const handleSuggestMusic = useCallback(
    async (video: VideoInfo) => {
      setIsSubmitting(true);
      setLoading("suggestions", true);
      createSuggestionMutation.mutate(video.youtubeVideoId);
    },
    [createSuggestionMutation, setLoading]
  );

  // Função para formatar duração
  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  return (
    <div className="space-y-6">
      {/* Toggle para escolher fonte de busca */}
      <div className="card p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-sm font-medium text-gray-900 dark:text-white">
              Fonte de Busca
            </h3>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              {useYouTubeAPI
                ? "Buscar em todo o YouTube (requer API key)"
                : "Buscar apenas na playlist do restaurante (recomendado)"}
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={useYouTubeAPI}
              onChange={(e) => setUseYouTubeAPI(e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>

        {useYouTubeAPI && (
          <div className="mt-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <div className="flex items-start space-x-2">
              <AlertTriangle className="w-4 h-4 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
              <div className="text-xs text-yellow-800 dark:text-yellow-200">
                <p className="font-medium">Busca no YouTube ativada</p>
                <p>
                  Esta opção busca em todo o YouTube. Para uso em produção,
                  configure uma API key válida no arquivo .env
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Barra de busca */}
      <div className="card p-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Buscar Música
        </h2>

        <div className="flex space-x-3">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              ref={searchInputRef}
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === "Enter" && handleSearch()}
              placeholder="Digite o nome da música ou artista..."
              className="input pl-10"
              disabled={isSearching}
            />
          </div>

          <Button
            onClick={handleSearch}
            disabled={isSearching || !searchQuery.trim()}
            className="btn-primary"
          >
            {isSearching ? (
              <LoadingSpinner size="sm" />
            ) : (
              <>
                <Search className="w-4 h-4 mr-2" />
                Buscar
              </>
            )}
          </Button>
        </div>

        {/* Dicas de busca */}
        <div className="mt-3 text-sm text-gray-600 dark:text-gray-400">
          💡 Dica: Tente buscar por "artista - música" para melhores resultados
        </div>
      </div>

      {/* Resultados da busca */}
      <AnimatePresence>
        {!!searchResults && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-4"
          >
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Resultados da Busca
              </h3>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {searchResults.videos.length} resultados
              </span>
            </div>

            {searchResults.videos.length === 0 ? (
              <div className="card p-8 text-center">
                <div className="w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
                  <Search className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  Nenhum resultado encontrado
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Tente buscar com termos diferentes ou verifique a ortografia.
                </p>
              </div>
            ) : (
              <div className="grid gap-4">
                {searchResults.videos.map((video) => (
                  <motion.div
                    key={video.youtubeVideoId}
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="music-card"
                  >
                    <div className="flex space-x-4">
                      {/* Thumbnail */}
                      <div className="flex-shrink-0">
                        <div className="relative w-24 h-18 rounded-lg overflow-hidden bg-gray-200 dark:bg-gray-700">
                          <img
                            src={video.thumbnailUrl}
                            alt={video.title}
                            className="w-full h-full object-cover"
                            loading="lazy"
                          />
                          <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                            <Play className="w-6 h-6 text-white" />
                          </div>
                        </div>
                      </div>

                      {/* Informações */}
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-gray-900 dark:text-white truncate">
                          {video.title}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                          {video.artist}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-500 truncate">
                          {video.channelName}
                        </p>

                        <div className="flex items-center flex-wrap gap-x-4 gap-y-1 mt-2 text-xs text-gray-500 dark:text-gray-500">
                          <div className="flex items-center space-x-1">
                            <Clock className="w-3 h-3 flex-shrink-0" />
                            <span>{formatDuration(video.duration)}</span>
                          </div>

                          <div className="flex items-center space-x-1">
                            <Eye className="w-3 h-3 flex-shrink-0" />
                            <span>
                              {video.metadata.viewCount.toLocaleString()} views
                            </span>
                          </div>

                          {video.metadata.explicit && (
                            <div className="flex items-center space-x-1 text-red-500">
                              <AlertCircle className="w-3 h-3 flex-shrink-0" />
                              <span>Explícito</span>
                            </div>
                          )}

                          {video.metadata.live && (
                            <div className="flex items-center space-x-1 text-red-500">
                              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse flex-shrink-0" />
                              <span>Ao vivo</span>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Botão de sugerir */}
                      <div className="flex-shrink-0">
                        <Button
                          onClick={() => handleSuggestMusic(video)}
                          disabled={isSubmitting}
                          className="btn-primary btn-sm"
                        >
                          {isSubmitting ? (
                            <LoadingSpinner size="sm" />
                          ) : (
                            <>
                              <Plus className="w-4 h-4 mr-1" />
                              Sugerir
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Erro de busca */}
  {!!searchError && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="card p-6 border-red-200 dark:border-red-800"
        >
          <div className="flex items-center space-x-3 text-red-600 dark:text-red-400">
            <AlertCircle className="w-5 h-5" />
            <span>Erro ao buscar músicas. Tente novamente.</span>
          </div>
        </motion.div>
      )}

      {/* Informações sobre quota */}
      {searchResults && (
        <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
          Quota da API: {searchResults.quotaRemaining} requisições restantes
        </div>
      )}
    </div>
  );
};

export default SearchMusic;
