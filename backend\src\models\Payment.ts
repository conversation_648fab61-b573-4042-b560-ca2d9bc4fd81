import {
  <PERSON><PERSON><PERSON>,
  PrimaryColumn,
  Column,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  BeforeInsert,
  BeforeUpdate,
} from "typeorm";
import { Suggestion } from "./Suggestion";
import { validate as uuidValidate, v4 as uuidv4 } from "uuid";

@Entity("payments")

export class Payment {
  @PrimaryColumn({ type: "varchar" })
  id: string; // ID do Mercado Pago

  @Column({ name: "suggestion_id", type: "uuid" })
  suggestionId: string;

  @ManyToOne(() => Suggestion, { onDelete: "CASCADE" })
  @JoinColumn({ name: "suggestion_id" })
  suggestion?: Suggestion;

  @Column({ name: "session_id", type: "uuid" })
  sessionId: string;

  @Column({ type: "integer" })
  amount: number; // Valor em centavos

  @Column({ type: "varchar", default: "pending" })
  status: "pending" | "approved" | "rejected" | "cancelled";

  @Column({ name: "status_detail", type: "varchar", nullable: true })
  statusDetail?: string;

  @Column({ name: "payment_method", type: "varchar", default: "pix" })
  paymentMethod: string;

  @Column({ name: "external_reference", type: "varchar", nullable: true })
  externalReference?: string;

  // Dados do Pix
  @Column({ name: "qr_code", type: "text", nullable: true })
  qrCode?: string;

  @Column({ name: "qr_code_base64", type: "text", nullable: true })
  qrCodeBase64?: string;

  @Column({ name: "ticket_url", type: "varchar", nullable: true })
  ticketUrl?: string;

  // Timestamps
  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at" })
  updatedAt: Date;

  @Column({ name: "approved_at", type: "timestamp", nullable: true })
  approvedAt?: Date;

  @Column({ name: "expires_at", type: "timestamp", nullable: true })
  expiresAt?: Date;

  // Dados do pagador
  @Column({ name: "payer_email", type: "varchar", nullable: true })
  payerEmail?: string;

  @Column({ name: "payer_name", type: "varchar", nullable: true })
  payerName?: string;

  // Dados de comissão
  @Column({ name: "platform_fee", type: "integer", default: 60 }) // 30% de R$ 2,00 = R$ 0,60 = 60 centavos
  platformFee: number;

  @Column({ name: "restaurant_amount", type: "integer", default: 140 }) // 70% de R$ 2,00 = R$ 1,40 = 140 centavos
  restaurantAmount: number;

  // Metadados
  @Column({ type: "json", nullable: true })
  metadata?: {
    songTitle?: string;
    artist?: string;
    clientName?: string;
    tableName?: string;
    restaurantName?: string;
    paymentSource?: string; // 'web', 'mobile', etc.
    userAgent?: string;
    ipAddress?: string;
  };

  // Métodos auxiliares
  @BeforeInsert()
  @BeforeUpdate()
  ensureValidSessionId() {
    if (!this.sessionId || !uuidValidate(this.sessionId)) {
      this.sessionId = uuidv4();
    }
  }

  getAmountInReais(): number {
    return this.amount / 100;
  }

  getPlatformFeeInReais(): number {
    return this.platformFee / 100;
  }

  getRestaurantAmountInReais(): number {
    return this.restaurantAmount / 100;
  }

  isExpired(): boolean {
    if (!this.expiresAt) return false;
    return new Date() > this.expiresAt;
  }

  isPending(): boolean {
    return this.status === "pending" && !this.isExpired();
  }

  isApproved(): boolean {
    return this.status === "approved";
  }

  isRejected(): boolean {
    return this.status === "rejected";
  }

  isCancelled(): boolean {
    return this.status === "cancelled";
  }

  getStatusMessage(): string {
    switch (this.status) {
      case "pending":
        return this.isExpired() ? "Pagamento expirado" : "Aguardando pagamento";
      case "approved":
        return "Pagamento aprovado";
      case "rejected":
        return "Pagamento rejeitado";
      case "cancelled":
        return "Pagamento cancelado";
      default:
        return "Status desconhecido";
    }
  }

  getFormattedAmount(): string {
    return `R$ ${this.getAmountInReais().toFixed(2).replace(".", ",")}`;
  }

  getPaymentSummary() {
    return {
      id: this.id,
      amount: this.getAmountInReais(),
      formattedAmount: this.getFormattedAmount(),
      status: this.status,
      statusMessage: this.getStatusMessage(),
      paymentMethod: this.paymentMethod,
      createdAt: this.createdAt,
      approvedAt: this.approvedAt,
      isExpired: this.isExpired(),
      isPending: this.isPending(),
      isApproved: this.isApproved(),
      metadata: this.metadata,
    };
  }

  // Método para criar pagamento
  static createPayment(data: {
    id: string;
    suggestionId: string;
    sessionId: string;
    amount?: number;
    qrCode?: string;
    qrCodeBase64?: string;
    ticketUrl?: string;
    expiresAt?: Date;
    metadata?: any;
  }): Payment {
    const payment = new Payment();
    
    payment.id = String(data.id);
    payment.suggestionId = data.suggestionId;
    // Normaliza aqui também (além do hook) para consistência em runtime
    payment.sessionId = uuidValidate(data.sessionId)
      ? data.sessionId
      : uuidv4();
    payment.amount = data.amount || 200; // R$ 2,00 padrão
    payment.status = "pending";
    payment.paymentMethod = "pix";
    payment.qrCode = data.qrCode;
    payment.qrCodeBase64 = data.qrCodeBase64;
    payment.ticketUrl = data.ticketUrl;
    payment.expiresAt = data.expiresAt || new Date(Date.now() + 30 * 60 * 1000); // 30 minutos
    payment.metadata = data.metadata;
    
    // Calcular comissões (30% plataforma, 70% restaurante)
    payment.platformFee = Math.round(payment.amount * 0.3);
    payment.restaurantAmount = payment.amount - payment.platformFee;
    
    return payment;
  }

  // Método para atualizar status
  updateStatus(status: Payment["status"], statusDetail?: string, approvedAt?: Date) {
    this.status = status;
    this.statusDetail = statusDetail;
    
    if (status === "approved" && approvedAt) {
      this.approvedAt = approvedAt;
    }
    
    this.updatedAt = new Date();
  }

  // Método para verificar se pode ser cancelado
  canBeCancelled(): boolean {
    return this.status === "pending" && !this.isExpired();
  }

  // Método para obter dados do QR Code
  getQRCodeData() {
    if (!this.qrCode && !this.qrCodeBase64) {
      return null;
    }

    return {
      qrCode: this.qrCode,
      qrCodeBase64: this.qrCodeBase64,
      ticketUrl: this.ticketUrl,
      amount: this.getAmountInReais(),
      formattedAmount: this.getFormattedAmount(),
      expiresAt: this.expiresAt,
      isExpired: this.isExpired(),
    };
  }

  // Método para JSON público (sem dados sensíveis)
  toPublicJSON() {
    return {
      id: this.id,
      suggestionId: this.suggestionId,
      amount: this.getAmountInReais(),
      formattedAmount: this.getFormattedAmount(),
      status: this.status,
      statusMessage: this.getStatusMessage(),
      paymentMethod: this.paymentMethod,
      createdAt: this.createdAt,
      approvedAt: this.approvedAt,
      expiresAt: this.expiresAt,
      isExpired: this.isExpired(),
      isPending: this.isPending(),
      isApproved: this.isApproved(),
      qrCodeData: this.getQRCodeData(),
      metadata: {
        songTitle: this.metadata?.songTitle,
        artist: this.metadata?.artist,
        clientName: this.metadata?.clientName,
        tableName: this.metadata?.tableName,
      },
    };
  }
}
