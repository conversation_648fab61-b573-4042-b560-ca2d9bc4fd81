import * as winston from 'winston';
import * as path from 'path';

// Configuração de formatos
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.prettyPrint()
);

// Configuração para desenvolvimento
const developmentFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let msg = `${timestamp} [${level}]: ${message}`;
    if (Object.keys(meta).length > 0) {
      msg += ` ${JSON.stringify(meta)}`;
    }
    return msg;
  })
);

// Criar diretório de logs se não existir
const logsDir = path.join(process.cwd(), 'logs');

// Configuração dos transportes
const transports: winston.transport[] = [
  // Console sempre ativo
  new winston.transports.Console({
    format: process.env.NODE_ENV === 'production' ? logFormat : developmentFormat,
    level: process.env.LOG_LEVEL || 'info'
  })
];

// Em produção, adicionar arquivos de log
if (process.env.NODE_ENV === 'production') {
  transports.push(
    // Log geral
    new winston.transports.File({
      filename: path.join(logsDir, 'app.log'),
      format: logFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    // Log de erros
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      format: logFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    })
  );
}

// Criar logger
export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  transports,
  // Não sair em caso de erro
  exitOnError: false,
});

// Função para log de requisições HTTP
export const logRequest = (req: any, res: any, responseTime: number) => {
  const logData = {
    method: req.method,
    url: req.originalUrl,
    statusCode: res.statusCode,
    responseTime: `${responseTime}ms`,
    userAgent: req.get('User-Agent'),
    ip: req.ip || req.connection.remoteAddress,
    userId: req.user?.id || 'anonymous'
  };

  if (res.statusCode >= 400) {
    logger.error('HTTP Request Error', logData);
  } else {
    logger.info('HTTP Request', logData);
  }
};

// Função para log de operações do banco de dados
export const logDatabase = (operation: string, table: string, duration: number, error?: any) => {
  const logData = {
    operation,
    table,
    duration: `${duration}ms`
  };

  if (error) {
    logger.error('Database Error', { ...logData, error: error.message });
  } else {
    logger.debug('Database Operation', logData);
  }
};

// Função para log de operações do YouTube API
export const logYouTubeAPI = (operation: string, query?: string, quotaUsed?: number, error?: any) => {
  const logData = {
    operation,
    query,
    quotaUsed,
    timestamp: new Date().toISOString()
  };

  if (error) {
    logger.error('YouTube API Error', { ...logData, error: error.message });
  } else {
    logger.info('YouTube API Operation', logData);
  }
};

// Função para log de eventos de WebSocket
export const logWebSocket = (event: string, socketId: string, data?: any) => {
  logger.info('WebSocket Event', {
    event,
    socketId,
    data,
    timestamp: new Date().toISOString()
  });
};

// Função para log de moderação
export const logModeration = (action: string, suggestionId: string, moderatorId: string, reason?: string) => {
  logger.info('Moderation Action', {
    action,
    suggestionId,
    moderatorId,
    reason,
    timestamp: new Date().toISOString()
  });
};

// Função para log de analytics
export const logAnalytics = (event: string, restaurantId: string, data: any) => {
  logger.info('Analytics Event', {
    event,
    restaurantId,
    data,
    timestamp: new Date().toISOString()
  });
};

// Stream para Morgan
export const morganStream = {
  write: (message: string) => {
    logger.info(message.trim());
  }
};

export default logger;
