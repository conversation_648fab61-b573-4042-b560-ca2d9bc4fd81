{"version": 3, "file": "sw.js", "sources": ["../../../AppData/Local/Temp/c87e782b4574f95f536c0d44c1ea24ec/sw.js"], "sourcesContent": ["import {registerRoute as workbox_routing_registerRoute} from 'C:/Users/<USER>/Desktop/YOUTUBE/frontend/node_modules/workbox-routing/registerRoute.mjs';\nimport {ExpirationPlugin as workbox_expiration_ExpirationPlugin} from 'C:/Users/<USER>/Desktop/YOUTUBE/frontend/node_modules/workbox-expiration/ExpirationPlugin.mjs';\nimport {NetworkFirst as workbox_strategies_NetworkFirst} from 'C:/Users/<USER>/Desktop/YOUTUBE/frontend/node_modules/workbox-strategies/NetworkFirst.mjs';\nimport {CacheFirst as workbox_strategies_CacheFirst} from 'C:/Users/<USER>/Desktop/YOUTUBE/frontend/node_modules/workbox-strategies/CacheFirst.mjs';\nimport {clientsClaim as workbox_core_clientsClaim} from 'C:/Users/<USER>/Desktop/YOUTUBE/frontend/node_modules/workbox-core/clientsClaim.mjs';\nimport {precacheAndRoute as workbox_precaching_precacheAndRoute} from 'C:/Users/<USER>/Desktop/YOUTUBE/frontend/node_modules/workbox-precaching/precacheAndRoute.mjs';\nimport {cleanupOutdatedCaches as workbox_precaching_cleanupOutdatedCaches} from 'C:/Users/<USER>/Desktop/YOUTUBE/frontend/node_modules/workbox-precaching/cleanupOutdatedCaches.mjs';\nimport {NavigationRoute as workbox_routing_NavigationRoute} from 'C:/Users/<USER>/Desktop/YOUTUBE/frontend/node_modules/workbox-routing/NavigationRoute.mjs';\nimport {createHandlerBoundToURL as workbox_precaching_createHandlerBoundToURL} from 'C:/Users/<USER>/Desktop/YOUTUBE/frontend/node_modules/workbox-precaching/createHandlerBoundToURL.mjs';/**\n * Welcome to your Workbox-powered service worker!\n *\n * You'll need to register this file in your web app.\n * See https://goo.gl/nhQhGp\n *\n * The rest of the code is auto-generated. Please don't update this file\n * directly; instead, make changes to your Workbox build configuration\n * and re-run your build process.\n * See https://goo.gl/2aRDsh\n */\n\n\n\n\n\n\n\n\nself.skipWaiting();\n\nworkbox_core_clientsClaim();\n\n\n/**\n * The precacheAndRoute() method efficiently caches and responds to\n * requests for URLs in the manifest.\n * See https://goo.gl/S9QRab\n */\nworkbox_precaching_precacheAndRoute([\n  {\n    \"url\": \"assets/EnhancedRestaurantProfile-39002e11.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/GenreManager-afd8429c.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/index-3b37f57e.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/index-521e0fa8.css\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/MusicPlayer-bc235a29.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/PlaylistManager-87d19449.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/ProblematicTracksAlert-71e8e181.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/QRCodeManager-2f65a890.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/RestaurantProfile-d1c07026.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/RestaurantSettings-dec5b9cc.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/router-f729e475.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/ui-1cb796d3.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/UnifiedAnalytics-a45d3510.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/utils-08f61814.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/vendor-66b0ef43.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/YouTubeAuthManager-d052a128.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"favicon.svg\",\n    \"revision\": \"c96e9c7ecec474350501d8a935b938d5\"\n  },\n  {\n    \"url\": \"icons/icon-144x144.png\",\n    \"revision\": \"c9df239901a3fed8c62afd267b59f293\"\n  },\n  {\n    \"url\": \"icons/icon-144x144.svg\",\n    \"revision\": \"81c9cbf42df0146e58c7a51443275f69\"\n  },\n  {\n    \"url\": \"index.html\",\n    \"revision\": \"9ff87c88885a197b7ba5392541218421\"\n  },\n  {\n    \"url\": \"registerSW.js\",\n    \"revision\": \"1872c500de691dce40960bb85481de07\"\n  },\n  {\n    \"url\": \"manifest.webmanifest\",\n    \"revision\": \"8a04d6eb7deb84e16ad45a8c4051427d\"\n  }\n], {});\nworkbox_precaching_cleanupOutdatedCaches();\nworkbox_routing_registerRoute(new workbox_routing_NavigationRoute(workbox_precaching_createHandlerBoundToURL(\"/index.html\")));\n\n\nworkbox_routing_registerRoute(/^https:\\/\\/api\\.*/i, new workbox_strategies_NetworkFirst({ \"cacheName\":\"api-cache\", plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 100, maxAgeSeconds: 86400 })] }), 'GET');\nworkbox_routing_registerRoute(/^https:\\/\\/i\\.ytimg\\.com\\/.*/i, new workbox_strategies_CacheFirst({ \"cacheName\":\"youtube-thumbnails\", plugins: [new workbox_expiration_ExpirationPlugin({ maxEntries: 200, maxAgeSeconds: 604800 })] }), 'GET');\n\n\n\n\n"], "names": ["self", "skipWaiting", "workbox_core_clientsClaim", "workbox_precaching_precacheAndRoute", "url", "revision", "workbox_precaching_cleanupOutdatedCaches", "workbox", "registerRoute", "workbox_routing_NavigationRoute", "workbox_precaching_createHandlerBoundToURL", "workbox_routing_registerRoute", "workbox_strategies_NetworkFirst", "cacheName", "plugins", "workbox_expiration_ExpirationPlugin", "maxEntries", "maxAgeSeconds", "workbox_strategies_CacheFirst"], "mappings": "inBA2BAA,KAAKC,cAELC,EAAAA,eAQAC,EAAAA,iBAAoC,CAClC,CACEC,IAAO,+CACPC,SAAY,MAEd,CACED,IAAO,kCACPC,SAAY,MAEd,CACED,IAAO,2BACPC,SAAY,MAEd,CACED,IAAO,4BACPC,SAAY,MAEd,CACED,IAAO,iCACPC,SAAY,MAEd,CACED,IAAO,qCACPC,SAAY,MAEd,CACED,IAAO,4CACPC,SAAY,MAEd,CACED,IAAO,mCACPC,SAAY,MAEd,CACED,IAAO,uCACPC,SAAY,MAEd,CACED,IAAO,wCACPC,SAAY,MAEd,CACED,IAAO,4BACPC,SAAY,MAEd,CACED,IAAO,wBACPC,SAAY,MAEd,CACED,IAAO,sCACPC,SAAY,MAEd,CACED,IAAO,2BACPC,SAAY,MAEd,CACED,IAAO,4BACPC,SAAY,MAEd,CACED,IAAO,wCACPC,SAAY,MAEd,CACED,IAAO,cACPC,SAAY,oCAEd,CACED,IAAO,yBACPC,SAAY,oCAEd,CACED,IAAO,yBACPC,SAAY,oCAEd,CACED,IAAO,aACPC,SAAY,oCAEd,CACED,IAAO,gBACPC,SAAY,oCAEd,CACED,IAAO,uBACPC,SAAY,qCAEb,CAAE,GACLC,EAAAA,wBAC6BC,EAAAC,cAAC,IAAIC,EAAAA,gBAAgCC,EAAAA,wBAA2C,iBAG7GC,EAAAA,cAA8B,qBAAsB,IAAIC,eAAgC,CAAEC,UAAY,YAAaC,QAAS,CAAC,IAAIC,mBAAoC,CAAEC,WAAY,IAAKC,cAAe,WAAc,OACrNN,EAAAA,cAA8B,gCAAiC,IAAIO,aAA8B,CAAEL,UAAY,qBAAsBC,QAAS,CAAC,IAAIC,mBAAoC,CAAEC,WAAY,IAAKC,cAAe,YAAe"}