import React, { useState, useEffect, useCallback, useRef, useMemo } from "react";
import { useParams } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { useRestaurantContext } from "./RestaurantDashboard";
import { buildApiUrl, getAuthHeaders } from "../../config/api";
import { useWebSocket } from "@/services/websocket";
import { Play, Pause, SkipForward, Volume2, VolumeX, Music, Clock, Users, TrendingUp, AlertTriangle, RefreshCw, Loader2, X, Shuffle, Repeat, Settings2, ArrowUp, ArrowDown, Lock, Unlock, Trash2, DollarSign, Gift, Zap, ThumbsUp, ExternalLink, Activity, ChevronUp } from "lucide-react";
import { toast, Toaster } from "react-hot-toast";
import type { WebSocketEvents, ConnectionStatus } from "@/types";

// Tipos auxiliares reutilizáveis
type LocalSettings = { shuffle: boolean; repeat: boolean; autoPlay: boolean; crossfade: number; lockVoting: boolean };
type LocalSettingsKey = keyof LocalSettings;

interface Track {
  id: string;
  title: string;
  artist: string;
  youtubeVideoId: string;
  duration: number;
  thumbnailUrl: string;
  upvotes: number;
  downvotes: number;
  score: number;
  suggestedBy?: string;
  createdAt: Date;
  isPaid?: boolean;
}

interface RankingItem {
  youtubeVideoId: string;
  title?: string;
  artist?: string;
  voteCount: number;
  superVoteCount: number;
  normalVoteCount: number;
  totalRevenue: number;
  isPaid: boolean;
  paymentAmount: number;
  tableNumber?: number;
}

interface PlaybackState {
  currentTrack: Track | null;
  isPlaying: boolean;
  currentTime: number;
  volume: number;
  queue: Track[];
  priorityQueue: Track[];
  normalQueue: Track[];
  history: Track[];
  estimatedWaitTime?: number;
  // Alinhar com ConnectionStatus do types.ts
  connectionStatus?: ConnectionStatus;
}

interface PlaybackCacheData {
  data: any;
  timestamp: number;
  ttl: number;
}

class PlaybackCache {
  private static CACHE_PREFIX = "playback_controller_";

  static setCache(key: string, data: any, ttl: number = 2 * 60 * 1000) {
    const cacheData: PlaybackCacheData = {
      data,
      timestamp: Date.now(),
      ttl,
    };
    localStorage.setItem(this.CACHE_PREFIX + key, JSON.stringify(cacheData));
  }

  static getCache(key: string): any | null {
    try {
      const cached = localStorage.getItem(this.CACHE_PREFIX + key);
      if (!cached) return null;

      const { data, timestamp, ttl }: PlaybackCacheData = JSON.parse(cached);
      if (Date.now() - timestamp > ttl) {
        localStorage.removeItem(this.CACHE_PREFIX + key);
        return null;
      }

      return data;
    } catch {
      return null;
    }
  }
}

const PlaybackController: React.FC = () => {
  const { restaurantId: paramRestaurantId } = useParams<{ restaurantId: string }>();
  const { restaurantId: contextRestaurantId, isConnected } = useRestaurantContext();
  const restaurantId = paramRestaurantId || contextRestaurantId;
  const { on, off, emit, onConnectionStatusChange } = useWebSocket();

  const [playbackState, setPlaybackState] = useState<PlaybackState | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [priorityQueue, setPriorityQueue] = useState<Track[]>([]);
  const [normalQueue, setNormalQueue] = useState<Track[]>([]);
  const [queueStats, setQueueStats] = useState({
    totalItems: 0,
    paidItems: 0,
    freeItems: 0,
    estimatedWaitTime: 0,
  });
  const [lastUpdateTime, setLastUpdateTime] = useState<Date>(new Date());
  const [retryCount, setRetryCount] = useState(0);

  const [rankingPaid, setRankingPaid] = useState<RankingItem[]>([]);
  const [rankingFree, setRankingFree] = useState<RankingItem[]>([]);
  const [nextReorderAt, setNextReorderAt] = useState<Date | null>(null);
  const [countdown, setCountdown] = useState<number>(0);
  const [autoReorder, setAutoReorder] = useState<boolean>(false);
  const [autoPreview, setAutoPreview] = useState<RankingItem[]>([]);
  const [totalVotes, setTotalVotes] = useState<number>(0);
  const [reorderHistory, setReorderHistory] = useState<any[]>([]);
  const [collabStats, setCollabStats] = useState<any>(null);
  const [voteAggregates, setVoteAggregates] = useState({
    totalSuperVotes: 0,
    totalNormalVotes: 0,
    paidItems: 0,
    freeItems: 0,
  });
  const [computedRevenue, setComputedRevenue] = useState<number>(0);

  // Layout helpers (cores seguindo ClientInterface)
  const cardClass = "bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700";
  const chip = (text: string) => (
    <span className="px-2 py-0.5 bg-gray-100 text-gray-700 dark:bg-white/10 dark:text-gray-200 border border-gray-200 dark:border-white/20 rounded text-xs">
      {text}
    </span>
  );

  // Mapa de ranking por youtubeVideoId para uso rápido nas filas e música atual
  const rankingMap = useMemo(() => {
    const map: Record<string, RankingItem> = {};
    for (const item of [...rankingPaid, ...rankingFree]) {
      if (item?.youtubeVideoId) map[item.youtubeVideoId] = item;
    }
    return map;
  }, [rankingPaid, rankingFree]);

  // Combinar filas atuais
  const presentQueue = useMemo(() => {
    return [...(priorityQueue || []), ...(normalQueue || [])];
  }, [priorityQueue, normalQueue]);

  // Prévia da ordem reordenada (regra: pagos primeiro, por valor pago; depois votos)
  const predictedReorder = useMemo(() => {
    const withRank = (t: Track) => {
      const r = rankingMap[t.youtubeVideoId];
      const isPaid = Number(r?.isPaid ?? t.isPaid ?? false);
      const payment = Number(r?.paymentAmount ?? 0);
      const votes = Number(r?.voteCount ?? 0);
      return { isPaid, payment, votes };
    };
    return [...presentQueue].sort((a, b) => {
      const ra = withRank(a);
      const rb = withRank(b);
      if (ra.isPaid !== rb.isPaid) return rb.isPaid - ra.isPaid;
      if (ra.payment !== rb.payment) return rb.payment - ra.payment;
      return rb.votes - ra.votes;
    });
  }, [presentQueue, rankingMap]);

  const [localSettings, setLocalSettings] = useState<LocalSettings>({
    shuffle: false,
    repeat: false,
    autoPlay: false,
    crossfade: 0,
    lockVoting: false,
  });

  const [manualVideoInput, setManualVideoInput] = useState<string>("");
  const [manualIsPaid, setManualIsPaid] = useState<boolean>(false);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const rankingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const countdownIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Funções de API unificadas
  const makeApiCall = useCallback(async (url: string, options?: RequestInit, retries = 3): Promise<any> => {
    for (let attempt = 0; attempt < retries; attempt++) {
      try {
        const headers: Record<string, string> = {
          "Content-Type": "application/json",
          ...(options?.headers as Record<string, string> || {}),
        };
        const token = localStorage.getItem("authToken");
        if (token) headers["Authorization"] = `Bearer ${token}`;

        const response = await fetch(url, {
          ...options,
          headers,
        });

        if (!response.ok) throw new Error(`HTTP ${response.status}: ${response.statusText}`);

        const data = await response.json();
        setRetryCount(0);
        return data;
      } catch (error) {
        console.error(`API call failed (attempt ${attempt + 1}):`, error);
        if (attempt === retries - 1) {
          setRetryCount((prev) => prev + 1);
          toast.error(`Falha na conexão. Tentativa ${retryCount + 1}`);
          throw error;
        }
        await new Promise((resolve) => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }
    return undefined;
  }, [retryCount]);

  const loadPlaybackState = useCallback(async () => {
    if (!restaurantId) return;

    try {
      console.log(`[PlaybackController] Loading playback state for restaurant: ${restaurantId}`);
      const data = await makeApiCall(buildApiUrl(`/playback/${restaurantId}/state`));
      console.log(`[PlaybackController] Playback state response:`, data);

      if (data?.state) {
        if (data.state.currentTrack && data.state.currentTrack.createdAt) {
          data.state.currentTrack.createdAt = new Date(data.state.currentTrack.createdAt);
        }
        setPlaybackState(data.state);
        PlaybackCache.setCache(`playback_${restaurantId}`, data.state);
        console.log(`[PlaybackController] Playback state loaded successfully:`, data.state);
      } else {
        console.log(`[PlaybackController] No playback state found, setting default state`);
        setPlaybackState({
          isPlaying: false,
          currentTrack: null,
          volume: 50,
          currentTime: 0,
          queue: [],
          priorityQueue: [],
          normalQueue: [],
          history: [],
          connectionStatus: isConnected ? "connected" : "disconnected",
        });
      }
    } catch (error) {
      console.error("Erro ao carregar estado de reprodução:", error);
      const cached = PlaybackCache.getCache(`playback_${restaurantId}`);
      if (cached) {
        setPlaybackState(cached);
        toast("Usando dados em cache devido à falha de conexão", { icon: "ℹ️" });
      }
    } finally {
      setLoading(false);
      setLastUpdateTime(new Date());
    }
  }, [restaurantId, isConnected, makeApiCall]);

  // Util para métricas: precisa estar antes de loadQueues
  const calculateEstimatedWaitTime = useCallback((queue: Track[]) => {
    return queue.reduce((total, track) => total + (track.duration || 180), 0);
  }, []);

  const loadQueues = useCallback(async () => {
    if (!restaurantId) return;

    try {
      // Depender apenas do endpoint de playback para refletir as filas atuais
      const data = await makeApiCall(buildApiUrl(`/playback/${restaurantId}/state`));
      const state = data?.state || {};

      const priority: Track[] = (state.priorityQueue || []).map((track: any) => ({
        ...track,
        createdAt: track.createdAt ? new Date(track.createdAt) : new Date(),
        isPaid: true,

      }));
      const normal: Track[] = (state.normalQueue || []).map((track: any) => ({
        ...track,
        createdAt: track.createdAt ? new Date(track.createdAt) : new Date(),
        isPaid: false,
      }));

      setPriorityQueue(priority);
      setNormalQueue(normal);

      const estimatedWait = calculateEstimatedWaitTime([...(priority || []), ...(normal || [])]);

      setQueueStats({
        totalItems: (priority?.length || 0) + (normal?.length || 0),
        paidItems: priority?.length || 0,
        freeItems: normal?.length || 0,
        estimatedWaitTime: estimatedWait,
      });

      PlaybackCache.setCache(`queue_${restaurantId}`, { priorityQueue: priority, normalQueue: normal });
    } catch (error) {
      console.error("Erro ao carregar filas pelo playback/state:", error);
      const cached = PlaybackCache.getCache(`queue_${restaurantId}`);
      if (cached) {
        setPriorityQueue(cached.priorityQueue || []);
        setNormalQueue(cached.normalQueue || []);
        toast("Usando dados de fila em cache", { icon: "ℹ️" });
      }
    }
  }, [restaurantId, makeApiCall, calculateEstimatedWaitTime]);

  const loadVotingRanking = useCallback(async () => {
    if (!restaurantId) return;

    try {
      const url = buildApiUrl(`/collaborative-playlist/${restaurantId}/ranking`, { limit: "50" });
      const res = await fetch(url, { headers: getAuthHeaders() });
      if (!res.ok) throw new Error(`Falha ao carregar ranking (${res.status})`);
      const json = await res.json();
      const items: RankingItem[] = json?.data || [];

      const coerce = (x: any): RankingItem => ({
        ...x,
        paymentAmount: Number(x.paymentAmount) || 0,
        voteCount: Number(x.voteCount) || 0,
        superVoteCount: Number(x.superVoteCount) || 0,
        normalVoteCount: Number(x.normalVoteCount) || 0,
        totalRevenue: Number(x.totalRevenue) || 0,
        isPaid: Boolean(x.isPaid),
      });
      const normalized = items.map(coerce);
      // Paid: sort by payment amount DESC, then by vote count DESC
      const paid = normalized.filter((i) => i.isPaid).sort((a, b) => {
        const payDiff = b.paymentAmount - a.paymentAmount;
        if (payDiff !== 0) return payDiff;
        return b.voteCount - a.voteCount;
      });
      // Free: sort by vote count DESC only
      const free = normalized.filter((i) => !i.isPaid).sort((a, b) => b.voteCount - a.voteCount);

      setRankingPaid(paid);
      setRankingFree(free);
    } catch (e) {
      console.warn("Não foi possível obter ranking colaborativo:", e);
    }
  }, [restaurantId]);

  const loadCollaborativeStats = useCallback(async () => {
    if (!restaurantId) return;
    try {
      const url = buildApiUrl(`/collaborative-playlist/${restaurantId}/stats`);
      const res = await fetch(url, { headers: getAuthHeaders() });
      if (!res.ok) throw new Error(`Falha ao carregar stats (${res.status})`);
      const json = await res.json();
      setCollabStats(json?.data ?? json ?? null);
    } catch (e) {
      console.warn("Não foi possível obter stats colaborativas:", e);
    }
  }, [restaurantId]);

  // Efeito para computar previews e aggregates
  useEffect(() => {
    const combined = [...rankingPaid, ...rankingFree].sort((a, b) => {
      const paidDiff = Number(b.isPaid) - Number(a.isPaid);
      if (paidDiff !== 0) return paidDiff;
      const payDiff = b.paymentAmount - a.paymentAmount;
      if (payDiff !== 0) return payDiff;
      return b.voteCount - a.voteCount;
    });
    setAutoPreview(combined);
    setTotalVotes(combined.reduce((sum, it) => sum + it.voteCount, 0));
    const totalSuperVotes = combined.reduce((sum, it) => sum + it.superVoteCount, 0);
    const totalNormalVotes = combined.reduce((sum, it) => sum + it.normalVoteCount, 0);
    setVoteAggregates({
      totalSuperVotes,
      totalNormalVotes,
      paidItems: rankingPaid.length,
      freeItems: rankingFree.length,
    });
    const revenue = rankingPaid.reduce((sum, it) => sum + it.paymentAmount, 0);
    setComputedRevenue(revenue);
  }, [rankingPaid, rankingFree]);


  const startCountdown = useCallback((target: Date) => {
    if (countdownIntervalRef.current) clearInterval(countdownIntervalRef.current);

    const tick = () => {
      const diff = Math.max(0, Math.floor((target.getTime() - Date.now()) / 1000));
      setCountdown(diff);
      if (diff <= 0) {
        if (countdownIntervalRef.current) clearInterval(countdownIntervalRef.current);
        countdownIntervalRef.current = null;
        if (autoReorder) {
          handleReorderByVotes();
        }
      }
    };

    tick();
    countdownIntervalRef.current = setInterval(tick, 1000);
  }, [autoReorder]);

  const handleReorderByVotes = useCallback(async () => {
    if (!restaurantId) return;
    try {
      const res = await makeApiCall(buildApiUrl(`/collaborative-playlist/${restaurantId}/reorder`), { method: "POST" });
      toast.success("Playlist reordenada por votos");
      await Promise.all([loadQueues(), loadVotingRanking(), loadPlaybackState()]);
    } catch (e) {
      console.error("Erro ao reordenar por votos:", e);
      toast.error("Erro ao reordenar por votos");
    } finally {
      const target = new Date(Date.now() + 5 * 60 * 1000);
      setNextReorderAt(target);
      startCountdown(target);
    }
  }, [restaurantId, makeApiCall, loadQueues, loadVotingRanking, loadPlaybackState, startCountdown]);

  // Inicialização e WebSocket
  useEffect(() => {
    if (!restaurantId || !isConnected) return;

    // Aceitar payload conforme WebSocketEvents["playback-state-update"]
    const handlePlaybackUpdate = (data: WebSocketEvents["playback-state-update"]) => {
      setPlaybackState((prev) => (prev ? { ...prev, ...(data as any) } : prev));
      setLastUpdateTime(new Date());
      PlaybackCache.setCache(`playback_${restaurantId}`, data);
    };

    // Normalizar "queue-update" que pode vir como PlayQueue completo ou filas separadas
    const handleQueueUpdate = (payload: WebSocketEvents["queue-update"]) => {
      let prio: Track[] = [];
      let norm: Track[] = [];
      if ((payload as any).priorityQueue || (payload as any).normalQueue) {
        prio = ((payload as any).priorityQueue || []) as Track[];
        norm = ((payload as any).normalQueue || []) as Track[];
      } else if ((payload as any).queue) {
        const all = ((payload as any).queue || []) as any[];
        prio = all.filter((t) => t?.isPaid);
        norm = all.filter((t) => !t?.isPaid);
      }
      setPriorityQueue(prio);
      setNormalQueue(norm);

      const estimatedWait = calculateEstimatedWaitTime([...(prio || []), ...(norm || [])]);
      setQueueStats((prev) => ({ ...prev, estimatedWaitTime: estimatedWait }));

      PlaybackCache.setCache(`queue_${restaurantId}`, { priorityQueue: prio, normalQueue: norm });
    };

  const unsubscribeStatus = onConnectionStatusChange((status) => {
      setPlaybackState((prev) => (prev ? { ...prev, connectionStatus: status as PlaybackState["connectionStatus"] } : prev));
    });

    on("playback-state-update", handlePlaybackUpdate as any);
    on("queue-update", handleQueueUpdate as any);

    const handlePlaylistReordered = (payload: any) => {
      toast.success(payload?.message || "Playlist reordenada por votos");
      setReorderHistory((prev) => [
        {
          time: new Date().toISOString(),
          playlistName: payload?.playlist?.name || payload?.playlistName,
          count: payload?.playlist?.tracksReordered || payload?.tracksReordered || 0,
          details: (payload?.topTracks || []).slice(0, 5),
        },
        ...prev.slice(0, 19),
      ]);
      loadQueues();
      loadPlaybackState();
      loadVotingRanking();
      loadCollaborativeStats();
      const target = new Date(Date.now() + 5 * 60 * 1000);
      setNextReorderAt(target);
      startCountdown(target);
    };
    on("playlistReordered", handlePlaylistReordered as any);

    emit("join-restaurant-playback", { restaurantId });

    return () => {
      off("playback-state-update", handlePlaybackUpdate as any);
      off("queue-update", handleQueueUpdate as any);
      unsubscribeStatus?.();
      off("playlistReordered", handlePlaylistReordered as any);
      emit("leave-restaurant-playback", { restaurantId });
    };
  }, [restaurantId, isConnected, on, off, emit, loadQueues, loadPlaybackState, loadVotingRanking, loadCollaborativeStats, calculateEstimatedWaitTime, startCountdown]);

  // Carregamento inicial e intervalos
  useEffect(() => {
    const loadInitialData = async () => {
      const cachedPlayback = PlaybackCache.getCache(`playback_${restaurantId}`);
      const cachedQueue = PlaybackCache.getCache(`queue_${restaurantId}`);

      if (cachedPlayback) {
        setPlaybackState(cachedPlayback);
        toast("Carregando dados em cache...", { duration: 2000, icon: "ℹ️" });
      }

      if (cachedQueue) {
        setPriorityQueue(cachedQueue.priorityQueue || []);
        setNormalQueue(cachedQueue.normalQueue || []);
      }

      await Promise.all([loadPlaybackState(), loadQueues()]);
    };

    const loadData = async () => {
      await loadInitialData();
      loadVotingRanking();
      loadCollaborativeStats();
    };
    loadData();

    // Force initial load after a short delay to ensure components are mounted
    setTimeout(() => {
      loadData();
    }, 1000);

    if (!isConnected) {
      intervalRef.current = setInterval(() => {
        loadPlaybackState();
        loadQueues();
      }, 10000);
    }

    rankingIntervalRef.current = setInterval(() => {
      loadVotingRanking();
      loadCollaborativeStats();
    }, 15000);

    if (!nextReorderAt) {
      const target = new Date(Date.now() + 5 * 60 * 1000);
      setNextReorderAt(target);
      startCountdown(target);
    }

    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
      if (retryTimeoutRef.current) clearTimeout(retryTimeoutRef.current);
      if (rankingIntervalRef.current) clearInterval(rankingIntervalRef.current);
      if (countdownIntervalRef.current) clearInterval(countdownIntervalRef.current);
    };
  }, [restaurantId, isConnected, loadPlaybackState, loadQueues, loadVotingRanking, loadCollaborativeStats, nextReorderAt, startCountdown]);

  const voteNormalFromController = useCallback(async (youtubeVideoId: string) => {
    if (!restaurantId) return;
    try {
      await makeApiCall(buildApiUrl(`/collaborative-playlist/${restaurantId}/vote`), {
        method: "POST",
        body: JSON.stringify({ youtubeVideoId, clientSessionId: "admin_panel" }),
      });
      toast.success("Voto registrado (normal)");
      await Promise.all([loadVotingRanking(), loadQueues()]);
    } catch (e) {
      console.error(e);
      toast.error("Falha ao registrar voto (normal)");
    }
  }, [restaurantId, makeApiCall, loadVotingRanking, loadQueues]);

  const superVoteFromController = useCallback(async (youtubeVideoId: string, paymentAmount: 5 | 20 | 50) => {
    if (!restaurantId) return;
    try {
      await makeApiCall(buildApiUrl(`/collaborative-playlist/${restaurantId}/supervote`), {
        method: "POST",
        body: JSON.stringify({
          youtubeVideoId,
          paymentAmount,
          paymentId: `admin_test_${Date.now()}`,
          clientSessionId: "admin_panel",
        }),
      });
      toast.success(`Supervoto R$ ${paymentAmount} registrado`);
      await Promise.all([loadVotingRanking(), loadQueues()]);
    } catch (e) {
      console.error(e);
      toast.error("Falha ao registrar supervoto");
    }
  }, [restaurantId, makeApiCall, loadVotingRanking, loadQueues]);

  const extractYouTubeId = useCallback((input: string): string | null => {
    if (!input) return null;
    if (/^[a-zA-Z0-9_-]{11}$/.test(input)) return input;
    try {
      const url = new URL(input);
      if (url.hostname.includes("youtu.be")) return url.pathname.slice(1);
      return url.searchParams.get("v");
    } catch {
      return null;
    }
  }, []);

  const handleManualAdd = useCallback((toPaid: boolean) => {
    const id = extractYouTubeId(manualVideoInput.trim());
    if (!id) {
      toast.error("Informe um link ou ID válido do YouTube");
      return;
    }
    const newTrack: Track = {
      id: `local_${Date.now()}`,
      youtubeVideoId: id,
      title: `Música (${id})`,
      artist: "Artista Desconhecido",
      duration: 180,
      thumbnailUrl: `https://img.youtube.com/vi/${id}/mqdefault.jpg`,
      upvotes: 0,
      downvotes: 0,
      score: 0,
      createdAt: new Date(),
      isPaid: toPaid,
    };
    if (toPaid) {
      setPriorityQueue((prev) => [newTrack, ...prev]);
      toast.success("Adicionada à fila prioritária (somente UI)");
    } else {
      setNormalQueue((prev) => [newTrack, ...prev]);
      toast.success("Adicionada à fila normal (somente UI)");
    }
    setManualVideoInput("");
  }, [manualVideoInput, extractYouTubeId]);

  const handleClearQueues = useCallback(() => {
    setPriorityQueue([]);
    setNormalQueue([]);
    setQueueStats({ totalItems: 0, paidItems: 0, freeItems: 0, estimatedWaitTime: 0 });
    toast.success("Filas limpas (somente UI)");
  }, []);

  const handleMoveInQueue = useCallback((which: "priority" | "normal", trackId: string, direction: "up" | "down") => {
    const setQueue = which === "priority" ? setPriorityQueue : setNormalQueue;
    setQueue((prev) => {
      const list = [...prev];
      const index = list.findIndex((t) => t.id === trackId);
      if (index < 0) return prev;
      const targetIndex = direction === "up" ? index - 1 : index + 1;
      if (targetIndex < 0 || targetIndex >= list.length) return prev;
      [list[index], list[targetIndex]] = [list[targetIndex], list[index]];
      return list;
    });
  }, []);

  const handleDemoteToNormal = useCallback((trackId: string) => {
    setPriorityQueue((prev) => {
      const idx = prev.findIndex((t) => t.id === trackId);
      if (idx < 0) return prev;
      const item = { ...prev[idx], isPaid: false };
      const nextPrio = prev.filter((_, i) => i !== idx);
      setNormalQueue((normal) => [item, ...normal]);
      toast("Movida para fila normal (somente UI)");
      return nextPrio;
    });
  }, []);

  const toggleLocal = useCallback((key: LocalSettingsKey) => {
    setLocalSettings((prev) => ({ ...prev, [key]: !prev[key] }));
  }, []);

  const handlePlayPause = useCallback(async () => {
    if (!playbackState || !restaurantId) return;
    setActionLoading("playpause");
    try {
  const endpoint = playbackState.isPlaying ? "pause" : "resume";
  await makeApiCall(buildApiUrl(`/playback/${restaurantId}/${endpoint}`), { method: "POST" });
      const newState = { ...playbackState, isPlaying: !playbackState.isPlaying };
      setPlaybackState(newState);
      PlaybackCache.setCache(`playback_${restaurantId}`, newState);
      toast.success(playbackState.isPlaying ? "Reprodução pausada" : "Reprodução retomada");
      logUserAction("playback_toggle", { action: endpoint, trackId: playbackState.currentTrack?.id });
    } catch {
      toast.error("Erro ao controlar reprodução");
    } finally {
      setActionLoading(null);
    }
  }, [playbackState, restaurantId, makeApiCall]);

  const handleSkip = useCallback(async () => {
    if (!restaurantId) return;
    setActionLoading("skip");
    try {
      await makeApiCall(buildApiUrl(`/playback/${restaurantId}/skip`), { method: "POST" });
      toast.success("Música pulada");
      logUserAction("track_skip", { trackId: playbackState?.currentTrack?.id, skipTime: playbackState?.currentTime });
      await Promise.all([loadPlaybackState(), loadQueues()]);
    } catch {
      toast.error("Erro ao pular música");
    } finally {
      setActionLoading(null);
    }
  }, [restaurantId, playbackState, makeApiCall, loadPlaybackState, loadQueues]);

  const handleStartNext = useCallback(async () => {
    if (!restaurantId) return;
    setActionLoading("start");
    let nextTrack = null;
    let queueType = "";
    if (priorityQueue.length > 0) {
      nextTrack = priorityQueue[0];
      queueType = "priority";
      toast.success(`Tocando da fila prioritária: ${nextTrack.title}`);
    } else if (normalQueue.length > 0) {
      const sortedNormal = [...normalQueue].sort((a, b) => (b.upvotes - b.downvotes) - (a.upvotes - a.downvotes));
      nextTrack = sortedNormal[0];
      queueType = "normal";
      toast.success(`Tocando da fila normal: ${nextTrack.title}`);
    }
    if (!nextTrack) {
      toast.error("Nenhuma música na fila");
      setActionLoading(null);
      return;
    }
    try {
      await makeApiCall(buildApiUrl(`/playback/${restaurantId}/play`), {
        method: "POST",
        body: JSON.stringify({ songId: nextTrack.id }),
      });
      toast.success(`Tocando: ${nextTrack.title}`);
      logUserAction("track_start", { trackId: nextTrack.id, queueType, trackTitle: nextTrack.title });
      await Promise.all([loadPlaybackState(), loadQueues()]);
    } catch (error) {
      console.error("Erro ao iniciar próxima música:", error);
      toast.error("Erro ao iniciar próxima música");
    } finally {
      setActionLoading(null);
    }
  }, [restaurantId, priorityQueue, normalQueue, makeApiCall, loadPlaybackState, loadQueues]);

  const handleVolumeChange = useCallback(async (newVolume: number) => {
    if (!restaurantId || !playbackState) return;
    try {
      await makeApiCall(buildApiUrl(`/playback/${restaurantId}/volume`), {
        method: "POST",
        body: JSON.stringify({ volume: newVolume }),
      });
      const newState = { ...playbackState, volume: newVolume };
      setPlaybackState(newState);
      PlaybackCache.setCache(`playback_${restaurantId}`, newState);
      logUserAction("volume_change", { previousVolume: playbackState.volume, newVolume });
    } catch {
      toast.error("Erro ao ajustar volume");
    }
  }, [restaurantId, playbackState, makeApiCall]);

  const toggleMute = useCallback(() => {
    if (!playbackState) return;
    const newVolume = playbackState.volume === 0 ? 50 : 0;
    handleVolumeChange(newVolume);
    logUserAction("volume_mute_toggle", { action: newVolume === 0 ? "mute" : "unmute" });
  }, [playbackState, handleVolumeChange]);

  const handleRemoveFromQueue = useCallback(async (trackId: string) => {
    if (!restaurantId) return;
    setActionLoading(`remove-${trackId}`);
    try {
      await makeApiCall(buildApiUrl(`/playback-queue/${restaurantId}/remove`), {
        method: "POST",
        body: JSON.stringify({ trackId }),
      });
      toast.success("Música removida da fila");
      logUserAction("track_remove", { trackId });
      await loadQueues();
    } catch {
      toast.error("Erro ao remover música da fila");
    } finally {
      setActionLoading(null);
    }
  }, [restaurantId, makeApiCall, loadQueues]);

  const handlePromoteTrack = useCallback(async (trackId: string) => {
    if (!restaurantId) return;
    setActionLoading(`promote-${trackId}`);
    try {
      await makeApiCall(buildApiUrl(`/playback-queue/${restaurantId}/promote`), {
        method: "POST",
        body: JSON.stringify({ trackId }),
      });
      toast.success("Música promovida para fila prioritária");
      logUserAction("track_promote", { trackId });
      await loadQueues();
    } catch {
      toast.error("Erro ao promover música");
    } finally {
      setActionLoading(null);
    }
  }, [restaurantId, makeApiCall, loadQueues]);

  const logUserAction = useCallback((action: string, data: any) => {
    try {
      const analyticsData = {
        timestamp: new Date().toISOString(),
        restaurantId,
        action,
        data,
      };
      let existingLogs = JSON.parse(localStorage.getItem("playback_analytics") || "[]");
      existingLogs.push(analyticsData);
      if (existingLogs.length > 100) existingLogs = existingLogs.slice(-100);
      localStorage.setItem("playback_analytics", JSON.stringify(existingLogs));
    } catch (error) {
      console.error("Erro ao registrar analytics:", error);
    }
  }, [restaurantId]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const formatEstimatedTime = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}min`;
    return `${Math.floor(seconds / 3600)}h`;
  };

  const formatBRL = (v: number): string =>
    v.toLocaleString("pt-BR", { style: "currency", currency: "BRL" }).slice(3);

  const getProgressPercentage = () => {
    if (!playbackState?.currentTrack?.duration) return 0;
    return (playbackState.currentTime / playbackState.currentTrack.duration) * 100;
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div className="flex justify-center items-center h-32">
          <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white text-gray-900">
      <div className="max-w-6xl mx-auto px-4 py-6 space-y-6">
      <Toaster position="top-left" />

      <Header
        autoReorder={autoReorder}
        setAutoReorder={setAutoReorder}
        handleReorderByVotes={handleReorderByVotes}
        onRefresh={() => { void loadPlaybackState(); void loadQueues(); }}
        onStartNext={handleStartNext}
        onClearQueues={handleClearQueues}
        actionLoading={actionLoading}
        localSettings={localSettings}
        toggleLocal={toggleLocal}
      />

      <StatsOverview
        countdown={countdown}
        totalVotes={collabStats?.totalVotes ?? totalVotes}
        computedRevenue={collabStats?.totalRevenue ?? computedRevenue}
        voteAggregates={voteAggregates}
        formatEstimatedTime={formatEstimatedTime}
      />

      <CurrentPlayback
        playbackState={playbackState}
        actionLoading={actionLoading}
        handlePlayPause={handlePlayPause}
        handleSkip={handleSkip}
        handleVolumeChange={handleVolumeChange}
        toggleMute={toggleMute}
        getProgressPercentage={getProgressPercentage}
        formatTime={formatTime}
        localSettings={localSettings}
        toggleLocal={toggleLocal}
        handleClearQueues={handleClearQueues}
        handleReorderByVotes={handleReorderByVotes}
        handleStartNext={handleStartNext}
        manualVideoInput={manualVideoInput}
        setManualVideoInput={setManualVideoInput}
        handleManualAdd={handleManualAdd}
        loadInitialData={() => { void loadPlaybackState(); void loadQueues(); }}
        rankingMap={rankingMap}
      />

      <QueueStats queueStats={queueStats} formatEstimatedTime={formatEstimatedTime} />

      <ReorderHistory reorderHistory={reorderHistory} />

      <QueuesSection
        priorityQueue={priorityQueue}
        normalQueue={normalQueue}
        actionLoading={actionLoading}
        handleMoveInQueue={handleMoveInQueue}
        handleDemoteToNormal={handleDemoteToNormal}
        handlePromoteTrack={handlePromoteTrack}
        handleRemoveFromQueue={handleRemoveFromQueue}
        formatTime={formatTime}
        rankingMap={rankingMap}
        predictedReorder={predictedReorder}
      />

      <RankingSection
        rankingPaid={rankingPaid}
        rankingFree={rankingFree}
        voteNormalFromController={voteNormalFromController}
        superVoteFromController={superVoteFromController}
        formatBRL={formatBRL}
      />

      <AutoPreviewSection autoPreview={autoPreview} formatBRL={formatBRL} />

      <Footer lastUpdateTime={lastUpdateTime} />
      </div>
    </div>
  );
};

// Sub-componentes
const Header: React.FC<{
  autoReorder: boolean;
  setAutoReorder: (value: boolean) => void;
  handleReorderByVotes: () => void;
  onRefresh: () => void;
  onStartNext: () => void;
  onClearQueues: () => void;
  actionLoading: string | null;
  localSettings: LocalSettings;
  toggleLocal: (key: LocalSettingsKey) => void;
}> = React.memo(({ autoReorder, setAutoReorder, handleReorderByVotes, onRefresh, onStartNext, onClearQueues, actionLoading, localSettings, toggleLocal }) => (
  <header className="bg-white/70 backdrop-blur-md border border-gray-200 p-4 sm:p-6 rounded-xl">
    <div className="flex flex-col gap-3">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
        <div className="text-gray-900">
          <h2 className="text-2xl font-bold">Controle da Playlist (Colaborativa)</h2>
          <p className="text-gray-600 mt-1">Gerencie a reprodução e acompanhe votos, supervotos e reordenações automáticas.</p>
        </div>
        <div className="flex items-center gap-2">
          <label className="flex items-center gap-2 text-sm text-gray-800 bg-white/70 border border-gray-300 px-3 py-1.5 rounded-lg">
            <input type="checkbox" checked={autoReorder} onChange={(e) => setAutoReorder(e.target.checked)} />
            Auto-reordenar
          </label>
          <button
            onClick={handleReorderByVotes}
            className="px-3 py-2 bg-gradient-to-r from-indigo-600 to-blue-600 text-white rounded-lg hover:from-indigo-700 hover:to-blue-700 text-sm"
          >
            Reordenar agora
          </button>
          <button
            onClick={onRefresh}
            disabled={actionLoading === "refresh"}
            className="flex items-center gap-2 px-3 py-2 bg-white/70 border border-gray-300 rounded-lg hover:bg-white text-sm disabled:opacity-50"
            aria-label="Atualizar dados de reprodução"
          >
            {actionLoading === "refresh" ? <Loader2 className="w-4 h-4 animate-spin" /> : <RefreshCw className="w-4 h-4" />}
            Atualizar
          </button>
        </div>
      </div>
      <div className="flex flex-wrap items-center gap-2">
        <button onClick={onStartNext} className="px-3 py-1.5 bg-white border border-gray-300 text-gray-800 rounded hover:bg-gray-50 text-sm flex items-center gap-2">
          <SkipForward className="w-4 h-4" /> Tocar próxima da fila
        </button>
        <button onClick={onClearQueues} className="px-3 py-1.5 bg-white border border-red-300 text-red-700 rounded hover:bg-red-50 text-sm flex items-center gap-2">
          <Trash2 className="w-4 h-4" /> Limpar Filas
        </button>
        <div className="h-5 w-px bg-gray-300" />
        <button onClick={() => toggleLocal('shuffle')} className={`px-3 py-1.5 rounded text-sm flex items-center gap-1 border ${localSettings.shuffle ? 'bg-indigo-50 border-indigo-200 text-indigo-700' : 'bg-white border-gray-300 text-gray-700'}`}>
          <Shuffle className="w-4 h-4" /> Shuffle
        </button>
        <button onClick={() => toggleLocal('repeat')} className={`px-3 py-1.5 rounded text-sm flex items-center gap-1 border ${localSettings.repeat ? 'bg-indigo-50 border-indigo-200 text-indigo-700' : 'bg-white border-gray-300 text-gray-700'}`}>
          <Repeat className="w-4 h-4" /> Repeat
        </button>
        <button onClick={() => toggleLocal('lockVoting')} className={`px-3 py-1.5 rounded text-sm flex items-center gap-1 border ${localSettings.lockVoting ? 'bg-yellow-50 border-yellow-300 text-yellow-700' : 'bg-white border-gray-300 text-gray-700'}`}>
          {localSettings.lockVoting ? <Lock className="w-4 h-4" /> : <Unlock className="w-4 h-4" />} {localSettings.lockVoting ? 'Votação Travada' : 'Votação Liberada'}
        </button>
      </div>
    </div>
  </header>
));

const StatsOverview: React.FC<{
  countdown: number;
  totalVotes: number;
  computedRevenue: number;
  voteAggregates: { paidItems: number; freeItems: number; totalSuperVotes: number; totalNormalVotes: number };
  formatEstimatedTime: (seconds: number) => string;
}> = React.memo(({ countdown, totalVotes, computedRevenue, voteAggregates, formatEstimatedTime }) => (
  <section className="bg-white/70 backdrop-blur-md rounded-xl p-4 border border-gray-200">

  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-gray-600">Próxima reordenação</p>
          <p className="text-2xl font-bold text-gray-900">{formatEstimatedTime(countdown)}</p>
        </div>
        <Clock className="w-7 h-7 text-indigo-500" />
      </div>
    </div>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-gray-600">Total de votos</p>
          <p className="text-2xl font-bold text-gray-900">{totalVotes}</p>
        </div>
        <TrendingUp className="w-7 h-7 text-green-500" />
      </div>
    </div>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-yellow-200">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-yellow-600">Fila Prioritária</p>
          <p className="text-2xl font-bold text-yellow-700">{voteAggregates.paidItems}</p>
        </div>
        <Users className="w-7 h-7 text-yellow-500" />
      </div>
    </div>
    <div className="bg-white rounded-lg p-4 shadow-sm border border-green-200">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-green-600">Fila Normal</p>
          <p className="text-2xl font-bold text-green-700">{voteAggregates.freeItems}</p>
        </div>
        <Users className="w-7 h-7 text-green-500" />
      </div>
    </div>
    </div>
  </section>
));



const CurrentPlayback: React.FC<{
  playbackState: PlaybackState | null;
  actionLoading: string | null;
  handlePlayPause: () => void;
  handleSkip: () => void;
  handleVolumeChange: (newVolume: number) => void;
  toggleMute: () => void;
  getProgressPercentage: () => number;
  formatTime: (seconds: number) => string;
  localSettings: LocalSettings;
  toggleLocal: (key: LocalSettingsKey) => void;
  handleClearQueues: () => void;
  handleReorderByVotes: () => void;
  handleStartNext: () => void;
  manualVideoInput: string;
  setManualVideoInput: (value: string) => void;
  handleManualAdd: (toPaid: boolean) => void;
  loadInitialData: () => void;
  rankingMap?: Record<string, RankingItem>;

}> = React.memo(({ playbackState, actionLoading, handlePlayPause, handleSkip, handleVolumeChange, toggleMute, getProgressPercentage, formatTime, localSettings, toggleLocal, handleClearQueues, handleReorderByVotes, handleStartNext, manualVideoInput, setManualVideoInput, handleManualAdd, loadInitialData, rankingMap }) => (
  <section className="bg-white/70 backdrop-blur-md rounded-xl p-6 border border-gray-200">
    {playbackState?.currentTrack ? (
      <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="space-y-4">
        <div className="flex items-center space-x-4">
          <img
            src={playbackState.currentTrack.thumbnailUrl}
            alt={playbackState.currentTrack.title}
            className="w-16 h-12 object-cover rounded-lg shadow-sm"
          />
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900">
              {playbackState.currentTrack.title}
            </h3>
            <p className="text-gray-600">
              {playbackState.currentTrack.artist}
            </p>
            <div className="flex items-center space-x-4 mt-1">
              <div className="flex items-center space-x-1 text-green-600">
                <TrendingUp className="w-4 h-4" aria-hidden="true" />
                <span className="text-sm">{playbackState.currentTrack.upvotes}</span>
              </div>
              <div className="flex items-center space-x-1 text-red-600">
                <AlertTriangle className="w-4 h-4" aria-hidden="true" />
                <span className="text-sm">{playbackState.currentTrack.downvotes}</span>
              </div>
              <div className="text-sm text-gray-600">
                Score: {playbackState.currentTrack.score}
              </div>
            </div>
            {/* Agregados de votação (ranking real) */}
            <div className="flex items-center space-x-3 mt-1 text-xs text-gray-600">
              {(() => {
                const yt = (playbackState.currentTrack as any).youtubeVideoId;
                const r = yt ? rankingMap?.[yt] : undefined;
                if (!r) return null;
                return (
                  <>
                    <span className="px-1.5 py-0.5 rounded bg-white/70 border border-gray-300 text-gray-800">Votos: {r.voteCount}</span>
                    {r.isPaid && <span className="px-1.5 py-0.5 rounded bg-yellow-100 text-yellow-800 border border-yellow-300">R$ {((r.paymentAmount ?? 0)/100).toFixed(2)}</span>}
                  </>
                );
              })()}
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between text-sm text-gray-500">
            <span>{formatTime(playbackState.currentTime)}</span>
            <span>{formatTime(playbackState.currentTrack.duration)}</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <motion.div
              className="bg-blue-600 h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${getProgressPercentage()}%` }}
              transition={{ duration: 1 }}
              role="progressbar"
              aria-valuenow={getProgressPercentage()}
              aria-valuemin={0}
              aria-valuemax={100}
              aria-label="Progresso da música"
            />
          </div>
        </div>

        <div className="flex items-center justify-center space-x-4">
          <button
            onClick={handlePlayPause}
            disabled={actionLoading === "playpause"}
            className="flex items-center justify-center w-12 h-12 bg-blue-600 text-white rounded-full hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            aria-label={playbackState.isPlaying ? "Pausar" : "Reproduzir"}
          >
            {actionLoading === "playpause" ? (
              <Loader2 className="w-6 h-6 animate-spin" />
            ) : playbackState.isPlaying ? (
              <Pause className="w-6 h-6" />
            ) : (
              <Play className="w-6 h-6 ml-1" />
            )}
          </button>

          <button
            onClick={handleSkip}
            disabled={actionLoading === "skip"}
            className="flex items-center justify-center w-10 h-10 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-300 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            aria-label="Pular música"
          >
            {actionLoading === "skip" ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <SkipForward className="w-5 h-5" />
            )}
          </button>

          <div className="flex items-center space-x-2">
            <button
              onClick={toggleMute}
              aria-label={playbackState.volume === 0 ? "Ativar som" : "Silenciar"}
            >
              {playbackState.volume === 0 ? (
                <VolumeX className="w-5 h-5 text-gray-600" />
              ) : (
                <Volume2 className="w-5 h-5 text-gray-600" />
              )}
            </button>
            <input
              type="range"
              min="0"
              max="100"
              value={playbackState.volume}
              onChange={(e) => handleVolumeChange(parseInt(e.target.value))}
              className="w-20 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              aria-label="Controle de volume"
            />
            <span className="text-sm text-gray-500 w-8">{playbackState.volume}</span>
          </div>
        </div>
      </motion.div>
    ) : (
      <div className="space-y-4">
        <div className="flex items-start gap-4">
          <div className="w-16 h-12 bg-white/10 rounded-lg" />
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900">Sem reprodução ativa no momento</h3>
            <p className="text-gray-600 text-sm">A playlist do restaurante toca normalmente até que a votação reordene a fila.</p>
          </div>
        </div>

  {/* Ações rápidas consolidadas no Header */}

        <div className="bg-gray-50 dark:bg-gray-900/40 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2 text-gray-800 dark:text-gray-200 mb-3">
            <ExternalLink className="w-4 h-4" />
            <span className="font-medium text-sm">Adicionar música manualmente</span>
          </div>
          <div className="flex flex-col sm:flex-row gap-2">
            <input
              value={manualVideoInput}
              onChange={(e) => setManualVideoInput(e.target.value)}
              placeholder="Cole o link ou ID do YouTube"
              className="flex-1 px-3 py-2 rounded border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
            />
            <div className="flex gap-2">
              <button onClick={() => handleManualAdd(false)} className="px-3 py-2 bg-green-600 text-white rounded hover:bg-green-700 text-sm">Adicionar na Fila Normal</button>
              <button onClick={() => handleManualAdd(true)} className="px-3 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 text-sm">Adicionar na Fila Prioritária</button>
            </div>
          </div>
        </div>
      </div>
    )}
  </section>
));

const QueueStats: React.FC<{
  queueStats: { totalItems: number; paidItems: number; freeItems: number; estimatedWaitTime: number };
  formatEstimatedTime: (seconds: number) => string;
}> = React.memo(({ queueStats, formatEstimatedTime }) => (
  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
    <div className="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-gray-600">Total na Fila</p>
          <p className="text-2xl font-bold text-gray-900">{queueStats.totalItems}</p>
        </div>
        <Music className="w-8 h-8 text-blue-500" />
      </div>
    </div>

    <div className="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-gray-600">Fila Prioritária</p>
          <p className="text-2xl font-bold text-gray-900">{queueStats.paidItems}</p>
        </div>
        <TrendingUp className="w-8 h-8 text-yellow-500" />
      </div>
    </div>

    <div className="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-gray-600">Fila Normal</p>
          <p className="text-2xl font-bold text-gray-900">{queueStats.freeItems}</p>
        </div>
        <Users className="w-8 h-8 text-green-500" />
      </div>
    </div>
  </div>
));

const ReorderHistory: React.FC<{
  reorderHistory: any[];
}> = React.memo(({ reorderHistory }) => (
  <section className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm mb-6">
    <div className="flex items-center justify-between mb-3">
      <h3 className="text-lg font-semibold">Histórico de Reordenação</h3>
      <span className="text-xs px-2 py-1 rounded bg-gray-100 text-gray-700 border border-gray-200">
        {reorderHistory.length} eventos
      </span>
    </div>
    {reorderHistory.length === 0 ? (
      <div className="text-sm text-gray-600 dark:text-gray-400">Nenhum evento de reordenação ainda</div>
    ) : (
      <div className="space-y-2 max-h-64 overflow-y-auto">
        {reorderHistory.map((evt, idx) => (
          <div key={idx} className="p-3 rounded border border-indigo-200 dark:border-indigo-700 bg-indigo-50/60 dark:bg-indigo-900/20">
            <div className="flex items-center justify-between text-sm">
              <div className="font-medium text-gray-800 dark:text-gray-200">
                {new Date(evt.time).toLocaleString("pt-BR")} • {evt.playlistName || "Playlist"}
              </div>
              <div className="text-xs text-indigo-700 dark:text-indigo-300">{evt.count} músicas impactadas</div>
            </div>
            <div className="mt-2 text-xs text-gray-700 dark:text-gray-300">
              Top 5:
              <ul className="list-disc list-inside">
                {(evt.details || []).map((t: any, i: number) => (
                  <li key={i} className="truncate">
                    {t.title || t.videoId} {t.isPaid ? "(Paga)" : ""} — votos: {t.voteCount ?? "?"}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        ))}
      </div>
    )}
  </section>
));

const QueuesSection: React.FC<{
  priorityQueue: Track[];
  normalQueue: Track[];
  actionLoading: string | null;
  handleMoveInQueue: (which: "priority" | "normal", trackId: string, direction: "up" | "down") => void;
  handleDemoteToNormal: (trackId: string) => void;
  handlePromoteTrack: (trackId: string) => void;
  handleRemoveFromQueue: (trackId: string) => void;
  formatTime: (seconds: number) => string;
  rankingMap?: Record<string, RankingItem>;
  predictedReorder?: Track[];
}> = React.memo(({ priorityQueue, normalQueue, actionLoading, handleMoveInQueue, handleDemoteToNormal, handlePromoteTrack, handleRemoveFromQueue, formatTime, rankingMap, predictedReorder }) => (
  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <section className="bg-white/70 backdrop-blur-md rounded-xl p-6 border border-gray-200">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold flex items-center text-gray-900">
          🔥 Fila Prioritária ({priorityQueue.length})
        </h3>
        <div className="bg-yellow-100 text-yellow-800 border border-yellow-300 px-2 py-1 rounded text-xs font-medium">
          SuperVoto (R$ 5, 20, 50)
        </div>
      </div>
      {/* Legenda agregada de votos */}
      <div className="text-xs text-gray-600 mb-2">
        {(() => {
          const totalPaidVotes = priorityQueue.reduce((sum, t) => sum + (rankingMap?.[t.youtubeVideoId]?.voteCount ?? 0), 0);
          const paidCount = priorityQueue.length;
          return <span>Itens pagos: {paidCount} • Votos somados: {totalPaidVotes}</span>;
        })()}
      </div>
      <div className="space-y-3 max-h-64 overflow-y-auto">
        {priorityQueue.length > 0 ? (
          <AnimatePresence>
            {priorityQueue.map((track, index) => (
              <motion.div
                key={track.id}
                layout
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.2 }}
                className="flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200"
              >
                <div className="w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-full text-xs flex items-center justify-center font-bold">
                  {index + 1}
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-medium text-gray-900 truncate">
                    {track.title}
                  </h4>
                  <p className="text-xs text-gray-600 truncate">
                    {track.artist}
                  </p>
                </div>
                  {/* Votos (ranking real) */}
                  <div className="flex items-center space-x-3 mt-1">
                    <div className="flex items-center space-x-1">
                      <TrendingUp className="w-3 h-3 text-yellow-600" aria-hidden="true" />
                      <span className="text-xs text-yellow-700">
                        {(rankingMap?.[track.youtubeVideoId]?.voteCount ?? 0)} votos
                      </span>
                    </div>
                    {rankingMap?.[track.youtubeVideoId]?.isPaid && (
                      <div className="text-xs text-green-700">
                        R$ {((rankingMap?.[track.youtubeVideoId]?.paymentAmount ?? 0) / 100).toFixed(2)}
                      </div>
                    )}
                  </div>
                <div className="flex items-center space-x-2">
                  <div className="text-xs text-gray-500">{formatTime(track.duration)}</div>
                  <button
                    onClick={() => handleMoveInQueue('priority', track.id, 'up')}
                    className="p-1 text-gray-600 hover:text-gray-800"
                    title="Mover para cima"
                    aria-label="Mover para cima"
                  >
                    <ArrowUp className="w-3 h-3" />
                  </button>
                  <button
                    onClick={() => handleMoveInQueue('priority', track.id, 'down')}
                    className="p-1 text-gray-600 hover:text-gray-800"
                    title="Mover para baixo"
                    aria-label="Mover para baixo"
                  >
                    <ArrowDown className="w-3 h-3" />
                  </button>
                  <button
                    onClick={() => handleDemoteToNormal(track.id)}
                    className="p-1 text-blue-600 hover:text-blue-800"
                    title="Mover para fila normal"
                    aria-label="Mover para fila normal"
                  >
                    <ChevronUp className="w-3 h-3 rotate-180" />
                  </button>
                  <button
                    onClick={() => handleRemoveFromQueue(track.id)}
                    disabled={actionLoading === `remove-${track.id}`}
                    className="p-1 text-red-600 hover:text-red-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    aria-label="Remover da fila prioritária"
                    title="Remover"
                  >
                    {actionLoading === `remove-${track.id}` ? (
                      <Loader2 className="w-3 h-3 animate-spin" />
                    ) : (
                      <X className="w-3 h-3" />
                    )}
                  </button>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-8"
          >
            <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <TrendingUp className="w-6 h-6 text-yellow-600" />
            </div>
            <p className="text-gray-600 text-sm">
              Nenhuma música na fila prioritária
            </p>
          </motion.div>
        )}
    {/* Prévia de Reordenação */}
    {predictedReorder && predictedReorder.length > 0 && (
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-indigo-200 dark:border-indigo-700 lg:col-span-2">
        <div className="flex items-center justify-between mb-2">
          <h4 className="text-md font-semibold text-indigo-700 dark:text-indigo-300">Prévia de Reordenação (por votos)</h4>
          <span className="text-xs text-gray-500">Pagas primeiro, depois por valor e votos</span>
        </div>
        <div className="flex flex-wrap gap-2">
          {predictedReorder.map((t, i) => (
            <div key={`${t.id}_${i}`} className="px-2 py-1 rounded text-xs border border-indigo-200 dark:border-indigo-700 bg-indigo-50/60 dark:bg-indigo-900/20">
              <span className="font-medium mr-1">{i + 1}.</span>
              <span className="mr-1">{t.title}</span>
              <span className="text-gray-500">({rankingMap?.[t.youtubeVideoId]?.voteCount ?? 0} votos{rankingMap?.[t.youtubeVideoId]?.isPaid ? ` · R$ ${((rankingMap?.[t.youtubeVideoId]?.paymentAmount ?? 0)/100).toFixed(2)}` : ''})</span>
            </div>
          ))}
        </div>
      </div>
    )}
      </div>
    </section>

    <section className="bg-white/70 backdrop-blur-md rounded-xl p-6 border border-gray-200">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold flex items-center text-gray-900">
          📋 Fila Normal ({normalQueue.length})
        </h3>
        <div className="bg-green-100 text-green-800 border border-green-300 px-2 py-1 rounded text-xs font-medium">
          Gratuito
        </div>
      </div>
      <div className="space-y-3 max-h-64 overflow-y-auto">
        {normalQueue.length > 0 ? (
          <AnimatePresence>
            {normalQueue
              .slice()
              .sort((a, b) => (b.upvotes - b.downvotes) - (a.upvotes - a.downvotes))
              .map((track, index) => (
                <motion.div
                  key={track.id}
                  layout
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.2 }}
                  className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg border border-green-200"
                >
                  <div className="w-6 h-6 bg-green-600 text-white rounded text-xs flex items-center justify-center font-medium">
                    {index + 1}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="text-sm font-medium text-gray-900 truncate">
                      {track.title}
                    </h4>
                    <p className="text-xs text-gray-600 truncate">
                      {track.artist}
                    </p>
                    <div className="flex items-center space-x-3 mt-1">
                      <div className="flex items-center space-x-1">
                        <TrendingUp className="w-3 h-3 text-green-600" aria-hidden="true" />
                        <span className="text-xs text-green-600">{track.upvotes}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <AlertTriangle className="w-3 h-3 text-red-600" aria-hidden="true" />
                        <span className="text-xs text-red-600">{track.downvotes}</span>
                      </div>
                      {/* Votos (ranking real) */}
                      <div className="flex items-center space-x-3 mt-1">
                        <div className="flex items-center space-x-1">
                          <TrendingUp className="w-3 h-3 text-green-600" aria-hidden="true" />
                          <span className="text-xs text-green-700">
                            {(rankingMap?.[track.youtubeVideoId]?.voteCount ?? 0)} votos
                          </span>
                        </div>
                      </div>
                      <div className="text-xs text-gray-500">
                        Score: {track.score || track.upvotes - track.downvotes}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="text-xs text-gray-500">{formatTime(track.duration)}</div>
                    <button
                      onClick={() => handleMoveInQueue('normal', track.id, 'up')}
                      className="p-1 text-gray-600 hover:text-gray-800"
                      title="Mover para cima"
                      aria-label="Mover para cima"
                    >
                      <ArrowUp className="w-3 h-3" />
                    </button>
                    <button
                      onClick={() => handleMoveInQueue('normal', track.id, 'down')}
                      className="p-1 text-gray-600 hover:text-gray-800"
                      title="Mover para baixo"
                      aria-label="Mover para baixo"
                    >
                      <ArrowDown className="w-3 h-3" />
                    </button>
                    <button
                      onClick={() => handlePromoteTrack(track.id)}
                      disabled={actionLoading === `promote-${track.id}`}
                      className="p-1 text-blue-600 hover:text-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      aria-label="Promover para fila prioritária"
                      title="Promover"
                    >
                      {actionLoading === `promote-${track.id}` ? (
                        <Loader2 className="w-3 h-3 animate-spin" />
                      ) : (
                        <ChevronUp className="w-3 h-3" />
                      )}
                    </button>
                    <button
                      onClick={() => handleRemoveFromQueue(track.id)}
                      disabled={actionLoading === `remove-${track.id}`}
                      className="p-1 text-red-600 hover:text-red-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      aria-label="Remover da fila normal"
                      title="Remover"
                    >
                      {actionLoading === `remove-${track.id}` ? (
                        <Loader2 className="w-3 h-3 animate-spin" />
                      ) : (
                        <X className="w-3 h-3" />
                      )}
                    </button>
                  </div>
                </motion.div>
              ))}
          </AnimatePresence>
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-8"
          >
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Users className="w-6 h-6 text-green-600" />
            </div>
            <p className="text-gray-600 text-sm">
              Nenhuma música na fila normal
            </p>
          </motion.div>
        )}
      </div>
    </section>
  </div>
));

const RankingSection: React.FC<{
  rankingPaid: RankingItem[];
  rankingFree: RankingItem[];
  voteNormalFromController: (youtubeVideoId: string) => void;
  superVoteFromController: (youtubeVideoId: string, paymentAmount: 5 | 20 | 50) => void;
  formatBRL: (v: number) => string;
}> = React.memo(({ rankingPaid, rankingFree, voteNormalFromController, superVoteFromController, formatBRL }) => (
  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <section className="bg-white/70 backdrop-blur-md rounded-xl p-6 border border-gray-200">
      <div className="flex items-center justify-between mb-3">
        <h4 className="text-lg font-semibold text-gray-900">SuperVotos (Pagas)</h4>
        <span className="text-xs px-2 py-1 rounded bg-yellow-100 text-yellow-800 border border-yellow-300">
          {rankingPaid.length} itens
        </span>
      </div>
      <div className="space-y-2 max-h-72 overflow-y-auto">
        {rankingPaid.length === 0 ? (
          <div className="text-sm text-gray-600">Nenhum supervoto no momento</div>
        ) : (
          rankingPaid.map((item, idx) => (
            <div key={item.youtubeVideoId} className="flex items-center gap-3 p-3 rounded border border-gray-200 bg-white/50">
              <div className="w-7 h-7 bg-yellow-500 text-white rounded flex items-center justify-center text-xs font-bold">{idx + 1}</div>
              <img src={`https://img.youtube.com/vi/${item.youtubeVideoId}/mqdefault.jpg`} alt={item.title || item.youtubeVideoId} className="w-10 h-8 object-cover rounded" />
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium truncate text-gray-900">{item.title || item.youtubeVideoId}</div>
                <div className="text-xs text-gray-600 truncate">{item.artist || "—"}</div>
              </div>
              <div className="flex items-center gap-2">
                <div className="text-xs text-purple-200 mr-1">Votos: {item.voteCount}</div>
                <div className="flex items-center gap-1">
                  <button
                    onClick={() => voteNormalFromController(item.youtubeVideoId)}
                    className="px-2 py-0.5 border border-white/20 rounded text-xs hover:bg-white/10"
                    title="Voto normal"
                  >+1</button>
                  <button onClick={() => superVoteFromController(item.youtubeVideoId, 5)} className="px-2 py-0.5 border border-yellow-500/40 rounded text-xs hover:bg-yellow-500/10" title="Supervoto R$5">5</button>
                  <button onClick={() => superVoteFromController(item.youtubeVideoId, 20)} className="px-2 py-0.5 border border-yellow-500/50 rounded text-xs hover:bg-yellow-500/10" title="Supervoto R$20">20</button>
                  <button onClick={() => superVoteFromController(item.youtubeVideoId, 50)} className="px-2 py-0.5 border border-yellow-500/60 rounded text-xs hover:bg-yellow-500/10" title="Supervoto R$50">50</button>
                </div>
                <div className="text-xs text-green-200 ml-1">R$ {formatBRL(item.paymentAmount)}</div>
              </div>
            </div>
          ))
        )}
      </div>
    </section>

    <section className="bg-white/70 backdrop-blur-md rounded-xl p-6 border border-gray-200">
      <div className="flex items-center justify-between mb-3">
        <h4 className="text-lg font-semibold text-gray-900">Votos (Grátis)</h4>
        <span className="text-xs px-2 py-1 rounded bg-green-100 text-green-800 border border-green-300">
          {rankingFree.length} itens
        </span>
      </div>
      <div className="space-y-2 max-h-72 overflow-y-auto">
        {rankingFree.length === 0 ? (
          <div className="text-sm text-gray-600">Nenhum voto gratuito no momento</div>
        ) : (
          rankingFree.map((item, idx) => (
            <div key={item.youtubeVideoId} className="flex items-center gap-3 p-3 rounded border border-gray-200 bg-white/50">
              <div className="w-7 h-7 bg-green-600 text-white rounded flex items-center justify-center text-xs font-bold">{idx + 1}</div>
              <img src={`https://img.youtube.com/vi/${item.youtubeVideoId}/mqdefault.jpg`} alt={item.title || item.youtubeVideoId} className="w-10 h-8 object-cover rounded" />
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium truncate text-gray-900">{item.title || item.youtubeVideoId}</div>
                <div className="text-xs text-gray-600 truncate">{item.artist || "—"}</div>
              </div>
              <div className="flex items-center gap-2">
                <div className="text-xs text-gray-600 mr-1">Votos: {item.voteCount}</div>
                <div className="flex items-center gap-1">
                  <button onClick={() => voteNormalFromController(item.youtubeVideoId)} className="px-2 py-0.5 border border-gray-300 rounded text-xs hover:bg-gray-100" title="Voto normal">+1</button>
                  <button onClick={() => superVoteFromController(item.youtubeVideoId, 5)} className="px-2 py-0.5 border border-yellow-400 rounded text-xs hover:bg-yellow-100" title="Supervoto R$5">5</button>
                  <button onClick={() => superVoteFromController(item.youtubeVideoId, 20)} className="px-2 py-0.5 border border-yellow-500 rounded text-xs hover:bg-yellow-100" title="Supervoto R$20">20</button>
                  <button onClick={() => superVoteFromController(item.youtubeVideoId, 50)} className="px-2 py-0.5 border border-yellow-600 rounded text-xs hover:bg-yellow-100" title="Supervoto R$50">50</button>
                </div>
                <div className="text-xs text-gray-600 ml-1">Mesa: {item.tableNumber ?? "—"}</div>
              </div>
            </div>
          ))
        )}
      </div>
    </section>
  </div>
));

const AutoPreviewSection: React.FC<{
  autoPreview: RankingItem[];
  formatBRL: (v: number) => string;
}> = React.memo(({ autoPreview, formatBRL }) => (
  <section className="bg-white/70 backdrop-blur-md rounded-xl p-6 border border-gray-200">
    <div className="flex items-center justify-between mb-3">
      <h4 className="text-lg font-semibold text-gray-900">Fila Automática (Prévia)</h4>
      <div className="text-xs text-gray-600">Baseada em votos e pagamentos</div>
    </div>
    {autoPreview.length > 0 ? (
      <div className="space-y-2 max-h-40 overflow-y-auto">
        {autoPreview.map((item, idx) => (
          <div key={idx} className="flex items-center justify-between p-2 rounded bg-white/50 border border-gray-200">
            <div className="truncate mr-2">
              <span className="text-sm font-medium text-gray-900">{item.title}</span>
              <span className="text-xs text-gray-600 ml-2">{item.voteCount} votos</span>
            </div>
            <div className="text-xs">
              {item.isPaid ? (
                <span className="text-green-600">R$ {formatBRL(item.paymentAmount ?? 0)}</span>
              ) : (
                <span className="text-gray-600">Grátis</span>
              )}
            </div>
          </div>
        ))}
      </div>
    ) : (
      <div className="text-sm text-gray-600">Nenhuma prévia disponível.</div>
    )}
  </section>
));

const Footer: React.FC<{
  lastUpdateTime: Date;
}> = React.memo(({ lastUpdateTime }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ delay: 0.8 }}
    className="mt-6"
  >
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Activity className="w-4 h-4 text-blue-500" />
          <span className="text-sm text-gray-600">
            Sistema ativo - Última atualização: {lastUpdateTime.toLocaleTimeString('pt-BR')}
          </span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span className="text-xs text-green-600">Online</span>
        </div>
      </div>
    </div>
  </motion.div>
));

export default PlaybackController;