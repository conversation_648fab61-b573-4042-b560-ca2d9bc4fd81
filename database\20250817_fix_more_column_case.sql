-- Additional case fixes

-- suggestions: rename createdAt/updatedAt to snake if present
DO $$
BEGIN
	IF EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='createdAt'
	) THEN
		ALTER TABLE suggestions RENAME COLUMN "createdAt" TO created_at;
	END IF;
	IF EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='updatedAt'
	) THEN
		ALTER TABLE suggestions RENAME COLUMN "updatedAt" TO updated_at;
	END IF;
END $$;

-- playlists: rename createdAt/updatedAt if present
DO $$
BEGIN
	IF EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='createdAt'
	) THEN
		ALTER TABLE playlists RENAME COLUMN "createdAt" TO created_at;
	END IF;
	IF EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='updatedAt'
	) THEN
		ALTER TABLE playlists RENAME COLUMN "updatedAt" TO updated_at;
	END IF;
END $$;

