import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from "typeorm";
import { Restaurant } from "./Restaurant";
import { Playlist } from "./Playlist";
import { Vote } from "./Vote";
import { User } from "./User";
import { ClientSession } from "./ClientSession";
// import { Genre } from "./Genre";

export enum SuggestionStatus {
  PENDING = "pending",
  APPROVED = "approved",
  REJECTED = "rejected",
  PLAYING = "playing",
  PLAYED = "played",
  SKIPPED = "skipped",
  COMPLETED = "completed",
  EXPIRED = "expired",
}

export enum SuggestionSource {
  CLIENT = "client",
  ADMIN = "admin",
  AUTO = "auto",
  IMPORT = "import",
}

@Entity("suggestions")

export class Suggestion {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ name: "youtube_video_id", type: "varchar" })
  youtubeVideoId: string;

  @Column({ type: "varchar" })
  title: string;

  @Column({ type: "varchar", nullable: true })
  artist: string;

  @Column({ type: "varchar", nullable: true })
  genre?: string;

  @Column({ name: "channel_name", type: "varchar", nullable: true })
  channelName: string;

  @Column({ type: "integer", nullable: true })
  duration: number; // em segundos

  @Column({ name: "thumbnail_url", type: "varchar", nullable: true })
  thumbnailUrl: string;

  @Column({ type: "text", nullable: true })
  description: string;

  @Column({
    type: "enum",
    enum: SuggestionStatus,
    default: SuggestionStatus.PENDING,
  })
  status: SuggestionStatus;

  @Column({
    type: "enum",
    enum: SuggestionSource,
    default: SuggestionSource.CLIENT,
  })
  source: SuggestionSource;

  @Column({ name: "client_session_id", type: "varchar", nullable: true })
  clientSessionId?: string;

  @Column({ name: "client_ip", type: "inet", nullable: true })
  clientIp?: string;

  @Column({ name: "client_user_agent", type: "text", nullable: true })
  clientUserAgent?: string;

  @Column({ name: "vote_count", type: "integer", default: 0 })
  voteCount: number;

  @Column({ type: "integer", default: 0 })
  upvotes: number;

  @Column({ type: "integer", default: 0 })
  downvotes: number;

  @Column({ name: "moderated_at", type: "timestamp", nullable: true })
  moderatedAt: Date;

  @Column({ name: "moderation_reason", type: "text", nullable: true })
  moderationReason?: string;

  @Column({ name: "played_at", type: "timestamp", nullable: true })
  playedAt: Date;

  @Column({ name: "play_duration", type: "integer", nullable: true })
  playDuration: number; // tempo que foi tocada em segundos

  @Column({ name: "skip_reason", type: "text", nullable: true })
  skipReason?: string;

  @Column({ type: "json", nullable: true })
  metadata: {
    genre?: string[];
    mood?: string[];
    language?: string;
    explicit?: boolean;
    live?: boolean;
    publishedAt?: string;
    viewCount?: number;
    likeCount?: number;
    tags?: string[];
  };

  @Column({ name: "moderation_flags", type: "json", nullable: true })
  moderationFlags: {
    autoRejected?: boolean;
    flaggedWords?: string[];
    contentWarnings?: string[];
    similarToRecent?: boolean;
    duplicateInQueue?: boolean;
    tooLong?: boolean;
    tooShort?: boolean;
  };

  // Campos de pagamento PIX
  @Column({ name: "is_priority", type: "boolean", default: false })
  isPriority: boolean;

  @Column({ name: "is_paid", type: "boolean", default: false })
  isPaid: boolean;

  @Column({ name: "paid_at", type: "timestamp", nullable: true })
  paidAt?: Date;

  @Column({
    name: "payment_amount",
    type: "decimal",
    precision: 10,
    scale: 2,
    nullable: true,
  })
  paymentAmount?: number; // Valor em reais (ex: 2.00)

  @Column({ name: "payment_id", type: "varchar", nullable: true })
  paymentId?: string; // ID do pagamento PIX

  @Column({
    name: "payment_status",
    type: "varchar",
    nullable: true,
  })
  paymentStatus?: string; // pending, paid, expired, cancelled

  @Column({ name: "pix_code", type: "text", nullable: true })
  pixCode?: string; // Código PIX para pagamento

  @Column({ name: "queue_position", type: "integer", nullable: true })
  queuePosition?: number;

  // Campos adicionais para cliente
  @Column({ name: "client_name", type: "varchar", nullable: true })
  clientName?: string;

  @Column({ name: "table_number", type: "integer", nullable: true })
  tableNumber?: number;

  @Column({ name: "session_id", type: "uuid", nullable: true })
  sessionId?: string;

  @Column({ name: "completed_at", type: "timestamp", nullable: true })
  completedAt?: Date;

  @Column({ name: "votes_count", type: "integer", default: 0 })
  votesCount: number;

  @Column({ name: "rejection_reason", type: "text", nullable: true })
  rejectionReason?: string;

  @ManyToOne(() => Restaurant, (restaurant) => restaurant.suggestions, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "restaurant_id" })
  restaurant: Restaurant;

  @ManyToOne(() => Playlist, (playlist) => playlist.suggestions, {
    nullable: true,
    onDelete: "SET NULL",
  })
  @JoinColumn({ name: "playlist_id" })
  playlist: Playlist;

  @ManyToOne(() => User, {
    nullable: true,
    onDelete: "SET NULL",
  })
  @JoinColumn({ name: "moderated_by" })
  moderatedBy?: User;

  @ManyToOne(() => User, {
    nullable: true,
    onDelete: "SET NULL",
  })
  @JoinColumn({ name: "suggested_by" })
  suggestedBy: User;

  @OneToMany(() => Vote, (vote) => vote.suggestion)
  votes: Vote[];

  @ManyToOne(() => ClientSession, {
    nullable: true,
    onDelete: "SET NULL",
  })
  // Importante: relacionar pela coluna session_id (UUID), não client_session_id (token string)
  @JoinColumn({ name: "session_id" })
  clientSession?: ClientSession;

  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at" })
  updatedAt: Date;

  // Métodos da instância

  // Calcular score baseado em votos e tempo
  calculateScore(): number {
    const baseScore = this.upvotes - this.downvotes;
    const timeDecay = this.getTimeDecayFactor();
    return baseScore * timeDecay;
  }

  // Fator de decaimento baseado no tempo
  private getTimeDecayFactor(): number {
    // createdAt pode ser nulo em dados antigos; nesse caso, não aplicar decaimento
    const created = this.createdAt ?? this.updatedAt ?? new Date();
    const createdMs = (() => {
      try {
        const t = new Date(created as any).getTime();
        return isFinite(t) ? t : Date.now();
      } catch {
        return Date.now();
      }
    })();
    const hoursOld = (Date.now() - createdMs) / (1000 * 60 * 60);
    return Math.exp(-hoursOld / 24); // Decai pela metade a cada 24 horas
  }

  // Verificar se pode ser votada
  canBeVoted(): boolean {
    return [SuggestionStatus.PENDING, SuggestionStatus.APPROVED].includes(
      this.status
    );
  }

  // Verificar se está na fila
  isInQueue(): boolean {
    return (
      this.status === SuggestionStatus.APPROVED && (this.queuePosition || 0) > 0
    );
  }

  // Obter duração formatada
  getFormattedDuration(): string {
    if (!this.duration) return "N/A";

    const minutes = Math.floor(this.duration / 60);
    const seconds = this.duration % 60;
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  }

  // Verificar se é conteúdo explícito
  isExplicit(): boolean {
    return this.metadata?.explicit || false;
  }

  // Verificar se é transmissão ao vivo
  isLive(): boolean {
    return this.metadata?.live || false;
  }

  // Verificar se é sugestão prioritária (paga)
  isPriorityPaid(): boolean {
    return this.isPriority && this.paymentStatus === "paid";
  }

  // Verificar se o pagamento está pendente
  isPaymentPending(): boolean {
    return this.isPriority && this.paymentStatus === "pending";
  }

  // Verificar se o pagamento expirou
  isPaymentExpired(): boolean {
    return this.isPriority && this.paymentStatus === "expired";
  }

  // Marcar pagamento como pago
  markAsPaid(paymentId: string): void {
    this.isPaid = true;
    this.paidAt = new Date();
    this.paymentId = paymentId;
    this.paymentStatus = "paid";
    // Sugestões pagas são automaticamente aprovadas
    if (this.status === SuggestionStatus.PENDING) {
      this.status = SuggestionStatus.APPROVED;
    }
  }

  // Marcar pagamento como expirado
  markPaymentAsExpired(): void {
    this.paymentStatus = "expired";
  }

  // Obter URL do YouTube
  getYouTubeUrl(): string {
    return `https://www.youtube.com/watch?v=${this.youtubeVideoId}`;
  }

  // Obter URL de embed do YouTube
  getYouTubeEmbedUrl(): string {
    return `https://www.youtube.com/embed/${this.youtubeVideoId}`;
  }

  // Aprovar sugestão
  approve(moderator?: User, reason?: string): void {
    this.status = SuggestionStatus.APPROVED;
    this.moderatedAt = new Date();
    this.moderatedBy = moderator;
    this.moderationReason = reason;
  }

  // Rejeitar sugestão
  reject(moderator?: User, reason?: string): void {
    this.status = SuggestionStatus.REJECTED;
    this.moderatedAt = new Date();
    this.moderatedBy = moderator;
    this.moderationReason = reason;
  }

  // Marcar como tocando
  startPlaying(): void {
    this.status = SuggestionStatus.PLAYING;
    this.playedAt = new Date();
  }

  // Marcar como tocada
  finishPlaying(duration?: number): void {
    this.status = SuggestionStatus.PLAYED;
    if (duration) {
      this.playDuration = duration;
    }
  }

  // Marcar como pulada
  skip(reason?: string): void {
    this.status = SuggestionStatus.SKIPPED;
    this.skipReason = reason;
  }

  // Atualizar posição na fila
  updateQueuePosition(position: number): void {
    this.queuePosition = position;
  }

  // Verificar se precisa de moderação
  needsModeration(): boolean {
    return this.status === SuggestionStatus.PENDING;
  }

  // Verificar se foi moderada
  isModerated(): boolean {
    return [SuggestionStatus.APPROVED, SuggestionStatus.REJECTED].includes(
      this.status
    );
  }

  // Serialização para JSON público
  toPublicJSON() {
    return {
      id: this.id,
      youtubeVideoId: this.youtubeVideoId,
      title: this.title,
      artist: this.artist,
      channelName: this.channelName,
      duration: this.duration,
      formattedDuration: this.getFormattedDuration(),
      thumbnailUrl: this.thumbnailUrl,
      status: this.status,
      voteCount: this.voteCount,
      upvotes: this.upvotes,
      downvotes: this.downvotes,
      votes: this.votes || [],
      queuePosition: this.queuePosition,
      score: this.calculateScore(),
      canBeVoted: this.canBeVoted(),
      isInQueue: this.isInQueue(),
      isExplicit: this.isExplicit(),
      isLive: this.isLive(),
      youtubeUrl: this.getYouTubeUrl(),
      createdAt: this.createdAt,
      // Campos de pagamento PIX
      isPriority: this.isPriority,
      isPaid: this.isPaid,
      paidAt: this.paidAt,
      paymentAmount: this.paymentAmount,
      paymentStatus: this.paymentStatus,
      isPriorityPaid: this.isPriorityPaid(),
      isPaymentPending: this.isPaymentPending(),
      // Campos do cliente
      clientName: this.clientName,
      tableNumber: this.tableNumber,
      metadata: {
        genre: this.metadata?.genre,
        mood: this.metadata?.mood,
        language: this.metadata?.language,
      },
    };
  }

  // Serialização para JSON administrativo
  toAdminJSON() {
    return {
      ...this.toPublicJSON(),
      clientSessionId: this.clientSessionId,
      clientIp: this.clientIp,
      source: this.source,
      moderatedAt: this.moderatedAt,
      moderationReason: this.moderationReason,
      moderatedBy: this.moderatedBy
        ? {
            id: this.moderatedBy.id,
            name: this.moderatedBy.name,
            email: this.moderatedBy.email,
          }
        : null,
      playedAt: this.playedAt,
      playDuration: this.playDuration,
      skipReason: this.skipReason,
      moderationFlags: this.moderationFlags,
      fullMetadata: this.metadata,
    };
  }
}
