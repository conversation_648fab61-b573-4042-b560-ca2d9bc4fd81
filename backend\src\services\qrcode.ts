import QRCode from 'qrcode';
import { AppDataSource } from '../config/database';
import { QRCode as QRCodeModel, QRCodeType } from '../models/QRCode';
import { Restaurant } from '../models/Restaurant';
import { logger } from '../utils/logger';
import { validateOrRej<PERSON>t, IsUUID, IsNotEmpty, IsInt, Min } from 'class-validator';

interface QRCodeOptions {
  width?: number;
  margin?: number;
  color?: {
    dark?: string;
    light?: string;
  };
  errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H';
}

interface QRCodeMetadata {
  [key: string]: string | number | boolean;
}

interface TableQRCode {
  id: string;
  restaurantId: string;
  tableNumber: string;
  tableName?: string;
  url: string;
  qrCodeDataURL: string;
  createdAt: string;
  isActive: boolean;
}

interface RestaurantQRCode {
  id: string;
  restaurantId: string;
  restaurantName: string;
  url: string;
  qrCodeDataURL: string;
  createdAt: string;
  isActive: boolean;
  customization?: {
    logo?: string;
    colors?: {
      primary: string;
      secondary: string;
    };
  };
}

class AppError extends Error {
  constructor(public statusCode: number, message: string) {
    super(message);
    this.name = 'AppError';
  }
}

class TableQRCodeInput {
  @IsUUID()
  restaurantId!: string;

  @IsNotEmpty()
  tableNumber!: string;

  tableName?: string;
}

class QRCodeService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = process.env.FRONTEND_URL || '';
    if (!this.baseUrl && process.env.NODE_ENV === 'production') {
      throw new AppError(500, 'FRONTEND_URL não configurado');
    }
    this.baseUrl = this.baseUrl || 'http://localhost:3001';
  }

  async generateTableQRCode(
    restaurantId: string,
    tableNumber: string,
    tableName?: string,
    options?: QRCodeOptions
  ): Promise<TableQRCode> {
    try {
      const input = new TableQRCodeInput();
      input.restaurantId = restaurantId;
      input.tableNumber = tableNumber;
      input.tableName = tableName;
      await validateOrReject(input);

      const qrCodeRepository = AppDataSource.getRepository(QRCodeModel);
      const restaurantRepository = AppDataSource.getRepository(Restaurant);

      const restaurant = await restaurantRepository.findOne({ where: { id: restaurantId } });
      if (!restaurant) {
        throw new AppError(404, 'Restaurante não encontrado');
      }

      const existingQR = await qrCodeRepository.findOne({
        where: { restaurant: { id: restaurantId }, tableNumber, type: QRCodeType.TABLE, isActive: true },
      });
      if (existingQR) {
        throw new AppError(400, `QR Code para mesa ${tableNumber} já existe`);
      }

      const tableId = `${restaurantId}-table-${tableNumber}`;
      const url = `${this.baseUrl}/restaurant/${restaurantId}?table=${tableNumber}`;

      const qrOptions: QRCodeOptions = {
        width: 300,
        margin: 2,
        color: { dark: '#000000', light: '#FFFFFF' },
        errorCorrectionLevel: 'M',
        ...options,
      };

      const qrCodeDataURL = await QRCode.toDataURL(url, qrOptions);

      const qrCode = qrCodeRepository.create({
        id: tableId,
        restaurant,
        type: QRCodeType.TABLE,
        name: tableName || `Mesa ${tableNumber}`,
        tableNumber,
        url,
        qrCodeData: qrCodeDataURL,
        isActive: true,
      });

      await qrCodeRepository.save(qrCode);

      return {
        id: tableId,
        restaurantId,
        tableNumber,
        tableName: tableName || `Mesa ${tableNumber}`,
        url,
        qrCodeDataURL,
        createdAt: new Date().toISOString(),
        isActive: true,
      };
    } catch (error) {
      logger.error(`Erro ao gerar QR code da mesa ${tableNumber}:`, error);
      throw error instanceof AppError ? error : new AppError(500, 'Falha ao gerar QR code da mesa');
    }
  }

  async generateRestaurantQRCode(
    restaurantId: string,
    restaurantName: string,
    customization?: RestaurantQRCode['customization'],
    options?: QRCodeOptions
  ): Promise<RestaurantQRCode> {
    try {
      if (!restaurantId || !restaurantName) {
        throw new AppError(400, 'restaurantId e restaurantName são obrigatórios');
      }

      const qrCodeRepository = AppDataSource.getRepository(QRCodeModel);
      const restaurantRepository = AppDataSource.getRepository(Restaurant);

      const restaurant = await restaurantRepository.findOne({ where: { id: restaurantId } });
      if (!restaurant) {
        throw new AppError(404, 'Restaurante não encontrado');
      }

      const existingQR = await qrCodeRepository.findOne({
        where: { restaurant: { id: restaurantId }, type: QRCodeType.RESTAURANT, isActive: true },
      });
      if (existingQR) {
        throw new AppError(400, 'QR Code geral do restaurante já existe');
      }

      const url = `${this.baseUrl}/restaurant/${restaurantId}`;
      const qrOptions: QRCodeOptions = {
        width: 400,
        margin: 2,
        color: {
          dark: customization?.colors?.primary || '#3B82F6',
          light: customization?.colors?.secondary || '#FFFFFF',
        },
        errorCorrectionLevel: 'H',
        ...options,
      };

      const qrCodeDataURL = await QRCode.toDataURL(url, qrOptions);

      const qrCode = qrCodeRepository.create({
        id: `${restaurantId}-main`,
        restaurant,
        type: QRCodeType.RESTAURANT,
        name: restaurantName,
        url,
        qrCodeData: qrCodeDataURL,
        isActive: true,
      });

      await qrCodeRepository.save(qrCode);

      return {
        id: `${restaurantId}-main`,
        restaurantId,
        restaurantName,
        url,
        qrCodeDataURL,
        createdAt: new Date().toISOString(),
        isActive: true,
        customization,
      };
    } catch (error) {
      logger.error('Erro ao gerar QR code do restaurante:', error);
      throw error instanceof AppError ? error : new AppError(500, 'Falha ao gerar QR code do restaurante');
    }
  }

  async generateMultipleTableQRCodes(
    restaurantId: string,
    tableCount: number,
    tablePrefix: string = 'Mesa',
    options?: QRCodeOptions
  ): Promise<TableQRCode[]> {
    try {
      if (tableCount < 1 || tableCount > 50) {
        throw new AppError(400, 'tableCount deve estar entre 1 e 50');
      }

      const qrCodePromises = Array.from({ length: tableCount }, (_, i) => {
        const tableNumber = (i + 1).toString().padStart(2, '0');
        const tableName = `${tablePrefix} ${i + 1}`;
        return this.generateTableQRCode(restaurantId, tableNumber, tableName, options);
      });

      return await Promise.all(qrCodePromises);
    } catch (error) {
      logger.error('Erro ao gerar QR codes em lote:', error);
      throw error instanceof AppError ? error : new AppError(500, 'Falha ao gerar QR codes em lote');
    }
  }

  async generateContextualQRCode(
    restaurantId: string,
    context: {
      type: 'table' | 'counter' | 'delivery' | 'takeout';
      identifier: string;
      name?: string;
      metadata?: QRCodeMetadata;
    },
    options?: QRCodeOptions
  ): Promise<TableQRCode> {
    try {
      if (!restaurantId || !context.identifier) {
        throw new AppError(400, 'restaurantId e identifier são obrigatórios');
      }

      const contextId = `${restaurantId}-${context.type}-${context.identifier}`;
      const urlParams = new URLSearchParams({
        type: context.type,
        id: context.identifier,
        ...(context.metadata || {}),
      });
      const url = `${this.baseUrl}/restaurant/${restaurantId}?${urlParams.toString()}`;

      const qrOptions: QRCodeOptions = {
        width: 300,
        margin: 2,
        color: {
          dark: this.getColorByType(context.type),
          light: '#FFFFFF',
        },
        errorCorrectionLevel: 'M',
        ...options,
      };

      const qrCodeDataURL = await QRCode.toDataURL(url, qrOptions);

      const qrCodeRepository = AppDataSource.getRepository(QRCodeModel);
      const restaurantRepository = AppDataSource.getRepository(Restaurant);

      const restaurant = await restaurantRepository.findOne({ where: { id: restaurantId } });
      if (!restaurant) {
        throw new AppError(404, 'Restaurante não encontrado');
      }

      const qrCode = qrCodeRepository.create({
        id: contextId,
        restaurant,
        type: QRCodeType[context.type.toUpperCase() as keyof typeof QRCodeType],
        name: context.name || `${context.type} ${context.identifier}`,
        tableNumber: context.identifier,
        url,
        qrCodeData: qrCodeDataURL,
        isActive: true,
      });

      await qrCodeRepository.save(qrCode);

      return {
        id: contextId,
        restaurantId,
        tableNumber: context.identifier,
        tableName: context.name || `${context.type} ${context.identifier}`,
        url,
        qrCodeDataURL,
        createdAt: new Date().toISOString(),
        isActive: true,
      };
    } catch (error) {
      logger.error(`Erro ao gerar QR code contextual ${context.type}:`, error);
      throw error instanceof AppError ? error : new AppError(500, 'Falha ao gerar QR code contextual');
    }
  }

  validateQRCodeURL(url: string): boolean {
    try {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split('/');
      if (pathParts[1] !== 'restaurant' || !/^[a-z0-9-]+$/.test(pathParts[2])) {
        return false;
      }
      return urlObj.hostname === new URL(this.baseUrl).hostname;
    } catch {
      return false;
    }
  }

  parseQRCodeURL(url: string): {
    restaurantId: string;
    tableNumber?: string;
    type?: string;
    metadata?: QRCodeMetadata;
  } | null {
    try {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split('/');
      if (pathParts[1] !== 'restaurant') {
        return null;
      }

      const restaurantId = pathParts[2];
      const params = new URLSearchParams(urlObj.search);

      return {
        restaurantId,
        tableNumber: params.get('table') || undefined,
        type: params.get('type') || undefined,
        metadata: Object.fromEntries(params.entries()),
      };
    } catch {
      return null;
    }
  }

  async generateQRCodeSVG(url: string, options?: QRCodeOptions): Promise<string> {
    try {
      const qrOptions = {
        type: 'svg' as const,
        width: 400,
        margin: 2,
        color: { dark: '#000000', light: '#FFFFFF' },
        errorCorrectionLevel: 'M' as const,
        ...options,
      };

      return await QRCode.toString(url, qrOptions);
    } catch (error) {
      logger.error('Erro ao gerar QR code SVG:', error);
      throw new AppError(500, 'Falha ao gerar QR code SVG');
    }
  }

  private getColorByType(type: string): string {
    const colors = {
      table: '#3B82F6',
      counter: '#10B981',
      delivery: '#F59E0B',
      takeout: '#8B5CF6',
    };
    return colors[type as keyof typeof colors] || '#6B7280';
  }
}

export const qrCodeService = new QRCodeService();
export type { TableQRCode, RestaurantQRCode, QRCodeOptions, QRCodeMetadata };
