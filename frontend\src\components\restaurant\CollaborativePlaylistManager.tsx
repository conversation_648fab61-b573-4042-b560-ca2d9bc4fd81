import React, { useState, useEffect } from "react";
import { 
  Music, 
  Plus, 
  Users, 
  DollarSign, 
  TrendingUp, 
  Youtube,
  Settings,
  Play,
  Pause,
  SkipForward
} from "lucide-react";
import { buildApiUrl, getAuthHeaders } from "../../config/api";

interface CollaborativePlaylistStats {
  playlistName: string;
  youtubePlaylistId: string;
  totalTracks: number;
  paidSuggestions: number;
  freeSuggestions: number;
  totalRevenue: number;
  tracks?: Track[];
}

interface Track {
  id: string;
  title: string;
  artist: string;
  genre?: string;
  duration?: number;
}

interface CollaborativePlaylistManagerProps {
  restaurantId: string;
}

export const CollaborativePlaylistManager: React.FC<CollaborativePlaylistManagerProps> = ({
  restaurantId,
}) => {
  const [stats, setStats] = useState<CollaborativePlaylistStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [formData, setFormData] = useState({
    playlistName: "",
    description: "",
    initialTracks: [] as string[],
  });
  const [genreFilter, setGenreFilter] = useState<string>("");

  // Carregar estatísticas da playlist colaborativa
  const loadStats = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        buildApiUrl(`/collaborative-playlist/${restaurantId}/stats`),
        {
          headers: getAuthHeaders(),
        }
      );

      if (response.ok) {
        const data = await response.json();
        setStats(data.data);
      } else {
        // Playlist ainda não existe
        setStats(null);
      }
    } catch (error) {
      console.error("Erro ao carregar estatísticas:", error);
      setStats(null);
    } finally {
      setLoading(false);
    }
  };

  // Criar nova playlist colaborativa
  const createPlaylist = async () => {
    if (!formData.playlistName.trim()) {
      alert("Nome da playlist é obrigatório!");
      return;
    }

    try {
      setCreating(true);
      const response = await fetch(
        buildApiUrl(`/collaborative-playlist/${restaurantId}/create`),
        {
          method: "POST",
          headers: getAuthHeaders(),
          body: JSON.stringify(formData),
        }
      );

      const data = await response.json();

      if (response.ok) {
        alert(`Playlist "${formData.playlistName}" criada com sucesso!`);
        setShowCreateForm(false);
        setFormData({ playlistName: "", description: "", initialTracks: [] });
        loadStats(); // Recarregar estatísticas
      } else {
        alert(`Erro ao criar playlist: ${data.message}`);
      }
    } catch (error) {
      console.error("Erro ao criar playlist:", error);
      alert("Erro ao criar playlist. Tente novamente.");
    } finally {
      setCreating(false);
    }
  };

  useEffect(() => {
    loadStats();
  }, [restaurantId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Carregando playlist colaborativa...</span>
      </div>
    );
  }

  // Lista de faixas (quando disponível)
  const tracks: Track[] = stats?.tracks ?? [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
            <Users className="h-6 w-6 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              Playlist Colaborativa
            </h2>
            <p className="text-gray-600">
              Sistema simplificado com conta YouTube central
            </p>
          </div>
        </div>

        {!stats && (
          <button
            onClick={() => setShowCreateForm(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4" />
            <span>Criar Playlist</span>
          </button>
        )}
      </div>

      {/* Formulário de Criação */}
      {showCreateForm && (
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Criar Nova Playlist Colaborativa
          </h3>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Nome da Playlist *
              </label>
              <input
                type="text"
                value={formData.playlistName}
                onChange={(e) =>
                  setFormData({ ...formData, playlistName: e.target.value })
                }
                placeholder="Ex: Playlist Principal"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Descrição
              </label>
              <textarea
                value={formData.description}
                onChange={(e) =>
                  setFormData({ ...formData, description: e.target.value })
                }
                placeholder="Descrição da playlist colaborativa..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div className="flex items-center space-x-3">
              <button
                onClick={createPlaylist}
                disabled={creating}
                className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors"
              >
                {creating ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <Plus className="h-4 w-4" />
                )}
                <span>{creating ? "Criando..." : "Criar Playlist"}</span>
              </button>

              <button
                onClick={() => setShowCreateForm(false)}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancelar
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Estatísticas da Playlist */}
      {stats && (
        <div className="space-y-6">
          {/* Cards de Estatísticas */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total de Músicas</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalTracks}</p>
                </div>
                <Music className="h-8 w-8 text-blue-600" />
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Sugestões Pagas</p>
                  <p className="text-2xl font-bold text-green-600">{stats.paidSuggestions}</p>
                </div>
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Sugestões Gratuitas</p>
                  <p className="text-2xl font-bold text-purple-600">{stats.freeSuggestions}</p>
                </div>
                <Users className="h-8 w-8 text-purple-600" />
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Receita Total</p>
                  <p className="text-2xl font-bold text-green-600">
                    R$ {Number(stats.totalRevenue || 0).toFixed(2)}
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-600" />
              </div>
            </div>
          </div>

          {/* Informações da Playlist */}
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                {stats.playlistName}
              </h3>
              <div className="flex items-center space-x-2">
                <Youtube className="h-5 w-5 text-red-600" />
                <span className="text-sm text-gray-600">
                  ID: {stats.youtubePlaylistId}
                </span>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <a
                href={`https://www.youtube.com/playlist?list=${stats.youtubePlaylistId}`}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                <Youtube className="h-4 w-4" />
                <span>Ver no YouTube</span>
              </a>

              <button
                onClick={loadStats}
                className="flex items-center space-x-2 px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Settings className="h-4 w-4" />
                <span>Atualizar</span>
              </button>
            </div>
          </div>

          {/* Filtro de Gênero */}
          {tracks.length > 0 && (
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Filtrar por gênero:</label>
              <select
                value={genreFilter}
                onChange={e => setGenreFilter(e.target.value)}
                className="px-3 py-2 border rounded-lg"
              >
                <option value="">Todos</option>
                {[...new Set(tracks.map((t: Track) => t.genre))].map((g) => (
                  <option key={String(g)} value={String(g)}>{g}</option>
                ))}
              </select>
            </div>
          )}
          {/* Lista de Músicas */}
          {tracks.length > 0 && (
            <div className="bg-white p-4 rounded-lg border border-gray-200 mb-6">
              <h4 className="text-md font-semibold mb-2">Músicas da Playlist</h4>
              <table className="min-w-full text-sm">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="px-2 py-1 text-left">Título</th>
                    <th className="px-2 py-1 text-left">Artista</th>
                    <th className="px-2 py-1 text-left">Gênero</th>
                    <th className="px-2 py-1 text-left">Duração</th>
                  </tr>
                </thead>
                <tbody>
                  {tracks
                    .filter((t: Track) => !genreFilter || t.genre === genreFilter)
                    .map((track: Track) => (
                    <tr key={track.id} className="border-b">
                      <td className="px-2 py-1">{track.title}</td>
                      <td className="px-2 py-1">{track.artist}</td>
                      <td className="px-2 py-1">{track.genre}</td>
                      <td className="px-2 py-1">{track.duration ? `${Math.floor(track.duration/60)}:${(track.duration%60).toString().padStart(2,"0")}` : "-"}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
          {/* Vantagens do Sistema Colaborativo */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg border border-blue-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              🎵 Vantagens da Playlist Colaborativa
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-700">
              <div className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <span>Uma única conta YouTube Premium gerencia tudo</span>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <span>Sem necessidade de OAuth individual por restaurante</span>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <span>Configuração simplificada no Docker</span>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <span>Criação rápida de novos restaurantes</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Estado Vazio */}
      {!stats && !showCreateForm && (
        <div className="text-center py-12">
          <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <Users className="h-12 w-12 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Nenhuma Playlist Colaborativa
          </h3>
          <p className="text-gray-600 mb-6">
            Crie uma playlist colaborativa para começar a receber sugestões dos clientes.
          </p>
          <button
            onClick={() => setShowCreateForm(true)}
            className="flex items-center space-x-2 mx-auto px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-5 w-5" />
            <span>Criar Primeira Playlist</span>
          </button>
        </div>
      )}
    </div>
  );
};
