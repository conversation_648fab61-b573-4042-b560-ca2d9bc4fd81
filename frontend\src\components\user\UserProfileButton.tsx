import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { User, Settings } from 'lucide-react';

// Componentes
import UserSession from './UserSession';

interface UserProfileButtonProps {
  restaurantId: string;
}

const UserProfileButton: React.FC<UserProfileButtonProps> = ({ restaurantId }) => {
  const [isSessionOpen, setIsSessionOpen] = useState(false);
  const [userName, setUserName] = useState('');
  const [hasActivity, setHasActivity] = useState(false);

  useEffect(() => {
    // Carregar nome do usuário
    const savedName = localStorage.getItem(`userName_${restaurantId}`);
    if (savedName) {
      setUserName(savedName);
    }

    // Verificar se há atividade do usuário
    const sessionStart = localStorage.getItem(`sessionStart_${restaurantId}`);
    const votesToday = localStorage.getItem(`votesToday_${restaurantId}`);
    
    if (sessionStart || votesToday) {
      setHasActivity(true);
    }
  }, [restaurantId]);

  return (
    <>
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={() => setIsSessionOpen(true)}
        className="relative flex items-center space-x-2 px-3 py-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
      >
        <div className="relative">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
            <User className="w-4 h-4 text-white" />
          </div>
          
          {hasActivity && (
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-800"></div>
          )}
        </div>
        
        <div className="hidden sm:block text-left">
          <p className="text-sm font-medium text-gray-900 dark:text-white">
            {userName || 'Meu Perfil'}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Ver estatísticas
          </p>
        </div>
        
        <Settings className="w-4 h-4 text-gray-400" />
      </motion.button>

      <UserSession
        isOpen={isSessionOpen}
        onClose={() => setIsSessionOpen(false)}
        restaurantId={restaurantId}
      />
    </>
  );
};

export default UserProfileButton;
