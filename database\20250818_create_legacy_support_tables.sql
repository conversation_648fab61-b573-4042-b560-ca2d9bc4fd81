-- Tabelas legadas (compat) para atender esquemas antigos e testes utilitários
-- Criação idempotente e sem FKs arriscadas

-- songs
CREATE TABLE IF NOT EXISTS songs (
  id VARCHAR(255) PRIMARY KEY,
  youtube_video_id VARCHAR(255) UNIQUE NOT NULL,
  title VARCHAR(500) NOT NULL,
  artist VA<PERSON>HA<PERSON>(255),
  duration INTEGER,
  thumbnail_url VARCHAR(500),
  channel_name VARCHAR(255),
  view_count BIGINT DEFAULT 0,
  published_at DATE,
  genre VARCHAR(100),
  language VARCHAR(10),
  created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- playback_queue
CREATE TABLE IF NOT EXISTS playback_queue (
  id VARCHAR(255) PRIMARY KEY,
  restaurant_id VARCHAR(255) NOT NULL,
  suggestion_id UUID NOT NULL,
  position INTEGER NOT NULL,
  is_playing BOOLEAN DEFAULT FALSE,
  played_at TIMESTAMP WITHOUT TIME ZONE,
  added_to_queue_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);
CREATE INDEX IF NOT EXISTS idx_playback_queue_restaurant ON playback_queue(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_playback_queue_position ON playback_queue(position);

-- analytics_events
CREATE TABLE IF NOT EXISTS analytics_events (
  id VARCHAR(255) PRIMARY KEY,
  restaurant_id VARCHAR(255) NOT NULL,
  client_session_id UUID,
  event_type VARCHAR(50) NOT NULL,
  event_data JSON,
  ip_address VARCHAR(45),
  user_agent TEXT,
  created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);
CREATE INDEX IF NOT EXISTS idx_analytics_restaurant ON analytics_events(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_analytics_type ON analytics_events(event_type);
CREATE INDEX IF NOT EXISTS idx_analytics_date ON analytics_events(created_at);

-- daily_stats
CREATE TABLE IF NOT EXISTS daily_stats (
  id VARCHAR(255) PRIMARY KEY,
  restaurant_id VARCHAR(255) NOT NULL,
  date DATE NOT NULL,
  suggestions_count INTEGER DEFAULT 0,
  votes_count INTEGER DEFAULT 0,
  plays_count INTEGER DEFAULT 0,
  unique_users INTEGER DEFAULT 0,
  total_session_time INTEGER DEFAULT 0,
  created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
  UNIQUE (restaurant_id, date)
);
CREATE INDEX IF NOT EXISTS idx_daily_stats_restaurant ON daily_stats(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_daily_stats_date ON daily_stats(date);
