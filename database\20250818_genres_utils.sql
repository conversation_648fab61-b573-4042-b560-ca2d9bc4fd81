-- Funções e VIEW relacionadas a gêneros (idempotente)

-- Função para atualizar updatedAt em genres
CREATE OR REPLACE FUNCTION update_genres_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger (cria somente se a tabela existir)
DO $$
DECLARE r_exists boolean;
BEGIN
  SELECT EXISTS (
    SELECT 1 FROM information_schema.tables WHERE table_schema='public' AND table_name='genres'
  ) INTO r_exists;

  IF r_exists THEN
    IF EXISTS (
      SELECT 1 FROM information_schema.triggers WHERE event_object_table='genres' AND trigger_name='trigger_update_genres_updated_at'
    ) THEN
      DROP TRIGGER trigger_update_genres_updated_at ON genres;
    END IF;

  CREATE TRIGGER trigger_update_genres_updated_at
        BEFORE UPDATE ON genres
        FOR EACH ROW
        EXECUTE FUNCTION update_genres_updated_at();
  END IF;
END $$;

-- Função para incrementar uso de gênero
CREATE OR REPLACE FUNCTION increment_genre_usage(genre_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE genres 
  SET usage_count = usage_count + 1,
    last_used_at = NOW()
    WHERE id = genre_id;
END;
$$ LANGUAGE plpgsql;

-- Função para obter gêneros por categoria
CREATE OR REPLACE FUNCTION get_genres_by_category(category_filter VARCHAR DEFAULT NULL)
RETURNS TABLE (
    id UUID,
    name VARCHAR,
    display_name VARCHAR,
    description VARCHAR,
    category VARCHAR,
    color VARCHAR,
    icon VARCHAR,
    priority INTEGER,
    is_active BOOLEAN,
    usage_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        g.id,
        g.name,
        g.display_name,
        g.description,
        g.category,
        g.color,
        g.icon,
        g.priority,
        g.is_active,
        g.usage_count
    FROM genres g
    WHERE (category_filter IS NULL OR g.category = category_filter)
      AND g.is_active = true
    ORDER BY g.priority ASC, g.display_name ASC;
END;
$$ LANGUAGE plpgsql;

-- Função para buscar gêneros por texto
CREATE OR REPLACE FUNCTION search_genres(search_text VARCHAR)
RETURNS TABLE (
    id UUID,
    name VARCHAR,
  display_name VARCHAR,
    description VARCHAR,
    category VARCHAR,
    color VARCHAR,
    icon VARCHAR,
    priority INTEGER,
  is_active BOOLEAN,
  usage_count INTEGER,
    relevance FLOAT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        g.id,
        g.name,
    g.display_name,
        g.description,
        g.category,
        g.color,
        g.icon,
        g.priority,
    g.is_active,
    g.usage_count,
        (
            CASE 
        WHEN LOWER(g.display_name) = LOWER(search_text) THEN 1.0
                WHEN LOWER(g.name) = LOWER(search_text) THEN 0.9
        WHEN LOWER(g.display_name) LIKE LOWER(search_text || '%') THEN 0.8
                WHEN LOWER(g.name) LIKE LOWER(search_text || '%') THEN 0.7
        WHEN LOWER(g.display_name) LIKE LOWER('%' || search_text || '%') THEN 0.6
                WHEN LOWER(g.name) LIKE LOWER('%' || search_text || '%') THEN 0.5
                WHEN LOWER(g.description) LIKE LOWER('%' || search_text || '%') THEN 0.3
                ELSE 0.1
            END
        ) as relevance
    FROM genres g
  WHERE g.is_active = true
      AND (
    LOWER(g.display_name) LIKE LOWER('%' || search_text || '%') OR
        LOWER(g.name) LIKE LOWER('%' || search_text || '%') OR
        LOWER(g.description) LIKE LOWER('%' || search_text || '%')
      )
  ORDER BY relevance DESC, g.priority ASC, g.display_name ASC;
END;
$$ LANGUAGE plpgsql;

-- VIEW para estatísticas de gêneros
CREATE OR REPLACE VIEW genre_stats AS
SELECT 
    g.id,
    g.name,
  g.display_name,
    g.category,
  g.usage_count,
  g.last_used_at,
    COUNT(sg.suggestion_id) as suggestions_count,
    COUNT(DISTINCT sg.suggestion_id) as unique_suggestions
FROM genres g
LEFT JOIN suggestion_genres sg ON g.id = sg.genre_id
GROUP BY g.id, g.name, g.display_name, g.category, g.usage_count, g.last_used_at
ORDER BY g.usage_count DESC, g.display_name ASC;
