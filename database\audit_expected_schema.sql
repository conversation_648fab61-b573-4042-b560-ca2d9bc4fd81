-- Auditoria do schema esperado (derivada de database/todos_bancos)
-- Lista o que está faltando no banco atual: tabelas, colunas-chave, enums/labels, índices e trigger

-- Tabelas esperadas
WITH expected(table_name) AS (
  VALUES
    ('restaurants'),('users'),('playlists'),('playlist_tracks'),('playlist_schedules'),
    ('suggestions'),('votes'),('client_sessions'),('play_history'),('analytics_daily'),
    ('moderation_rules'),('competitive_votes'),('payments'),('qr_codes'),('lyrics'),
    ('genres'),('rewards')
)
SELECT 'MISSING_TABLE' AS issue, e.table_name
FROM expected e
LEFT JOIN information_schema.tables t ON t.table_schema='public' AND t.table_name=e.table_name
WHERE t.table_name IS NULL
ORDER BY e.table_name;

-- Colunas-chave por tabela (parcial)
WITH expected_cols AS (
  SELECT * FROM (VALUES
    ('restaurants','id'),('restaurants','name'),('restaurants','isActive'),
    ('users','id'),('users','email'),('users','password'),('users','restaurant_id'),
    ('playlists','id'),('playlists','restaurant_id'),('playlist_tracks','playlist_id'),
    ('suggestions','id'),('suggestions','youtubeVideoId'),('suggestions','status'),('suggestions','restaurant_id'),
    ('votes','id'),('votes','vote_type'),('votes','suggestion_id'),
    ('client_sessions','id'),('client_sessions','sessionToken'),('client_sessions','restaurant_id'),
    ('play_history','id'),('play_history','status'),('play_history','restaurant_id'),
    ('analytics_daily','id'),('analytics_daily','restaurant_id'),
    ('moderation_rules','id'),('moderation_rules','ruleType'),('moderation_rules','restaurant_id'),
    ('payments','id'),('payments','status'),('payments','suggestion_id')
  ) AS t(table_name,column_name)
)
SELECT 'MISSING_COLUMN' AS issue, ec.table_name, ec.column_name
FROM expected_cols ec
LEFT JOIN information_schema.columns c ON c.table_schema='public' AND c.table_name=ec.table_name AND c.column_name=ec.column_name
WHERE c.column_name IS NULL
ORDER BY ec.table_name, ec.column_name;

-- Enums esperados e valores
WITH expected_enums AS (
  SELECT * FROM (VALUES
    ('restaurant_status', ARRAY['active','inactive','suspended','trial']::text[]),
    ('user_role', ARRAY['super_admin','admin','moderator','staff']::text[]),
    ('playlist_type', ARRAY['custom','youtube','youtube_import','auto_generated','suggestions']::text[]),
    ('playlist_status', ARRAY['active','inactive','archived','deleted']::text[]),
    ('suggestion_status', ARRAY['pending','approved','rejected','playing','played','skipped','completed','expired']::text[]),
    ('suggestion_source', ARRAY['client','admin','auto','import']::text[]),
    ('vote_type', ARRAY['up','down']::text[]),
    ('play_status', ARRAY['playing','completed','skipped','interrupted','error']::text[]),
    ('rule_type', ARRAY['blacklist_word','blacklist_artist','blacklist_channel','genre_restriction','duration_limit','content_rating','language_filter','time_restriction']::text[]),
    ('rule_action', ARRAY['auto_reject','flag_for_review','require_approval','auto_approve']::text[])
  ) AS t(enum_name, values)
)
SELECT 'MISSING_ENUM' AS issue, e.enum_name
FROM expected_enums e
LEFT JOIN pg_type t ON t.typname=e.enum_name
WHERE t.oid IS NULL
UNION ALL
SELECT 'MISSING_ENUM_LABEL' AS issue, e.enum_name||':'||v.value
FROM expected_enums e
JOIN pg_type t ON t.typname=e.enum_name
LEFT JOIN LATERAL unnest(e.values) v(value) ON TRUE
WHERE NOT EXISTS (
  SELECT 1 FROM pg_enum en2 WHERE en2.enumtypid=t.oid AND en2.enumlabel=v.value
)
ORDER BY 1,2;

-- Índices essenciais
WITH expected_idx AS (
  SELECT * FROM (VALUES
    ('users','idx_users_email'),('users','idx_users_role'),('users','idx_users_active'),('users','idx_users_restaurant_role'),
    ('playlists','idx_playlists_restaurant'),('playlists','idx_playlists_type'),('playlists','idx_playlists_status'),('playlists','idx_playlists_default'),
    ('suggestions','idx_suggestions_restaurant'),('suggestions','idx_suggestions_youtube'),('suggestions','idx_suggestions_status'),('suggestions','idx_suggestions_created'),
    ('votes','uq_vote_suggestion_client_session'),('votes','idx_votes_suggestion'),('votes','idx_votes_type')
  ) AS t(table_name,indexname)
)
SELECT 'MISSING_INDEX' AS issue, ei.table_name, ei.indexname
FROM expected_idx ei
LEFT JOIN pg_indexes pi ON pi.schemaname='public' AND pi.indexname=ei.indexname
WHERE pi.indexname IS NULL
ORDER BY ei.table_name, ei.indexname;

-- Gatilho de manutenção esperado (contadores de votos)
SELECT 'MISSING_TRIGGER' AS issue, 'votes' AS table_name, 'trg_update_vote_counts' AS trigger
WHERE NOT EXISTS (
  SELECT 1 FROM information_schema.triggers WHERE event_object_table='votes' AND trigger_name='trg_update_vote_counts'
);

-- Fim da auditoria
