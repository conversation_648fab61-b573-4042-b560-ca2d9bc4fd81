{"version": 3, "file": "ProblematicTracksAlert-71e8e181.js", "sources": ["../../src/components/restaurant/ProblematicTracksAlert.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport {\r\n  AlertTriangle,\r\n  X,\r\n  <PERSON><PERSON><PERSON>3,\r\n  CheckCircle,\r\n  Eye,\r\n  Music,\r\n  RefreshCw,\r\n  Trash2,\r\n  XCircle,\r\n} from \"lucide-react\";\r\nimport { toast } from \"react-hot-toast\";\r\nimport { useRestaurantContext } from \"./RestaurantDashboard\";\r\nimport { buildApiUrl } from \"../../config/api\";\r\n// import apiService from \"@/services/api\";\r\n\r\n// Definindo tipos de forma mais clara e reutilizável\r\nenum Performance {\r\n  Excellent = \"excellent\",\r\n  Good = \"good\",\r\n  Average = \"average\",\r\n  Poor = \"poor\",\r\n  Terrible = \"terrible\",\r\n}\r\n\r\nenum Recommendation {\r\n  Keep = \"keep\",\r\n  Monitor = \"monitor\",\r\n  Remove = \"remove\",\r\n  Blacklist = \"blacklist\",\r\n}\r\n\r\ninterface ProblematicTrack {\r\n  id: string;\r\n  title: string;\r\n  artist: string;\r\n  youtubeVideoId: string;\r\n  totalVotes: number;\r\n  upvotes: number;\r\n  downvotes: number;\r\n  score: number;\r\n  negativeVoteRatio: number;\r\n  playCount: number;\r\n  skipCount: number;\r\n  completionRate: number;\r\n  performance: Performance;\r\n  recommendation: Recommendation;\r\n}\r\n\r\ninterface ProblematicTracksReport {\r\n  problematicTracks: ProblematicTrack[];\r\n  recommendations: string[];\r\n  healthScore: number;\r\n}\r\n\r\nconst ProblematicTracksAlert: React.FC = () => {\r\n  const { restaurantId } = useRestaurantContext();\r\n  const [report, setReport] = useState<ProblematicTracksReport | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [showAlert, setShowAlert] = useState(false);\r\n  const [dismissed, setDismissed] = useState(false);\r\n\r\n  // Usar useCallback para evitar recriação desnecessária da função\r\n  const loadProblematicTracksReport = useCallback(async () => {\r\n    if (!restaurantId) {\r\n      console.error(\"Restaurant ID não encontrado\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setLoading(true);\r\n      const response = await fetch(\r\n        buildApiUrl(`/playback/${restaurantId}/problematic-report`)\r\n      );\r\n\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        setReport(data.report);\r\n      } else {\r\n        throw new Error(`HTTP ${response.status}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\r\n        \"Erro ao carregar relatório de músicas problemáticas:\",\r\n        error\r\n      );\r\n      setReport({\r\n        problematicTracks: [],\r\n        recommendations: [],\r\n        healthScore: 85,\r\n      });\r\n      toast.error(\"Falha ao carregar relatório\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [restaurantId]);\r\n\r\n  // Função para lidar com remoção de música\r\n  const handleRemoveTrack = useCallback(\r\n    async (trackId: string) => {\r\n      if (!restaurantId) return;\r\n\r\n      try {\r\n        const response = await fetch(\r\n          buildApiUrl(`/restaurants/${restaurantId}/playlist/${trackId}`),\r\n          { method: \"DELETE\" }\r\n        );\r\n\r\n        if (response.ok) {\r\n          toast.success(\"Música removida com sucesso\");\r\n          await loadProblematicTracksReport();\r\n        } else {\r\n          throw new Error(`HTTP ${response.status}`);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Erro ao remover música:\", error);\r\n        toast.error(\"Erro ao remover música\");\r\n      }\r\n    },\r\n    [restaurantId, loadProblematicTracksReport]\r\n  );\r\n\r\n  // Função para dispensar alerta\r\n  const handleDismissAlert = useCallback(() => {\r\n    setShowAlert(false);\r\n    setDismissed(true);\r\n    setTimeout(() => setDismissed(false), 60 * 60 * 1000); // 1 hora\r\n  }, []);\r\n\r\n  // Carregar relatório e configurar intervalo\r\n  useEffect(() => {\r\n    if (!restaurantId) return;\r\n\r\n    // Carregar apenas uma vez quando o componente montar\r\n    loadProblematicTracksReport();\r\n\r\n    // Configurar intervalo apenas se necessário (a cada 10 minutos para reduzir carga)\r\n    const interval = setInterval(loadProblematicTracksReport, 10 * 60 * 1000);\r\n    return () => clearInterval(interval);\r\n  }, [restaurantId]); // Remover loadProblematicTracksReport das dependências para evitar loops\r\n\r\n  // Controlar exibição do alerta\r\n  useEffect(() => {\r\n    if (report?.problematicTracks.length && !dismissed) {\r\n      setShowAlert(true);\r\n    }\r\n  }, [report, dismissed]);\r\n\r\n  // Função para determinar cor do score de saúde\r\n  const getHealthColor = (score: number): string => {\r\n    if (score >= 80) return \"text-green-600\";\r\n    if (score >= 60) return \"text-yellow-600\";\r\n    if (score >= 40) return \"text-orange-600\";\r\n    return \"text-red-600\";\r\n  };\r\n\r\n  // Função para determinar cor de fundo do score de saúde\r\n  const getHealthBgColor = (score: number): string => {\r\n    if (score >= 80) return \"bg-green-50 border-green-200\";\r\n    if (score >= 60) return \"bg-yellow-50 border-yellow-200\";\r\n    if (score >= 40) return \"bg-orange-50 border-orange-200\";\r\n    return \"bg-red-50 border-red-200\";\r\n  };\r\n\r\n  // Função para determinar cor da recomendação\r\n  const getRecommendationColor = (recommendation: Recommendation): string => {\r\n    switch (recommendation) {\r\n      case Recommendation.Keep:\r\n        return \"text-green-600 bg-green-100\";\r\n      case Recommendation.Monitor:\r\n        return \"text-yellow-600 bg-yellow-100\";\r\n      case Recommendation.Remove:\r\n        return \"text-red-600 bg-red-100\";\r\n      case Recommendation.Blacklist:\r\n        return \"text-red-800 bg-red-200\";\r\n      default:\r\n        return \"text-gray-600 bg-gray-100\";\r\n    }\r\n  };\r\n\r\n  // Função para determinar ícone da recomendação\r\n  const getRecommendationIcon = (recommendation: Recommendation) => {\r\n    switch (recommendation) {\r\n      case Recommendation.Keep:\r\n        return <CheckCircle className=\"w-4 h-4\" />;\r\n      case Recommendation.Monitor:\r\n        return <Eye className=\"w-4 h-4\" />;\r\n      case Recommendation.Remove:\r\n        return <XCircle className=\"w-4 h-4\" />;\r\n      case Recommendation.Blacklist:\r\n        return <Trash2 className=\"w-4 h-4\" />;\r\n      default:\r\n        return <Music className=\"w-4 h-4\" />;\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center space-x-2 text-gray-600\">\r\n        <RefreshCw className=\"w-4 h-4 animate-spin\" />\r\n        <span className=\"text-sm\">Verificando músicas problemáticas...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!report || !report.problematicTracks.length) {\r\n    return (\r\n      <div className=\"flex items-center space-x-2 text-green-600\">\r\n        <CheckCircle className=\"w-4 h-4\" />\r\n        <span className=\"text-sm\">\r\n          Playlist saudável - nenhuma música problemática\r\n        </span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {/* Alerta flutuante */}\r\n      <AnimatePresence>\r\n        {showAlert && (\r\n          <motion.div\r\n            initial={{ opacity: 0, y: -50, scale: 0.9 }}\r\n            animate={{ opacity: 1, y: 0, scale: 1 }}\r\n            exit={{ opacity: 0, y: -50, scale: 0.9 }}\r\n            className=\"fixed top-4 right-4 z-50 max-w-md\"\r\n          >\r\n            <div\r\n              className={`rounded-lg border-2 p-4 shadow-lg ${getHealthBgColor(\r\n                report.healthScore\r\n              )}`}\r\n            >\r\n              <div className=\"flex items-start justify-between\">\r\n                <div className=\"flex items-start space-x-3\">\r\n                  <AlertTriangle\r\n                    className={`w-6 h-6 mt-0.5 ${getHealthColor(\r\n                      report.healthScore\r\n                    )}`}\r\n                  />\r\n                  <div className=\"flex-1\">\r\n                    <h3 className=\"font-semibold text-gray-900\">\r\n                      🚨 Músicas Problemáticas Detectadas\r\n                    </h3>\r\n                    <p className=\"text-sm text-gray-700 mt-1\">\r\n                      {report.problematicTracks.length} música(s) com alta taxa\r\n                      de rejeição\r\n                    </p>\r\n                    <div className=\"mt-2\">\r\n                      <div className=\"flex items-center space-x-2 text-sm\">\r\n                        <BarChart3 className=\"w-4 h-4\" />\r\n                        <span\r\n                          className={`font-medium ${getHealthColor(\r\n                            report.healthScore\r\n                          )}`}\r\n                        >\r\n                          Score de Saúde: {report.healthScore}/100\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <button\r\n                  onClick={handleDismissAlert}\r\n                  className=\"text-gray-400 hover:text-gray-600 transition-colors\"\r\n                  aria-label=\"Fechar alerta\"\r\n                >\r\n                  <X className=\"w-5 h-5\" />\r\n                </button>\r\n              </div>\r\n              <div className=\"mt-3 flex space-x-2\">\r\n                <button\r\n                  onClick={() => setShowAlert(false)}\r\n                  className=\"text-sm px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\"\r\n                >\r\n                  Ver Detalhes\r\n                </button>\r\n                <button\r\n                  onClick={handleDismissAlert}\r\n                  className=\"text-sm px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors\"\r\n                >\r\n                  Dispensar\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n\r\n      {/* Painel de detalhes */}\r\n      {!showAlert && (\r\n        <div className=\"space-y-4\">\r\n          <div\r\n            className={`rounded-lg border p-4 ${getHealthBgColor(\r\n              report.healthScore\r\n            )}`}\r\n          >\r\n            <div className=\"flex items-center justify-between\">\r\n              <div className=\"flex items-center space-x-3\">\r\n                <AlertTriangle\r\n                  className={`w-6 h-6 ${getHealthColor(report.healthScore)}`}\r\n                />\r\n                <div>\r\n                  <h3 className=\"font-semibold text-gray-900\">\r\n                    Saúde da Playlist\r\n                  </h3>\r\n                  <p className=\"text-sm text-gray-700\">\r\n                    Score:{\" \"}\r\n                    <span\r\n                      className={`font-medium ${getHealthColor(\r\n                        report.healthScore\r\n                      )}`}\r\n                    >\r\n                      {report.healthScore}/100\r\n                    </span>\r\n                  </p>\r\n                </div>\r\n              </div>\r\n              <button\r\n                onClick={loadProblematicTracksReport}\r\n                className=\"p-2 text-gray-600 hover:text-gray-800 transition-colors\"\r\n                aria-label=\"Atualizar relatório\"\r\n              >\r\n                <RefreshCw className=\"w-4 h-4\" />\r\n              </button>\r\n            </div>\r\n\r\n            {report.recommendations.length > 0 && (\r\n              <div className=\"mt-3\">\r\n                <h4 className=\"text-sm font-medium text-gray-900 mb-2\">\r\n                  Recomendações:\r\n                </h4>\r\n                <ul className=\"space-y-1\">\r\n                  {report.recommendations.map((rec, index) => (\r\n                    <li\r\n                      key={index}\r\n                      className=\"text-sm text-gray-700 flex items-start space-x-2\"\r\n                    >\r\n                      <span className=\"text-gray-400 mt-0.5\">•</span>\r\n                      <span>{rec}</span>\r\n                    </li>\r\n                  ))}\r\n                </ul>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"space-y-3\">\r\n            <h4 className=\"font-medium text-gray-900\">\r\n              Músicas Problemáticas ({report.problematicTracks.length})\r\n            </h4>\r\n            {report.problematicTracks.map((track) => (\r\n              <div\r\n                key={track.id}\r\n                className=\"bg-white border border-gray-200 rounded-lg p-4 shadow-sm\"\r\n              >\r\n                <div className=\"flex items-start justify-between\">\r\n                  <div className=\"flex-1\">\r\n                    <h5 className=\"font-medium text-gray-900\">{track.title}</h5>\r\n                    <p className=\"text-sm text-gray-600\">{track.artist}</p>\r\n                    <div className=\"mt-2 grid grid-cols-2 md:grid-cols-4 gap-3 text-sm\">\r\n                      <div className=\"text-center\">\r\n                        <div className=\"text-red-600 font-medium\">\r\n                          {(track.negativeVoteRatio * 100).toFixed(1)}%\r\n                        </div>\r\n                        <div className=\"text-gray-500\">Rejeição</div>\r\n                      </div>\r\n                      <div className=\"text-center\">\r\n                        <div className=\"text-blue-600 font-medium\">\r\n                          {track.score}\r\n                        </div>\r\n                        <div className=\"text-gray-500\">Score</div>\r\n                      </div>\r\n                      <div className=\"text-center\">\r\n                        <div className=\"text-purple-600 font-medium\">\r\n                          {track.playCount}\r\n                        </div>\r\n                        <div className=\"text-gray-500\">Reproduções</div>\r\n                      </div>\r\n                      <div className=\"text-center\">\r\n                        <div className=\"text-green-600 font-medium\">\r\n                          {(track.completionRate * 100).toFixed(1)}%\r\n                        </div>\r\n                        <div className=\"text-gray-500\">Conclusão</div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-2 ml-4\">\r\n                    <span\r\n                      className={`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${getRecommendationColor(\r\n                        track.recommendation\r\n                      )}`}\r\n                    >\r\n                      {getRecommendationIcon(track.recommendation)}\r\n                      <span className=\"capitalize\">{track.recommendation}</span>\r\n                    </span>\r\n                    {(track.recommendation === Recommendation.Remove ||\r\n                      track.recommendation === Recommendation.Blacklist) && (\r\n                      <button\r\n                        onClick={() => handleRemoveTrack(track.id)}\r\n                        className=\"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors\"\r\n                        aria-label={`Remover ${track.title} da playlist`}\r\n                      >\r\n                        <Trash2 className=\"w-4 h-4\" />\r\n                      </button>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ProblematicTracksAlert;\r\n"], "names": ["ProblematicTracksAlert", "restaurantId", "useRestaurantContext", "report", "setReport", "useState", "loading", "setLoading", "show<PERSON><PERSON><PERSON>", "setShowAlert", "dismissed", "setDismissed", "loadProblematicTracksReport", "useCallback", "response", "buildApiUrl", "data", "error", "toast", "handleRemoveTrack", "trackId", "handleDismissAlert", "useEffect", "interval", "getHealthColor", "score", "getHealthBgColor", "getRecommendationColor", "recommendation", "getRecommendationIcon", "jsx", "CheckCircle", "Eye", "XCircle", "Trash2", "Music", "jsxs", "RefreshCw", "Fragment", "AnimatePresence", "motion", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BarChart3", "X", "rec", "index", "track"], "mappings": "6QAyDA,MAAMA,EAAmC,IAAM,CACvC,KAAA,CAAE,aAAAC,GAAiBC,IACnB,CAACC,EAAQC,CAAS,EAAIC,WAAyC,IAAI,EACnE,CAACC,EAASC,CAAU,EAAIF,WAAS,EAAI,EACrC,CAACG,EAAWC,CAAY,EAAIJ,WAAS,EAAK,EAC1C,CAACK,EAAWC,CAAY,EAAIN,WAAS,EAAK,EAG1CO,EAA8BC,EAAAA,YAAY,SAAY,CAC1D,GAAI,CAACZ,EAAc,CACjB,QAAQ,MAAM,8BAA8B,EAC5C,MACF,CAEI,GAAA,CACFM,EAAW,EAAI,EACf,MAAMO,EAAW,MAAM,MACrBC,EAAY,aAAad,CAAY,qBAAqB,CAAA,EAG5D,GAAIa,EAAS,GAAI,CACT,MAAAE,EAAO,MAAMF,EAAS,OAC5BV,EAAUY,EAAK,MAAM,CAAA,KAErB,OAAM,IAAI,MAAM,QAAQF,EAAS,MAAM,EAAE,QAEpCG,EAAO,CACN,QAAA,MACN,uDACAA,CAAA,EAEQb,EAAA,CACR,kBAAmB,CAAC,EACpB,gBAAiB,CAAC,EAClB,YAAa,EAAA,CACd,EACDc,EAAM,MAAM,6BAA6B,CAAA,QACzC,CACAX,EAAW,EAAK,CAClB,CAAA,EACC,CAACN,CAAY,CAAC,EAGXkB,EAAoBN,EAAA,YACxB,MAAOO,GAAoB,CACzB,GAAKnB,EAED,GAAA,CACF,MAAMa,EAAW,MAAM,MACrBC,EAAY,gBAAgBd,CAAY,aAAamB,CAAO,EAAE,EAC9D,CAAE,OAAQ,QAAS,CAAA,EAGrB,GAAIN,EAAS,GACXI,EAAM,QAAQ,6BAA6B,EAC3C,MAAMN,EAA4B,MAElC,OAAM,IAAI,MAAM,QAAQE,EAAS,MAAM,EAAE,QAEpCG,EAAO,CACN,QAAA,MAAM,0BAA2BA,CAAK,EAC9CC,EAAM,MAAM,wBAAwB,CACtC,CACF,EACA,CAACjB,EAAcW,CAA2B,CAAA,EAItCS,EAAqBR,EAAAA,YAAY,IAAM,CAC3CJ,EAAa,EAAK,EAClBE,EAAa,EAAI,EACjB,WAAW,IAAMA,EAAa,EAAK,EAAG,GAAK,GAAK,GAAI,CACtD,EAAG,CAAE,CAAA,EAGLW,EAAAA,UAAU,IAAM,CACd,GAAI,CAACrB,EAAc,OAGSW,IAG5B,MAAMW,EAAW,YAAYX,EAA6B,GAAK,GAAK,GAAI,EACjE,MAAA,IAAM,cAAcW,CAAQ,CAAA,EAClC,CAACtB,CAAY,CAAC,EAGjBqB,EAAAA,UAAU,IAAM,CACVnB,GAAA,MAAAA,EAAQ,kBAAkB,QAAU,CAACO,GACvCD,EAAa,EAAI,CACnB,EACC,CAACN,EAAQO,CAAS,CAAC,EAGhB,MAAAc,EAAkBC,GAClBA,GAAS,GAAW,iBACpBA,GAAS,GAAW,kBACpBA,GAAS,GAAW,kBACjB,eAIHC,EAAoBD,GACpBA,GAAS,GAAW,+BACpBA,GAAS,GAAW,iCACpBA,GAAS,GAAW,iCACjB,2BAIHE,EAA0BC,GAA2C,CACzE,OAAQA,EAAgB,CACtB,IAAK,OACI,MAAA,8BACT,IAAK,UACI,MAAA,gCACT,IAAK,SACI,MAAA,0BACT,IAAK,YACI,MAAA,0BACT,QACS,MAAA,2BACX,CAAA,EAIIC,EAAyBD,GAAmC,CAChE,OAAQA,EAAgB,CACtB,IAAK,OACI,OAAAE,EAAA,IAACC,EAAY,CAAA,UAAU,SAAU,CAAA,EAC1C,IAAK,UACI,OAAAD,EAAA,IAACE,EAAI,CAAA,UAAU,SAAU,CAAA,EAClC,IAAK,SACI,OAAAF,EAAA,IAACG,EAAQ,CAAA,UAAU,SAAU,CAAA,EACtC,IAAK,YACI,OAAAH,EAAA,IAACI,EAAO,CAAA,UAAU,SAAU,CAAA,EACrC,QACS,OAAAJ,EAAA,IAACK,EAAM,CAAA,UAAU,SAAU,CAAA,CACtC,CAAA,EAGF,OAAI7B,EAEA8B,EAAA,KAAC,MAAI,CAAA,UAAU,4CACb,SAAA,CAACN,EAAAA,IAAAO,EAAA,CAAU,UAAU,sBAAuB,CAAA,EAC3CP,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAAoC,uCAAA,CAChE,CAAA,CAAA,EAIA,CAAC3B,GAAU,CAACA,EAAO,kBAAkB,OAErCiC,EAAA,KAAC,MAAI,CAAA,UAAU,6CACb,SAAA,CAACN,EAAAA,IAAAC,EAAA,CAAY,UAAU,SAAU,CAAA,EAChCD,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAE1B,kDAAA,CACF,CAAA,CAAA,EAOAM,EAAA,KAAAE,WAAA,CAAA,SAAA,CAAAR,EAAAA,IAACS,GACE,SACC/B,GAAAsB,EAAA,IAACU,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,EAAG,EAAG,IAAK,MAAO,EAAI,EAC1C,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,MAAO,CAAE,EACtC,KAAM,CAAE,QAAS,EAAG,EAAG,IAAK,MAAO,EAAI,EACvC,UAAU,oCAEV,SAAAJ,EAAA,KAAC,MAAA,CACC,UAAW,qCAAqCV,EAC9CvB,EAAO,WAAA,CACR,GAED,SAAA,CAACiC,EAAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,6BACb,SAAA,CAAAN,EAAA,IAACW,EAAA,CACC,UAAW,kBAAkBjB,EAC3BrB,EAAO,WAAA,CACR,EAAA,CACH,EACAiC,EAAAA,KAAC,MAAI,CAAA,UAAU,SACb,SAAA,CAACN,EAAA,IAAA,KAAA,CAAG,UAAU,8BAA8B,SAE5C,sCAAA,EACAM,EAAAA,KAAC,IAAE,CAAA,UAAU,6BACV,SAAA,CAAAjC,EAAO,kBAAkB,OAAO,sCAAA,EAEnC,QACC,MAAI,CAAA,UAAU,OACb,SAACiC,EAAA,KAAA,MAAA,CAAI,UAAU,sCACb,SAAA,CAACN,EAAAA,IAAAY,EAAA,CAAU,UAAU,SAAU,CAAA,EAC/BN,EAAA,KAAC,OAAA,CACC,UAAW,eAAeZ,EACxBrB,EAAO,WAAA,CACR,GACF,SAAA,CAAA,mBACkBA,EAAO,YAAY,MAAA,CAAA,CACtC,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,CAAA,EACF,EACA2B,EAAA,IAAC,SAAA,CACC,QAAST,EACT,UAAU,sDACV,aAAW,gBAEX,SAAAS,EAAAA,IAACa,EAAE,CAAA,UAAU,SAAU,CAAA,CAAA,CACzB,CAAA,EACF,EACAP,EAAAA,KAAC,MAAI,CAAA,UAAU,sBACb,SAAA,CAAAN,EAAA,IAAC,SAAA,CACC,QAAS,IAAMrB,EAAa,EAAK,EACjC,UAAU,uFACX,SAAA,cAAA,CAED,EACAqB,EAAA,IAAC,SAAA,CACC,QAAST,EACT,UAAU,0FACX,SAAA,WAAA,CAED,CAAA,EACF,CAAA,CAAA,CACF,CAAA,CAAA,EAGN,EAGC,CAACb,GACC4B,EAAAA,KAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAAAA,EAAA,KAAC,MAAA,CACC,UAAW,yBAAyBV,EAClCvB,EAAO,WAAA,CACR,GAED,SAAA,CAACiC,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAAAN,EAAA,IAACW,EAAA,CACC,UAAW,WAAWjB,EAAerB,EAAO,WAAW,CAAC,EAAA,CAC1D,SACC,MACC,CAAA,SAAA,CAAC2B,EAAA,IAAA,KAAA,CAAG,UAAU,8BAA8B,SAE5C,oBAAA,EACAM,EAAAA,KAAC,IAAE,CAAA,UAAU,wBAAwB,SAAA,CAAA,SAC5B,IACPA,EAAA,KAAC,OAAA,CACC,UAAW,eAAeZ,EACxBrB,EAAO,WAAA,CACR,GAEA,SAAA,CAAOA,EAAA,YAAY,MAAA,CAAA,CACtB,CAAA,EACF,CAAA,EACF,CAAA,EACF,EACA2B,EAAA,IAAC,SAAA,CACC,QAASlB,EACT,UAAU,0DACV,aAAW,sBAEX,SAAAkB,EAAAA,IAACO,EAAU,CAAA,UAAU,SAAU,CAAA,CAAA,CACjC,CAAA,EACF,EAEClC,EAAO,gBAAgB,OAAS,GAC9BiC,EAAAA,KAAA,MAAA,CAAI,UAAU,OACb,SAAA,CAACN,EAAA,IAAA,KAAA,CAAG,UAAU,yCAAyC,SAEvD,iBAAA,EACAA,EAAAA,IAAC,MAAG,UAAU,YACX,WAAO,gBAAgB,IAAI,CAACc,EAAKC,IAChCT,EAAA,KAAC,KAAA,CAEC,UAAU,mDAEV,SAAA,CAACN,EAAA,IAAA,OAAA,CAAK,UAAU,uBAAuB,SAAC,IAAA,EACxCA,EAAAA,IAAC,QAAM,SAAIc,CAAA,CAAA,CAAA,CAAA,EAJNC,CAMR,CAAA,EACH,CAAA,EACF,CAAA,CAAA,CAEJ,EAEAT,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,4BAA4B,SAAA,CAAA,0BAChBjC,EAAO,kBAAkB,OAAO,GAAA,EAC1D,EACCA,EAAO,kBAAkB,IAAK2C,GAC7BhB,EAAA,IAAC,MAAA,CAEC,UAAU,2DAEV,SAAAM,EAAA,KAAC,MAAI,CAAA,UAAU,mCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,SACb,SAAA,CAAAN,EAAA,IAAC,KAAG,CAAA,UAAU,4BAA6B,SAAAgB,EAAM,MAAM,EACtDhB,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAyB,WAAM,OAAO,EACnDM,EAAAA,KAAC,MAAI,CAAA,UAAU,qDACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,2BACX,SAAA,EAAMU,EAAA,kBAAoB,KAAK,QAAQ,CAAC,EAAE,GAAA,EAC9C,EACChB,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAQ,WAAA,CAAA,EACzC,EACAM,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAAAN,EAAA,IAAC,MAAI,CAAA,UAAU,4BACZ,SAAAgB,EAAM,MACT,EACChB,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAK,QAAA,CAAA,EACtC,EACAM,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAAAN,EAAA,IAAC,MAAI,CAAA,UAAU,8BACZ,SAAAgB,EAAM,UACT,EACChB,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAW,cAAA,CAAA,EAC5C,EACAM,EAAAA,KAAC,MAAI,CAAA,UAAU,cACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,6BACX,SAAA,EAAMU,EAAA,eAAiB,KAAK,QAAQ,CAAC,EAAE,GAAA,EAC3C,EACChB,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAS,YAAA,CAAA,EAC1C,CAAA,EACF,CAAA,EACF,EACAM,EAAAA,KAAC,MAAI,CAAA,UAAU,mCACb,SAAA,CAAAA,EAAA,KAAC,OAAA,CACC,UAAW,0EAA0ET,EACnFmB,EAAM,cAAA,CACP,GAEA,SAAA,CAAAjB,EAAsBiB,EAAM,cAAc,EAC1ChB,EAAA,IAAA,OAAA,CAAK,UAAU,aAAc,WAAM,eAAe,CAAA,CAAA,CACrD,GACEgB,EAAM,iBAAmB,UACzBA,EAAM,iBAAmB,cACzBhB,EAAA,IAAC,SAAA,CACC,QAAS,IAAMX,EAAkB2B,EAAM,EAAE,EACzC,UAAU,gEACV,aAAY,WAAWA,EAAM,KAAK,eAElC,SAAAhB,EAAAA,IAACI,EAAO,CAAA,UAAU,SAAU,CAAA,CAAA,CAC9B,CAAA,EAEJ,CAAA,EACF,CAAA,EAtDKY,EAAM,EAAA,CAwDd,CAAA,EACH,CAAA,EACF,CAEJ,CAAA,CAAA,CAEJ"}