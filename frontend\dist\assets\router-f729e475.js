import{r as s,R as ce}from"./vendor-66b0ef43.js";/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function N(){return N=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},N.apply(this,arguments)}var S;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(S||(S={}));const K="popstate";function fe(e){e===void 0&&(e={});function t(r,a){let{pathname:l,search:i,hash:u}=r.location;return k("",{pathname:l,search:i,hash:u},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function n(r,a){return typeof a=="string"?a:_(a)}return de(t,n,null,e)}function v(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function te(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function he(){return Math.random().toString(36).substr(2,8)}function q(e,t){return{usr:e.state,key:e.key,idx:t}}function k(e,t,n,r){return n===void 0&&(n=null),N({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?L(t):t,{state:n,key:t&&t.key||r||he()})}function _(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function L(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function de(e,t,n,r){r===void 0&&(r={});let{window:a=document.defaultView,v5Compat:l=!1}=r,i=a.history,u=S.Pop,o=null,f=h();f==null&&(f=0,i.replaceState(N({},i.state,{idx:f}),""));function h(){return(i.state||{idx:null}).idx}function c(){u=S.Pop;let d=h(),x=d==null?null:d-f;f=d,o&&o({action:u,location:m.location,delta:x})}function p(d,x){u=S.Push;let C=k(m.location,d,x);n&&n(C,d),f=h()+1;let E=q(C,f),P=m.createHref(C);try{i.pushState(E,"",P)}catch(U){if(U instanceof DOMException&&U.name==="DataCloneError")throw U;a.location.assign(P)}l&&o&&o({action:u,location:m.location,delta:1})}function y(d,x){u=S.Replace;let C=k(m.location,d,x);n&&n(C,d),f=h();let E=q(C,f),P=m.createHref(C);i.replaceState(E,"",P),l&&o&&o({action:u,location:m.location,delta:0})}function g(d){let x=a.location.origin!=="null"?a.location.origin:a.location.href,C=typeof d=="string"?d:_(d);return C=C.replace(/ $/,"%20"),v(x,"No window.location.(origin|href) available to create URL for href: "+C),new URL(C,x)}let m={get action(){return u},get location(){return e(a,i)},listen(d){if(o)throw new Error("A history only accepts one active listener");return a.addEventListener(K,c),o=d,()=>{a.removeEventListener(K,c),o=null}},createHref(d){return t(a,d)},createURL:g,encodeLocation(d){let x=g(d);return{pathname:x.pathname,search:x.search,hash:x.hash}},push:p,replace:y,go(d){return i.go(d)}};return m}var G;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(G||(G={}));function pe(e,t,n){return n===void 0&&(n="/"),me(e,t,n,!1)}function me(e,t,n,r){let a=typeof t=="string"?L(t):t,l=A(a.pathname||"/",n);if(l==null)return null;let i=ne(e);ve(i);let u=null;for(let o=0;u==null&&o<i.length;++o){let f=Ue(l);u=we(i[o],f,r)}return u}function ne(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let a=(l,i,u)=>{let o={relativePath:u===void 0?l.path||"":u,caseSensitive:l.caseSensitive===!0,childrenIndex:i,route:l};o.relativePath.startsWith("/")&&(v(o.relativePath.startsWith(r),'Absolute route path "'+o.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),o.relativePath=o.relativePath.slice(r.length));let f=w([r,o.relativePath]),h=n.concat(o);l.children&&l.children.length>0&&(v(l.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+f+'".')),ne(l.children,t,h,f)),!(l.path==null&&!l.index)&&t.push({path:f,score:Re(f,l.index),routesMeta:h})};return e.forEach((l,i)=>{var u;if(l.path===""||!((u=l.path)!=null&&u.includes("?")))a(l,i);else for(let o of re(l.path))a(l,i,o)}),t}function re(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,a=n.endsWith("?"),l=n.replace(/\?$/,"");if(r.length===0)return a?[l,""]:[l];let i=re(r.join("/")),u=[];return u.push(...i.map(o=>o===""?l:[l,o].join("/"))),a&&u.push(...i),u.map(o=>e.startsWith("/")&&o===""?"/":o)}function ve(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:Se(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const ge=/^:[\w-]+$/,ye=3,xe=2,Ee=1,Ce=10,Pe=-2,H=e=>e==="*";function Re(e,t){let n=e.split("/"),r=n.length;return n.some(H)&&(r+=Pe),t&&(r+=xe),n.filter(a=>!H(a)).reduce((a,l)=>a+(ge.test(l)?ye:l===""?Ee:Ce),r)}function Se(e,t){return e.length===t.length&&e.slice(0,-1).every((r,a)=>r===t[a])?e[e.length-1]-t[t.length-1]:0}function we(e,t,n){n===void 0&&(n=!1);let{routesMeta:r}=e,a={},l="/",i=[];for(let u=0;u<r.length;++u){let o=r[u],f=u===r.length-1,h=l==="/"?t:t.slice(l.length)||"/",c=X({path:o.relativePath,caseSensitive:o.caseSensitive,end:f},h),p=o.route;if(!c&&f&&n&&!r[r.length-1].route.index&&(c=X({path:o.relativePath,caseSensitive:o.caseSensitive,end:!1},h)),!c)return null;Object.assign(a,c.params),i.push({params:a,pathname:w([l,c.pathname]),pathnameBase:Ne(w([l,c.pathnameBase])),route:p}),c.pathnameBase!=="/"&&(l=w([l,c.pathnameBase]))}return i}function X(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=be(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let l=a[0],i=l.replace(/(.)\/+$/,"$1"),u=a.slice(1);return{params:r.reduce((f,h,c)=>{let{paramName:p,isOptional:y}=h;if(p==="*"){let m=u[c]||"";i=l.slice(0,l.length-m.length).replace(/(.)\/+$/,"$1")}const g=u[c];return y&&!g?f[p]=void 0:f[p]=(g||"").replace(/%2F/g,"/"),f},{}),pathname:l,pathnameBase:i,pattern:e}}function be(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),te(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,u,o)=>(r.push({paramName:u,isOptional:o!=null}),o?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function Ue(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return te(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function A(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function Le(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:a=""}=typeof e=="string"?L(e):e;return{pathname:n?n.startsWith("/")?n:Oe(n,t):t,search:Ie(r),hash:_e(a)}}function Oe(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?n.length>1&&n.pop():a!=="."&&n.push(a)}),n.length>1?n.join("/"):"/"}function $(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Be(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function V(e,t){let n=Be(e);return t?n.map((r,a)=>a===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function z(e,t,n,r){r===void 0&&(r=!1);let a;typeof e=="string"?a=L(e):(a=N({},e),v(!a.pathname||!a.pathname.includes("?"),$("?","pathname","search",a)),v(!a.pathname||!a.pathname.includes("#"),$("#","pathname","hash",a)),v(!a.search||!a.search.includes("#"),$("#","search","hash",a)));let l=e===""||a.pathname==="",i=l?"/":a.pathname,u;if(i==null)u=n;else{let c=t.length-1;if(!r&&i.startsWith("..")){let p=i.split("/");for(;p[0]==="..";)p.shift(),c-=1;a.pathname=p.join("/")}u=c>=0?t[c]:"/"}let o=Le(a,u),f=i&&i!=="/"&&i.endsWith("/"),h=(l||i===".")&&n.endsWith("/");return!o.pathname.endsWith("/")&&(f||h)&&(o.pathname+="/"),o}const w=e=>e.join("/").replace(/\/\/+/g,"/"),Ne=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Ie=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,_e=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Te(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const ae=["post","put","patch","delete"];new Set(ae);const je=["get",...ae];new Set(je);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function I(){return I=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},I.apply(this,arguments)}const D=s.createContext(null),$e=s.createContext(null),b=s.createContext(null),j=s.createContext(null),R=s.createContext({outlet:null,matches:[],isDataRoute:!1}),le=s.createContext(null);function ke(e,t){let{relative:n}=t===void 0?{}:t;O()||v(!1);let{basename:r,navigator:a}=s.useContext(b),{hash:l,pathname:i,search:u}=oe(e,{relative:n}),o=i;return r!=="/"&&(o=i==="/"?r:w([r,i])),a.createHref({pathname:o,search:u,hash:l})}function O(){return s.useContext(j)!=null}function B(){return O()||v(!1),s.useContext(j).location}function ie(e){s.useContext(b).static||s.useLayoutEffect(e)}function J(){let{isDataRoute:e}=s.useContext(R);return e?Xe():Me()}function Me(){O()||v(!1);let e=s.useContext(D),{basename:t,future:n,navigator:r}=s.useContext(b),{matches:a}=s.useContext(R),{pathname:l}=B(),i=JSON.stringify(V(a,n.v7_relativeSplatPath)),u=s.useRef(!1);return ie(()=>{u.current=!0}),s.useCallback(function(f,h){if(h===void 0&&(h={}),!u.current)return;if(typeof f=="number"){r.go(f);return}let c=z(f,JSON.parse(i),l,h.relative==="path");e==null&&t!=="/"&&(c.pathname=c.pathname==="/"?t:w([t,c.pathname])),(h.replace?r.replace:r.push)(c,h.state,h)},[t,r,i,l,e])}function ht(){let{matches:e}=s.useContext(R),t=e[e.length-1];return t?t.params:{}}function oe(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=s.useContext(b),{matches:a}=s.useContext(R),{pathname:l}=B(),i=JSON.stringify(V(a,r.v7_relativeSplatPath));return s.useMemo(()=>z(e,JSON.parse(i),l,n==="path"),[e,i,l,n])}function We(e,t){return Fe(e,t)}function Fe(e,t,n,r){O()||v(!1);let{navigator:a}=s.useContext(b),{matches:l}=s.useContext(R),i=l[l.length-1],u=i?i.params:{};i&&i.pathname;let o=i?i.pathnameBase:"/";i&&i.route;let f=B(),h;if(t){var c;let d=typeof t=="string"?L(t):t;o==="/"||(c=d.pathname)!=null&&c.startsWith(o)||v(!1),h=d}else h=f;let p=h.pathname||"/",y=p;if(o!=="/"){let d=o.replace(/^\//,"").split("/");y="/"+p.replace(/^\//,"").split("/").slice(d.length).join("/")}let g=pe(e,{pathname:y}),m=Je(g&&g.map(d=>Object.assign({},d,{params:Object.assign({},u,d.params),pathname:w([o,a.encodeLocation?a.encodeLocation(d.pathname).pathname:d.pathname]),pathnameBase:d.pathnameBase==="/"?o:w([o,a.encodeLocation?a.encodeLocation(d.pathnameBase).pathname:d.pathnameBase])})),l,n,r);return t&&m?s.createElement(j.Provider,{value:{location:I({pathname:"/",search:"",hash:"",state:null,key:"default"},h),navigationType:S.Pop}},m):m}function Ae(){let e=He(),t=Te(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},l=null;return s.createElement(s.Fragment,null,s.createElement("h2",null,"Unexpected Application Error!"),s.createElement("h3",{style:{fontStyle:"italic"}},t),n?s.createElement("pre",{style:a},n):null,l)}const Ve=s.createElement(Ae,null);class ze extends s.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?s.createElement(R.Provider,{value:this.props.routeContext},s.createElement(le.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function De(e){let{routeContext:t,match:n,children:r}=e,a=s.useContext(D);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),s.createElement(R.Provider,{value:t},r)}function Je(e,t,n,r){var a;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var l;if(!n)return null;if(n.errors)e=n.matches;else if((l=r)!=null&&l.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let i=e,u=(a=n)==null?void 0:a.errors;if(u!=null){let h=i.findIndex(c=>c.route.id&&(u==null?void 0:u[c.route.id])!==void 0);h>=0||v(!1),i=i.slice(0,Math.min(i.length,h+1))}let o=!1,f=-1;if(n&&r&&r.v7_partialHydration)for(let h=0;h<i.length;h++){let c=i[h];if((c.route.HydrateFallback||c.route.hydrateFallbackElement)&&(f=h),c.route.id){let{loaderData:p,errors:y}=n,g=c.route.loader&&p[c.route.id]===void 0&&(!y||y[c.route.id]===void 0);if(c.route.lazy||g){o=!0,f>=0?i=i.slice(0,f+1):i=[i[0]];break}}}return i.reduceRight((h,c,p)=>{let y,g=!1,m=null,d=null;n&&(y=u&&c.route.id?u[c.route.id]:void 0,m=c.route.errorElement||Ve,o&&(f<0&&p===0?(Qe("route-fallback",!1),g=!0,d=null):f===p&&(g=!0,d=c.route.hydrateFallbackElement||null)));let x=t.concat(i.slice(0,p+1)),C=()=>{let E;return y?E=m:g?E=d:c.route.Component?E=s.createElement(c.route.Component,null):c.route.element?E=c.route.element:E=h,s.createElement(De,{match:c,routeContext:{outlet:h,matches:x,isDataRoute:n!=null},children:E})};return n&&(c.route.ErrorBoundary||c.route.errorElement||p===0)?s.createElement(ze,{location:n.location,revalidation:n.revalidation,component:m,error:y,children:C(),routeContext:{outlet:null,matches:x,isDataRoute:!0}}):C()},null)}var se=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(se||{}),T=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(T||{});function Ke(e){let t=s.useContext(D);return t||v(!1),t}function qe(e){let t=s.useContext($e);return t||v(!1),t}function Ge(e){let t=s.useContext(R);return t||v(!1),t}function ue(e){let t=Ge(),n=t.matches[t.matches.length-1];return n.route.id||v(!1),n.route.id}function He(){var e;let t=s.useContext(le),n=qe(T.UseRouteError),r=ue(T.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function Xe(){let{router:e}=Ke(se.UseNavigateStable),t=ue(T.UseNavigateStable),n=s.useRef(!1);return ie(()=>{n.current=!0}),s.useCallback(function(a,l){l===void 0&&(l={}),n.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,I({fromRouteId:t},l)))},[e,t])}const Q={};function Qe(e,t,n){!t&&!Q[e]&&(Q[e]=!0)}function Ye(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function dt(e){let{to:t,replace:n,state:r,relative:a}=e;O()||v(!1);let{future:l,static:i}=s.useContext(b),{matches:u}=s.useContext(R),{pathname:o}=B(),f=J(),h=z(t,V(u,l.v7_relativeSplatPath),o,a==="path"),c=JSON.stringify(h);return s.useEffect(()=>f(JSON.parse(c),{replace:n,state:r,relative:a}),[f,c,a,n,r]),null}function Ze(e){v(!1)}function et(e){let{basename:t="/",children:n=null,location:r,navigationType:a=S.Pop,navigator:l,static:i=!1,future:u}=e;O()&&v(!1);let o=t.replace(/^\/*/,"/"),f=s.useMemo(()=>({basename:o,navigator:l,static:i,future:I({v7_relativeSplatPath:!1},u)}),[o,u,l,i]);typeof r=="string"&&(r=L(r));let{pathname:h="/",search:c="",hash:p="",state:y=null,key:g="default"}=r,m=s.useMemo(()=>{let d=A(h,o);return d==null?null:{location:{pathname:d,search:c,hash:p,state:y,key:g},navigationType:a}},[o,h,c,p,y,g,a]);return m==null?null:s.createElement(b.Provider,{value:f},s.createElement(j.Provider,{children:n,value:m}))}function pt(e){let{children:t,location:n}=e;return We(M(t),n)}new Promise(()=>{});function M(e,t){t===void 0&&(t=[]);let n=[];return s.Children.forEach(e,(r,a)=>{if(!s.isValidElement(r))return;let l=[...t,a];if(r.type===s.Fragment){n.push.apply(n,M(r.props.children,l));return}r.type!==Ze&&v(!1),!r.props.index||!r.props.children||v(!1);let i={id:r.props.id||l.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(i.children=M(r.props.children,l)),n.push(i)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function W(){return W=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},W.apply(this,arguments)}function tt(e,t){if(e==null)return{};var n={},r=Object.keys(e),a,l;for(l=0;l<r.length;l++)a=r[l],!(t.indexOf(a)>=0)&&(n[a]=e[a]);return n}function nt(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function rt(e,t){return e.button===0&&(!t||t==="_self")&&!nt(e)}function F(e){return e===void 0&&(e=""),new URLSearchParams(typeof e=="string"||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((t,n)=>{let r=e[n];return t.concat(Array.isArray(r)?r.map(a=>[n,a]):[[n,r]])},[]))}function at(e,t){let n=F(e);return t&&t.forEach((r,a)=>{n.has(a)||t.getAll(a).forEach(l=>{n.append(a,l)})}),n}const lt=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],it="6";try{window.__reactRouterVersion=it}catch{}const ot="startTransition",Y=ce[ot];function mt(e){let{basename:t,children:n,future:r,window:a}=e,l=s.useRef();l.current==null&&(l.current=fe({window:a,v5Compat:!0}));let i=l.current,[u,o]=s.useState({action:i.action,location:i.location}),{v7_startTransition:f}=r||{},h=s.useCallback(c=>{f&&Y?Y(()=>o(c)):o(c)},[o,f]);return s.useLayoutEffect(()=>i.listen(h),[i,h]),s.useEffect(()=>Ye(r),[r]),s.createElement(et,{basename:t,children:n,location:u.location,navigationType:u.action,navigator:i,future:r})}const st=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",ut=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,vt=s.forwardRef(function(t,n){let{onClick:r,relative:a,reloadDocument:l,replace:i,state:u,target:o,to:f,preventScrollReset:h,viewTransition:c}=t,p=tt(t,lt),{basename:y}=s.useContext(b),g,m=!1;if(typeof f=="string"&&ut.test(f)&&(g=f,st))try{let E=new URL(window.location.href),P=f.startsWith("//")?new URL(E.protocol+f):new URL(f),U=A(P.pathname,y);P.origin===E.origin&&U!=null?f=U+P.search+P.hash:m=!0}catch{}let d=ke(f,{relative:a}),x=ct(f,{replace:i,state:u,target:o,preventScrollReset:h,relative:a,viewTransition:c});function C(E){r&&r(E),E.defaultPrevented||x(E)}return s.createElement("a",W({},p,{href:g||d,onClick:m||l?r:C,ref:n,target:o}))});var Z;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Z||(Z={}));var ee;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(ee||(ee={}));function ct(e,t){let{target:n,replace:r,state:a,preventScrollReset:l,relative:i,viewTransition:u}=t===void 0?{}:t,o=J(),f=B(),h=oe(e,{relative:i});return s.useCallback(c=>{if(rt(c,n)){c.preventDefault();let p=r!==void 0?r:_(f)===_(h);o(e,{replace:p,state:a,preventScrollReset:l,relative:i,viewTransition:u})}},[f,o,h,r,a,n,e,l,i,u])}function gt(e){let t=s.useRef(F(e)),n=s.useRef(!1),r=B(),a=s.useMemo(()=>at(r.search,n.current?null:t.current),[r.search]),l=J(),i=s.useCallback((u,o)=>{const f=F(typeof u=="function"?u(a):u);n.current=!0,l("?"+f,o)},[l,a]);return[a,i]}export{mt as B,vt as L,dt as N,pt as R,Ze as a,B as b,ht as c,gt as d,J as u};
//# sourceMappingURL=router-f729e475.js.map
