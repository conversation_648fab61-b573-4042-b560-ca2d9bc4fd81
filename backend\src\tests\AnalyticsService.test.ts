import { AnalyticsService, AnalyticsPeriod, AnalyticsError } from '../services/AnalyticsService';
import { TestDataSource, initializeTestDatabase, cleanTestData, closeTestDatabase } from '../config/test-database';
import { Restaurant, RestaurantStatus } from '../models/Restaurant';
import { Repository } from 'typeorm';

// Mock AppDataSource para usar TestDataSource
jest.mock('../config/database', () => ({
  AppDataSource: {}
}));

// Mock Redis
jest.mock('../config/redis', () => ({
  redisClient: {
    isReady: false, // Desabilitar Redis para simplificar
    get: jest.fn(),
    setex: jest.fn(),
    del: jest.fn(),
    keys: jest.fn().mockResolvedValue([])
  }
}));

describe('AnalyticsService', () => {
  let analyticsService: AnalyticsService;
  let restaurantRepository: Repository<Restaurant>;
  let testRestaurant: Restaurant;

  beforeAll(async () => {
    await initializeTestDatabase();
    restaurantRepository = TestDataSource.getRepository(Restaurant);
  });

  afterAll(async () => {
    await closeTestDatabase();
  });

  beforeEach(async () => {
    jest.clearAllMocks();
    
    // Limpar dados antes de cada teste
    await cleanTestData();
    
    // Reset singleton instance
    (AnalyticsService as any).instance = undefined;
    
    // Mock AppDataSource.getRepository para retornar nossos repositórios de teste
    const { AppDataSource } = require('../config/database');
    AppDataSource.getRepository = jest.fn(() => {
      // Retorna repositórios de teste para todas as entidades
      return TestDataSource.getRepository(Restaurant);
    });
    
    // Criar instância do serviço
    analyticsService = AnalyticsService.getInstance();
    
    // Criar restaurante de teste
    testRestaurant = restaurantRepository.create({
      id: 'test-restaurant-' + Date.now(),
      name: 'Test Restaurant',
      email: '<EMAIL>',
      status: RestaurantStatus.ACTIVE
    });
    await restaurantRepository.save(testRestaurant);
  });

  describe('getMetrics', () => {
    it('deve obter métricas básicas sem dados', async () => {
      const result = await analyticsService.getMetrics(testRestaurant.id, AnalyticsPeriod.DAY);

      expect(result).toBeDefined();
      expect(result.totalSuggestions).toBe(0);
      expect(result.totalVotes).toBe(0);
      expect(result.totalSessions).toBe(0);
      expect(result.period).toBe(AnalyticsPeriod.DAY);
    });

    it('deve funcionar com diferentes períodos', async () => {
      const dayResult = await analyticsService.getMetrics(testRestaurant.id, AnalyticsPeriod.DAY);
      const weekResult = await analyticsService.getMetrics(testRestaurant.id, AnalyticsPeriod.WEEK);

      expect(dayResult.period).toBe(AnalyticsPeriod.DAY);
      expect(weekResult.period).toBe(AnalyticsPeriod.WEEK);
    });
  });

  describe('getDashboardSummary', () => {
    it('deve retornar resumo básico', async () => {
      const result = await analyticsService.getDashboardSummary(testRestaurant.id);

      expect(result).toBeDefined();
      expect(result.totalSuggestions).toBe(0);
      expect(result.totalVotes).toBe(0);
    });
  });

  describe('Performance', () => {
    it('deve executar queries rapidamente', async () => {
      const startTime = Date.now();

      await analyticsService.getMetrics(testRestaurant.id, AnalyticsPeriod.DAY);

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // Query deve ser rápida (menos de 5 segundos)
      expect(executionTime).toBeLessThan(5000);
    });
  });
});

describe('AnalyticsService', () => {
  let analyticsService: AnalyticsService;
  let restaurantRepository: Repository<Restaurant>;
  let testRestaurant: Restaurant;

  beforeAll(async () => {
    await initializeTestDatabase();
    restaurantRepository = TestDataSource.getRepository(Restaurant);
  });

  afterAll(async () => {
    await closeTestDatabase();
  });

  beforeEach(async () => {
    jest.clearAllMocks();
    
    // Limpar dados antes de cada teste
    await cleanTestData();
    
    // Reset singleton instance
    (AnalyticsService as any).instance = undefined;
    
    // Mock AppDataSource.getRepository para retornar nossos repositórios de teste
    const { AppDataSource } = require('../config/database');
    AppDataSource.getRepository = jest.fn(() => {
      // Retorna repositórios de teste para todas as entidades
      return TestDataSource.getRepository(Restaurant);
    });
    
    // Criar instância do serviço
    analyticsService = AnalyticsService.getInstance();
    
    // Criar restaurante de teste
    testRestaurant = restaurantRepository.create({
      id: 'test-restaurant-' + Date.now(),
      name: 'Test Restaurant',
      email: '<EMAIL>',
      status: RestaurantStatus.ACTIVE
    });
    await restaurantRepository.save(testRestaurant);
  });

  describe('getMetrics', () => {
    it('deve obter métricas básicas sem dados', async () => {
      const result = await analyticsService.getMetrics(testRestaurant.id, AnalyticsPeriod.DAY);

      expect(result).toBeDefined();
      expect(result.totalSuggestions).toBe(0);
      expect(result.totalVotes).toBe(0);
      expect(result.totalSessions).toBe(0);
      expect(result.period).toBe(AnalyticsPeriod.DAY);
    });

    it('deve funcionar com diferentes períodos', async () => {
      const dayResult = await analyticsService.getMetrics(testRestaurant.id, AnalyticsPeriod.DAY);
      const weekResult = await analyticsService.getMetrics(testRestaurant.id, AnalyticsPeriod.WEEK);

      expect(dayResult.period).toBe(AnalyticsPeriod.DAY);
      expect(weekResult.period).toBe(AnalyticsPeriod.WEEK);
    });
  });

  describe('getDashboardSummary', () => {
    it('deve retornar resumo básico', async () => {
      const result = await analyticsService.getDashboardSummary(testRestaurant.id);

      expect(result).toBeDefined();
      expect(result.totalSuggestions).toBe(0);
      expect(result.totalVotes).toBe(0);
    });
  });

  describe('Performance', () => {
    it('deve executar queries rapidamente', async () => {
      const startTime = Date.now();

      await analyticsService.getMetrics(testRestaurant.id, AnalyticsPeriod.DAY);

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // Query deve ser rápida (menos de 5 segundos)
      expect(executionTime).toBeLessThan(5000);
    });
  });
});

describe('AnalyticsService', () => {
  let analyticsService: AnalyticsService;
  let restaurantRepository: Repository<Restaurant>;
  let testRestaurant: Restaurant;

  beforeAll(async () => {
    await initializeTestDatabase();
    restaurantRepository = TestDataSource.getRepository(Restaurant);
  });

  afterAll(async () => {
    await closeTestDatabase();
  });

  beforeEach(async () => {
    jest.clearAllMocks();
    
    // Limpar dados antes de cada teste
    await cleanTestData();
    
    // Reset singleton instance
    (AnalyticsService as any).instance = undefined;
    
    // Mock AppDataSource.getRepository para retornar nossos repositórios de teste
    const { AppDataSource } = require('../config/database');
    AppDataSource.getRepository = jest.fn(() => {
      // Retorna repositórios de teste para todas as entidades
      return TestDataSource.getRepository(Restaurant);
    });
    
    // Criar instância do serviço
    analyticsService = AnalyticsService.getInstance();
    
    // Criar restaurante de teste
    testRestaurant = restaurantRepository.create({
      id: 'test-restaurant-' + Date.now(),
      name: 'Test Restaurant',
      email: '<EMAIL>',
      status: RestaurantStatus.ACTIVE
    });
    await restaurantRepository.save(testRestaurant);
  });

  describe('getMetrics', () => {
    it('deve obter métricas básicas sem dados', async () => {
      const result = await analyticsService.getMetrics(testRestaurant.id, AnalyticsPeriod.DAY);

      expect(result).toBeDefined();
      expect(result.totalSuggestions).toBe(0);
      expect(result.totalVotes).toBe(0);
      expect(result.totalSessions).toBe(0);
      expect(result.period).toBe(AnalyticsPeriod.DAY);
    });

    it('deve funcionar com diferentes períodos', async () => {
      const dayResult = await analyticsService.getMetrics(testRestaurant.id, AnalyticsPeriod.DAY);
      const weekResult = await analyticsService.getMetrics(testRestaurant.id, AnalyticsPeriod.WEEK);

      expect(dayResult.period).toBe(AnalyticsPeriod.DAY);
      expect(weekResult.period).toBe(AnalyticsPeriod.WEEK);
    });
  });

  describe('getDashboardSummary', () => {
    it('deve retornar resumo básico', async () => {
      const result = await analyticsService.getDashboardSummary(testRestaurant.id);

      expect(result).toBeDefined();
      expect(result.totalSuggestions).toBe(0);
      expect(result.totalVotes).toBe(0);
    });
  });

  describe('Performance', () => {
    it('deve executar queries rapidamente', async () => {
      const startTime = Date.now();

      await analyticsService.getMetrics(testRestaurant.id, AnalyticsPeriod.DAY);

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // Query deve ser rápida (menos de 5 segundos)
      expect(executionTime).toBeLessThan(5000);
    });
  });
});

describe('AnalyticsService', () => {
  let analyticsService: AnalyticsService;
  let restaurantRepository: Repository<Restaurant>;
  let suggestionRepository: Repository<Suggestion>;
  let voteRepository: Repository<Vote>;
  let sessionRepository: Repository<ClientSession>;
  let testRestaurant: Restaurant;

  beforeAll(async () => {
    await initializeTestDatabase();
    
    // Obter repositórios do TestDataSource
    restaurantRepository = TestDataSource.getRepository(Restaurant);
    suggestionRepository = TestDataSource.getRepository(Suggestion);
    voteRepository = TestDataSource.getRepository(Vote);
    sessionRepository = TestDataSource.getRepository(ClientSession);
  });

  afterAll(async () => {
    await closeTestDatabase();
  });

  beforeEach(async () => {
    jest.clearAllMocks();
    
    // Limpar dados antes de cada teste
    await cleanTestData();
    
    // Reset singleton instance
    (AnalyticsService as any).instance = undefined;
    
    // Mock AppDataSource.getRepository para retornar nossos repositórios de teste
    const { AppDataSource } = require('../config/database');
    AppDataSource.getRepository = jest.fn((entity: any) => {
      if (entity === Restaurant) return restaurantRepository;
      if (entity === Suggestion) return suggestionRepository;
      if (entity === Vote) return voteRepository;
      if (entity === ClientSession) return sessionRepository;
      return TestDataSource.getRepository(entity);
    });
    
    // Criar instância do serviço
    analyticsService = AnalyticsService.getInstance();
    
    // Criar restaurante de teste
    testRestaurant = restaurantRepository.create({
      id: 'test-restaurant-' + Date.now(),
      name: 'Test Restaurant',
      email: '<EMAIL>',
      status: RestaurantStatus.ACTIVE
    });
    await restaurantRepository.save(testRestaurant);
  });

  describe('getMetrics', () => {
    it('deve obter métricas básicas', async () => {
      const result = await analyticsService.getMetrics(testRestaurant.id, AnalyticsPeriod.DAY);

      expect(result).toBeDefined();
      expect(result.totalSuggestions).toBe(0);
      expect(result.totalVotes).toBe(0);
      expect(result.totalSessions).toBe(0);
      expect(result.period).toBe(AnalyticsPeriod.DAY);
    });

    it('deve obter métricas com dados reais', async () => {
      // Criar uma sugestão
      const suggestion = suggestionRepository.create({
        restaurant: testRestaurant,
        title: 'Test Song',
        artist: 'Test Artist',
        youtubeVideoId: 'test-video-id',
        status: SuggestionStatus.APPROVED,
        priority: false
      });
      await suggestionRepository.save(suggestion);

      // Criar um voto
      const vote = voteRepository.create({
        suggestion: suggestion,
        userIp: '127.0.0.1',
        voteType: VoteType.NORMAL
      });
      await voteRepository.save(vote);

      // Criar uma sessão
      const session = sessionRepository.create({
        restaurant: testRestaurant,
        sessionToken: 'test-session-token',
        userAgent: 'Test User Agent',
        ipAddress: '127.0.0.1',
        isActive: true
      });
      await sessionRepository.save(session);

      // Executar teste
      const result = await analyticsService.getMetrics(testRestaurant.id, AnalyticsPeriod.DAY);

      // Verificar resultados
      expect(result).toBeDefined();
      expect(result.totalSuggestions).toBeGreaterThanOrEqual(1);
      expect(result.totalVotes).toBeGreaterThanOrEqual(1);
      expect(result.totalSessions).toBeGreaterThanOrEqual(1);
    });
  });

  describe('getDashboardSummary', () => {
    it('deve retornar resumo básico', async () => {
      const result = await analyticsService.getDashboardSummary(testRestaurant.id);

      expect(result).toBeDefined();
      expect(result.totalSuggestions).toBe(0);
      expect(result.totalVotes).toBe(0);
    });
  });

  describe('Cache', () => {
    it('deve funcionar sem cache', async () => {
      // Mock Redis para simular que não está disponível
      const { redisClient } = require('../config/redis');
      redisClient.isReady = false;

      const result = await analyticsService.getMetrics(testRestaurant.id, AnalyticsPeriod.DAY);

      expect(result).toBeDefined();
      expect(result.totalSuggestions).toBe(0);
    });
  });

  describe('Performance', () => {
    it('deve executar queries otimizadas', async () => {
      const startTime = Date.now();

      await analyticsService.getMetrics(testRestaurant.id, AnalyticsPeriod.DAY);

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // Query deve ser rápida (menos de 5 segundos)
      expect(executionTime).toBeLessThan(5000);
    });
  });
});
    });

  describe('getMetrics', () => {
    it('deve obter métricas com dados reais', async () => {
      // Criar dados de teste
      const suggestion = suggestionRepository.create({
        id: 'suggestion-1',
        restaurant: testRestaurant,
        title: 'Test Song',
        artist: 'Test Artist',
        youtubeVideoId: 'test-video-id',
        status: 'approved',
        priority: false,
        createdAt: new Date()
      });
      await suggestionRepository.save(suggestion);

      const vote = voteRepository.create({
        id: 'vote-1',
        suggestion: suggestion,
        userIp: '127.0.0.1',
        voteType: 'normal',
        createdAt: new Date()
      });
      await voteRepository.save(vote);

      const session = sessionRepository.create({
        id: 'session-1',
        restaurant: testRestaurant,
        sessionId: 'test-session',
        userAgent: 'Test User Agent',
        ipAddress: '127.0.0.1',
        isActive: true,
        createdAt: new Date()
      });
      await sessionRepository.save(session);

      // Executar teste
      const result = await analyticsService.getMetrics('restaurant-123', AnalyticsPeriod.DAY);

      // Verificar resultados
      expect(result).toBeDefined();
      expect(result.totalSuggestions).toBe(1);
      expect(result.totalVotes).toBe(1);
      expect(result.activeSessions).toBe(1);
    });

    it('deve retornar métricas vazias para restaurante sem dados', async () => {
      const result = await analyticsService.getMetrics('restaurant-123', AnalyticsPeriod.DAY);

      expect(result).toBeDefined();
      expect(result.totalSuggestions).toBe(0);
      expect(result.totalVotes).toBe(0);
      expect(result.activeSessions).toBe(0);
    });
  });

  describe('getDashboardSummary', () => {
    it('deve obter resumo do dashboard com dados reais', async () => {
      // Criar múltiplas sugestões
      for (let i = 0; i < 3; i++) {
        const suggestion = suggestionRepository.create({
          id: `suggestion-${i}`,
          restaurant: testRestaurant,
          title: `Test Song ${i}`,
          artist: `Test Artist ${i}`,
          youtubeVideoId: `test-video-id-${i}`,
          status: 'approved',
          priority: i === 0, // Primeira é prioritária
          createdAt: new Date()
        });
        await suggestionRepository.save(suggestion);

        // Adicionar votos
        for (let j = 0; j < i + 1; j++) {
          const vote = voteRepository.create({
            id: `vote-${i}-${j}`,
            suggestion: suggestion,
            userIp: `127.0.0.${j}`,
            voteType: 'normal',
            createdAt: new Date()
          });
          await voteRepository.save(vote);
        }
      }

      const result = await analyticsService.getDashboardSummary('restaurant-123');

      expect(result).toBeDefined();
      expect(result.totalSuggestions).toBe(3);
      expect(result.prioritySuggestions).toBe(1);
      expect(result.totalVotes).toBe(6); // 1 + 2 + 3 = 6
    });
  });

  describe('getPopularSongs', () => {
    it('deve obter músicas populares ordenadas por votos', async () => {
      // Criar sugestões com diferentes quantidades de votos
      const suggestions = [];
      for (let i = 0; i < 3; i++) {
        const suggestion = suggestionRepository.create({
          id: `suggestion-${i}`,
          restaurant: testRestaurant,
          title: `Song ${i}`,
          artist: `Artist ${i}`,
          youtubeVideoId: `video-${i}`,
          status: 'approved',
          priority: false,
          createdAt: new Date()
        });
        const savedSuggestion = await suggestionRepository.save(suggestion);
        suggestions.push(savedSuggestion);

        // Criar votos (mais votos para índices maiores)
        for (let j = 0; j < (i + 1) * 2; j++) {
          const vote = voteRepository.create({
            id: `vote-${i}-${j}`,
            suggestion: savedSuggestion,
            userIp: `127.0.${j}.1`,
            voteType: 'normal',
            createdAt: new Date()
          });
          await voteRepository.save(vote);
        }
      }

      const result = await analyticsService.getPopularSongs('restaurant-123', 10);

      expect(result).toBeDefined();
      expect(result.length).toBe(3);
      // Deve estar ordenado por votos (decrescente)
      expect(result[0].voteCount).toBeGreaterThan(result[1].voteCount);
      expect(result[1].voteCount).toBeGreaterThan(result[2].voteCount);
    });
  });

  describe('Cache', () => {
    it('deve usar cache quando disponível', async () => {
      const mockMetrics = {
        totalSuggestions: 5,
        totalVotes: 10,
        activeSessions: 3,
        period: AnalyticsPeriod.DAY,
        periodStart: new Date(),
        periodEnd: new Date(),
        metrics: {}
      };

      mockRedisClient.getClient().get = jest.fn().mockResolvedValue(JSON.stringify(mockMetrics));

      const result = await analyticsService.getMetrics('restaurant-123', AnalyticsPeriod.DAY);

      expect(result).toEqual(mockMetrics);
      expect(mockRedisClient.getClient().get).toHaveBeenCalledWith('analytics:restaurant-123:DAY');
    });

    it('deve funcionar sem Redis', async () => {
      mockRedisClient.isReady.mockReturnValue(false);

      const result = await analyticsService.getMetrics('restaurant-123', AnalyticsPeriod.DAY);

      expect(result).toBeDefined();
      expect(result.totalSuggestions).toBe(0);
    });

    it('deve invalidar cache quando solicitado', async () => {
      mockRedisClient.getClient().keys = jest.fn().mockResolvedValue(['analytics:restaurant-123:DAY']);
      mockRedisClient.getClient().del = jest.fn().mockResolvedValue(1);

      await analyticsService.clearCache('restaurant-123');

      expect(mockRedisClient.getClient().keys).toHaveBeenCalledWith('analytics:restaurant-123:*');
      expect(mockRedisClient.getClient().del).toHaveBeenCalledWith(['analytics:restaurant-123:DAY']);
    });
  });

  describe('Tratamento de Erros', () => {
    it('deve lidar com falhas de query graciosamente', async () => {
      // Simular erro no banco fechando a conexão
      await TestDataSource.destroy();

      await expect(
        analyticsService.getMetrics('restaurant-123', AnalyticsPeriod.DAY)
      ).rejects.toThrow();

      // Reconectar para outros testes
      await TestDataSource.initialize();
    });
  });

  describe('Performance', () => {
    it('deve executar queries otimizadas', async () => {
      const startTime = Date.now();

      await analyticsService.getMetrics('restaurant-123', AnalyticsPeriod.DAY);

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // Query deve ser rápida (menos de 1 segundo)
      expect(executionTime).toBeLessThan(1000);
    });
  });
});
  };

  beforeEach(() => {
    // Setup repository mocks
    mockAnalyticsRepository = {
      find: jest.fn(),
      findOne: jest.fn(),
      save: jest.fn(),
      createQueryBuilder: jest.fn()
    };

    mockRestaurantRepository = {
      findOne: jest.fn(),
      find: jest.fn()
    };

    mockSuggestionRepository = {
      find: jest.fn(),
      count: jest.fn(),
      createQueryBuilder: jest.fn()
    };

    mockVoteRepository = {
      find: jest.fn(),
      count: jest.fn(),
      createQueryBuilder: jest.fn()
    };

    mockSessionRepository = {
      find: jest.fn(),
      count: jest.fn(),
      createQueryBuilder: jest.fn()
    };

    // Setup Redis mock
    mockRedisClient = {
      isReady: jest.fn().mockReturnValue(true),
      getClient: jest.fn().mockReturnValue({
        get: jest.fn(),
        setEx: jest.fn(),
        del: jest.fn(),
        keys: jest.fn().mockResolvedValue([])
      })
    };

    // Reset the singleton instance to force new instantiation with mocked repos
    (AnalyticsService as any).instance = null;
    
    (AppDataSource.getRepository as jest.Mock)
      .mockReturnValueOnce(mockAnalyticsRepository)
      .mockReturnValueOnce(mockRestaurantRepository)
      .mockReturnValueOnce(mockSuggestionRepository)
      .mockReturnValueOnce(mockVoteRepository)
      .mockReturnValueOnce(mockSessionRepository);

    (redisClient as any) = mockRedisClient;

    analyticsService = AnalyticsService.getInstance();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Singleton', () => {
    it('deve ser singleton', () => {
      const instance1 = AnalyticsService.getInstance();
      const instance2 = AnalyticsService.getInstance();
      
      expect(instance1).toBe(instance2);
    });
  });

  describe('getMetrics', () => {
    beforeEach(() => {
      // Mock query builders para retornar dados simulados
      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        groupBy: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        getRawMany: jest.fn(),
        getCount: jest.fn(),
        getOne: jest.fn()
      };

      mockSuggestionRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
      mockVoteRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
      mockSessionRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      // Setup return values
      mockQueryBuilder.getRawMany.mockResolvedValue([]);
      mockQueryBuilder.getCount.mockResolvedValue(100);
      mockQueryBuilder.getOne.mockResolvedValue({ count: 100 });
    });

    it('deve obter métricas com cache', async () => {
      const cachedData = JSON.stringify(mockMetrics);
      mockRedisClient.getClient().get.mockResolvedValue(cachedData);

      const result = await analyticsService.getMetrics('restaurant-123', AnalyticsPeriod.DAY);

      expect(result).toEqual(mockMetrics);
      expect(mockRedisClient.getClient().get).toHaveBeenCalled();
    });

    it('deve gerar métricas quando cache não disponível', async () => {
      mockRedisClient.getClient().get.mockResolvedValue(null);

      // Mock dos repositórios para simular dados do banco
      mockSuggestionRepository.find.mockResolvedValue([]);
      mockVoteRepository.find.mockResolvedValue([]);
      mockSessionRepository.find.mockResolvedValue([]);

      const result = await analyticsService.getMetrics('restaurant-123', AnalyticsPeriod.DAY);

      expect(result).toBeDefined();
      expect(mockRedisClient.getClient().setEx).toHaveBeenCalled();
    });

    it('deve validar parâmetros de entrada', async () => {
      await expect(
        analyticsService.getMetrics('', AnalyticsPeriod.DAY)
      ).rejects.toThrow(AnalyticsError);
    });

    it('deve funcionar com diferentes períodos', async () => {
      mockRedisClient.getClient().get.mockResolvedValue(null);
      
      // Mock dos repositórios para diferentes períodos
      mockSuggestionRepository.find.mockResolvedValue([]);
      mockVoteRepository.find.mockResolvedValue([]);
      mockSessionRepository.find.mockResolvedValue([]);

      const weeklyMetrics = await analyticsService.getMetrics('restaurant-123', AnalyticsPeriod.WEEK);
      const monthlyMetrics = await analyticsService.getMetrics('restaurant-123', AnalyticsPeriod.MONTH);

      expect(weeklyMetrics).toBeDefined();
      expect(monthlyMetrics).toBeDefined();
    });
  });

  describe('getDashboardSummary', () => {
    it('deve obter resumo do dashboard', async () => {
      const mockSummary: IDashboardSummary = {
        totalPlays: 200,
        totalSuggestions: 100,
        totalVotes: 500,
        pendingSuggestions: 10,
        dailySuggestions: 25,
        dailyVotes: 125,
        activeUsers: 50,
        averageRating: 4.2,
        growthRate: 15.5,
        peakHour: '20:00',
        topGenre: 'Rock',
        lastUpdated: new Date()
      };

      jest.spyOn(analyticsService as any, 'generateDashboardSummary')
        .mockResolvedValue(mockSummary);

      const result = await analyticsService.getDashboardSummary('restaurant-123');

      expect(result).toEqual(mockSummary);
    });
  });

  describe('getPopularSongs', () => {
    it('deve obter músicas populares', async () => {
      const mockPopularSongs = [
        {
          id: 'song-1',
          title: 'Popular Song 1',
          artist: 'Artist 1',
          votes: 50,
          plays: 100,
          score: 4.5,
          genre: 'Rock'
        },
        {
          id: 'song-2',
          title: 'Popular Song 2',
          artist: 'Artist 2',
          votes: 45,
          plays: 90,
          score: 4.3,
          genre: 'Pop'
        }
      ];

      // Mock do AppDataSource.query usado em getPopularSongs
      jest.spyOn(require('../config/database').AppDataSource, 'query')
        .mockResolvedValue([
          {
            id: 'song-1',
            title: 'Popular Song 1',
            artist: 'Artist 1',
            thumbnail_url: 'thumb1.jpg',
            genre: 'Rock',
            total_votes: '25',
            upvotes: '20',
            downvotes: '5',
            score: '15'
          },
          {
            id: 'song-2',
            title: 'Popular Song 2',
            artist: 'Artist 2',
            thumbnail_url: 'thumb2.jpg',
            genre: 'Pop',
            total_votes: '45',
            upvotes: '35',
            downvotes: '10',
            score: '25'
          }
        ]);

      const result = await analyticsService.getPopularSongs('restaurant-123', 10);

      expect(result).toHaveLength(2);
      expect(result[0]).toMatchObject({
        id: 'song-1',
        title: 'Popular Song 1',
        artist: 'Artist 1'
      });
    });
  });

  describe('Cache', () => {
    it('deve usar cache quando disponível', async () => {
      const cachedData = JSON.stringify(mockMetrics);
      mockRedisClient.getClient().get.mockResolvedValue(cachedData);
      
      // Mock dos repositórios para evitar erro
      mockSuggestionRepository.find.mockResolvedValue([]);
      mockVoteRepository.find.mockResolvedValue([]);
      mockSessionRepository.find.mockResolvedValue([]);

      await analyticsService.getMetrics('restaurant-123', AnalyticsPeriod.DAY);

      expect(mockRedisClient.getClient().get).toHaveBeenCalled();
      expect(mockRedisClient.getClient().setEx).not.toHaveBeenCalled();
    });

    it('deve funcionar sem Redis', async () => {
      mockRedisClient.isReady.mockReturnValue(false);
      
      // Mock dos repositórios para simular dados do banco
      mockSuggestionRepository.find.mockResolvedValue([]);
      mockVoteRepository.find.mockResolvedValue([]);
      mockSessionRepository.find.mockResolvedValue([]);

      const result = await analyticsService.getMetrics('restaurant-123', AnalyticsPeriod.DAY);

      expect(result).toBeDefined();
      expect(mockRedisClient.getClient().get).not.toHaveBeenCalled();
    });

    it('deve invalidar cache quando solicitado', async () => {
      // Mock para simular que existem chaves para deletar
      mockRedisClient.getClient().keys.mockResolvedValue(['analytics:restaurant-123:key1', 'analytics:restaurant-123:key2']);
      
      await analyticsService.clearCache('restaurant-123');

      expect(mockRedisClient.getClient().keys).toHaveBeenCalledWith('analytics:restaurant-123:*');
      expect(mockRedisClient.getClient().del).toHaveBeenCalledWith(['analytics:restaurant-123:key1', 'analytics:restaurant-123:key2']);
    });
  });

  describe('Tratamento de Erros', () => {
    it('deve lançar AnalyticsError com código específico', () => {
      const error = new AnalyticsError('Test error', 'TEST_ERROR', 400, true, 'restaurant-123');

      expect(error).toBeInstanceOf(AnalyticsError);
      expect(error.code).toBe('TEST_ERROR');
      expect(error.statusCode).toBe(400);
      expect(error.restaurantId).toBe('restaurant-123');
    });

    it('deve lidar com falhas de query graciosamente', async () => {
      mockSuggestionRepository.find.mockRejectedValue(new Error('Database error'));

      await expect(
        analyticsService.getMetrics('restaurant-123', AnalyticsPeriod.DAY)
      ).rejects.toThrow('Database error');
    });
  });

  describe('Performance', () => {
    it('deve executar queries otimizadas', async () => {
      const startTime = Date.now();
      
      // Mock dos repositórios para teste de performance
      mockSuggestionRepository.find.mockResolvedValue([]);
      mockVoteRepository.find.mockResolvedValue([]);
      mockSessionRepository.find.mockResolvedValue([]);

      await analyticsService.getMetrics('restaurant-123', AnalyticsPeriod.DAY);

      const executionTime = Date.now() - startTime;
      expect(executionTime).toBeLessThan(1000); // Menos de 1 segundo
    });
  });
});
