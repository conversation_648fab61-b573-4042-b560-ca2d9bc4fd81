-- Ajustes idempotentes de colunas ausentes em tabelas principais

-- restaurants: colunas utilitárias
DO $$
BEGIN
	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='restaurants' AND column_name='code'
	) THEN
		ALTER TABLE restaurants ADD COLUMN code VARCHAR(50);
	END IF;

	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='restaurants' AND column_name='slug'
	) THEN
		ALTER TABLE restaurants ADD COLUMN slug VARCHAR(80);
	END IF;

	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='restaurants' AND column_name='timezone'
	) THEN
		ALTER TABLE restaurants ADD COLUMN timezone VARCHAR(60) NOT NULL DEFAULT 'America/Sao_Paulo';
	END IF;

	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='restaurants' AND column_name='isActive'
	) THEN
		ALTER TABLE restaurants ADD COLUMN "isActive" BOOLEAN NOT NULL DEFAULT true;
	END IF;

	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='restaurants' AND column_name='created_at'
	) THEN
		ALTER TABLE restaurants ADD COLUMN created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW();
	END IF;
	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='restaurants' AND column_name='updated_at'
	) THEN
		ALTER TABLE restaurants ADD COLUMN updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW();
	END IF;
END $$;

-- users: assegurar role e vínculo opcional ao restaurante (compatível com restaurants.id VARCHAR)
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_name='users' AND column_name='role'
  ) THEN
    ALTER TABLE users ADD COLUMN role VARCHAR(20) NOT NULL DEFAULT 'user';
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_name='users' AND column_name='restaurant_id'
  ) THEN
    ALTER TABLE users ADD COLUMN restaurant_id VARCHAR(255) NULL;
  ELSE
    -- Se o tipo for diferente de VARCHAR, ajustar com cast seguro
    IF EXISTS (
      SELECT 1 FROM information_schema.columns
      WHERE table_name='users' AND column_name='restaurant_id' AND data_type <> 'character varying'
    ) THEN
      ALTER TABLE users ALTER COLUMN restaurant_id TYPE VARCHAR(255) USING restaurant_id::text;
    END IF;
  END IF;

  -- Remover FK incorreta (se existir) e recriar corretamente
  IF EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE constraint_name='fk_users_restaurant' AND constraint_type='FOREIGN KEY'
  ) THEN
    ALTER TABLE users DROP CONSTRAINT fk_users_restaurant;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE constraint_name='fk_users_restaurant' AND constraint_type='FOREIGN KEY'
  ) THEN
    ALTER TABLE users
      ADD CONSTRAINT fk_users_restaurant FOREIGN KEY (restaurant_id)
      REFERENCES restaurants(id) ON DELETE SET NULL;
  END IF;
END $$;

-- playlists: sinalizadores comuns
DO $$
BEGIN
	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='is_public'
	) THEN
		ALTER TABLE playlists ADD COLUMN is_public BOOLEAN NOT NULL DEFAULT true;
	END IF;
	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='is_default'
	) THEN
		ALTER TABLE playlists ADD COLUMN is_default BOOLEAN NOT NULL DEFAULT false;
	END IF;
END $$;

-- suggestions: compatibilidade de pagamento (não conflitará com scripts anteriores)
DO $$
BEGIN
	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='is_paid'
	) THEN
		ALTER TABLE suggestions ADD COLUMN is_paid BOOLEAN NOT NULL DEFAULT false;
	END IF;
	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='payment_amount'
	) THEN
		ALTER TABLE suggestions ADD COLUMN payment_amount INTEGER NOT NULL DEFAULT 0;
	END IF;
END $$;

