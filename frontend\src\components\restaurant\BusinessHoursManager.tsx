import React, { useState, useEffect, useCallback, memo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Clock,
  Calendar,
  Plus,
  Edit3,
  Trash2,
  Save,
  X,
  AlertCircle,
  Copy,
  RotateCcw,
  Globe,
} from "lucide-react";
import { toast } from "react-hot-toast";
import apiService from "@/services/api";

enum DayOfWeek {
  Monday = "monday",
  Tuesday = "tuesday",
  Wednesday = "wednesday",
  Thursday = "thursday",
  Friday = "friday",
  Saturday = "saturday",
  Sunday = "sunday",
}

interface BusinessHours {
  [key: string]: {
    open: string;
    close: string;
    isOpen: boolean;
  };
}

interface SpecialHours {
  id: string;
  date: string;
  name: string;
  hours: {
    open: string;
    close: string;
    isOpen: boolean;
  };
  description?: string;
}

interface BusinessHoursManagerProps {
  restaurantId: string;
  initialHours?: BusinessHours;
  onSave?: (hours: BusinessHours, specialHours: SpecialHours[]) => void;
}

const BusinessHoursManager: React.FC<BusinessHoursManagerProps> = memo(
  ({ restaurantId, initialHours, onSave }) => {
    const [businessHours, setBusinessHours] = useState<BusinessHours>({
      [DayOfWeek.Monday]: { open: "11:00", close: "23:00", isOpen: true },
      [DayOfWeek.Tuesday]: { open: "11:00", close: "23:00", isOpen: true },
      [DayOfWeek.Wednesday]: { open: "11:00", close: "23:00", isOpen: true },
      [DayOfWeek.Thursday]: { open: "11:00", close: "23:00", isOpen: true },
      [DayOfWeek.Friday]: { open: "11:00", close: "24:00", isOpen: true },
      [DayOfWeek.Saturday]: { open: "11:00", close: "24:00", isOpen: true },
      [DayOfWeek.Sunday]: { open: "11:00", close: "22:00", isOpen: true },
    });
    const [specialHours, setSpecialHours] = useState<SpecialHours[]>([]);
    const [timezone, setTimezone] = useState("America/Sao_Paulo");
    const [isEditing, setIsEditing] = useState(false);
    const [loading, setLoading] = useState(false);
    const [status, setStatus] = useState<{ isOpen: boolean; message: string }>({
      isOpen: false,
      message: "Carregando...",
    });

    const daysOfWeek = [
      { key: DayOfWeek.Monday, label: "Segunda-feira", short: "Seg" },
      { key: DayOfWeek.Tuesday, label: "Terça-feira", short: "Ter" },
      { key: DayOfWeek.Wednesday, label: "Quarta-feira", short: "Qua" },
      { key: DayOfWeek.Thursday, label: "Quinta-feira", short: "Qui" },
      { key: DayOfWeek.Friday, label: "Sexta-feira", short: "Sex" },
      { key: DayOfWeek.Saturday, label: "Sábado", short: "Sáb" },
      { key: DayOfWeek.Sunday, label: "Domingo", short: "Dom" },
    ];

    const timezones = [
      { value: "America/Sao_Paulo", label: "São Paulo (GMT-3)" },
      { value: "America/New_York", label: "Nova York (GMT-5)" },
      { value: "Europe/London", label: "Londres (GMT+0)" },
      { value: "Asia/Tokyo", label: "Tóquio (GMT+9)" },
      { value: "Australia/Sydney", label: "Sydney (GMT+10)" },
    ];

    const loadBusinessHours = useCallback(async () => {
      try {
        setLoading(true);
        const [hoursResponse, statusResponse] = await Promise.all([
          apiService.client.get(`/business-hours/${restaurantId}`),
          apiService.client.get(`/business-hours/${restaurantId}/status`),
        ]);

        if (hoursResponse.data.hours?.regular) {
          const hoursObj: BusinessHours = {};
          hoursResponse.data.hours.regular.forEach((day: any) => {
            hoursObj[day.day as keyof BusinessHours] = {
              open: day.open,
              close: day.close,
              isOpen: day.isOpen,
            };
          });
          setBusinessHours(hoursObj);
        }

        if (hoursResponse.data.hours?.special) {
          setSpecialHours(
            hoursResponse.data.hours.special.map((sh: any) => ({
              ...sh,
              date: new Date(sh.date).toISOString().split("T")[0],
            }))
          );
        }

        if (hoursResponse.data.hours?.timezone) {
          setTimezone(hoursResponse.data.hours.timezone);
        }

        if (statusResponse.data.status) {
          setStatus(statusResponse.data.status);
        }
      } catch (error) {
        console.error("Erro ao carregar horários:", error);
        toast.error("Erro ao carregar horários de funcionamento");
      } finally {
        setLoading(false);
      }
    }, [restaurantId]);

    useEffect(() => {
      if (initialHours) {
        setBusinessHours(initialHours);
      }
      loadBusinessHours();
    }, [initialHours, loadBusinessHours]);

    const updateBusinessHours = useCallback(
      (day: string, field: "open" | "close" | "isOpen", value: string | boolean) => {
        setBusinessHours((prev) => ({
          ...prev,
          [day]: { ...prev[day], [field]: value },
        }));
      },
      []
    );

    const copyHours = useCallback(
      (fromDay: string, toDay: string) => {
        setBusinessHours((prev) => ({
          ...prev,
          [toDay]: { ...prev[fromDay] },
        }));
        toast.success(
          `Horários de ${daysOfWeek.find((d) => d.key === fromDay)?.label} copiados para ${
            daysOfWeek.find((d) => d.key === toDay)?.label
          }`
        );
      },
      []
    );

    const applyToAllDays = useCallback(
      (day: string) => {
        const sourceHours = businessHours[day];
        const newHours = { ...businessHours };
        daysOfWeek.forEach((d) => {
          if (d.key !== day) {
            newHours[d.key] = { ...sourceHours };
          }
        });
        setBusinessHours(newHours);
        toast.success("Horários aplicados a todos os dias");
      },
      [businessHours]
    );

    const resetToDefault = useCallback(() => {
      setBusinessHours({
        [DayOfWeek.Monday]: { open: "11:00", close: "23:00", isOpen: true },
        [DayOfWeek.Tuesday]: { open: "11:00", close: "23:00", isOpen: true },
        [DayOfWeek.Wednesday]: { open: "11:00", close: "23:00", isOpen: true },
        [DayOfWeek.Thursday]: { open: "11:00", close: "23:00", isOpen: true },
        [DayOfWeek.Friday]: { open: "11:00", close: "24:00", isOpen: true },
        [DayOfWeek.Saturday]: { open: "11:00", close: "24:00", isOpen: true },
        [DayOfWeek.Sunday]: { open: "11:00", close: "22:00", isOpen: true },
      });
      toast.success("Horários resetados para padrão");
    }, []);

    const addSpecialHours = useCallback(() => {
      const newSpecialHours: SpecialHours = {
        id: crypto.randomUUID(),
        date: new Date().toISOString().split("T")[0],
        name: "Horário Especial",
        hours: { open: "11:00", close: "23:00", isOpen: true },
        description: "",
      };
      setSpecialHours((prev) => [...prev, newSpecialHours]);
      toast.success("Novo horário especial adicionado");
    }, []);

    const updateSpecialHours = useCallback((id: string, field: keyof SpecialHours, value: any) => {
      setSpecialHours((prev) =>
        prev.map((sh) => (sh.id === id ? { ...sh, [field]: value } : sh))
      );
    }, []);

    const removeSpecialHours = useCallback((id: string) => {
      setSpecialHours((prev) => prev.filter((sh) => sh.id !== id));
      toast.success("Horário especial removido");
    }, []);

    const saveBusinessHours = useCallback(async () => {
      setLoading(true);
      try {
        const response = await apiService.client.put(`/business-hours/${restaurantId}`, {
          businessHours,
          specialHours,
          timezone,
        });

        toast.success("Horários salvos com sucesso!");
        setIsEditing(false);
        if (onSave) {
          onSave(businessHours, specialHours);
        }
        await loadBusinessHours();
      } catch (error) {
        console.error("Erro ao salvar:", error);
        toast.error("Erro ao salvar horários");
      } finally {
        setLoading(false);
      }
    }, [businessHours, specialHours, timezone, restaurantId, onSave, loadBusinessHours]);

    const getCurrentStatus = useCallback(() => {
      const now = new Date();
      const currentDay = now.toLocaleDateString("en-US", { weekday: "long" }).toLowerCase();
      const currentTime = now.toTimeString().slice(0, 5);

      const specialHour = specialHours.find(
        (sh) => sh.date === now.toISOString().split("T")[0]
      );

      if (specialHour) {
        if (!specialHour.hours.isOpen) {
          return { isOpen: false, message: `Fechado - ${specialHour.name}` };
        }
        const isOpen = currentTime >= specialHour.hours.open && currentTime <= specialHour.hours.close;
        return {
          isOpen,
          message: isOpen ? `Aberto até ${specialHour.hours.close} (${specialHour.name})` : `Fechado - Abre às ${specialHour.hours.open} (${specialHour.name})`,
        };
      }

      const todayHours = businessHours[currentDay];
      if (!todayHours?.isOpen) {
        return { isOpen: false, message: "Fechado hoje" };
      }

      const isCurrentlyOpen = currentTime >= todayHours.open && currentTime <= todayHours.close;
      return {
        isOpen: isCurrentlyOpen,
        message: isCurrentlyOpen ? `Aberto até ${todayHours.close}` : `Fechado - Abre às ${todayHours.open}`,
      };
    }, [businessHours, specialHours]);

    useEffect(() => {
      setStatus(getCurrentStatus());
    }, [businessHours, specialHours, getCurrentStatus]);

    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <Clock className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Horários de Funcionamento</h2>
              <div className="flex items-center space-x-2 mt-1">
                <div className={`w-2 h-2 rounded-full ${status.isOpen ? "bg-green-500" : "bg-red-500"}`} />
                <span className="text-sm text-gray-600 dark:text-gray-400">{status.message}</span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {!isEditing ? (
              <button
                onClick={() => setIsEditing(true)}
                onKeyDown={(e) => e.key === "Enter" && setIsEditing(true)}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                aria-label="Editar horários"
              >
                <Edit3 className="w-4 h-4" />
                <span>Editar</span>
              </button>
            ) : (
              <>
                <button
                  onClick={() => setIsEditing(false)}
                  onKeyDown={(e) => e.key === "Enter" && setIsEditing(false)}
                  className="flex items-center space-x-2 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                  aria-label="Cancelar edição"
                >
                  <X className="w-4 h-4" />
                  <span>Cancelar</span>
                </button>
                <button
                  onClick={saveBusinessHours}
                  disabled={loading}
                  className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors"
                  aria-label="Salvar horários"
                >
                  <Save className="w-4 h-4" />
                  <span>{loading ? "Salvando..." : "Salvar"}</span>
                </button>
              </>
            )}
          </div>
        </div>

        {/* Timezone Selection */}
        {isEditing && (
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3 mb-3">
              <Globe className="w-5 h-5 text-gray-600 dark:text-gray-400" />
              <h3 className="font-medium text-gray-900 dark:text-white">Fuso Horário</h3>
            </div>
            <select
              value={timezone}
              onChange={(e) => setTimezone(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              aria-label="Selecionar fuso horário"
            >
              {timezones.map((tz) => (
                <option key={tz.value} value={tz.value}>
                  {tz.label}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Business Hours */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-medium text-gray-900 dark:text-white">Horários Regulares</h3>
            {isEditing && (
              <div className="flex items-center space-x-2">
                <button
                  onClick={resetToDefault}
                  onKeyDown={(e) => e.key === "Enter" && resetToDefault()}
                  className="flex items-center space-x-1 px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                  aria-label="Resetar horários"
                >
                  <RotateCcw className="w-3 h-3" />
                  <span>Resetar</span>
                </button>
              </div>
            )}
          </div>
          <div className="space-y-3">
            {daysOfWeek.map((day) => (
              <motion.div
                key={day.key}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="flex items-center space-x-4 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg"
              >
                <div className="w-24">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{day.short}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={businessHours[day.key]?.isOpen || false}
                    onChange={(e) => updateBusinessHours(day.key, "isOpen", e.target.checked)}
                    disabled={!isEditing}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    aria-label={`Aberto na ${day.label}`}
                  />
                  <span className="text-sm text-gray-600 dark:text-gray-400">Aberto</span>
                </div>
                {businessHours[day.key]?.isOpen && (
                  <div className="flex items-center space-x-2 flex-1">
                    <input
                      type="time"
                      value={businessHours[day.key]?.open || ""}
                      onChange={(e) => updateBusinessHours(day.key, "open", e.target.value)}
                      disabled={!isEditing}
                      className="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      aria-label={`Horário de abertura na ${day.label}`}
                    />
                    <span className="text-gray-500 text-sm">às</span>
                    <input
                      type="time"
                      value={businessHours[day.key]?.close || ""}
                      onChange={(e) => updateBusinessHours(day.key, "close", e.target.value)}
                      disabled={!isEditing}
                      className="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      aria-label={`Horário de fechamento na ${day.label}`}
                    />
                  </div>
                )}
                {isEditing && (
                  <div className="flex items-center space-x-1">
                    <button
                      onClick={() => applyToAllDays(day.key)}
                      onKeyDown={(e) => e.key === "Enter" && applyToAllDays(day.key)}
                      className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                      title="Aplicar a todos os dias"
                      aria-label={`Aplicar horários de ${day.label} a todos os dias`}
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </div>

        {/* Special Hours */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-medium text-gray-900 dark:text-white">Horários Especiais</h3>
            {isEditing && (
              <button
                onClick={addSpecialHours}
                onKeyDown={(e) => e.key === "Enter" && addSpecialHours()}
                className="flex items-center space-x-2 px-3 py-1 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                aria-label="Adicionar horário especial"
              >
                <Plus className="w-4 h-4" />
                <span>Adicionar</span>
              </button>
            )}
          </div>
          {specialHours.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <Calendar className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>Nenhum horário especial configurado</p>
              <p className="text-sm">Adicione feriados ou eventos especiais</p>
            </div>
          ) : (
            <div className="space-y-3">
              {specialHours.map((special) => (
                <motion.div
                  key={special.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className="flex items-center space-x-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800"
                >
                  <input
                    type="date"
                    value={special.date}
                    onChange={(e) => updateSpecialHours(special.id, "date", e.target.value)}
                    disabled={!isEditing}
                    className="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    aria-label={`Data do horário especial ${special.name}`}
                  />
                  <input
                    type="text"
                    value={special.name}
                    onChange={(e) => updateSpecialHours(special.id, "name", e.target.value)}
                    disabled={!isEditing}
                    placeholder="Nome do evento"
                    className="flex-1 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    aria-label={`Nome do horário especial`}
                  />
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={special.hours.isOpen}
                      onChange={(e) =>
                        updateSpecialHours(special.id, "hours", {
                          ...special.hours,
                          isOpen: e.target.checked,
                        })
                      }
                      disabled={!isEditing}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      aria-label={`Aberto para ${special.name}`}
                    />
                    {special.hours.isOpen && (
                      <>
                        <input
                          type="time"
                          value={special.hours.open}
                          onChange={(e) =>
                            updateSpecialHours(special.id, "hours", {
                              ...special.hours,
                              open: e.target.value,
                            })
                          }
                          disabled={!isEditing}
                          className="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          aria-label={`Horário de abertura para ${special.name}`}
                        />
                        <span className="text-gray-500 text-sm">às</span>
                        <input
                          type="time"
                          value={special.hours.close}
                          onChange={(e) =>
                            updateSpecialHours(special.id, "hours", {
                              ...special.hours,
                              close: e.target.value,
                            })
                          }
                          disabled={!isEditing}
                          className="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          aria-label={`Horário de fechamento para ${special.name}`}
                        />
                      </>
                    )}
                  </div>
                  {isEditing && (
                    <button
                      onClick={() => removeSpecialHours(special.id)}
                      onKeyDown={(e) => e.key === "Enter" && removeSpecialHours(special.id)}
                      className="p-1 text-red-400 hover:text-red-600 transition-colors"
                      aria-label={`Remover horário especial ${special.name}`}
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  )}
                </motion.div>
              ))}
            </div>
          )}
        </div>

        {/* Status Info */}
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
          <div className="flex items-start space-x-3">
            <AlertCircle className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-1">Informações Importantes</h4>
              <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                <li>• Os horários são aplicados no fuso horário selecionado</li>
                <li>• Horários especiais têm prioridade sobre horários regulares</li>
                <li>• Clientes verão uma mensagem quando o restaurante estiver fechado</li>
                <li>• Use "24:00" para meia-noite do dia seguinte</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    );
  }
);

export default BusinessHoursManager;
