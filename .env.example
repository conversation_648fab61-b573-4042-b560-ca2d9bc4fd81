# ==================== CONFIGURAÇÕES DO YOUTUBE API ====================
# Para obter uma API Key do YouTube:
# 1. Acesse: https://console.cloud.google.com/
# 2. Crie um novo projeto ou selecione um existente
# 3. Vá para "APIs e Serviços" > "Biblioteca"
# 4. Procure por "YouTube Data API v3" e ative
# 5. Vá para "Credenciais" > "Criar Credenciais" > "Chave de API"
# 6. Configure restrições (opcional mas recomendado)
# 7. Cole a chave abaixo (formato: AIzaSyBxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx)

YOUTUBE_API_KEY=sua_chave_api_youtube_aqui

# Para testes, você pode usar uma API key real do YouTube Data API v3
# Exemplo: YOUTUBE_API_KEY=AIzaSyBxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# IMPORTANTE: Sem uma API Key válida, o sistema usará dados mock
# Com API Key real, você terá acesso ao catálogo completo do YouTube

# Configurações de Segurança
JWT_SECRET=seu_jwt_secret_super_seguro_aqui

# Configurações do Banco de Dados
DATABASE_URL=postgresql://restaurant_user:restaurant_pass@localhost:5432/restaurant_playlist

# Configurações do Redis
REDIS_URL=redis://localhost:6379

# Configurações do Servidor
NODE_ENV=development
PORT=5000

# Configurações de URLs (IMPORTANTE: Use as portas corretas)
API_URL=http://localhost:8001
FRONTEND_URL=http://localhost:8000

# Configurações do Frontend (Vite)
VITE_API_URL=http://localhost:8001
VITE_WS_URL=ws://localhost:8001

# CORS Origins (separados por vírgula)
CORS_ORIGINS=http://localhost:8000,http://localhost:8001,http://localhost:8002

# ==================== MERCADO PAGO (PAGAMENTOS PIX) ====================
# Para obter credenciais do Mercado Pago:
# 1. Acesse: https://www.mercadopago.com.br/developers
# 2. Crie uma aplicação
# 3. Obtenha as credenciais de teste (TEST-) para desenvolvimento
# 4. Para produção, use as credenciais reais (APP_USR-)

MERCADO_PAGO_ACCESS_TOKEN=TEST-1234567890123456-123456-abcdef1234567890abcdef1234567890-123456789
MERCADO_PAGO_PUBLIC_KEY=TEST-abcdef12-3456-7890-abcd-ef1234567890
MERCADO_PAGO_WEBHOOK_SECRET=webhook_secret_key_here
MERCADO_PAGO_ENVIRONMENT=sandbox

# Configurações ECAD (Licenciamento Musical)
ECAD_ENABLED=true
ECAD_CONTACT_INFO="Para licenciamento musical, entre em contato com ECAD: www.ecad.org.br"

# Configurações de Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
YOUTUBE_API_QUOTA_LIMIT=10000

# Logs
LOG_LEVEL=info

# ===== Seed Demo/Admin (opcional) =====
# Ative para criar usuário admin de demonstração automaticamente
# RUN_SEED=true
# ADMIN_EMAIL=<EMAIL>
# ADMIN_PASSWORD=admin123
# ADMIN_NAME=Administrador
# ADMIN_RESTAURANT_ID=demo-restaurant
