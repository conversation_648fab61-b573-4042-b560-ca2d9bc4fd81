import { PlaybackService, PlaybackError, PlaybackAction, PlaybackTransitionMode, ITrack, IPlaybackState } from '../services/PlaybackService';
import { TestDataSource, initializeTestDatabase, cleanTestData, closeTestDatabase } from '../config/test-database';
import { Restaurant, RestaurantStatus } from '../models/Restaurant';
import { Suggestion, SuggestionStatus } from '../models/Suggestion';
import { PlayHistory } from '../models/PlayHistory';
import { Playlist } from '../models/Playlist';
import { Repository } from 'typeorm';

// Mock apenas do WebSocket e Analytics para simplificar
jest.mock('../services/WebSocketService', () => ({
  WebSocketService: {
    getInstance: () => ({
      emitToRestaurant: jest.fn(),
      emitToUser: jest.fn(),
      broadcast: jest.fn(),
      broadcastToRestaurant: jest.fn()
    })
  }
}));

jest.mock('../services/PlaylistAnalyticsService', () => ({
  PlaylistAnalyticsService: {
    getInstance: () => ({
      recordPlay: jest.fn(),
      recordSkip: jest.fn(),
      updateStats: jest.fn()
    })
  }
}));

// Mock AppDataSource para usar TestDataSource
jest.mock('../config/database', () => ({
  AppDataSource: {}
}));

describe('PlaybackService com dados reais', () => {
  let playbackService: PlaybackService;
  let restaurantRepository: Repository<Restaurant>;
  let suggestionRepository: Repository<Suggestion>;
  let testRestaurant: Restaurant;

  const mockTrack: ITrack = {
    id: 'track-123',
    title: 'Test Song',
    artist: 'Test Artist',
    youtubeVideoId: 'test-video-id',
    duration: 180,
    thumbnailUrl: 'https://test.com/thumb.jpg',
    upvotes: 10,
    downvotes: 2,
    score: 8,
    createdAt: new Date(),
    metadata: {
      genre: 'rock',
      mood: 'energetic',
      language: 'en'
    }
  };

  beforeAll(async () => {
    await initializeTestDatabase();
    restaurantRepository = TestDataSource.getRepository(Restaurant);
    suggestionRepository = TestDataSource.getRepository(Suggestion);
  });

  afterAll(async () => {
    try {
      if (playbackService && (playbackService as any).shutdown) {
        (playbackService as any).shutdown();
      }
    } finally {
      await closeTestDatabase();
    }
  });

  beforeEach(async () => {
    jest.clearAllMocks();
    
    // Limpar dados antes de cada teste
    await cleanTestData();
    
    // Reset singleton instance
    (PlaybackService as any).instance = undefined;
    
    // Mock AppDataSource.getRepository para retornar nossos repositórios de teste
    const { AppDataSource } = require('../config/database');
    AppDataSource.getRepository = jest.fn((entity: any) => {
      if (entity.name === 'Restaurant') return restaurantRepository;
      if (entity.name === 'Suggestion') return suggestionRepository;
      return TestDataSource.getRepository(entity);
    });
    
    // Criar restaurante de teste
    testRestaurant = restaurantRepository.create({
      id: 'test-restaurant-' + Date.now(),
      name: 'Test Restaurant',
      email: '<EMAIL>',
      status: RestaurantStatus.ACTIVE
    });
    await restaurantRepository.save(testRestaurant);
    
    // Criar instância do serviço
    playbackService = PlaybackService.getInstance();
  });

  describe('Inicialização com dados reais', () => {
    it('deve ser singleton', () => {
      const instance1 = PlaybackService.getInstance();
      const instance2 = PlaybackService.getInstance();
      expect(instance1).toBe(instance2);
    });

    it('deve inicializar estado de reprodução para restaurante válido', async () => {
      const state = await playbackService.initializePlayback(testRestaurant.id);

      expect(state).toBeDefined();
      expect(state.currentTrack).toBeNull();
      expect(state.isPlaying).toBe(false);
      expect(state.queue).toEqual([]);
      expect(state.priorityQueue).toEqual([]);
      expect(state.normalQueue).toEqual([]);
      expect(state.volume).toBe(70);
    });

    it('deve lançar erro para restaurante inexistente', async () => {
      await expect(
        playbackService.initializePlayback('invalid-restaurant')
      ).rejects.toMatchObject({
        code: 'RESTAURANT_NOT_FOUND',
        statusCode: 404
      });
    });
  });

  describe('Operações básicas', () => {
    beforeEach(async () => {
      // Inicializar estado antes de cada teste
      await playbackService.initializePlayback(testRestaurant.id);
    });

    it('deve obter estado de reprodução', async () => {
      const state = await playbackService.getPlaybackState(testRestaurant.id);
      expect(state).toBeDefined();
      expect(state?.currentTrack).toBeNull();
      expect(state?.isPlaying).toBe(false);
    });

    it('deve gerenciar volume', async () => {
      await playbackService.setVolume(testRestaurant.id, 50);
      const state = await playbackService.getPlaybackState(testRestaurant.id);
      expect(state?.volume).toBe(50);
    });

    it('deve adicionar música à fila', async () => {
      await playbackService.addToQueue(testRestaurant.id, mockTrack, false);
      const state = await playbackService.getPlaybackState(testRestaurant.id);
      expect(state?.normalQueue).toHaveLength(1);
      expect(state?.normalQueue[0].id).toBe(mockTrack.id);
    });

    it('deve adicionar música à fila prioritária', async () => {
      await playbackService.addToQueue(testRestaurant.id, mockTrack, true);
      const state = await playbackService.getPlaybackState(testRestaurant.id);
      expect(state?.priorityQueue).toHaveLength(1);
      expect(state?.priorityQueue[0].id).toBe(mockTrack.id);
    });
  });

  describe('Performance', () => {
    it('deve executar operações rapidamente', async () => {
      const startTime = Date.now();

      await playbackService.initializePlayback(testRestaurant.id);
      await playbackService.addToQueue(testRestaurant.id, mockTrack, false);
      await playbackService.getPlaybackState(testRestaurant.id);

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // Operações devem ser rápidas (menos de 2 segundos)
      expect(executionTime).toBeLessThan(2000);
    });
  });
});
