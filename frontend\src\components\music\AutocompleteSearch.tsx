import React, { useState, useEffect, useRef, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Search,
  X,
  TrendingUp,
  History,
  Clock,
  Music,
  Play,
  Plus,
  Loader2,
} from "lucide-react";
import { toast } from "react-hot-toast";

interface SearchSuggestion {
  id: string;
  query: string;
  type: "history" | "trending" | "suggestion";
  count?: number;
}

interface AutocompleteSearchProps {
  onSearch: (query: string) => void;
  onSuggestionCreated?: () => void;
  placeholder?: string;
  restaurantId: string;
}

const AutocompleteSearch: React.FC<AutocompleteSearchProps> = ({
  onSearch,
  onSuggestionCreated,
  placeholder = "Buscar música, artista ou álbum...",
  restaurantId,
}) => {
  const [query, setQuery] = useState("");
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [trendingQueries, setTrendingQueries] = useState<string[]>([]);

  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Debounce para autocomplete
  const debounceTimeout = useRef<NodeJS.Timeout>();

  useEffect(() => {
    loadSearchHistory();
    loadTrendingQueries();
  }, [restaurantId]);

  const loadSearchHistory = () => {
    const history = localStorage.getItem(`searchHistory_${restaurantId}`);
    if (history) {
      setSearchHistory(JSON.parse(history).slice(0, 5));
    }
  };

  const loadTrendingQueries = async () => {
    try {
      const response = await fetch(
        `http://localhost:8001/api/v1/search/trending/${restaurantId}`
      );
      if (response.ok) {
        const data = await response.json();
        setTrendingQueries(data.queries || []);
      }
    } catch (error) {
      // Fallback para queries trending mock
      setTrendingQueries([
        "rock nacional",
        "sertanejo",
        "pop internacional",
        "mpb",
        "eletrônica",
      ]);
    }
  };

  const saveToHistory = (searchQuery: string) => {
    const history = [...searchHistory];
    const existingIndex = history.indexOf(searchQuery);
    
    if (existingIndex > -1) {
      history.splice(existingIndex, 1);
    }
    
    history.unshift(searchQuery);
    const newHistory = history.slice(0, 10);
    
    setSearchHistory(newHistory);
    localStorage.setItem(
      `searchHistory_${restaurantId}`,
      JSON.stringify(newHistory)
    );
  };

  const generateSuggestions = useCallback(
    (searchQuery: string) => {
      if (!searchQuery.trim()) {
        // Mostrar histórico e trending quando não há query
        const historySuggestions: SearchSuggestion[] = searchHistory.map(
          (item, index) => ({
            id: `history_${index}`,
            query: item,
            type: "history",
          })
        );

        const trendingSuggestions: SearchSuggestion[] = trendingQueries.map(
          (item, index) => ({
            id: `trending_${index}`,
            query: item,
            type: "trending",
            count: Math.floor(Math.random() * 50) + 10,
          })
        );

        setSuggestions([...historySuggestions, ...trendingSuggestions]);
        return;
      }

      // Filtrar histórico baseado na query
      const filteredHistory = searchHistory
        .filter((item) =>
          item.toLowerCase().includes(searchQuery.toLowerCase())
        )
        .map((item, index) => ({
          id: `history_${index}`,
          query: item,
          type: "history" as const,
        }));

      // Filtrar trending baseado na query
      const filteredTrending = trendingQueries
        .filter((item) =>
          item.toLowerCase().includes(searchQuery.toLowerCase())
        )
        .map((item, index) => ({
          id: `trending_${index}`,
          query: item,
          type: "trending" as const,
          count: Math.floor(Math.random() * 50) + 10,
        }));

      // Sugestões automáticas baseadas na query
      const autoSuggestions: SearchSuggestion[] = [];
      
      if (searchQuery.length >= 2) {
        // Gerar sugestões baseadas em padrões comuns
        const commonSuffixes = [
          " ao vivo",
          " acústico",
          " remix",
          " cover",
          " instrumental",
        ];
        
        commonSuffixes.forEach((suffix, index) => {
          if (!searchQuery.includes(suffix)) {
            autoSuggestions.push({
              id: `auto_${index}`,
              query: searchQuery + suffix,
              type: "suggestion",
            });
          }
        });
      }

      setSuggestions([
        ...filteredHistory,
        ...filteredTrending,
        ...autoSuggestions.slice(0, 3),
      ]);
    },
    [searchHistory, trendingQueries]
  );

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    setSelectedIndex(-1);

    // Debounce para evitar muitas chamadas
    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }

    debounceTimeout.current = setTimeout(() => {
      generateSuggestions(value);
      setShowSuggestions(true);
    }, 300);
  };

  const handleInputFocus = () => {
    generateSuggestions(query);
    setShowSuggestions(true);
  };

  const handleInputBlur = () => {
    // Delay para permitir clique nas sugestões
    setTimeout(() => {
      setShowSuggestions(false);
    }, 200);
  };

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setQuery(suggestion.query);
    setShowSuggestions(false);
    handleSearch(suggestion.query);
  };

  const handleSearch = (searchQuery?: string) => {
    const finalQuery = searchQuery || query;
    if (!finalQuery.trim()) return;

    saveToHistory(finalQuery);
    onSearch(finalQuery);
    setShowSuggestions(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions || suggestions.length === 0) {
      if (e.key === "Enter") {
        handleSearch();
      }
      return;
    }

    switch (e.key) {
      case "ArrowDown":
        e.preventDefault();
        setSelectedIndex((prev) =>
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case "ArrowUp":
        e.preventDefault();
        setSelectedIndex((prev) =>
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case "Enter":
        e.preventDefault();
        if (selectedIndex >= 0) {
          handleSuggestionClick(suggestions[selectedIndex]);
        } else {
          handleSearch();
        }
        break;
      case "Escape":
        setShowSuggestions(false);
        setSelectedIndex(-1);
        break;
    }
  };

  const clearQuery = () => {
    setQuery("");
    setShowSuggestions(false);
    inputRef.current?.focus();
  };

  const getSuggestionIcon = (type: SearchSuggestion["type"]) => {
    switch (type) {
      case "history":
        return <History className="w-4 h-4 text-gray-400" />;
      case "trending":
        return <TrendingUp className="w-4 h-4 text-orange-500" />;
      case "suggestion":
        return <Search className="w-4 h-4 text-blue-500" />;
      default:
        return <Music className="w-4 h-4 text-gray-400" />;
    }
  };

  return (
    <div className="relative w-full">
      {/* Input de busca */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className="block w-full pl-10 pr-10 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
        />
        
        {query && (
          <button
            onClick={clearQuery}
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
          >
            <X className="h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors" />
          </button>
        )}
      </div>

      {/* Sugestões */}
      <AnimatePresence>
        {showSuggestions && suggestions.length > 0 && (
          <motion.div
            ref={suggestionsRef}
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg max-h-80 overflow-y-auto"
          >
            {suggestions.map((suggestion, index) => (
              <motion.div
                key={suggestion.id}
                className={`px-4 py-3 cursor-pointer transition-colors ${
                  index === selectedIndex
                    ? "bg-blue-50 dark:bg-blue-900/20"
                    : "hover:bg-gray-50 dark:hover:bg-gray-700"
                } ${index === 0 ? "rounded-t-lg" : ""} ${
                  index === suggestions.length - 1 ? "rounded-b-lg" : ""
                }`}
                onClick={() => handleSuggestionClick(suggestion)}
                whileHover={{ backgroundColor: "rgba(59, 130, 246, 0.05)" }}
              >
                <div className="flex items-center space-x-3">
                  {getSuggestionIcon(suggestion.type)}
                  <div className="flex-1">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {suggestion.query}
                    </div>
                    {suggestion.type === "trending" && suggestion.count && (
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {suggestion.count} buscas recentes
                      </div>
                    )}
                    {suggestion.type === "history" && (
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        Busca anterior
                      </div>
                    )}
                  </div>
                  <Plus className="w-4 h-4 text-gray-400" />
                </div>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AutocompleteSearch;
