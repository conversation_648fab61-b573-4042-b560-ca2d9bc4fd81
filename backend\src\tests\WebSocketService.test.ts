import { Server as SocketIOServer } from 'socket.io';
import { createServer } from 'http';
import { AddressInfo } from 'net';
import { io as Client, Socket as ClientSocket } from 'socket.io-client';
import { WebSocketService, ISuggestion, ITrack, WebSocketError } from '../services/WebSocketService';

describe('WebSocketService', () => {
  let httpServer: any;
  let ioServer: SocketIOServer;
  let wsService: WebSocketService;
  let clientSocket: ClientSocket;
  let serverPort: number;

  beforeAll((done) => {
    httpServer = createServer();
    ioServer = new SocketIOServer(httpServer, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"]
      }
    });

    wsService = WebSocketService.getInstance(ioServer);

    httpServer.listen(() => {
      serverPort = (httpServer.address() as AddressInfo).port;
      done();
    });
  });

  beforeEach((done) => {
    clientSocket = Client(`http://localhost:${serverPort}`);
    clientSocket.on('connect', done);
  });

  afterEach(() => {
    if (clientSocket.connected) {
      clientSocket.disconnect();
    }
  });

  afterAll(() => {
    ioServer.close();
    httpServer.close();
  });

  describe('Autenticação', () => {
    it('deve autenticar socket com token válido', (done) => {
      clientSocket.emit('authenticate', { token: 'admin-token', restaurantId: 'test-restaurant' });
      
      clientSocket.on('authenticated', (data) => {
        expect(data.success).toBe(true);
        expect(data.userId).toBe('admin-user');
        done();
      });
    });

    it('deve rejeitar autenticação com token inválido', (done) => {
      clientSocket.emit('authenticate', { token: 'invalid-token' });
      
      clientSocket.on('authenticationError', (data) => {
        expect(data.error).toContain('Token inválido');
        done();
      });
    });

    it('deve rejeitar autenticação sem token', (done) => {
      clientSocket.emit('authenticate', {});
      
      clientSocket.on('authenticationError', (data) => {
        expect(data.error).toContain('Token de autenticação obrigatório');
        done();
      });
    });
  });

  describe('Gerenciamento de Salas', () => {
    beforeEach((done) => {
      clientSocket.emit('authenticate', { token: 'admin-token', restaurantId: 'test-restaurant' });
      clientSocket.on('authenticated', () => done());
    });

    it('deve permitir entrada em sala de restaurante', (done) => {
      clientSocket.emit('joinRoom', { 
        restaurantId: 'test-restaurant', 
        role: 'admin' 
      });
      
      clientSocket.on('joinedRoom', (data) => {
        expect(data.restaurantId).toBe('test-restaurant');
        expect(data.rooms).toContain('restaurant-test-restaurant');
        expect(data.rooms).toContain('restaurant-test-restaurant-admins');
        done();
      });
    });

    it('deve permitir entrada em sala de mesa', (done) => {
      clientSocket.emit('joinRoom', { 
        restaurantId: 'test-restaurant', 
        tableNumber: 5,
        role: 'client'
      });
      
      clientSocket.on('joinedRoom', (data) => {
        expect(data.rooms).toContain('restaurant-test-restaurant-table-5');
        done();
      });
    });
  });

  describe('Notificações de Sugestões', () => {
    const mockSuggestion: ISuggestion = {
      id: 'suggestion-123',
      title: 'Test Song',
      artist: 'Test Artist',
      youtubeVideoId: 'test-video-id',
      upvotes: 5,
      downvotes: 1,
      status: 'pending',
      isPaid: false,
      sessionId: 'session-123',
      restaurantId: 'test-restaurant',
      createdAt: new Date()
    };

    beforeEach((done) => {
      clientSocket.emit('authenticate', { token: 'admin-token', restaurantId: 'test-restaurant' });
      clientSocket.on('authenticated', () => {
        clientSocket.emit('joinRoom', { restaurantId: 'test-restaurant', role: 'admin' });
        clientSocket.on('joinedRoom', () => done());
      });
    });

    it('deve notificar nova sugestão', (done) => {
      clientSocket.on('newSuggestion', (data) => {
        expect(data.data.id).toBe(mockSuggestion.id);
        expect(data.data.title).toBe(mockSuggestion.title);
        expect(data.timestamp).toBeDefined();
        done();
      });

      wsService.notifyNewSuggestion('test-restaurant', mockSuggestion);
    });

    it('deve notificar sugestão prioritária', (done) => {
      const prioritySuggestion = { ...mockSuggestion, isPaid: true };
      
      clientSocket.on('prioritySuggestion', (data) => {
        expect(data.data.isPaid).toBe(true);
        expect(data.data.priority).toBe(true);
        done();
      });

      wsService.notifyPrioritySuggestion('test-restaurant', prioritySuggestion);
    });

    it('deve notificar atualização de votos', (done) => {
      const votes = { upvotes: 10, downvotes: 2, total: 8 };
      
      clientSocket.on('voteUpdate', (data) => {
        expect(data.data.suggestionId).toBe('suggestion-123');
        expect(data.data.votes.total).toBe(8);
        done();
      });

      wsService.notifyVoteUpdate('test-restaurant', 'suggestion-123', votes);
    });
  });

  describe('Notificações de Reprodução', () => {
    const mockTrack: ITrack = {
      id: 'track-123',
      title: 'Test Track',
      artist: 'Test Artist',
      youtubeVideoId: 'test-video',
      duration: 180
    };

    beforeEach((done) => {
      clientSocket.emit('authenticate', { token: 'admin-token', restaurantId: 'test-restaurant' });
      clientSocket.on('authenticated', () => {
        clientSocket.emit('joinRoom', { restaurantId: 'test-restaurant', role: 'admin' });
        clientSocket.on('joinedRoom', () => done());
      });
    });

    it('deve notificar início de reprodução', (done) => {
      clientSocket.on('playbackStart', (data) => {
        expect(data.data.track.id).toBe(mockTrack.id);
        expect(data.data.event).toBe('start');
        expect(data.data.serverTime).toBeDefined();
        done();
      });

      wsService.notifyPlaybackStart('test-restaurant', mockTrack);
    });

    it('deve notificar fim de reprodução', (done) => {
      clientSocket.on('playbackEnd', (data) => {
        expect(data.data.track.id).toBe(mockTrack.id);
        expect(data.data.event).toBe('end');
        done();
      });

      wsService.notifyPlaybackEnd('test-restaurant', mockTrack);
    });
  });

  describe('Estatísticas de Conexão', () => {
    it('deve retornar estatísticas básicas', () => {
      const stats = wsService.getConnectionStats();
      
      expect(stats.totalConnections).toBeGreaterThanOrEqual(0);
      expect(stats.timestamp).toBeDefined();
      expect(stats.activeRooms).toBeInstanceOf(Array);
      expect(stats.restaurantConnections).toBeInstanceOf(Object);
    });

    it('deve retornar estatísticas de sala específica', () => {
      const roomStats = wsService.getRoomStats('restaurant-test-restaurant');
      expect(typeof roomStats).toBe('number');
    });
  });

  describe('Tratamento de Erros', () => {
    it('deve lançar WebSocketError para dados inválidos', async () => {
      await expect(
        wsService.emitToRestaurant('', 'test-event', {})
      ).rejects.toThrow(WebSocketError);
    });

    it('deve lançar erro para validação de votos inválidos', async () => {
      await expect(
        wsService.notifyVoteUpdate('test-restaurant', '', { upvotes: 0, downvotes: 0, total: -1 })
      ).rejects.toThrow(WebSocketError);
    });
  });

  describe('Limpeza de Conexões', () => {
    it('deve limpar conexões inativas', async () => {
      await expect(wsService.cleanupInactiveConnections()).resolves.not.toThrow();
    });
  });

  describe('Autorização de Papéis', () => {
    it('deve permitir acesso de admin a eventos sensíveis', (done) => {
      clientSocket.emit('authenticate', { token: 'admin-token', restaurantId: 'test-restaurant' });
      clientSocket.on('authenticated', () => {
        clientSocket.emit('joinRoom', { restaurantId: 'test-restaurant', role: 'admin' });
        clientSocket.on('joinedRoom', () => {
          
          clientSocket.on('newSuggestionAdmin', (data) => {
            expect(data.data.metadata).toBeDefined();
            expect(data.data.metadata.requiresModeration).toBeDefined();
            done();
          });

          const mockSuggestion: ISuggestion = {
            id: 'suggestion-123',
            title: 'Test Song',
            artist: 'Test Artist',
            youtubeVideoId: 'test-video-id',
            upvotes: 5,
            downvotes: 1,
            status: 'pending',
            isPaid: false,
            sessionId: 'session-123',
            restaurantId: 'test-restaurant',
            createdAt: new Date()
          };

          wsService.notifyNewSuggestion('test-restaurant', mockSuggestion);
        });
      });
    });
  });
});
