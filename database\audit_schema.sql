-- Auditoria rápida: lista tabelas, colunas e índices principais

-- Tabelas
SELECT table_schema, table_name
FROM information_schema.tables
WHERE table_schema NOT IN ('pg_catalog','information_schema')
ORDER BY table_schema, table_name;

-- <PERSON><PERSON>s
SELECT table_name, column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_schema='public'
ORDER BY table_name, ordinal_position;

-- Índices
SELECT t.relname AS table_name, i.relname AS index_name, a.amname AS index_type
FROM pg_class t
JOIN pg_index ix ON t.oid = ix.indrelid
JOIN pg_class i ON i.oid = ix.indexrelid
JOIN pg_am a ON i.relam = a.oid
JOIN pg_namespace n ON n.oid = t.relnamespace
WHERE t.relkind='r' AND n.nspname='public'
ORDER BY t.relname, i.relname;

