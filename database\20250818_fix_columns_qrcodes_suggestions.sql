-- Idempotent fix: align qr_codes columns and add suggestions.genre if missing
DO $$
BEGIN
  -- Ensure qr_codes has expected columns
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'qr_codes') THEN
    -- Add type
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='qr_codes' AND column_name='type'
    ) THEN
      ALTER TABLE qr_codes ADD COLUMN type VARCHAR;
    END IF;

    -- Add name
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='qr_codes' AND column_name='name'
    ) THEN
      ALTER TABLE qr_codes ADD COLUMN name VARCHAR;
    END IF;

    -- table_number as VARCHAR (some code uses string)
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='qr_codes' AND column_name='table_number'
    ) THEN
      ALTER TABLE qr_codes ADD COLUMN table_number VARCHAR;
    END IF;

    -- url
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='qr_codes' AND column_name='url'
    ) THEN
      ALTER TABLE qr_codes ADD COLUMN url VARCHAR;
    END IF;

    -- qr_code_data
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='qr_codes' AND column_name='qr_code_data'
    ) THEN
      ALTER TABLE qr_codes ADD COLUMN qr_code_data TEXT;
    END IF;
    -- Backfill qr_code_data from legacy 'code'
    IF EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='qr_codes' AND column_name='code'
    ) THEN
      EXECUTE 'UPDATE qr_codes SET qr_code_data = COALESCE(qr_code_data, code) WHERE qr_code_data IS NULL AND code IS NOT NULL';
    END IF;

    -- is_active
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='qr_codes' AND column_name='is_active'
    ) THEN
      ALTER TABLE qr_codes ADD COLUMN is_active BOOLEAN DEFAULT true;
    END IF;

    -- created_at already present; ensure updated_at exists
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='qr_codes' AND column_name='updated_at'
    ) THEN
      ALTER TABLE qr_codes ADD COLUMN updated_at TIMESTAMP;
    END IF;
  END IF;

  -- Ensure suggestions.genre exists (nullable)
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'suggestions') THEN
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='genre'
    ) THEN
      ALTER TABLE suggestions ADD COLUMN genre VARCHAR;
    END IF;
  END IF;
END $$;

-- Optional indexes for qr_codes
CREATE INDEX IF NOT EXISTS idx_qr_codes_active ON qr_codes(is_active);
CREATE INDEX IF NOT EXISTS idx_qr_codes_restaurant ON qr_codes(restaurant_id);
