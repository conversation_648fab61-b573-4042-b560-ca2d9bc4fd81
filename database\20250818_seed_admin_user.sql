-- Seed admin user with known password (admin123) if missing; update password if email exists
DO $$
DECLARE
  admin_email CONSTANT VARCHAR := '<EMAIL>';
  admin_hash  CONSTANT VARCHAR := '$2b$12$LQv3c1yqBwEHFl5ePEjNNONciJ0MGhppMn5rjJ9TndsqKJbvHEubS';
  admin_id UUID;
BEGIN
  IF NOT EXISTS (SELECT 1 FROM users WHERE email = admin_email) THEN
    admin_id := gen_random_uuid();
    INSERT INTO users (id, name, email, password, role, "isActive", "createdAt", "updatedAt")
    VALUES (admin_id, 'Administrador', admin_email, admin_hash, 'admin', TRUE, NOW(), NOW());
  ELSE
    UPDATE users SET password = admin_hash, "updatedAt"=NOW() WHERE email = admin_email;
  END IF;
END $$;
