-- Idempotent fixes for playlists: garantir colunas camelCase/snake_case usadas pelo backend
-- Inclui ajuste do enum playlist_status para conter 'deleted'

-- Adicionar valor 'deleted' ao enum playlist_status se não existir
DO $$
BEGIN
	IF NOT EXISTS (
		SELECT 1 FROM pg_type t
		JOIN pg_enum e ON t.oid = e.enumtypid
		WHERE t.typname = 'playlist_status' AND e.enumlabel = 'deleted'
	) THEN
		ALTER TYPE playlist_status ADD VALUE IF NOT EXISTS 'deleted';
	END IF;
EXCEPTION WHEN duplicate_object THEN
	-- ignorar corrida de condição
END $$;

-- Garan<PERSON><PERSON> colunas esperadas na tabela playlists
DO $$
BEGIN
	IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'playlists') THEN
		-- colunas básicas
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='name') THEN
			EXECUTE 'ALTER TABLE playlists ADD COLUMN name VARCHAR';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='description') THEN
			EXECUTE 'ALTER TABLE playlists ADD COLUMN description TEXT';
		END IF;

		-- enums type/status
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='type') THEN
			EXECUTE 'ALTER TABLE playlists ADD COLUMN type playlist_type DEFAULT ''custom''';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='status') THEN
			EXECUTE 'ALTER TABLE playlists ADD COLUMN status playlist_status DEFAULT ''active''';
		END IF;

		-- camelCase esperados pelo backend
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='youtubePlaylistId') THEN
			EXECUTE 'ALTER TABLE playlists ADD COLUMN "youtubePlaylistId" VARCHAR';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='coverImage') THEN
			EXECUTE 'ALTER TABLE playlists ADD COLUMN "coverImage" VARCHAR';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='genreTags') THEN
			EXECUTE 'ALTER TABLE playlists ADD COLUMN "genreTags" JSON';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='moodTags') THEN
			EXECUTE 'ALTER TABLE playlists ADD COLUMN "moodTags" JSON';
		END IF;

		-- flags e métricas
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='isDefault') THEN
			EXECUTE 'ALTER TABLE playlists ADD COLUMN "isDefault" BOOLEAN DEFAULT FALSE';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='isPublic') THEN
			EXECUTE 'ALTER TABLE playlists ADD COLUMN "isPublic" BOOLEAN DEFAULT TRUE';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='execution_order') THEN
			EXECUTE 'ALTER TABLE playlists ADD COLUMN execution_order INTEGER';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='trackCount') THEN
			EXECUTE 'ALTER TABLE playlists ADD COLUMN "trackCount" INTEGER DEFAULT 0';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='totalDuration') THEN
			EXECUTE 'ALTER TABLE playlists ADD COLUMN "totalDuration" INTEGER DEFAULT 0';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='playCount') THEN
			EXECUTE 'ALTER TABLE playlists ADD COLUMN "playCount" INTEGER DEFAULT 0';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='averageRating') THEN
			EXECUTE 'ALTER TABLE playlists ADD COLUMN "averageRating" FLOAT DEFAULT 0';
		END IF;

		-- JSONs
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='tracks') THEN
			EXECUTE 'ALTER TABLE playlists ADD COLUMN tracks JSON';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='settings') THEN
			EXECUTE 'ALTER TABLE playlists ADD COLUMN settings JSON';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='schedule') THEN
			EXECUTE 'ALTER TABLE playlists ADD COLUMN schedule JSON';
		END IF;

		-- relacionamento com restaurante
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='restaurant_id') THEN
			EXECUTE 'ALTER TABLE playlists ADD COLUMN restaurant_id VARCHAR(255)';
		END IF;
		IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='restaurant_id') THEN
			IF NOT EXISTS (
				SELECT 1 FROM information_schema.table_constraints 
				WHERE table_name='playlists' AND constraint_type='FOREIGN KEY' AND constraint_name='fk_playlists_restaurant'
			) THEN
				EXECUTE 'ALTER TABLE playlists ADD CONSTRAINT fk_playlists_restaurant FOREIGN KEY (restaurant_id) REFERENCES restaurants(id) ON DELETE CASCADE';
			END IF;
		END IF;

		-- timestamps
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='created_at') THEN
			EXECUTE 'ALTER TABLE playlists ADD COLUMN created_at TIMESTAMP DEFAULT NOW()';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='updated_at') THEN
			EXECUTE 'ALTER TABLE playlists ADD COLUMN updated_at TIMESTAMP DEFAULT NOW()';
		END IF;
	END IF;
END $$;

-- Índices úteis
CREATE INDEX IF NOT EXISTS idx_playlists_restaurant ON playlists(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_playlists_execution_order ON playlists(restaurant_id, execution_order);
CREATE INDEX IF NOT EXISTS idx_playlists_is_default ON playlists("isDefault");

