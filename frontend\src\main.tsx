import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

// Registrar Service Worker para PWA
if ('serviceWorker' in navigator && import.meta.env.PROD) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('SW registered: ', registration);
      })
      .catch((registrationError) => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}

// Configurar variáveis CSS para tema
const setThemeVariables = () => {
  const root = document.documentElement;
  
  // Variáveis para toast notifications
  root.style.setProperty('--toast-bg', 'rgb(255 255 255)');
  root.style.setProperty('--toast-color', 'rgb(17 24 39)');
  root.style.setProperty('--toast-border', 'rgb(229 231 235)');
  
  // Detectar tema escuro
  const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
  if (isDark) {
    root.style.setProperty('--toast-bg', 'rgb(31 41 55)');
    root.style.setProperty('--toast-color', 'rgb(243 244 246)');
    root.style.setProperty('--toast-border', 'rgb(75 85 99)');
  }
};

// Aplicar variáveis de tema
setThemeVariables();

// Monitorar mudanças no tema do sistema
window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', setThemeVariables);

// Renderizar aplicação
ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
