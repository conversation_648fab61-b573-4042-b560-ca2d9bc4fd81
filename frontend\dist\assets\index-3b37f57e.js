var Dn=Object.defineProperty;var $n=(r,t,s)=>t in r?Dn(r,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):r[t]=s;var Ce=(r,t,s)=>($n(r,typeof t!="symbol"?t+"":t,s),s);import{r as u,b as Ln,c as Fn,a as ne,g as qs}from"./vendor-66b0ef43.js";import{u as cs,L as $t,R as Or,a as ge,b as Un,c as Et,N as Tt,d as Vr,B as Mn}from"./router-f729e475.js";import{A as ze,m as q,L as ir,X as bt,B as Ot,M as Dr,a as Bs,E as $r,b as ds,c as we,S as Xr,P as Je,d as qt,e as qn,T as De,Q as ts,U as rt,f as vt,H as Qs,g as Bn,h as _s,V as Qn,R as Qe,i as ss,j as Zr,k as Lr,l as zn,n as Fr,o as Ta,p as Wn,q as Oa,D as Ur,r as Va,s as Gt,t as Da,C as Ye,u as $a,v as Hn,G as Kn,w as Yn,x as Ft,y as La,z as Fa,F as Jn,I as Ut,J as Xt,K as yt,W as Gn,N as Xn,O as Lt,Y as Ua,Z as Zn,_ as ei,$ as ti,a0 as Mr,a1 as Ma,a2 as us,a3 as ea,a4 as ta,a5 as rs,a6 as si,a7 as ri,a8 as ai,a9 as qa,aa as Ba,ab as qr,ac as Ts,ad as ni,ae as or,af as Os,ag as Qa,ah as ii,ai as oi,aj as li,ak as sa,al as ra,am as aa,an as ci,ao as di}from"./ui-1cb796d3.js";import{_ as ui,a as hi,v as Gs}from"./utils-08f61814.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))a(n);new MutationObserver(n=>{for(const i of n)if(i.type==="childList")for(const l of i.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&a(l)}).observe(document,{childList:!0,subtree:!0});function s(n){const i={};return n.integrity&&(i.integrity=n.integrity),n.referrerPolicy&&(i.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?i.credentials="include":n.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function a(n){if(n.ep)return;n.ep=!0;const i=s(n);fetch(n.href,i)}})();var za={exports:{}},zs={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var mi=u,xi=Symbol.for("react.element"),fi=Symbol.for("react.fragment"),pi=Object.prototype.hasOwnProperty,gi=mi.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,yi={key:!0,ref:!0,__self:!0,__source:!0};function Wa(r,t,s){var a,n={},i=null,l=null;s!==void 0&&(i=""+s),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(l=t.ref);for(a in t)pi.call(t,a)&&!yi.hasOwnProperty(a)&&(n[a]=t[a]);if(r&&r.defaultProps)for(a in t=r.defaultProps,t)n[a]===void 0&&(n[a]=t[a]);return{$$typeof:xi,type:r,key:i,ref:l,props:n,_owner:gi.current}}zs.Fragment=fi;zs.jsx=Wa;zs.jsxs=Wa;za.exports=zs;var e=za.exports,lr={},na=Ln;lr.createRoot=na.createRoot,lr.hydrateRoot=na.hydrateRoot;function Bt(r,t){r.prototype=Object.create(t.prototype),r.prototype.constructor=r,ui(r,t)}var Qt=function(){function r(){this.listeners=[]}var t=r.prototype;return t.subscribe=function(a){var n=this,i=a||function(){};return this.listeners.push(i),this.onSubscribe(),function(){n.listeners=n.listeners.filter(function(l){return l!==i}),n.onUnsubscribe()}},t.hasListeners=function(){return this.listeners.length>0},t.onSubscribe=function(){},t.onUnsubscribe=function(){},r}();function de(){return de=Object.assign?Object.assign.bind():function(r){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)({}).hasOwnProperty.call(s,a)&&(r[a]=s[a])}return r},de.apply(null,arguments)}var Vs=typeof window>"u";function Le(){}function bi(r,t){return typeof r=="function"?r(t):r}function cr(r){return typeof r=="number"&&r>=0&&r!==1/0}function Ds(r){return Array.isArray(r)?r:[r]}function Ha(r,t){return Math.max(r+(t||0)-Date.now(),0)}function js(r,t,s){return hs(r)?typeof t=="function"?de({},s,{queryKey:r,queryFn:t}):de({},t,{queryKey:r}):r}function vi(r,t,s){return hs(r)?typeof t=="function"?de({},s,{mutationKey:r,mutationFn:t}):de({},t,{mutationKey:r}):typeof r=="function"?de({},t,{mutationFn:r}):de({},r)}function kt(r,t,s){return hs(r)?[de({},t,{queryKey:r}),s]:[r||{},t]}function wi(r,t){if(r===!0&&t===!0||r==null&&t==null)return"all";if(r===!1&&t===!1)return"none";var s=r??!t;return s?"active":"inactive"}function ia(r,t){var s=r.active,a=r.exact,n=r.fetching,i=r.inactive,l=r.predicate,o=r.queryKey,c=r.stale;if(hs(o)){if(a){if(t.queryHash!==Br(o,t.options))return!1}else if(!$s(t.queryKey,o))return!1}var h=wi(s,i);if(h==="none")return!1;if(h!=="all"){var d=t.isActive();if(h==="active"&&!d||h==="inactive"&&d)return!1}return!(typeof c=="boolean"&&t.isStale()!==c||typeof n=="boolean"&&t.isFetching()!==n||l&&!l(t))}function oa(r,t){var s=r.exact,a=r.fetching,n=r.predicate,i=r.mutationKey;if(hs(i)){if(!t.options.mutationKey)return!1;if(s){if(It(t.options.mutationKey)!==It(i))return!1}else if(!$s(t.options.mutationKey,i))return!1}return!(typeof a=="boolean"&&t.state.status==="loading"!==a||n&&!n(t))}function Br(r,t){var s=(t==null?void 0:t.queryKeyHashFn)||It;return s(r)}function It(r){var t=Ds(r);return ji(t)}function ji(r){return JSON.stringify(r,function(t,s){return dr(s)?Object.keys(s).sort().reduce(function(a,n){return a[n]=s[n],a},{}):s})}function $s(r,t){return Ka(Ds(r),Ds(t))}function Ka(r,t){return r===t?!0:typeof r!=typeof t?!1:r&&t&&typeof r=="object"&&typeof t=="object"?!Object.keys(t).some(function(s){return!Ka(r[s],t[s])}):!1}function Ls(r,t){if(r===t)return r;var s=Array.isArray(r)&&Array.isArray(t);if(s||dr(r)&&dr(t)){for(var a=s?r.length:Object.keys(r).length,n=s?t:Object.keys(t),i=n.length,l=s?[]:{},o=0,c=0;c<i;c++){var h=s?c:n[c];l[h]=Ls(r[h],t[h]),l[h]===r[h]&&o++}return a===i&&o===a?r:l}return t}function Ni(r,t){if(r&&!t||t&&!r)return!1;for(var s in r)if(r[s]!==t[s])return!1;return!0}function dr(r){if(!la(r))return!1;var t=r.constructor;if(typeof t>"u")return!0;var s=t.prototype;return!(!la(s)||!s.hasOwnProperty("isPrototypeOf"))}function la(r){return Object.prototype.toString.call(r)==="[object Object]"}function hs(r){return typeof r=="string"||Array.isArray(r)}function ki(r){return new Promise(function(t){setTimeout(t,r)})}function ca(r){Promise.resolve().then(r).catch(function(t){return setTimeout(function(){throw t})})}function Ya(){if(typeof AbortController=="function")return new AbortController}var Si=function(r){Bt(t,r);function t(){var a;return a=r.call(this)||this,a.setup=function(n){var i;if(!Vs&&((i=window)!=null&&i.addEventListener)){var l=function(){return n()};return window.addEventListener("visibilitychange",l,!1),window.addEventListener("focus",l,!1),function(){window.removeEventListener("visibilitychange",l),window.removeEventListener("focus",l)}}},a}var s=t.prototype;return s.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},s.onUnsubscribe=function(){if(!this.hasListeners()){var n;(n=this.cleanup)==null||n.call(this),this.cleanup=void 0}},s.setEventListener=function(n){var i,l=this;this.setup=n,(i=this.cleanup)==null||i.call(this),this.cleanup=n(function(o){typeof o=="boolean"?l.setFocused(o):l.onFocus()})},s.setFocused=function(n){this.focused=n,n&&this.onFocus()},s.onFocus=function(){this.listeners.forEach(function(n){n()})},s.isFocused=function(){return typeof this.focused=="boolean"?this.focused:typeof document>"u"?!0:[void 0,"visible","prerender"].includes(document.visibilityState)},t}(Qt),Zt=new Si,Ci=function(r){Bt(t,r);function t(){var a;return a=r.call(this)||this,a.setup=function(n){var i;if(!Vs&&((i=window)!=null&&i.addEventListener)){var l=function(){return n()};return window.addEventListener("online",l,!1),window.addEventListener("offline",l,!1),function(){window.removeEventListener("online",l),window.removeEventListener("offline",l)}}},a}var s=t.prototype;return s.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},s.onUnsubscribe=function(){if(!this.hasListeners()){var n;(n=this.cleanup)==null||n.call(this),this.cleanup=void 0}},s.setEventListener=function(n){var i,l=this;this.setup=n,(i=this.cleanup)==null||i.call(this),this.cleanup=n(function(o){typeof o=="boolean"?l.setOnline(o):l.onOnline()})},s.setOnline=function(n){this.online=n,n&&this.onOnline()},s.onOnline=function(){this.listeners.forEach(function(n){n()})},s.isOnline=function(){return typeof this.online=="boolean"?this.online:typeof navigator>"u"||typeof navigator.onLine>"u"?!0:navigator.onLine},t}(Qt),Ns=new Ci;function Ei(r){return Math.min(1e3*Math.pow(2,r),3e4)}function Fs(r){return typeof(r==null?void 0:r.cancel)=="function"}var Ja=function(t){this.revert=t==null?void 0:t.revert,this.silent=t==null?void 0:t.silent};function ks(r){return r instanceof Ja}var Ga=function(t){var s=this,a=!1,n,i,l,o;this.abort=t.abort,this.cancel=function(p){return n==null?void 0:n(p)},this.cancelRetry=function(){a=!0},this.continueRetry=function(){a=!1},this.continue=function(){return i==null?void 0:i()},this.failureCount=0,this.isPaused=!1,this.isResolved=!1,this.isTransportCancelable=!1,this.promise=new Promise(function(p,m){l=p,o=m});var c=function(m){s.isResolved||(s.isResolved=!0,t.onSuccess==null||t.onSuccess(m),i==null||i(),l(m))},h=function(m){s.isResolved||(s.isResolved=!0,t.onError==null||t.onError(m),i==null||i(),o(m))},d=function(){return new Promise(function(m){i=m,s.isPaused=!0,t.onPause==null||t.onPause()}).then(function(){i=void 0,s.isPaused=!1,t.onContinue==null||t.onContinue()})},x=function p(){if(!s.isResolved){var m;try{m=t.fn()}catch(f){m=Promise.reject(f)}n=function(y){if(!s.isResolved&&(h(new Ja(y)),s.abort==null||s.abort(),Fs(m)))try{m.cancel()}catch{}},s.isTransportCancelable=Fs(m),Promise.resolve(m).then(c).catch(function(f){var y,v;if(!s.isResolved){var g=(y=t.retry)!=null?y:3,k=(v=t.retryDelay)!=null?v:Ei,T=typeof k=="function"?k(s.failureCount,f):k,ee=g===!0||typeof g=="number"&&s.failureCount<g||typeof g=="function"&&g(s.failureCount,f);if(a||!ee){h(f);return}s.failureCount++,t.onFail==null||t.onFail(s.failureCount,f),ki(T).then(function(){if(!Zt.isFocused()||!Ns.isOnline())return d()}).then(function(){a?h(f):p()})}})}};x()},Ri=function(){function r(){this.queue=[],this.transactions=0,this.notifyFn=function(s){s()},this.batchNotifyFn=function(s){s()}}var t=r.prototype;return t.batch=function(a){var n;this.transactions++;try{n=a()}finally{this.transactions--,this.transactions||this.flush()}return n},t.schedule=function(a){var n=this;this.transactions?this.queue.push(a):ca(function(){n.notifyFn(a)})},t.batchCalls=function(a){var n=this;return function(){for(var i=arguments.length,l=new Array(i),o=0;o<i;o++)l[o]=arguments[o];n.schedule(function(){a.apply(void 0,l)})}},t.flush=function(){var a=this,n=this.queue;this.queue=[],n.length&&ca(function(){a.batchNotifyFn(function(){n.forEach(function(i){a.notifyFn(i)})})})},t.setNotifyFunction=function(a){this.notifyFn=a},t.setBatchNotifyFunction=function(a){this.batchNotifyFn=a},r}(),Pe=new Ri,Xa=console;function Us(){return Xa}function Pi(r){Xa=r}var Ii=function(){function r(s){this.abortSignalConsumed=!1,this.hadObservers=!1,this.defaultOptions=s.defaultOptions,this.setOptions(s.options),this.observers=[],this.cache=s.cache,this.queryKey=s.queryKey,this.queryHash=s.queryHash,this.initialState=s.state||this.getDefaultState(this.options),this.state=this.initialState,this.meta=s.meta,this.scheduleGc()}var t=r.prototype;return t.setOptions=function(a){var n;this.options=de({},this.defaultOptions,a),this.meta=a==null?void 0:a.meta,this.cacheTime=Math.max(this.cacheTime||0,(n=this.options.cacheTime)!=null?n:5*60*1e3)},t.setDefaultOptions=function(a){this.defaultOptions=a},t.scheduleGc=function(){var a=this;this.clearGcTimeout(),cr(this.cacheTime)&&(this.gcTimeout=setTimeout(function(){a.optionalRemove()},this.cacheTime))},t.clearGcTimeout=function(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)},t.optionalRemove=function(){this.observers.length||(this.state.isFetching?this.hadObservers&&this.scheduleGc():this.cache.remove(this))},t.setData=function(a,n){var i,l,o=this.state.data,c=bi(a,o);return(i=(l=this.options).isDataEqual)!=null&&i.call(l,o,c)?c=o:this.options.structuralSharing!==!1&&(c=Ls(o,c)),this.dispatch({data:c,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt}),c},t.setState=function(a,n){this.dispatch({type:"setState",state:a,setStateOptions:n})},t.cancel=function(a){var n,i=this.promise;return(n=this.retryer)==null||n.cancel(a),i?i.then(Le).catch(Le):Promise.resolve()},t.destroy=function(){this.clearGcTimeout(),this.cancel({silent:!0})},t.reset=function(){this.destroy(),this.setState(this.initialState)},t.isActive=function(){return this.observers.some(function(a){return a.options.enabled!==!1})},t.isFetching=function(){return this.state.isFetching},t.isStale=function(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some(function(a){return a.getCurrentResult().isStale})},t.isStaleByTime=function(a){return a===void 0&&(a=0),this.state.isInvalidated||!this.state.dataUpdatedAt||!Ha(this.state.dataUpdatedAt,a)},t.onFocus=function(){var a,n=this.observers.find(function(i){return i.shouldFetchOnWindowFocus()});n&&n.refetch(),(a=this.retryer)==null||a.continue()},t.onOnline=function(){var a,n=this.observers.find(function(i){return i.shouldFetchOnReconnect()});n&&n.refetch(),(a=this.retryer)==null||a.continue()},t.addObserver=function(a){this.observers.indexOf(a)===-1&&(this.observers.push(a),this.hadObservers=!0,this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:a}))},t.removeObserver=function(a){this.observers.indexOf(a)!==-1&&(this.observers=this.observers.filter(function(n){return n!==a}),this.observers.length||(this.retryer&&(this.retryer.isTransportCancelable||this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.cacheTime?this.scheduleGc():this.cache.remove(this)),this.cache.notify({type:"observerRemoved",query:this,observer:a}))},t.getObserversCount=function(){return this.observers.length},t.invalidate=function(){this.state.isInvalidated||this.dispatch({type:"invalidate"})},t.fetch=function(a,n){var i=this,l,o,c;if(this.state.isFetching){if(this.state.dataUpdatedAt&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(this.promise){var h;return(h=this.retryer)==null||h.continueRetry(),this.promise}}if(a&&this.setOptions(a),!this.options.queryFn){var d=this.observers.find(function(k){return k.options.queryFn});d&&this.setOptions(d.options)}var x=Ds(this.queryKey),p=Ya(),m={queryKey:x,pageParam:void 0,meta:this.meta};Object.defineProperty(m,"signal",{enumerable:!0,get:function(){if(p)return i.abortSignalConsumed=!0,p.signal}});var f=function(){return i.options.queryFn?(i.abortSignalConsumed=!1,i.options.queryFn(m)):Promise.reject("Missing queryFn")},y={fetchOptions:n,options:this.options,queryKey:x,state:this.state,fetchFn:f,meta:this.meta};if((l=this.options.behavior)!=null&&l.onFetch){var v;(v=this.options.behavior)==null||v.onFetch(y)}if(this.revertState=this.state,!this.state.isFetching||this.state.fetchMeta!==((o=y.fetchOptions)==null?void 0:o.meta)){var g;this.dispatch({type:"fetch",meta:(g=y.fetchOptions)==null?void 0:g.meta})}return this.retryer=new Ga({fn:y.fetchFn,abort:p==null||(c=p.abort)==null?void 0:c.bind(p),onSuccess:function(T){i.setData(T),i.cache.config.onSuccess==null||i.cache.config.onSuccess(T,i),i.cacheTime===0&&i.optionalRemove()},onError:function(T){ks(T)&&T.silent||i.dispatch({type:"error",error:T}),ks(T)||(i.cache.config.onError==null||i.cache.config.onError(T,i),Us().error(T)),i.cacheTime===0&&i.optionalRemove()},onFail:function(){i.dispatch({type:"failed"})},onPause:function(){i.dispatch({type:"pause"})},onContinue:function(){i.dispatch({type:"continue"})},retry:y.options.retry,retryDelay:y.options.retryDelay}),this.promise=this.retryer.promise,this.promise},t.dispatch=function(a){var n=this;this.state=this.reducer(this.state,a),Pe.batch(function(){n.observers.forEach(function(i){i.onQueryUpdate(a)}),n.cache.notify({query:n,type:"queryUpdated",action:a})})},t.getDefaultState=function(a){var n=typeof a.initialData=="function"?a.initialData():a.initialData,i=typeof a.initialData<"u",l=i?typeof a.initialDataUpdatedAt=="function"?a.initialDataUpdatedAt():a.initialDataUpdatedAt:0,o=typeof n<"u";return{data:n,dataUpdateCount:0,dataUpdatedAt:o?l??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchMeta:null,isFetching:!1,isInvalidated:!1,isPaused:!1,status:o?"success":"idle"}},t.reducer=function(a,n){var i,l;switch(n.type){case"failed":return de({},a,{fetchFailureCount:a.fetchFailureCount+1});case"pause":return de({},a,{isPaused:!0});case"continue":return de({},a,{isPaused:!1});case"fetch":return de({},a,{fetchFailureCount:0,fetchMeta:(i=n.meta)!=null?i:null,isFetching:!0,isPaused:!1},!a.dataUpdatedAt&&{error:null,status:"loading"});case"success":return de({},a,{data:n.data,dataUpdateCount:a.dataUpdateCount+1,dataUpdatedAt:(l=n.dataUpdatedAt)!=null?l:Date.now(),error:null,fetchFailureCount:0,isFetching:!1,isInvalidated:!1,isPaused:!1,status:"success"});case"error":var o=n.error;return ks(o)&&o.revert&&this.revertState?de({},this.revertState):de({},a,{error:o,errorUpdateCount:a.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:a.fetchFailureCount+1,isFetching:!1,isPaused:!1,status:"error"});case"invalidate":return de({},a,{isInvalidated:!0});case"setState":return de({},a,n.state);default:return a}},r}(),Ai=function(r){Bt(t,r);function t(a){var n;return n=r.call(this)||this,n.config=a||{},n.queries=[],n.queriesMap={},n}var s=t.prototype;return s.build=function(n,i,l){var o,c=i.queryKey,h=(o=i.queryHash)!=null?o:Br(c,i),d=this.get(h);return d||(d=new Ii({cache:this,queryKey:c,queryHash:h,options:n.defaultQueryOptions(i),state:l,defaultOptions:n.getQueryDefaults(c),meta:i.meta}),this.add(d)),d},s.add=function(n){this.queriesMap[n.queryHash]||(this.queriesMap[n.queryHash]=n,this.queries.push(n),this.notify({type:"queryAdded",query:n}))},s.remove=function(n){var i=this.queriesMap[n.queryHash];i&&(n.destroy(),this.queries=this.queries.filter(function(l){return l!==n}),i===n&&delete this.queriesMap[n.queryHash],this.notify({type:"queryRemoved",query:n}))},s.clear=function(){var n=this;Pe.batch(function(){n.queries.forEach(function(i){n.remove(i)})})},s.get=function(n){return this.queriesMap[n]},s.getAll=function(){return this.queries},s.find=function(n,i){var l=kt(n,i),o=l[0];return typeof o.exact>"u"&&(o.exact=!0),this.queries.find(function(c){return ia(o,c)})},s.findAll=function(n,i){var l=kt(n,i),o=l[0];return Object.keys(o).length>0?this.queries.filter(function(c){return ia(o,c)}):this.queries},s.notify=function(n){var i=this;Pe.batch(function(){i.listeners.forEach(function(l){l(n)})})},s.onFocus=function(){var n=this;Pe.batch(function(){n.queries.forEach(function(i){i.onFocus()})})},s.onOnline=function(){var n=this;Pe.batch(function(){n.queries.forEach(function(i){i.onOnline()})})},t}(Qt),_i=function(){function r(s){this.options=de({},s.defaultOptions,s.options),this.mutationId=s.mutationId,this.mutationCache=s.mutationCache,this.observers=[],this.state=s.state||Za(),this.meta=s.meta}var t=r.prototype;return t.setState=function(a){this.dispatch({type:"setState",state:a})},t.addObserver=function(a){this.observers.indexOf(a)===-1&&this.observers.push(a)},t.removeObserver=function(a){this.observers=this.observers.filter(function(n){return n!==a})},t.cancel=function(){return this.retryer?(this.retryer.cancel(),this.retryer.promise.then(Le).catch(Le)):Promise.resolve()},t.continue=function(){return this.retryer?(this.retryer.continue(),this.retryer.promise):this.execute()},t.execute=function(){var a=this,n,i=this.state.status==="loading",l=Promise.resolve();return i||(this.dispatch({type:"loading",variables:this.options.variables}),l=l.then(function(){a.mutationCache.config.onMutate==null||a.mutationCache.config.onMutate(a.state.variables,a)}).then(function(){return a.options.onMutate==null?void 0:a.options.onMutate(a.state.variables)}).then(function(o){o!==a.state.context&&a.dispatch({type:"loading",context:o,variables:a.state.variables})})),l.then(function(){return a.executeMutation()}).then(function(o){n=o,a.mutationCache.config.onSuccess==null||a.mutationCache.config.onSuccess(n,a.state.variables,a.state.context,a)}).then(function(){return a.options.onSuccess==null?void 0:a.options.onSuccess(n,a.state.variables,a.state.context)}).then(function(){return a.options.onSettled==null?void 0:a.options.onSettled(n,null,a.state.variables,a.state.context)}).then(function(){return a.dispatch({type:"success",data:n}),n}).catch(function(o){return a.mutationCache.config.onError==null||a.mutationCache.config.onError(o,a.state.variables,a.state.context,a),Us().error(o),Promise.resolve().then(function(){return a.options.onError==null?void 0:a.options.onError(o,a.state.variables,a.state.context)}).then(function(){return a.options.onSettled==null?void 0:a.options.onSettled(void 0,o,a.state.variables,a.state.context)}).then(function(){throw a.dispatch({type:"error",error:o}),o})})},t.executeMutation=function(){var a=this,n;return this.retryer=new Ga({fn:function(){return a.options.mutationFn?a.options.mutationFn(a.state.variables):Promise.reject("No mutationFn found")},onFail:function(){a.dispatch({type:"failed"})},onPause:function(){a.dispatch({type:"pause"})},onContinue:function(){a.dispatch({type:"continue"})},retry:(n=this.options.retry)!=null?n:0,retryDelay:this.options.retryDelay}),this.retryer.promise},t.dispatch=function(a){var n=this;this.state=Ti(this.state,a),Pe.batch(function(){n.observers.forEach(function(i){i.onMutationUpdate(a)}),n.mutationCache.notify(n)})},r}();function Za(){return{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0}}function Ti(r,t){switch(t.type){case"failed":return de({},r,{failureCount:r.failureCount+1});case"pause":return de({},r,{isPaused:!0});case"continue":return de({},r,{isPaused:!1});case"loading":return de({},r,{context:t.context,data:void 0,error:null,isPaused:!1,status:"loading",variables:t.variables});case"success":return de({},r,{data:t.data,error:null,status:"success",isPaused:!1});case"error":return de({},r,{data:void 0,error:t.error,failureCount:r.failureCount+1,isPaused:!1,status:"error"});case"setState":return de({},r,t.state);default:return r}}var Oi=function(r){Bt(t,r);function t(a){var n;return n=r.call(this)||this,n.config=a||{},n.mutations=[],n.mutationId=0,n}var s=t.prototype;return s.build=function(n,i,l){var o=new _i({mutationCache:this,mutationId:++this.mutationId,options:n.defaultMutationOptions(i),state:l,defaultOptions:i.mutationKey?n.getMutationDefaults(i.mutationKey):void 0,meta:i.meta});return this.add(o),o},s.add=function(n){this.mutations.push(n),this.notify(n)},s.remove=function(n){this.mutations=this.mutations.filter(function(i){return i!==n}),n.cancel(),this.notify(n)},s.clear=function(){var n=this;Pe.batch(function(){n.mutations.forEach(function(i){n.remove(i)})})},s.getAll=function(){return this.mutations},s.find=function(n){return typeof n.exact>"u"&&(n.exact=!0),this.mutations.find(function(i){return oa(n,i)})},s.findAll=function(n){return this.mutations.filter(function(i){return oa(n,i)})},s.notify=function(n){var i=this;Pe.batch(function(){i.listeners.forEach(function(l){l(n)})})},s.onFocus=function(){this.resumePausedMutations()},s.onOnline=function(){this.resumePausedMutations()},s.resumePausedMutations=function(){var n=this.mutations.filter(function(i){return i.state.isPaused});return Pe.batch(function(){return n.reduce(function(i,l){return i.then(function(){return l.continue().catch(Le)})},Promise.resolve())})},t}(Qt);function Vi(){return{onFetch:function(t){t.fetchFn=function(){var s,a,n,i,l,o,c=(s=t.fetchOptions)==null||(a=s.meta)==null?void 0:a.refetchPage,h=(n=t.fetchOptions)==null||(i=n.meta)==null?void 0:i.fetchMore,d=h==null?void 0:h.pageParam,x=(h==null?void 0:h.direction)==="forward",p=(h==null?void 0:h.direction)==="backward",m=((l=t.state.data)==null?void 0:l.pages)||[],f=((o=t.state.data)==null?void 0:o.pageParams)||[],y=Ya(),v=y==null?void 0:y.signal,g=f,k=!1,T=t.options.queryFn||function(){return Promise.reject("Missing queryFn")},ee=function(V,J,Q,z){return g=z?[J].concat(g):[].concat(g,[J]),z?[Q].concat(V):[].concat(V,[Q])},se=function(V,J,Q,z){if(k)return Promise.reject("Cancelled");if(typeof Q>"u"&&!J&&V.length)return Promise.resolve(V);var L={queryKey:t.queryKey,signal:v,pageParam:Q,meta:t.meta},re=T(L),P=Promise.resolve(re).then(function(ue){return ee(V,Q,ue,z)});if(Fs(re)){var X=P;X.cancel=re.cancel}return P},te;if(!m.length)te=se([]);else if(x){var j=typeof d<"u",$=j?d:da(t.options,m);te=se(m,j,$)}else if(p){var A=typeof d<"u",O=A?d:Di(t.options,m);te=se(m,A,O,!0)}else(function(){g=[];var S=typeof t.options.getNextPageParam>"u",V=c&&m[0]?c(m[0],0,m):!0;te=V?se([],S,f[0]):Promise.resolve(ee([],f[0],m[0]));for(var J=function(L){te=te.then(function(re){var P=c&&m[L]?c(m[L],L,m):!0;if(P){var X=S?f[L]:da(t.options,re);return se(re,S,X)}return Promise.resolve(ee(re,f[L],m[L]))})},Q=1;Q<m.length;Q++)J(Q)})();var N=te.then(function(S){return{pages:S,pageParams:g}}),B=N;return B.cancel=function(){k=!0,y==null||y.abort(),Fs(te)&&te.cancel()},N}}}}function da(r,t){return r.getNextPageParam==null?void 0:r.getNextPageParam(t[t.length-1],t)}function Di(r,t){return r.getPreviousPageParam==null?void 0:r.getPreviousPageParam(t[0],t)}var $i=function(){function r(s){s===void 0&&(s={}),this.queryCache=s.queryCache||new Ai,this.mutationCache=s.mutationCache||new Oi,this.defaultOptions=s.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[]}var t=r.prototype;return t.mount=function(){var a=this;this.unsubscribeFocus=Zt.subscribe(function(){Zt.isFocused()&&Ns.isOnline()&&(a.mutationCache.onFocus(),a.queryCache.onFocus())}),this.unsubscribeOnline=Ns.subscribe(function(){Zt.isFocused()&&Ns.isOnline()&&(a.mutationCache.onOnline(),a.queryCache.onOnline())})},t.unmount=function(){var a,n;(a=this.unsubscribeFocus)==null||a.call(this),(n=this.unsubscribeOnline)==null||n.call(this)},t.isFetching=function(a,n){var i=kt(a,n),l=i[0];return l.fetching=!0,this.queryCache.findAll(l).length},t.isMutating=function(a){return this.mutationCache.findAll(de({},a,{fetching:!0})).length},t.getQueryData=function(a,n){var i;return(i=this.queryCache.find(a,n))==null?void 0:i.state.data},t.getQueriesData=function(a){return this.getQueryCache().findAll(a).map(function(n){var i=n.queryKey,l=n.state,o=l.data;return[i,o]})},t.setQueryData=function(a,n,i){var l=js(a),o=this.defaultQueryOptions(l);return this.queryCache.build(this,o).setData(n,i)},t.setQueriesData=function(a,n,i){var l=this;return Pe.batch(function(){return l.getQueryCache().findAll(a).map(function(o){var c=o.queryKey;return[c,l.setQueryData(c,n,i)]})})},t.getQueryState=function(a,n){var i;return(i=this.queryCache.find(a,n))==null?void 0:i.state},t.removeQueries=function(a,n){var i=kt(a,n),l=i[0],o=this.queryCache;Pe.batch(function(){o.findAll(l).forEach(function(c){o.remove(c)})})},t.resetQueries=function(a,n,i){var l=this,o=kt(a,n,i),c=o[0],h=o[1],d=this.queryCache,x=de({},c,{active:!0});return Pe.batch(function(){return d.findAll(c).forEach(function(p){p.reset()}),l.refetchQueries(x,h)})},t.cancelQueries=function(a,n,i){var l=this,o=kt(a,n,i),c=o[0],h=o[1],d=h===void 0?{}:h;typeof d.revert>"u"&&(d.revert=!0);var x=Pe.batch(function(){return l.queryCache.findAll(c).map(function(p){return p.cancel(d)})});return Promise.all(x).then(Le).catch(Le)},t.invalidateQueries=function(a,n,i){var l,o,c,h=this,d=kt(a,n,i),x=d[0],p=d[1],m=de({},x,{active:(l=(o=x.refetchActive)!=null?o:x.active)!=null?l:!0,inactive:(c=x.refetchInactive)!=null?c:!1});return Pe.batch(function(){return h.queryCache.findAll(x).forEach(function(f){f.invalidate()}),h.refetchQueries(m,p)})},t.refetchQueries=function(a,n,i){var l=this,o=kt(a,n,i),c=o[0],h=o[1],d=Pe.batch(function(){return l.queryCache.findAll(c).map(function(p){return p.fetch(void 0,de({},h,{meta:{refetchPage:c==null?void 0:c.refetchPage}}))})}),x=Promise.all(d).then(Le);return h!=null&&h.throwOnError||(x=x.catch(Le)),x},t.fetchQuery=function(a,n,i){var l=js(a,n,i),o=this.defaultQueryOptions(l);typeof o.retry>"u"&&(o.retry=!1);var c=this.queryCache.build(this,o);return c.isStaleByTime(o.staleTime)?c.fetch(o):Promise.resolve(c.state.data)},t.prefetchQuery=function(a,n,i){return this.fetchQuery(a,n,i).then(Le).catch(Le)},t.fetchInfiniteQuery=function(a,n,i){var l=js(a,n,i);return l.behavior=Vi(),this.fetchQuery(l)},t.prefetchInfiniteQuery=function(a,n,i){return this.fetchInfiniteQuery(a,n,i).then(Le).catch(Le)},t.cancelMutations=function(){var a=this,n=Pe.batch(function(){return a.mutationCache.getAll().map(function(i){return i.cancel()})});return Promise.all(n).then(Le).catch(Le)},t.resumePausedMutations=function(){return this.getMutationCache().resumePausedMutations()},t.executeMutation=function(a){return this.mutationCache.build(this,a).execute()},t.getQueryCache=function(){return this.queryCache},t.getMutationCache=function(){return this.mutationCache},t.getDefaultOptions=function(){return this.defaultOptions},t.setDefaultOptions=function(a){this.defaultOptions=a},t.setQueryDefaults=function(a,n){var i=this.queryDefaults.find(function(l){return It(a)===It(l.queryKey)});i?i.defaultOptions=n:this.queryDefaults.push({queryKey:a,defaultOptions:n})},t.getQueryDefaults=function(a){var n;return a?(n=this.queryDefaults.find(function(i){return $s(a,i.queryKey)}))==null?void 0:n.defaultOptions:void 0},t.setMutationDefaults=function(a,n){var i=this.mutationDefaults.find(function(l){return It(a)===It(l.mutationKey)});i?i.defaultOptions=n:this.mutationDefaults.push({mutationKey:a,defaultOptions:n})},t.getMutationDefaults=function(a){var n;return a?(n=this.mutationDefaults.find(function(i){return $s(a,i.mutationKey)}))==null?void 0:n.defaultOptions:void 0},t.defaultQueryOptions=function(a){if(a!=null&&a._defaulted)return a;var n=de({},this.defaultOptions.queries,this.getQueryDefaults(a==null?void 0:a.queryKey),a,{_defaulted:!0});return!n.queryHash&&n.queryKey&&(n.queryHash=Br(n.queryKey,n)),n},t.defaultQueryObserverOptions=function(a){return this.defaultQueryOptions(a)},t.defaultMutationOptions=function(a){return a!=null&&a._defaulted?a:de({},this.defaultOptions.mutations,this.getMutationDefaults(a==null?void 0:a.mutationKey),a,{_defaulted:!0})},t.clear=function(){this.queryCache.clear(),this.mutationCache.clear()},r}(),Li=function(r){Bt(t,r);function t(a,n){var i;return i=r.call(this)||this,i.client=a,i.options=n,i.trackedProps=[],i.selectError=null,i.bindMethods(),i.setOptions(n),i}var s=t.prototype;return s.bindMethods=function(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)},s.onSubscribe=function(){this.listeners.length===1&&(this.currentQuery.addObserver(this),ua(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())},s.onUnsubscribe=function(){this.listeners.length||this.destroy()},s.shouldFetchOnReconnect=function(){return ur(this.currentQuery,this.options,this.options.refetchOnReconnect)},s.shouldFetchOnWindowFocus=function(){return ur(this.currentQuery,this.options,this.options.refetchOnWindowFocus)},s.destroy=function(){this.listeners=[],this.clearTimers(),this.currentQuery.removeObserver(this)},s.setOptions=function(n,i){var l=this.options,o=this.currentQuery;if(this.options=this.client.defaultQueryObserverOptions(n),typeof this.options.enabled<"u"&&typeof this.options.enabled!="boolean")throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=l.queryKey),this.updateQuery();var c=this.hasListeners();c&&ha(this.currentQuery,o,this.options,l)&&this.executeFetch(),this.updateResult(i),c&&(this.currentQuery!==o||this.options.enabled!==l.enabled||this.options.staleTime!==l.staleTime)&&this.updateStaleTimeout();var h=this.computeRefetchInterval();c&&(this.currentQuery!==o||this.options.enabled!==l.enabled||h!==this.currentRefetchInterval)&&this.updateRefetchInterval(h)},s.getOptimisticResult=function(n){var i=this.client.defaultQueryObserverOptions(n),l=this.client.getQueryCache().build(this.client,i);return this.createResult(l,i)},s.getCurrentResult=function(){return this.currentResult},s.trackResult=function(n,i){var l=this,o={},c=function(d){l.trackedProps.includes(d)||l.trackedProps.push(d)};return Object.keys(n).forEach(function(h){Object.defineProperty(o,h,{configurable:!1,enumerable:!0,get:function(){return c(h),n[h]}})}),(i.useErrorBoundary||i.suspense)&&c("error"),o},s.getNextResult=function(n){var i=this;return new Promise(function(l,o){var c=i.subscribe(function(h){h.isFetching||(c(),h.isError&&(n!=null&&n.throwOnError)?o(h.error):l(h))})})},s.getCurrentQuery=function(){return this.currentQuery},s.remove=function(){this.client.getQueryCache().remove(this.currentQuery)},s.refetch=function(n){return this.fetch(de({},n,{meta:{refetchPage:n==null?void 0:n.refetchPage}}))},s.fetchOptimistic=function(n){var i=this,l=this.client.defaultQueryObserverOptions(n),o=this.client.getQueryCache().build(this.client,l);return o.fetch().then(function(){return i.createResult(o,l)})},s.fetch=function(n){var i=this;return this.executeFetch(n).then(function(){return i.updateResult(),i.currentResult})},s.executeFetch=function(n){this.updateQuery();var i=this.currentQuery.fetch(this.options,n);return n!=null&&n.throwOnError||(i=i.catch(Le)),i},s.updateStaleTimeout=function(){var n=this;if(this.clearStaleTimeout(),!(Vs||this.currentResult.isStale||!cr(this.options.staleTime))){var i=Ha(this.currentResult.dataUpdatedAt,this.options.staleTime),l=i+1;this.staleTimeoutId=setTimeout(function(){n.currentResult.isStale||n.updateResult()},l)}},s.computeRefetchInterval=function(){var n;return typeof this.options.refetchInterval=="function"?this.options.refetchInterval(this.currentResult.data,this.currentQuery):(n=this.options.refetchInterval)!=null?n:!1},s.updateRefetchInterval=function(n){var i=this;this.clearRefetchInterval(),this.currentRefetchInterval=n,!(Vs||this.options.enabled===!1||!cr(this.currentRefetchInterval)||this.currentRefetchInterval===0)&&(this.refetchIntervalId=setInterval(function(){(i.options.refetchIntervalInBackground||Zt.isFocused())&&i.executeFetch()},this.currentRefetchInterval))},s.updateTimers=function(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())},s.clearTimers=function(){this.clearStaleTimeout(),this.clearRefetchInterval()},s.clearStaleTimeout=function(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)},s.clearRefetchInterval=function(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)},s.createResult=function(n,i){var l=this.currentQuery,o=this.options,c=this.currentResult,h=this.currentResultState,d=this.currentResultOptions,x=n!==l,p=x?n.state:this.currentQueryInitialState,m=x?this.currentResult:this.previousQueryResult,f=n.state,y=f.dataUpdatedAt,v=f.error,g=f.errorUpdatedAt,k=f.isFetching,T=f.status,ee=!1,se=!1,te;if(i.optimisticResults){var j=this.hasListeners(),$=!j&&ua(n,i),A=j&&ha(n,l,i,o);($||A)&&(k=!0,y||(T="loading"))}if(i.keepPreviousData&&!f.dataUpdateCount&&(m!=null&&m.isSuccess)&&T!=="error")te=m.data,y=m.dataUpdatedAt,T=m.status,ee=!0;else if(i.select&&typeof f.data<"u")if(c&&f.data===(h==null?void 0:h.data)&&i.select===this.selectFn)te=this.selectResult;else try{this.selectFn=i.select,te=i.select(f.data),i.structuralSharing!==!1&&(te=Ls(c==null?void 0:c.data,te)),this.selectResult=te,this.selectError=null}catch(B){Us().error(B),this.selectError=B}else te=f.data;if(typeof i.placeholderData<"u"&&typeof te>"u"&&(T==="loading"||T==="idle")){var O;if(c!=null&&c.isPlaceholderData&&i.placeholderData===(d==null?void 0:d.placeholderData))O=c.data;else if(O=typeof i.placeholderData=="function"?i.placeholderData():i.placeholderData,i.select&&typeof O<"u")try{O=i.select(O),i.structuralSharing!==!1&&(O=Ls(c==null?void 0:c.data,O)),this.selectError=null}catch(B){Us().error(B),this.selectError=B}typeof O<"u"&&(T="success",te=O,se=!0)}this.selectError&&(v=this.selectError,te=this.selectResult,g=Date.now(),T="error");var N={status:T,isLoading:T==="loading",isSuccess:T==="success",isError:T==="error",isIdle:T==="idle",data:te,dataUpdatedAt:y,error:v,errorUpdatedAt:g,failureCount:f.fetchFailureCount,errorUpdateCount:f.errorUpdateCount,isFetched:f.dataUpdateCount>0||f.errorUpdateCount>0,isFetchedAfterMount:f.dataUpdateCount>p.dataUpdateCount||f.errorUpdateCount>p.errorUpdateCount,isFetching:k,isRefetching:k&&T!=="loading",isLoadingError:T==="error"&&f.dataUpdatedAt===0,isPlaceholderData:se,isPreviousData:ee,isRefetchError:T==="error"&&f.dataUpdatedAt!==0,isStale:Qr(n,i),refetch:this.refetch,remove:this.remove};return N},s.shouldNotifyListeners=function(n,i){if(!i)return!0;var l=this.options,o=l.notifyOnChangeProps,c=l.notifyOnChangePropsExclusions;if(!o&&!c||o==="tracked"&&!this.trackedProps.length)return!0;var h=o==="tracked"?this.trackedProps:o;return Object.keys(n).some(function(d){var x=d,p=n[x]!==i[x],m=h==null?void 0:h.some(function(y){return y===d}),f=c==null?void 0:c.some(function(y){return y===d});return p&&!f&&(!h||m)})},s.updateResult=function(n){var i=this.currentResult;if(this.currentResult=this.createResult(this.currentQuery,this.options),this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,!Ni(this.currentResult,i)){var l={cache:!0};(n==null?void 0:n.listeners)!==!1&&this.shouldNotifyListeners(this.currentResult,i)&&(l.listeners=!0),this.notify(de({},l,n))}},s.updateQuery=function(){var n=this.client.getQueryCache().build(this.client,this.options);if(n!==this.currentQuery){var i=this.currentQuery;this.currentQuery=n,this.currentQueryInitialState=n.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(i==null||i.removeObserver(this),n.addObserver(this))}},s.onQueryUpdate=function(n){var i={};n.type==="success"?i.onSuccess=!0:n.type==="error"&&!ks(n.error)&&(i.onError=!0),this.updateResult(i),this.hasListeners()&&this.updateTimers()},s.notify=function(n){var i=this;Pe.batch(function(){n.onSuccess?(i.options.onSuccess==null||i.options.onSuccess(i.currentResult.data),i.options.onSettled==null||i.options.onSettled(i.currentResult.data,null)):n.onError&&(i.options.onError==null||i.options.onError(i.currentResult.error),i.options.onSettled==null||i.options.onSettled(void 0,i.currentResult.error)),n.listeners&&i.listeners.forEach(function(l){l(i.currentResult)}),n.cache&&i.client.getQueryCache().notify({query:i.currentQuery,type:"observerResultsUpdated"})})},t}(Qt);function Fi(r,t){return t.enabled!==!1&&!r.state.dataUpdatedAt&&!(r.state.status==="error"&&t.retryOnMount===!1)}function ua(r,t){return Fi(r,t)||r.state.dataUpdatedAt>0&&ur(r,t,t.refetchOnMount)}function ur(r,t,s){if(t.enabled!==!1){var a=typeof s=="function"?s(r):s;return a==="always"||a!==!1&&Qr(r,t)}return!1}function ha(r,t,s,a){return s.enabled!==!1&&(r!==t||a.enabled===!1)&&(!s.suspense||r.state.status!=="error")&&Qr(r,s)}function Qr(r,t){return r.isStaleByTime(t.staleTime)}var Ui=function(r){Bt(t,r);function t(a,n){var i;return i=r.call(this)||this,i.client=a,i.setOptions(n),i.bindMethods(),i.updateResult(),i}var s=t.prototype;return s.bindMethods=function(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)},s.setOptions=function(n){this.options=this.client.defaultMutationOptions(n)},s.onUnsubscribe=function(){if(!this.listeners.length){var n;(n=this.currentMutation)==null||n.removeObserver(this)}},s.onMutationUpdate=function(n){this.updateResult();var i={listeners:!0};n.type==="success"?i.onSuccess=!0:n.type==="error"&&(i.onError=!0),this.notify(i)},s.getCurrentResult=function(){return this.currentResult},s.reset=function(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})},s.mutate=function(n,i){return this.mutateOptions=i,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,de({},this.options,{variables:typeof n<"u"?n:this.options.variables})),this.currentMutation.addObserver(this),this.currentMutation.execute()},s.updateResult=function(){var n=this.currentMutation?this.currentMutation.state:Za(),i=de({},n,{isLoading:n.status==="loading",isSuccess:n.status==="success",isError:n.status==="error",isIdle:n.status==="idle",mutate:this.mutate,reset:this.reset});this.currentResult=i},s.notify=function(n){var i=this;Pe.batch(function(){i.mutateOptions&&(n.onSuccess?(i.mutateOptions.onSuccess==null||i.mutateOptions.onSuccess(i.currentResult.data,i.currentResult.variables,i.currentResult.context),i.mutateOptions.onSettled==null||i.mutateOptions.onSettled(i.currentResult.data,null,i.currentResult.variables,i.currentResult.context)):n.onError&&(i.mutateOptions.onError==null||i.mutateOptions.onError(i.currentResult.error,i.currentResult.variables,i.currentResult.context),i.mutateOptions.onSettled==null||i.mutateOptions.onSettled(void 0,i.currentResult.error,i.currentResult.variables,i.currentResult.context))),n.listeners&&i.listeners.forEach(function(l){l(i.currentResult)})})},t}(Qt),Mi=Fn.unstable_batchedUpdates;Pe.setBatchNotifyFunction(Mi);var qi=console;Pi(qi);var ma=ne.createContext(void 0),en=ne.createContext(!1);function tn(r){return r&&typeof window<"u"?(window.ReactQueryClientContext||(window.ReactQueryClientContext=ma),window.ReactQueryClientContext):ma}var zr=function(){var t=ne.useContext(tn(ne.useContext(en)));if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},Bi=function(t){var s=t.client,a=t.contextSharing,n=a===void 0?!1:a,i=t.children;ne.useEffect(function(){return s.mount(),function(){s.unmount()}},[s]);var l=tn(n);return ne.createElement(en.Provider,{value:n},ne.createElement(l.Provider,{value:s},i))};function Qi(){var r=!1;return{clearReset:function(){r=!1},reset:function(){r=!0},isReset:function(){return r}}}var zi=ne.createContext(Qi()),Wi=function(){return ne.useContext(zi)};function sn(r,t,s){return typeof t=="function"?t.apply(void 0,s):typeof t=="boolean"?t:!!r}function Hi(r,t,s){var a=ne.useRef(!1),n=ne.useState(0),i=n[1],l=vi(r,t,s),o=zr(),c=ne.useRef();c.current?c.current.setOptions(l):c.current=new Ui(o,l);var h=c.current.getCurrentResult();ne.useEffect(function(){a.current=!0;var x=c.current.subscribe(Pe.batchCalls(function(){a.current&&i(function(p){return p+1})}));return function(){a.current=!1,x()}},[]);var d=ne.useCallback(function(x,p){c.current.mutate(x,p).catch(Le)},[]);if(h.error&&sn(void 0,c.current.options.useErrorBoundary,[h.error]))throw h.error;return de({},h,{mutate:d,mutateAsync:h.mutate})}function Ki(r,t){var s=ne.useRef(!1),a=ne.useState(0),n=a[1],i=zr(),l=Wi(),o=i.defaultQueryObserverOptions(r);o.optimisticResults=!0,o.onError&&(o.onError=Pe.batchCalls(o.onError)),o.onSuccess&&(o.onSuccess=Pe.batchCalls(o.onSuccess)),o.onSettled&&(o.onSettled=Pe.batchCalls(o.onSettled)),o.suspense&&(typeof o.staleTime!="number"&&(o.staleTime=1e3),o.cacheTime===0&&(o.cacheTime=1)),(o.suspense||o.useErrorBoundary)&&(l.isReset()||(o.retryOnMount=!1));var c=ne.useState(function(){return new t(i,o)}),h=c[0],d=h.getOptimisticResult(o);if(ne.useEffect(function(){s.current=!0,l.clearReset();var x=h.subscribe(Pe.batchCalls(function(){s.current&&n(function(p){return p+1})}));return h.updateResult(),function(){s.current=!1,x()}},[l,h]),ne.useEffect(function(){h.setOptions(o,{listeners:!1})},[o,h]),o.suspense&&d.isLoading)throw h.fetchOptimistic(o).then(function(x){var p=x.data;o.onSuccess==null||o.onSuccess(p),o.onSettled==null||o.onSettled(p,null)}).catch(function(x){l.clearReset(),o.onError==null||o.onError(x),o.onSettled==null||o.onSettled(void 0,x)});if(d.isError&&!l.isReset()&&!d.isFetching&&sn(o.suspense,o.useErrorBoundary,[d.error,h.getCurrentQuery()]))throw d.error;return o.notifyOnChangeProps==="tracked"&&(d=h.trackResult(d,o)),d}function Ss(r,t,s){var a=js(r,t,s);return Ki(a,Li)}let Yi={data:""},Ji=r=>typeof window=="object"?((r?r.querySelector("#_goober"):window._goober)||Object.assign((r||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:r||Yi,Gi=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,Xi=/\/\*[^]*?\*\/|  +/g,xa=/\n+/g,St=(r,t)=>{let s="",a="",n="";for(let i in r){let l=r[i];i[0]=="@"?i[1]=="i"?s=i+" "+l+";":a+=i[1]=="f"?St(l,i):i+"{"+St(l,i[1]=="k"?"":t)+"}":typeof l=="object"?a+=St(l,t?t.replace(/([^,])+/g,o=>i.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,c=>/&/.test(c)?c.replace(/&/g,o):o?o+" "+c:c)):i):l!=null&&(i=/^--/.test(i)?i:i.replace(/[A-Z]/g,"-$&").toLowerCase(),n+=St.p?St.p(i,l):i+":"+l+";")}return s+(t&&n?t+"{"+n+"}":n)+a},gt={},rn=r=>{if(typeof r=="object"){let t="";for(let s in r)t+=s+rn(r[s]);return t}return r},Zi=(r,t,s,a,n)=>{let i=rn(r),l=gt[i]||(gt[i]=(c=>{let h=0,d=11;for(;h<c.length;)d=101*d+c.charCodeAt(h++)>>>0;return"go"+d})(i));if(!gt[l]){let c=i!==r?r:(h=>{let d,x,p=[{}];for(;d=Gi.exec(h.replace(Xi,""));)d[4]?p.shift():d[3]?(x=d[3].replace(xa," ").trim(),p.unshift(p[0][x]=p[0][x]||{})):p[0][d[1]]=d[2].replace(xa," ").trim();return p[0]})(r);gt[l]=St(n?{["@keyframes "+l]:c}:c,s?"":"."+l)}let o=s&&gt.g?gt.g:null;return s&&(gt.g=gt[l]),((c,h,d,x)=>{x?h.data=h.data.replace(x,c):h.data.indexOf(c)===-1&&(h.data=d?c+h.data:h.data+c)})(gt[l],t,a,o),l},eo=(r,t,s)=>r.reduce((a,n,i)=>{let l=t[i];if(l&&l.call){let o=l(s),c=o&&o.props&&o.props.className||/^go/.test(o)&&o;l=c?"."+c:o&&typeof o=="object"?o.props?"":St(o,""):o===!1?"":o}return a+n+(l??"")},"");function Ws(r){let t=this||{},s=r.call?r(t.p):r;return Zi(s.unshift?s.raw?eo(s,[].slice.call(arguments,1),t.p):s.reduce((a,n)=>Object.assign(a,n&&n.call?n(t.p):n),{}):s,Ji(t.target),t.g,t.o,t.k)}let an,hr,mr;Ws.bind({g:1});let wt=Ws.bind({k:1});function to(r,t,s,a){St.p=t,an=r,hr=s,mr=a}function Rt(r,t){let s=this||{};return function(){let a=arguments;function n(i,l){let o=Object.assign({},i),c=o.className||n.className;s.p=Object.assign({theme:hr&&hr()},o),s.o=/ *go\d+/.test(c),o.className=Ws.apply(s,a)+(c?" "+c:""),t&&(o.ref=l);let h=r;return r[0]&&(h=o.as||r,delete o.as),mr&&h[0]&&mr(o),an(h,o)}return t?t(n):n}}var so=r=>typeof r=="function",Ms=(r,t)=>so(r)?r(t):r,ro=(()=>{let r=0;return()=>(++r).toString()})(),nn=(()=>{let r;return()=>{if(r===void 0&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");r=!t||t.matches}return r}})(),ao=20,on=(r,t)=>{switch(t.type){case 0:return{...r,toasts:[t.toast,...r.toasts].slice(0,ao)};case 1:return{...r,toasts:r.toasts.map(i=>i.id===t.toast.id?{...i,...t.toast}:i)};case 2:let{toast:s}=t;return on(r,{type:r.toasts.find(i=>i.id===s.id)?1:0,toast:s});case 3:let{toastId:a}=t;return{...r,toasts:r.toasts.map(i=>i.id===a||a===void 0?{...i,dismissed:!0,visible:!1}:i)};case 4:return t.toastId===void 0?{...r,toasts:[]}:{...r,toasts:r.toasts.filter(i=>i.id!==t.toastId)};case 5:return{...r,pausedAt:t.time};case 6:let n=t.time-(r.pausedAt||0);return{...r,pausedAt:void 0,toasts:r.toasts.map(i=>({...i,pauseDuration:i.pauseDuration+n}))}}},Cs=[],At={toasts:[],pausedAt:void 0},Vt=r=>{At=on(At,r),Cs.forEach(t=>{t(At)})},no={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},io=(r={})=>{let[t,s]=u.useState(At),a=u.useRef(At);u.useEffect(()=>(a.current!==At&&s(At),Cs.push(s),()=>{let i=Cs.indexOf(s);i>-1&&Cs.splice(i,1)}),[]);let n=t.toasts.map(i=>{var l,o,c;return{...r,...r[i.type],...i,removeDelay:i.removeDelay||((l=r[i.type])==null?void 0:l.removeDelay)||(r==null?void 0:r.removeDelay),duration:i.duration||((o=r[i.type])==null?void 0:o.duration)||(r==null?void 0:r.duration)||no[i.type],style:{...r.style,...(c=r[i.type])==null?void 0:c.style,...i.style}}});return{...t,toasts:n}},oo=(r,t="blank",s)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:r,pauseDuration:0,...s,id:(s==null?void 0:s.id)||ro()}),ms=r=>(t,s)=>{let a=oo(t,r,s);return Vt({type:2,toast:a}),a.id},b=(r,t)=>ms("blank")(r,t);b.error=ms("error");b.success=ms("success");b.loading=ms("loading");b.custom=ms("custom");b.dismiss=r=>{Vt({type:3,toastId:r})};b.remove=r=>Vt({type:4,toastId:r});b.promise=(r,t,s)=>{let a=b.loading(t.loading,{...s,...s==null?void 0:s.loading});return typeof r=="function"&&(r=r()),r.then(n=>{let i=t.success?Ms(t.success,n):void 0;return i?b.success(i,{id:a,...s,...s==null?void 0:s.success}):b.dismiss(a),n}).catch(n=>{let i=t.error?Ms(t.error,n):void 0;i?b.error(i,{id:a,...s,...s==null?void 0:s.error}):b.dismiss(a)}),r};var lo=(r,t)=>{Vt({type:1,toast:{id:r,height:t}})},co=()=>{Vt({type:5,time:Date.now()})},es=new Map,uo=1e3,ho=(r,t=uo)=>{if(es.has(r))return;let s=setTimeout(()=>{es.delete(r),Vt({type:4,toastId:r})},t);es.set(r,s)},mo=r=>{let{toasts:t,pausedAt:s}=io(r);u.useEffect(()=>{if(s)return;let i=Date.now(),l=t.map(o=>{if(o.duration===1/0)return;let c=(o.duration||0)+o.pauseDuration-(i-o.createdAt);if(c<0){o.visible&&b.dismiss(o.id);return}return setTimeout(()=>b.dismiss(o.id),c)});return()=>{l.forEach(o=>o&&clearTimeout(o))}},[t,s]);let a=u.useCallback(()=>{s&&Vt({type:6,time:Date.now()})},[s]),n=u.useCallback((i,l)=>{let{reverseOrder:o=!1,gutter:c=8,defaultPosition:h}=l||{},d=t.filter(m=>(m.position||h)===(i.position||h)&&m.height),x=d.findIndex(m=>m.id===i.id),p=d.filter((m,f)=>f<x&&m.visible).length;return d.filter(m=>m.visible).slice(...o?[p+1]:[0,p]).reduce((m,f)=>m+(f.height||0)+c,0)},[t]);return u.useEffect(()=>{t.forEach(i=>{if(i.dismissed)ho(i.id,i.removeDelay);else{let l=es.get(i.id);l&&(clearTimeout(l),es.delete(i.id))}})},[t]),{toasts:t,handlers:{updateHeight:lo,startPause:co,endPause:a,calculateOffset:n}}},xo=wt`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,fo=wt`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,po=wt`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,go=Rt("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${r=>r.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${xo} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${fo} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${r=>r.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${po} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,yo=wt`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,bo=Rt("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${r=>r.secondary||"#e0e0e0"};
  border-right-color: ${r=>r.primary||"#616161"};
  animation: ${yo} 1s linear infinite;
`,vo=wt`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,wo=wt`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,jo=Rt("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${r=>r.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${vo} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${wo} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${r=>r.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,No=Rt("div")`
  position: absolute;
`,ko=Rt("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,So=wt`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Co=Rt("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${So} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,Eo=({toast:r})=>{let{icon:t,type:s,iconTheme:a}=r;return t!==void 0?typeof t=="string"?u.createElement(Co,null,t):t:s==="blank"?null:u.createElement(ko,null,u.createElement(bo,{...a}),s!=="loading"&&u.createElement(No,null,s==="error"?u.createElement(go,{...a}):u.createElement(jo,{...a})))},Ro=r=>`
0% {transform: translate3d(0,${r*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,Po=r=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${r*-150}%,-1px) scale(.6); opacity:0;}
`,Io="0%{opacity:0;} 100%{opacity:1;}",Ao="0%{opacity:1;} 100%{opacity:0;}",_o=Rt("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,To=Rt("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,Oo=(r,t)=>{let s=r.includes("top")?1:-1,[a,n]=nn()?[Io,Ao]:[Ro(s),Po(s)];return{animation:t?`${wt(a)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${wt(n)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},Vo=u.memo(({toast:r,position:t,style:s,children:a})=>{let n=r.height?Oo(r.position||t||"top-center",r.visible):{opacity:0},i=u.createElement(Eo,{toast:r}),l=u.createElement(To,{...r.ariaProps},Ms(r.message,r));return u.createElement(_o,{className:r.className,style:{...n,...s,...r.style}},typeof a=="function"?a({icon:i,message:l}):u.createElement(u.Fragment,null,i,l))});to(u.createElement);var Do=({id:r,className:t,style:s,onHeightUpdate:a,children:n})=>{let i=u.useCallback(l=>{if(l){let o=()=>{let c=l.getBoundingClientRect().height;a(r,c)};o(),new MutationObserver(o).observe(l,{subtree:!0,childList:!0,characterData:!0})}},[r,a]);return u.createElement("div",{ref:i,className:t,style:s},n)},$o=(r,t)=>{let s=r.includes("top"),a=s?{top:0}:{bottom:0},n=r.includes("center")?{justifyContent:"center"}:r.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:nn()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(s?1:-1)}px)`,...a,...n}},Lo=Ws`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,fs=16,ln=({reverseOrder:r,position:t="top-center",toastOptions:s,gutter:a,children:n,containerStyle:i,containerClassName:l})=>{let{toasts:o,handlers:c}=mo(s);return u.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:fs,left:fs,right:fs,bottom:fs,pointerEvents:"none",...i},className:l,onMouseEnter:c.startPause,onMouseLeave:c.endPause},o.map(h=>{let d=h.position||t,x=c.calculateOffset(h,{reverseOrder:r,gutter:a,defaultPosition:t}),p=$o(d,x);return u.createElement(Do,{id:h.id,key:h.id,onHeightUpdate:c.updateHeight,className:h.visible?Lo:"",style:p},h.type==="custom"?Ms(h.message,h):n?n(h):u.createElement(Vo,{toast:h,position:d}))}))},mu=b;const fa=r=>{let t;const s=new Set,a=(d,x)=>{const p=typeof d=="function"?d(t):d;if(!Object.is(p,t)){const m=t;t=x??(typeof p!="object"||p===null)?p:Object.assign({},t,p),s.forEach(f=>f(t,m))}},n=()=>t,c={setState:a,getState:n,getInitialState:()=>h,subscribe:d=>(s.add(d),()=>s.delete(d)),destroy:()=>{s.clear()}},h=t=r(a,n,c);return c},Fo=r=>r?fa(r):fa;var cn={exports:{}},dn={},un={exports:{}},hn={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Mt=u;function Uo(r,t){return r===t&&(r!==0||1/r===1/t)||r!==r&&t!==t}var Mo=typeof Object.is=="function"?Object.is:Uo,qo=Mt.useState,Bo=Mt.useEffect,Qo=Mt.useLayoutEffect,zo=Mt.useDebugValue;function Wo(r,t){var s=t(),a=qo({inst:{value:s,getSnapshot:t}}),n=a[0].inst,i=a[1];return Qo(function(){n.value=s,n.getSnapshot=t,Xs(n)&&i({inst:n})},[r,s,t]),Bo(function(){return Xs(n)&&i({inst:n}),r(function(){Xs(n)&&i({inst:n})})},[r]),zo(s),s}function Xs(r){var t=r.getSnapshot;r=r.value;try{var s=t();return!Mo(r,s)}catch{return!0}}function Ho(r,t){return t()}var Ko=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?Ho:Wo;hn.useSyncExternalStore=Mt.useSyncExternalStore!==void 0?Mt.useSyncExternalStore:Ko;un.exports=hn;var Yo=un.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hs=u,Jo=Yo;function Go(r,t){return r===t&&(r!==0||1/r===1/t)||r!==r&&t!==t}var Xo=typeof Object.is=="function"?Object.is:Go,Zo=Jo.useSyncExternalStore,el=Hs.useRef,tl=Hs.useEffect,sl=Hs.useMemo,rl=Hs.useDebugValue;dn.useSyncExternalStoreWithSelector=function(r,t,s,a,n){var i=el(null);if(i.current===null){var l={hasValue:!1,value:null};i.current=l}else l=i.current;i=sl(function(){function c(m){if(!h){if(h=!0,d=m,m=a(m),n!==void 0&&l.hasValue){var f=l.value;if(n(f,m))return x=f}return x=m}if(f=x,Xo(d,m))return f;var y=a(m);return n!==void 0&&n(f,y)?(d=m,f):(d=m,x=y)}var h=!1,d,x,p=s===void 0?null:s;return[function(){return c(t())},p===null?void 0:function(){return c(p())}]},[t,s,a,n]);var o=Zo(r,i[0],i[1]);return tl(function(){l.hasValue=!0,l.value=o},[o]),rl(o),o};cn.exports=dn;var al=cn.exports;const nl=qs(al),{useDebugValue:il}=ne,{useSyncExternalStoreWithSelector:ol}=nl;const ll=r=>r;function cl(r,t=ll,s){const a=ol(r.subscribe,r.getState,r.getServerState||r.getInitialState,t,s);return il(a),a}const pa=r=>{const t=typeof r=="function"?Fo(r):r,s=(a,n)=>cl(t,a,n);return Object.assign(s,t),s},dl=r=>r?pa(r):pa,xr=new Map,ps=r=>{const t=xr.get(r);return t?Object.fromEntries(Object.entries(t.stores).map(([s,a])=>[s,a.getState()])):{}},ul=(r,t,s)=>{if(r===void 0)return{type:"untracked",connection:t.connect(s)};const a=xr.get(s.name);if(a)return{type:"tracked",store:r,...a};const n={connection:t.connect(s),stores:{}};return xr.set(s.name,n),{type:"tracked",store:r,...n}},hl=(r,t={})=>(s,a,n)=>{const{enabled:i,anonymousActionType:l,store:o,...c}=t;let h;try{h=(i??!1)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch{}if(!h)return r(s,a,n);const{connection:d,...x}=ul(o,h,c);let p=!0;n.setState=(y,v,g)=>{const k=s(y,v);if(!p)return k;const T=g===void 0?{type:l||"anonymous"}:typeof g=="string"?{type:g}:g;return o===void 0?(d==null||d.send(T,a()),k):(d==null||d.send({...T,type:`${o}/${T.type}`},{...ps(c.name),[o]:n.getState()}),k)};const m=(...y)=>{const v=p;p=!1,s(...y),p=v},f=r(n.setState,a,n);if(x.type==="untracked"?d==null||d.init(f):(x.stores[x.store]=n,d==null||d.init(Object.fromEntries(Object.entries(x.stores).map(([y,v])=>[y,y===x.store?f:v.getState()])))),n.dispatchFromDevtools&&typeof n.dispatch=="function"){let y=!1;const v=n.dispatch;n.dispatch=(...g)=>{v(...g)}}return d.subscribe(y=>{var v;switch(y.type){case"ACTION":if(typeof y.payload!="string"){console.error("[zustand devtools middleware] Unsupported action format");return}return Zs(y.payload,g=>{if(g.type==="__setState"){if(o===void 0){m(g.state);return}Object.keys(g.state).length!==1&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format. 
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);const k=g.state[o];if(k==null)return;JSON.stringify(n.getState())!==JSON.stringify(k)&&m(k);return}n.dispatchFromDevtools&&typeof n.dispatch=="function"&&n.dispatch(g)});case"DISPATCH":switch(y.payload.type){case"RESET":return m(f),o===void 0?d==null?void 0:d.init(n.getState()):d==null?void 0:d.init(ps(c.name));case"COMMIT":if(o===void 0){d==null||d.init(n.getState());return}return d==null?void 0:d.init(ps(c.name));case"ROLLBACK":return Zs(y.state,g=>{if(o===void 0){m(g),d==null||d.init(n.getState());return}m(g[o]),d==null||d.init(ps(c.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return Zs(y.state,g=>{if(o===void 0){m(g);return}JSON.stringify(n.getState())!==JSON.stringify(g[o])&&m(g[o])});case"IMPORT_STATE":{const{nextLiftedState:g}=y.payload,k=(v=g.computedStates.slice(-1)[0])==null?void 0:v.state;if(!k)return;m(o===void 0?k:k[o]),d==null||d.send(null,g);return}case"PAUSE_RECORDING":return p=!p}return}}),f},ml=hl,Zs=(r,t)=>{let s;try{s=JSON.parse(r)}catch(a){console.error("[zustand devtools middleware] Could not parse the received json",a)}s!==void 0&&t(s)};function xl(r,t){let s;try{s=r()}catch{return}return{getItem:n=>{var i;const l=c=>c===null?null:JSON.parse(c,t==null?void 0:t.reviver),o=(i=s.getItem(n))!=null?i:null;return o instanceof Promise?o.then(l):l(o)},setItem:(n,i)=>s.setItem(n,JSON.stringify(i,t==null?void 0:t.replacer)),removeItem:n=>s.removeItem(n)}}const as=r=>t=>{try{const s=r(t);return s instanceof Promise?s:{then(a){return as(a)(s)},catch(a){return this}}}catch(s){return{then(a){return this},catch(a){return as(a)(s)}}}},fl=(r,t)=>(s,a,n)=>{let i={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:v=>v,version:0,merge:(v,g)=>({...g,...v}),...t},l=!1;const o=new Set,c=new Set;let h;try{h=i.getStorage()}catch{}if(!h)return r((...v)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),s(...v)},a,n);const d=as(i.serialize),x=()=>{const v=i.partialize({...a()});let g;const k=d({state:v,version:i.version}).then(T=>h.setItem(i.name,T)).catch(T=>{g=T});if(g)throw g;return k},p=n.setState;n.setState=(v,g)=>{p(v,g),x()};const m=r((...v)=>{s(...v),x()},a,n);let f;const y=()=>{var v;if(!h)return;l=!1,o.forEach(k=>k(a()));const g=((v=i.onRehydrateStorage)==null?void 0:v.call(i,a()))||void 0;return as(h.getItem.bind(h))(i.name).then(k=>{if(k)return i.deserialize(k)}).then(k=>{if(k)if(typeof k.version=="number"&&k.version!==i.version){if(i.migrate)return i.migrate(k.state,k.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return k.state}).then(k=>{var T;return f=i.merge(k,(T=a())!=null?T:m),s(f,!0),x()}).then(()=>{g==null||g(f,void 0),l=!0,c.forEach(k=>k(f))}).catch(k=>{g==null||g(void 0,k)})};return n.persist={setOptions:v=>{i={...i,...v},v.getStorage&&(h=v.getStorage())},clearStorage:()=>{h==null||h.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>y(),hasHydrated:()=>l,onHydrate:v=>(o.add(v),()=>{o.delete(v)}),onFinishHydration:v=>(c.add(v),()=>{c.delete(v)})},y(),f||m},pl=(r,t)=>(s,a,n)=>{let i={storage:xl(()=>localStorage),partialize:y=>y,version:0,merge:(y,v)=>({...v,...y}),...t},l=!1;const o=new Set,c=new Set;let h=i.storage;if(!h)return r((...y)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),s(...y)},a,n);const d=()=>{const y=i.partialize({...a()});return h.setItem(i.name,{state:y,version:i.version})},x=n.setState;n.setState=(y,v)=>{x(y,v),d()};const p=r((...y)=>{s(...y),d()},a,n);n.getInitialState=()=>p;let m;const f=()=>{var y,v;if(!h)return;l=!1,o.forEach(k=>{var T;return k((T=a())!=null?T:p)});const g=((v=i.onRehydrateStorage)==null?void 0:v.call(i,(y=a())!=null?y:p))||void 0;return as(h.getItem.bind(h))(i.name).then(k=>{if(k)if(typeof k.version=="number"&&k.version!==i.version){if(i.migrate)return[!0,i.migrate(k.state,k.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,k.state];return[!1,void 0]}).then(k=>{var T;const[ee,se]=k;if(m=i.merge(se,(T=a())!=null?T:p),s(m,!0),ee)return d()}).then(()=>{g==null||g(m,void 0),m=a(),l=!0,c.forEach(k=>k(m))}).catch(k=>{g==null||g(void 0,k)})};return n.persist={setOptions:y=>{i={...i,...y},y.storage&&(h=y.storage)},clearStorage:()=>{h==null||h.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>f(),hasHydrated:()=>l,onHydrate:y=>(o.add(y),()=>{o.delete(y)}),onFinishHydration:y=>(c.add(y),()=>{c.delete(y)})},i.skipHydration||f(),m||p},gl=(r,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?fl(r,t):pl(r,t),yl=gl,ga={user:null,isAuthenticated:!1,authToken:null,currentRestaurant:null,suggestions:[],playQueue:null,currentlyPlaying:null,theme:"auto",deviceType:"desktop",isOnline:navigator.onLine,connectionStatus:"disconnected",notifications:[],settings:{autoRefresh:!0,soundEnabled:!0,showNotifications:!0,language:"pt-BR",maxSuggestionsPerSession:3,notificationPosition:"top-left"},loading:{suggestions:!1,queue:!1,search:!1,voting:!1}},zt=dl()(ml(yl((r,t)=>({...ga,setUser:s=>r(a=>({user:s,isAuthenticated:!!s&&!!a.authToken})),setAuthToken:s=>{r(a=>({authToken:s,isAuthenticated:!!s&&!!a.user})),s?localStorage.setItem("authToken",s):localStorage.removeItem("authToken")},setCurrentRestaurant:s=>r({currentRestaurant:s}),setSuggestions:s=>r({suggestions:s}),addSuggestion:s=>r(a=>({suggestions:[s,...a.suggestions]})),updateSuggestion:(s,a)=>r(n=>({suggestions:n.suggestions.map(i=>i.id===s?{...i,...a}:i)})),removeSuggestion:s=>r(a=>({suggestions:a.suggestions.filter(n=>n.id!==s)})),setPlayQueue:s=>r({playQueue:s}),setCurrentlyPlaying:s=>r({currentlyPlaying:s}),setTheme:s=>{r({theme:s});const a=document.documentElement;s==="dark"?a.classList.add("dark"):s==="light"?a.classList.remove("dark"):window.matchMedia("(prefers-color-scheme: dark)").matches?a.classList.add("dark"):a.classList.remove("dark")},setDeviceType:s=>r({deviceType:s}),setOnlineStatus:s=>r({isOnline:s}),setConnectionStatus:s=>r({connectionStatus:s}),addNotification:s=>{const a=Date.now().toString(),n={...s,id:a,createdAt:new Date().toISOString()};r(i=>({notifications:[n,...i.notifications]})),s.duration&&s.duration>0&&setTimeout(()=>{t().removeNotification(a)},s.duration)},removeNotification:s=>r(a=>({notifications:a.notifications.filter(n=>n.id!==s)})),clearNotifications:()=>r({notifications:[]}),updateSettings:s=>r(a=>({settings:{...a.settings,...s}})),setLoading:(s,a)=>r(n=>({loading:{...n.loading,[s]:a}})),reset:()=>r(()=>({...ga,suggestions:[],notifications:[],isOnline:navigator.onLine}))}),{name:"restaurant-playlist-store",partialize:r=>({user:r.user,isAuthenticated:r.isAuthenticated,authToken:r.authToken,theme:r.theme,settings:r.settings})}),{name:"restaurant-playlist-store"})),xs=()=>{const{user:r,isAuthenticated:t,authToken:s,setUser:a,setAuthToken:n}=zt();return{user:r,isAuthenticated:t,authToken:s,setUser:a,setAuthToken:n}},bl=()=>{const{notifications:r,addNotification:t,removeNotification:s,clearNotifications:a}=zt();return{notifications:r,addNotification:t,removeNotification:s,clearNotifications:a}},mn=()=>{const{settings:r,updateSettings:t}=zt();return{settings:r,updateSettings:t}},vl=()=>{var a;const r=zt.getState(),t=()=>{const n=window.innerWidth;return n<768?"mobile":n<1024?"tablet":"desktop"};r.setDeviceType(t()),r.setTheme(r.theme);try{const n=(a=r.settings)==null?void 0:a.notificationPosition,l=n?{"top-right":"top-left","bottom-right":"bottom-left"}[n]||n:"top-left";l!==n&&r.updateSettings({notificationPosition:l})}catch{}window.addEventListener("online",()=>r.setOnlineStatus(!0)),window.addEventListener("offline",()=>r.setOnlineStatus(!1)),window.addEventListener("resize",()=>{r.setDeviceType(t())}),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",()=>{r.theme==="auto"&&r.setTheme("auto")})};function xn(r){var t,s,a="";if(typeof r=="string"||typeof r=="number")a+=r;else if(typeof r=="object")if(Array.isArray(r)){var n=r.length;for(t=0;t<n;t++)r[t]&&(s=xn(r[t]))&&(a&&(a+=" "),a+=s)}else for(s in r)r[s]&&(a&&(a+=" "),a+=s);return a}function fr(){for(var r,t,s=0,a="",n=arguments.length;s<n;s++)(r=arguments[s])&&(t=xn(r))&&(a&&(a+=" "),a+=t);return a}const tt=({variant:r="primary",size:t="md",loading:s=!1,icon:a,children:n,className:i,disabled:l,...o})=>{const c="inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",h={primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 dark:bg-primary-500 dark:hover:bg-primary-600",secondary:"bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500 dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600",success:"bg-green-600 text-white hover:bg-green-700 focus:ring-green-500",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500",outline:"border border-gray-300 bg-transparent text-gray-700 hover:bg-gray-50 focus:ring-gray-500 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800",ghost:"bg-transparent text-gray-700 hover:bg-gray-100 focus:ring-gray-500 dark:text-gray-300 dark:hover:bg-gray-800"},d={sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"};return e.jsxs("button",{className:fr(c,h[r],d[t],i),disabled:l||s,...o,children:[s&&e.jsxs("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!s&&a&&e.jsx("span",{className:"mr-2",children:a}),n]})},fn={BASE_URL:"http://localhost:8001",API_VERSION:"v1",get API_BASE(){return`${this.BASE_URL}/api/${this.API_VERSION}`},ENDPOINTS:{RESTAURANTS:"/restaurants",SUGGESTIONS:"/suggestions",PLAYBACK:"/playback",ANALYTICS:"/analytics",NOTIFICATIONS:"/notifications",GENRES:"/genres"}},ce=(r,t)=>{try{const s="http://localhost:8001",a=(s&&s.trim()!==""?s:"http://localhost:8001").replace(/\/$/,""),n=new URL(`/api/${fn.API_VERSION}${r}`,a);return t&&Object.entries(t).forEach(([i,l])=>{l&&l.trim()!==""&&n.searchParams.append(i,l)}),n.toString()}catch(s){return console.error("❌ Erro ao construir URL da API:",s,{endpoint:r,params:t}),`/api/v1${r}`}},_t=(r="application/json")=>{const t=typeof localStorage<"u"?localStorage.getItem("authToken"):null,s={};return r&&(s["Content-Type"]=r),t&&(s.Authorization=`Bearer ${t}`),s},wl=({isOpen:r,onClose:t,onLogin:s})=>{const[a,n]=u.useState(""),[i,l]=u.useState(""),[o,c]=u.useState(""),[h,d]=u.useState(!1),[x,p]=u.useState("admin"),[m,f]=u.useState(!1),y=async g=>{if(g.preventDefault(),!a||!i){b.error("Por favor, preencha todos os campos");return}if(x==="restaurant"&&!o){b.error("Por favor, informe o ID do restaurante");return}f(!0);try{const k=await fetch(ce("/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:a,password:i})});if(!k.ok){const ee=await k.json();throw new Error(ee.error||"Erro ao fazer login")}const T=await k.json();localStorage.setItem("authToken",T.token),localStorage.setItem("user",JSON.stringify(T.user)),T.restaurant&&localStorage.setItem("restaurant",JSON.stringify(T.restaurant)),s({email:a,password:i,restaurantId:x==="restaurant"?o:void 0}),b.success("Login realizado com sucesso!"),t(),n(""),l(""),c("")}catch(k){console.error("Erro no login:",k),b.error(k.message||"Erro ao fazer login. Verifique suas credenciais.")}finally{f(!1)}},v=()=>{n("<EMAIL>"),l("admin123"),x==="restaurant"&&c("demo-restaurant")};return r?e.jsx(ze,{children:e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] p-4",children:e.jsxs(q.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:20},transition:{duration:.2},className:"bg-white dark:bg-gray-800 rounded-2xl shadow-2xl w-full max-w-md overflow-hidden",children:[e.jsx("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 p-6 text-white",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"p-2 bg-white/20 rounded-lg",children:e.jsx(ir,{className:"w-6 h-6"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold",children:"Login"}),e.jsx("p",{className:"text-blue-100 text-sm",children:"Acesse sua conta"})]})]}),e.jsx("button",{onClick:t,className:"p-2 hover:bg-white/20 rounded-lg transition-colors",children:e.jsx(bt,{className:"w-5 h-5"})})]})}),e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1 mb-6",children:[e.jsx("button",{type:"button",onClick:()=>p("admin"),className:`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${x==="admin"?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"}`,children:"Admin Geral"}),e.jsx("button",{type:"button",onClick:()=>p("restaurant"),className:`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${x==="restaurant"?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"}`,children:"Restaurante"})]}),e.jsxs("form",{onSubmit:y,className:"space-y-4",children:[x==="restaurant"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"ID do Restaurante"}),e.jsxs("div",{className:"relative",children:[e.jsx(Ot,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{type:"text",value:o,onChange:g=>c(g.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"demo-restaurant"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email"}),e.jsxs("div",{className:"relative",children:[e.jsx(Dr,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{type:"email",value:a,onChange:g=>n(g.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"<EMAIL>",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Senha"}),e.jsxs("div",{className:"relative",children:[e.jsx(Bs,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{type:h?"text":"password",value:i,onChange:g=>l(g.target.value),className:"w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"••••••••",required:!0}),e.jsx("button",{type:"button",onClick:()=>d(!h),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:h?e.jsx($r,{className:"w-5 h-5"}):e.jsx(ds,{className:"w-5 h-5"})})]})]}),e.jsx("button",{type:"button",onClick:v,className:"w-full text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-center py-2",children:"Usar credenciais de demonstração"}),e.jsx(tt,{type:"submit",disabled:m,className:"w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white py-3",children:m?e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),e.jsx("span",{children:"Entrando..."})]}):e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsx(ir,{className:"w-5 h-5"}),e.jsx("span",{children:"Entrar"})]})})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Não tem uma conta?"," ",e.jsx("button",{className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium",children:"Entre em contato"})]})})]})]})})}):null},jl=()=>{const r=cs(),[t,s]=u.useState("");u.useState("");const[a,n]=u.useState(!1),i=()=>{t.trim()&&r(`/restaurant/${t.trim()}`)},l=()=>{r("/restaurant/demo-restaurant")},o=()=>{r("/admin/login")},c=()=>{r("/login")},h=m=>{console.log("Login credentials:",m),m.restaurantId?r(`/restaurant/${m.restaurantId}/admin`):r("/admin")},d=[{icon:ts,title:"QR Code Inteligente",description:"Clientes escaneiam QR codes únicos por mesa e acessam instantaneamente o sistema de sugestões musicais",color:"from-blue-500 to-cyan-500"},{icon:we,title:"Integração YouTube Premium",description:"Acesso completo ao catálogo do YouTube sem anúncios, com suas próprias playlists personalizadas",color:"from-red-500 to-pink-500"},{icon:rt,title:"Experiência Colaborativa",description:"Clientes sugerem e votam em músicas em tempo real, criando uma atmosfera única e envolvente",color:"from-green-500 to-emerald-500"},{icon:vt,title:"Analytics Avançados",description:"Dashboard completo com métricas, relatórios e insights sobre preferências musicais dos clientes",color:"from-purple-500 to-violet-500"}],x=[{icon:De,title:"Aumente o Engajamento",description:"Clientes ficam mais tempo no restaurante quando participam da experiência musical",stats:"+35% tempo de permanência"},{icon:Qs,title:"Melhore a Satisfação",description:"Música personalizada pelos próprios clientes resulta em experiências mais memoráveis",stats:"94% aprovação dos clientes"},{icon:Bn,title:"Reduza Reclamações",description:"Elimine reclamações sobre música inadequada - os clientes escolhem o que querem ouvir",stats:"-80% reclamações sobre música"},{icon:Xr,title:"Diferencial Competitivo",description:"Seja o primeiro restaurante da região com tecnologia interativa de música",stats:"Inovação garantida"}],p=[{step:"1",title:"Cliente Escaneia QR Code",description:"Cada mesa tem um QR code único que direciona para a interface de sugestões",icon:ts},{step:"2",title:"Sugere Músicas",description:"Busca no YouTube ou nas playlists do restaurante e sugere suas favoritas",icon:_s},{step:"3",title:"Comunidade Vota",description:"Outros clientes votam nas sugestões, criando uma fila democrática",icon:Qn},{step:"4",title:"Música Toca Automaticamente",description:"Sistema reproduz as músicas mais votadas sem intervenção manual",icon:Je}];return e.jsxs("div",{className:"min-h-screen bg-white dark:bg-gray-900",children:[e.jsx("nav",{className:"fixed top-0 w-full bg-white/80 dark:bg-gray-900/80 backdrop-blur-md z-40 border-b border-gray-200 dark:border-gray-800",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center py-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg",children:e.jsx(we,{className:"w-6 h-6 text-white"})}),e.jsx("span",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"PlaylistInterativa"})]}),e.jsxs("div",{className:"hidden md:flex items-center space-x-8",children:[e.jsx("a",{href:"#features",className:"text-gray-600 dark:text-gray-300 hover:text-blue-600 transition-colors",children:"Recursos"}),e.jsx("a",{href:"#how-it-works",className:"text-gray-600 dark:text-gray-300 hover:text-blue-600 transition-colors",children:"Como Funciona"}),e.jsx("a",{href:"#benefits",className:"text-gray-600 dark:text-gray-300 hover:text-blue-600 transition-colors",children:"Benefícios"}),e.jsx(tt,{onClick:c,variant:"outline",size:"sm",children:"Login"}),e.jsx(tt,{onClick:l,size:"sm",children:"Demo Grátis"})]})]})})}),e.jsx("section",{className:"pt-24 pb-20 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs(q.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center",children:[e.jsxs("div",{className:"inline-flex items-center px-4 py-2 bg-blue-100 dark:bg-blue-900/30 rounded-full text-blue-800 dark:text-blue-200 text-sm font-medium mb-8",children:[e.jsx(Xr,{className:"w-4 h-4 mr-2"}),"Revolucione a experiência musical do seu restaurante"]}),e.jsxs("h1",{className:"text-5xl md:text-7xl font-bold text-gray-900 dark:text-white mb-6 leading-tight",children:["Seus Clientes"," ",e.jsx("span",{className:"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"Escolhem"})," ","a Música"]}),e.jsx("p",{className:"text-xl text-gray-600 dark:text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed",children:"Sistema interativo que permite aos clientes sugerir e votar em músicas através de QR codes. Transforme seu restaurante em uma experiência única e memorável."}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-6 justify-center items-center mb-16",children:[e.jsxs(tt,{onClick:l,size:"lg",className:"bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-lg px-8 py-4",children:[e.jsx(Je,{className:"w-5 h-5 mr-2"}),"Experimentar Demo"]}),e.jsxs(tt,{onClick:o,variant:"outline",size:"lg",className:"text-lg px-8 py-4",children:[e.jsx(qt,{className:"w-5 h-5 mr-2"}),"Área Administrativa"]})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 max-w-md mx-auto",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Acesso Rápido"}),e.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[e.jsx("input",{type:"text",placeholder:"Código do restaurante (ex.: demo-restaurant)",value:t,onChange:m=>s(m.target.value),className:"flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",onKeyPress:m=>m.key==="Enter"&&i()}),e.jsx(tt,{onClick:i,className:"px-6 py-3",children:e.jsx(qn,{className:"w-4 h-4"})})]})]})]})})}),e.jsx("section",{id:"features",className:"py-20 bg-white dark:bg-gray-900",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"text-center mb-16",children:[e.jsx("h2",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"Recursos Principais"}),e.jsx("p",{className:"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto",children:"Tecnologia avançada para criar a experiência musical perfeita no seu restaurante"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:d.map((m,f)=>e.jsxs(q.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:f*.1},className:"bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow",children:[e.jsx("div",{className:`w-12 h-12 bg-gradient-to-r ${m.color} rounded-lg flex items-center justify-center mb-4`,children:e.jsx(m.icon,{className:"w-6 h-6 text-white"})}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:m.title}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:m.description})]},m.title))})]})}),e.jsx("section",{id:"how-it-works",className:"py-20 bg-gray-50 dark:bg-gray-800",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"text-center mb-16",children:[e.jsx("h2",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"Como Funciona"}),e.jsx("p",{className:"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto",children:"Processo simples e intuitivo que seus clientes vão adorar"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:p.map((m,f)=>e.jsxs(q.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:f*.1},className:"text-center",children:[e.jsxs("div",{className:"relative mb-6",children:[e.jsx("div",{className:"w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(m.icon,{className:"w-8 h-8 text-white"})}),e.jsx("div",{className:"absolute -top-2 -right-2 w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm",children:m.step})]}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:m.title}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:m.description})]},m.step))})]})}),e.jsx("section",{id:"benefits",className:"py-20 bg-white dark:bg-gray-900",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"text-center mb-16",children:[e.jsx("h2",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"Por que Escolher Nossa Solução?"}),e.jsx("p",{className:"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto",children:"Resultados comprovados que transformam a experiência do seu restaurante"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:x.map((m,f)=>e.jsx(q.div,{initial:{opacity:0,x:f%2===0?-20:20},animate:{opacity:1,x:0},transition:{duration:.5,delay:f*.1},className:"bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-700 rounded-2xl p-8",children:e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0",children:e.jsx(m.icon,{className:"w-6 h-6 text-white"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:m.title}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-3",children:m.description}),e.jsxs("div",{className:"inline-flex items-center px-3 py-1 bg-green-100 dark:bg-green-900/30 rounded-full text-green-800 dark:text-green-200 text-sm font-medium",children:[e.jsx(De,{className:"w-4 h-4 mr-1"}),m.stats]})]})]})},m.title))})]})}),e.jsx("section",{className:"py-20 bg-gradient-to-r from-blue-600 to-purple-600",children:e.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:e.jsxs(q.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[e.jsx("h2",{className:"text-4xl font-bold text-white mb-6",children:"Pronto para Revolucionar seu Restaurante?"}),e.jsx("p",{className:"text-xl text-blue-100 mb-8 max-w-2xl mx-auto",children:"Junte-se aos restaurantes que já estão oferecendo uma experiência musical única aos seus clientes"}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[e.jsxs("button",{onClick:l,className:"inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-blue-600 bg-white rounded-lg shadow-lg hover:bg-gray-100 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2",children:[e.jsx(Je,{className:"w-5 h-5 mr-2"}),"Testar Gratuitamente"]}),e.jsxs("button",{onClick:o,className:"inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white border-2 border-white rounded-lg shadow-lg hover:bg-white hover:text-blue-600 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2",children:[e.jsx(ds,{className:"w-5 h-5 mr-2"}),"Ver Dashboard"]})]})]})})}),e.jsx("footer",{className:"bg-gray-900 text-white py-12",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[e.jsxs("div",{className:"col-span-1 md:col-span-2",children:[e.jsxs("div",{className:"flex items-center space-x-2 mb-4",children:[e.jsx("div",{className:"p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg",children:e.jsx(we,{className:"w-6 h-6 text-white"})}),e.jsx("span",{className:"text-xl font-bold",children:"PlaylistInterativa"})]}),e.jsx("p",{className:"text-gray-400 mb-4 max-w-md",children:"Transformando a experiência musical em restaurantes através de tecnologia interativa e colaborativa."}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(tt,{onClick:l,size:"sm",children:"Experimentar Demo"}),e.jsx(tt,{onClick:o,variant:"outline",size:"sm",children:"Área Admin"})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Recursos"}),e.jsxs("ul",{className:"space-y-2 text-gray-400",children:[e.jsx("li",{children:"QR Codes Dinâmicos"}),e.jsx("li",{children:"Integração YouTube"}),e.jsx("li",{children:"Analytics Avançados"}),e.jsx("li",{children:"Dashboard Admin"})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Suporte"}),e.jsxs("ul",{className:"space-y-2 text-gray-400",children:[e.jsx("li",{children:"Documentação"}),e.jsx("li",{children:"Tutoriais"}),e.jsx("li",{children:"Suporte Técnico"}),e.jsx("li",{children:"FAQ"})]})]})]}),e.jsx("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400",children:e.jsxs("p",{children:["© 2025 Uniqsuporte. Todos os direitos reservados. |"," ",e.jsx("a",{href:"https://www.uniqsuporte.com.br",target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:text-blue-300 transition-colors",children:"www.uniqsuporte.com.br"})," ","|"," ",e.jsx("a",{href:"tel:+5522997986724",className:"text-blue-400 hover:text-blue-300 transition-colors",children:"22 99798-6724"})]})})]})}),e.jsx(wl,{isOpen:a,onClose:()=>n(!1),onLogin:h})]})},Wt=({size:r="md",color:t="primary",className:s})=>{const a={sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8",xl:"w-12 h-12"},n={primary:"text-primary-600 dark:text-primary-400",secondary:"text-secondary-600 dark:text-secondary-400",white:"text-white",gray:"text-gray-600 dark:text-gray-400"};return e.jsx(q.div,{initial:{opacity:0},animate:{opacity:1},className:fr("inline-block",s),children:e.jsxs("svg",{className:fr("animate-spin",a[r],n[t]),xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})})},Nl="http://localhost:8001";class kl{constructor(){Ce(this,"client");Ce(this,"sessionId",null);this.client=hi.create({baseURL:`${Nl}/api/v1`,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors(),this.initializeSessionId()}setupInterceptors(){this.client.interceptors.request.use(t=>{const s=this.getAuthToken();return s&&(t.headers.Authorization=`Bearer ${s}`),this.sessionId&&(t.headers["X-Session-ID"]=this.sessionId),t.headers["X-Device-Info"]=JSON.stringify({userAgent:navigator.userAgent,language:navigator.language,timezone:Intl.DateTimeFormat().resolvedOptions().timeZone,screenResolution:`${screen.width}x${screen.height}`}),t},t=>Promise.reject(t)),this.client.interceptors.response.use(t=>t,t=>(this.handleApiError(t),Promise.reject(t)))}initializeSessionId(){let t=localStorage.getItem("sessionId");t||(t=this.generateSessionId(),localStorage.setItem("sessionId",t)),this.sessionId=t}generateSessionId(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(t){const s=Math.random()*16|0;return(t==="x"?s:s&3|8).toString(16)})}getAuthToken(){return localStorage.getItem("authToken")}setAuthToken(t){localStorage.setItem("authToken",t)}removeAuthToken(){localStorage.removeItem("authToken")}handleApiError(t){var a,n;const s=(a=t.response)==null?void 0:a.data;switch((n=t.response)==null?void 0:n.status){case 401:this.removeAuthToken(),window.location.pathname.includes("/admin")&&(window.location.href="/admin/login");break;case 403:b.error("Acesso negado");break;case 404:b.error("Recurso não encontrado");break;case 429:b.error("Muitas requisições. Tente novamente em alguns minutos.");break;case 500:b.error("Erro interno do servidor");break;default:s!=null&&s.message?b.error(s.message):t.message?b.error(t.message):b.error("Erro inesperado")}}async login(t,s){const a=await this.client.post("/auth/login",{email:t,password:s});return this.setAuthToken(a.data.token),a.data}async logout(){this.removeAuthToken()}async getCurrentUser(){return(await this.client.get("/auth/me")).data}async getRestaurant(t){return(await this.client.get(`/restaurants/${t}`)).data}async searchYouTube(t,s,a=10,n,i=!1){return(await this.client.get("/youtube/search",{params:{q:t,maxResults:a,pageToken:n,useYouTubeAPI:i.toString(),restaurantId:s}})).data}async getVideoInfo(t){return(await this.client.get(`/youtube/video/${t}`)).data}async createSuggestion(t){return(await this.client.post("/suggestions",t)).data}async getSuggestions(t,s){const a={...s};return a.status==="all"&&delete a.status,(await this.client.get(`/suggestions/${t}`,{params:a})).data}async getPlayQueue(t){return(await this.client.get(`/playback-queue/${t}`)).data}async voteSuggestion(t,s){return(await this.client.post(`/suggestions/${t}/vote`,{voteType:s})).data}async getPlaylists(t){return(await this.client.get(`/playlists/${t}`)).data}async getPlaylist(t){return(await this.client.get(`/playlists/${t}`)).data}async getAnalytics(t,s){return(await this.client.get(`/analytics/dashboard/${t}`,{params:s})).data}async getYouTubeQuota(){return(await this.client.get("/youtube/quota")).data}getSessionId(){return this.sessionId}isAuthenticated(){return!!this.getAuthToken()}async request(t){return(await this.client.request(t)).data}}const Be=new kl,Sl=()=>{const r=cs(),{setUser:t,setAuthToken:s}=xs(),[a,n]=u.useState({email:"",password:""}),[i,l]=u.useState(!1),o=Hi(({email:d,password:x})=>Be.login(d,x),{onSuccess:d=>{s(d.token),t(d.user),b.success("Login realizado com sucesso!"),r("/admin/dashboard")},onError:d=>{var p,m;const x=((m=(p=d.response)==null?void 0:p.data)==null?void 0:m.message)||"Erro ao fazer login";b.error(x)}}),c=async d=>{if(d.preventDefault(),!a.email||!a.password){b.error("Preencha todos os campos");return}try{if(await new Promise(x=>setTimeout(x,1e3)),a.email==="<EMAIL>"&&a.password==="Adm!n2024#Secure$"){console.log("Login Admin Principal válido, redirecionando para /admin/dashboard");const x={id:"super-admin-1",name:"Admin Principal",email:"<EMAIL>",role:"super_admin",isActive:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},p="mock-admin-token-"+Date.now();t(x),s(p),b.success("Login realizado com sucesso!"),r("/admin/dashboard")}else{b.error("Credenciais inválidas. Verifique email e senha.");return}}catch{b.error("Erro ao fazer login. Verifique suas credenciais.")}},h=d=>{const{name:x,value:p}=d.target;n(m=>({...m,[x]:p}))};return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center px-4",children:e.jsxs(q.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"w-full max-w-md",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx(q.div,{initial:{scale:.8},animate:{scale:1},transition:{delay:.2},className:"w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg",children:e.jsx(we,{className:"w-8 h-8 text-white"})}),e.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"Admin Principal"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Gerencie todos os restaurantes da plataforma"})]}),e.jsxs(q.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700",children:[e.jsxs("form",{onSubmit:c,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email"}),e.jsxs("div",{className:"relative",children:[e.jsx(Dr,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:a.email,onChange:h,className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",placeholder:"<EMAIL>"})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Senha"}),e.jsxs("div",{className:"relative",children:[e.jsx(Bs,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{id:"password",name:"password",type:i?"text":"password",autoComplete:"current-password",required:!0,value:a.password,onChange:h,className:"w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",placeholder:"••••••••"}),e.jsx("button",{type:"button",onClick:()=>l(!i),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:i?e.jsx($r,{className:"w-5 h-5"}):e.jsx(ds,{className:"w-5 h-5"})})]})]}),e.jsx(tt,{type:"submit",disabled:o.isLoading,className:"w-full",size:"lg",children:o.isLoading?e.jsxs(e.Fragment,{children:[e.jsx(Wt,{size:"sm"}),"Entrando..."]}):"Entrar"})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsx("button",{onClick:()=>r("/"),className:"text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors",children:"← Voltar para página inicial"})})]}),e.jsx("div",{className:"mt-8 text-center text-sm text-gray-500 dark:text-gray-400",children:"Restaurant Playlist System © 2024"})]})})},Cl=()=>{const r=cs(),{setUser:t,setAuthToken:s}=xs(),[a,n]=u.useState("<EMAIL>"),[i,l]=u.useState("admin123"),[o,c]=u.useState(!1),[h,d]=u.useState(!1),x=async m=>{if(m.preventDefault(),!a||!i){b.error("Por favor, preencha todos os campos");return}d(!0);try{const f=await fetch(ce("/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:a,password:i})});if(!f.ok){const v=await f.json();throw new Error(v.error||"Erro ao fazer login")}const y=await f.json();console.log("🔐 Login realizado com sucesso:",y),localStorage.setItem("authToken",y.token),localStorage.setItem("user",JSON.stringify(y.user)),y.restaurant&&localStorage.setItem("restaurant",JSON.stringify(y.restaurant)),t(y.user),s(y.token),b.success("Login realizado com sucesso!"),y.restaurant?(console.log(`🔄 Redirecionando para /restaurant/${y.restaurant.id}/dashboard`),r(`/restaurant/${y.restaurant.id}/dashboard`,{replace:!0})):r("/restaurant/demo-restaurant/dashboard",{replace:!0})}catch(f){console.error("Erro no login:",f),b.error(f.message||"Erro ao fazer login. Verifique suas credenciais.")}finally{d(!1)}},p=()=>{n("<EMAIL>"),l("admin123")};return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4",children:e.jsxs(q.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"w-full max-w-md",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx(q.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-4",children:e.jsx(we,{className:"w-8 h-8 text-white"})}),e.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:"Restaurante Admin"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Acesse o painel do seu restaurante"})]}),e.jsxs(q.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3,duration:.5},className:"bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8",children:[e.jsxs("form",{onSubmit:x,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email"}),e.jsxs("div",{className:"relative",children:[e.jsx(Dr,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{type:"email",value:a,onChange:m=>n(m.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",placeholder:"<EMAIL>",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Senha"}),e.jsxs("div",{className:"relative",children:[e.jsx(Bs,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{type:o?"text":"password",value:i,onChange:m=>l(m.target.value),className:"w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",placeholder:"••••••••",required:!0}),e.jsx("button",{type:"button",onClick:()=>c(!o),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",children:o?e.jsx($r,{className:"w-5 h-5"}):e.jsx(ds,{className:"w-5 h-5"})})]})]}),e.jsx("button",{type:"button",onClick:p,className:"w-full text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-center py-2 transition-colors",children:"← Usar credenciais de demonstração"}),e.jsx(tt,{type:"submit",disabled:h,className:"w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white py-3 font-medium transition-all duration-200 transform hover:scale-[1.02]",children:h?e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsx("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),e.jsx("span",{children:"Entrando..."})]}):e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsx(ir,{className:"w-5 h-5"}),e.jsx("span",{children:"Entrar"})]})})]}),e.jsxs("div",{className:"mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800",children:[e.jsx("h4",{className:"text-sm font-medium text-blue-900 dark:text-blue-100 mb-2",children:"Credenciais para demonstração:"}),e.jsxs("div",{className:"text-sm text-blue-700 dark:text-blue-300 space-y-1",children:[e.jsxs("div",{children:[e.jsx("strong",{children:"Email:"})," <EMAIL>"]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Senha:"})," admin123"]})]})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Não tem uma conta?"," ",e.jsx("button",{onClick:()=>r("/"),className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors",children:"Voltar ao início"})]})})]})]})})},El=()=>{const[r,t]=u.useState([]),[s,a]=u.useState(!0),[n,i]=u.useState(null),[l,o]=u.useState(!1),[c,h]=u.useState(null),[d,x]=u.useState({email:"",password:""}),[p,m]=u.useState({name:"",email:"",description:"",address:"",phone:"",password:""});u.useEffect(()=>{f()},[]);const f=async(j=!1)=>{var $;try{a(!0),i(null);const A=await Be.client.get("/admin/restaurants");console.log("Resposta da API:",A.data);const O=(($=A.data.restaurants)==null?void 0:$.map(N=>({...N,status:N.isActive?"active":"inactive"})))||[];t(O),j&&b.success("Lista de restaurantes atualizada com sucesso!")}catch(A){console.error("Erro ao carregar restaurantes:",A),i("Erro ao carregar restaurantes");const O=[{id:"demo-restaurant",name:"Restaurante Demo",email:"<EMAIL>",description:"Restaurante de demonstração",phone:"(11) 99999-9999",address:"Rua Demo, 123",isActive:!0,status:"active",createdAt:new Date().toISOString(),settings:{allowSuggestions:!0}}];t(O),b.error("Erro ao carregar restaurantes - usando dados de demonstração")}finally{a(!1)}},y=async()=>{var j,$;try{const O=(await Be.client.post("/admin/restaurants",p)).data;b.success("Restaurante criado com sucesso!"),b.success(`Login: ${O.credentials.email}
Senha: ${O.credentials.password}
URL: ${O.loginUrl}`,{duration:1e4}),o(!1),m({name:"",email:"",description:"",address:"",phone:"",password:""}),f()}catch(A){console.error("Erro ao criar restaurante:",A),b.error((($=(j=A.response)==null?void 0:j.data)==null?void 0:$.error)||"Erro ao criar restaurante")}},v=async()=>{var j,$;if(c)try{const A={name:c.name,description:c.description,phone:c.phone,address:c.address};d.email.trim()&&(A.email=d.email),d.password.trim()&&(A.password=d.password);const O=await Be.client.put(`/admin/restaurants/${c.id}`,A);b.success("Restaurante atualizado com sucesso!"),h(null),x({email:"",password:""}),f()}catch(A){console.error("Erro ao atualizar restaurante:",A),b.error((($=(j=A.response)==null?void 0:j.data)==null?void 0:$.error)||"Erro ao atualizar restaurante")}},g=async(j,$)=>{var A,O;try{const N=await Be.client.patch(`/admin/restaurants/${j}/status`,{isActive:$}),B=$?"ativado":"desativado";b.success(`Restaurante ${B} com sucesso!`),f()}catch(N){console.error("Erro ao atualizar restaurante:",N),b.error(((O=(A=N.response)==null?void 0:A.data)==null?void 0:O.error)||"Erro ao atualizar restaurante")}},k=async j=>{var $,A;if(confirm("Tem certeza que deseja suspender este restaurante?"))try{await Be.client.patch(`/admin/restaurants/${j}/status`,{isActive:!1}),b.success("Restaurante suspenso com sucesso!"),f()}catch(O){console.error("Erro ao suspender restaurante:",O),b.error(((A=($=O.response)==null?void 0:$.data)==null?void 0:A.error)||"Erro ao suspender restaurante")}},T=async j=>{var $,A;try{await Be.client.patch(`/admin/restaurants/${j}/status`,{isActive:!0}),b.success("Restaurante reativado com sucesso!"),f()}catch(O){console.error("Erro ao reativar restaurante:",O),b.error(((A=($=O.response)==null?void 0:$.data)==null?void 0:A.error)||"Erro ao reativar restaurante")}},ee=async j=>{var $,A;if(confirm("Tem certeza que deseja deletar este restaurante? Esta ação não pode ser desfeita."))try{await Be.client.delete(`/admin/restaurants/${j}`),b.success("Restaurante deletado com sucesso!"),f()}catch(O){console.error("Erro ao deletar restaurante:",O),b.error(((A=($=O.response)==null?void 0:$.data)==null?void 0:A.error)||"Erro ao deletar restaurante")}},se=j=>{switch(j.status){case"active":return"text-green-600 bg-green-100 dark:bg-green-900/20";case"inactive":return"text-gray-600 bg-gray-100 dark:bg-gray-900/20";case"suspended":return"text-red-600 bg-red-100 dark:bg-red-900/20";case"trial":return"text-blue-600 bg-blue-100 dark:bg-blue-900/20";default:return"text-gray-600 bg-gray-100 dark:bg-gray-900/20"}},te=j=>{switch(j.status){case"active":return"Ativo";case"inactive":return"Inativo";case"suspended":return"Suspenso";case"trial":return"Trial";default:return"Desconhecido"}};return s?e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex justify-between items-center",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Gerenciamento de Restaurantes"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Gerencie todos os restaurantes da plataforma"})]})}),e.jsxs("div",{className:"flex flex-col justify-center items-center h-64 space-y-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(Qe,{className:"w-12 h-12 animate-spin text-blue-600"}),e.jsx("div",{className:"absolute inset-0 w-12 h-12 border-4 border-blue-100 dark:border-blue-900/20 rounded-full animate-pulse"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Carregando restaurantes"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Por favor, aguarde..."})]})]})]}):n&&r.length===0?e.jsxs("div",{className:"flex flex-col justify-center items-center h-64 space-y-4",children:[e.jsx(ss,{className:"w-12 h-12 text-red-500"}),e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Erro ao carregar restaurantes"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:n}),e.jsxs("button",{onClick:()=>f(!0),disabled:s,className:"mt-6 inline-flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsx(Qe,{className:`w-5 h-5 ${s?"animate-spin":""}`}),e.jsx("span",{children:"Tentar novamente"})]})]})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Gerenciamento de Restaurantes"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Gerencie todos os restaurantes da plataforma"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("button",{onClick:()=>f(!0),disabled:s,className:"flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Recarregar lista de restaurantes",children:[e.jsx(Qe,{className:`w-4 h-4 ${s?"animate-spin":""}`}),e.jsx("span",{children:"Recarregar"})]}),e.jsxs("button",{onClick:()=>o(!0),className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",children:[e.jsx(Zr,{className:"w-4 h-4"}),e.jsx("span",{children:"Novo Restaurante"})]})]})]}),r.length===0?e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4",children:e.jsx(rt,{className:"w-12 h-12 text-gray-400"})}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"Nenhum restaurante encontrado"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"Comece criando seu primeiro restaurante na plataforma."}),e.jsxs("button",{onClick:()=>o(!0),className:"inline-flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[e.jsx(Zr,{className:"w-5 h-5"}),e.jsx("span",{children:"Criar Primeiro Restaurante"})]})]}):e.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6",children:r.map(j=>{var $,A;return e.jsxs(q.div,{layout:!0,initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.3},className:"bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-100 dark:border-gray-700 hover:shadow-xl transition-all duration-300",children:[e.jsxs("div",{className:"flex justify-between items-start mb-6",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-xl font-bold text-gray-900 dark:text-white mb-2",children:j.name}),e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400 flex items-center",children:[e.jsxs("svg",{className:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:[e.jsx("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),e.jsx("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})]}),j.email]})]}),e.jsx("div",{className:`px-3 py-1 rounded-full text-xs font-semibold ${se(j)}`,children:te(j)})]}),j.description&&e.jsx("div",{className:"mb-4",children:e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg",children:j.description})}),e.jsxs("div",{className:"space-y-2 mb-6",children:[j.phone&&e.jsxs("div",{className:"flex items-center text-sm text-gray-600 dark:text-gray-400",children:[e.jsx("svg",{className:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"})}),j.phone]}),j.address&&e.jsxs("div",{className:"flex items-center text-sm text-gray-600 dark:text-gray-400",children:[e.jsx("svg",{className:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z",clipRule:"evenodd"})}),j.address]})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 mb-6 space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Sugestões"}),e.jsx("span",{className:`font-medium ${($=j.settings)!=null&&$.allowSuggestions?"text-green-600":"text-red-600"}`,children:(A=j.settings)!=null&&A.allowSuggestions?"Ativas":"Inativas"})]}),e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Criado em"}),e.jsx("span",{className:"text-gray-900 dark:text-white font-medium",children:new Date(j.createdAt).toLocaleDateString("pt-BR")})]}),j.lastActivityAt&&e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Última atividade"}),e.jsx("span",{className:"text-gray-900 dark:text-white font-medium",children:new Date(j.lastActivityAt).toLocaleDateString("pt-BR")})]})]}),e.jsxs("div",{className:"flex flex-col gap-3",children:[e.jsxs("button",{onClick:()=>window.open(`/restaurant/${j.id}/dashboard`,"_blank"),className:"w-full flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium",title:"Acessar Dashboard do Restaurante",children:[e.jsx(Lr,{className:"w-4 h-4"}),e.jsx("span",{children:"Acessar Dashboard"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsxs("button",{onClick:()=>{h(j),j.adminUser?x({email:j.adminUser.email||"",password:""}):x({email:"",password:""})},className:"flex items-center justify-center space-x-1 px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm",title:"Editar Restaurante",children:[e.jsx(zn,{className:"w-4 h-4"}),e.jsx("span",{children:"Editar"})]}),e.jsxs("button",{onClick:()=>g(j.id,!j.isActive),className:`flex items-center justify-center space-x-1 px-3 py-2 rounded-lg transition-colors text-sm font-medium ${j.isActive?"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400 hover:bg-yellow-200 dark:hover:bg-yellow-900/50":"bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 hover:bg-green-200 dark:hover:bg-green-900/50"}`,title:j.isActive?"Desativar Restaurante":"Ativar Restaurante",children:[j.isActive?e.jsx(Fr,{className:"w-4 h-4"}):e.jsx(Je,{className:"w-4 h-4"}),e.jsx("span",{children:j.isActive?"Pausar":"Ativar"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[j.status!=="suspended"?e.jsxs("button",{onClick:()=>k(j.id),className:"flex items-center justify-center space-x-1 px-3 py-2 bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-400 rounded-lg hover:bg-orange-200 dark:hover:bg-orange-900/50 transition-colors text-sm",title:"Suspender Restaurante",children:[e.jsx(Ta,{className:"w-4 h-4"}),e.jsx("span",{children:"Suspender"})]}):e.jsxs("button",{onClick:()=>T(j.id),className:"flex items-center justify-center space-x-1 px-3 py-2 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors text-sm",title:"Reativar Restaurante",children:[e.jsx(Wn,{className:"w-4 h-4"}),e.jsx("span",{children:"Reativar"})]}),e.jsxs("button",{onClick:()=>ee(j.id),className:"flex items-center justify-center space-x-1 px-3 py-2 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 rounded-lg hover:bg-red-200 dark:hover:bg-red-900/50 transition-colors text-sm",title:"Deletar Restaurante",children:[e.jsx(Oa,{className:"w-4 h-4"}),e.jsx("span",{children:"Deletar"})]})]})]})]},j.id)})}),e.jsxs(ze,{children:[l&&e.jsx(q.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs(q.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Criar Novo Restaurante"}),"              ",e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Nome do Restaurante *"}),e.jsx("input",{type:"text",value:p.name,onChange:j=>m({...p,name:j.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Ex: Restaurante do João"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Email de Login *"}),e.jsx("input",{type:"email",value:p.email,onChange:j=>m({...p,email:j.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"<EMAIL>"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Senha *"}),e.jsx("input",{type:"password",value:p.password,onChange:j=>m({...p,password:j.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Senha segura"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Descrição"}),e.jsx("textarea",{value:p.description,onChange:j=>m({...p,description:j.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",rows:2,placeholder:"Descrição do restaurante"})]})]}),e.jsxs("div",{className:"flex space-x-3 mt-6",children:[e.jsx("button",{onClick:()=>o(!1),className:"flex-1 px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300",children:"Cancelar"}),e.jsx("button",{onClick:y,disabled:!p.name||!p.email||!p.password,className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50",children:"Criar"})]})]})}),c&&e.jsx(q.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs(q.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Editar Restaurante"}),"              ",e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Nome do Restaurante *"}),e.jsx("input",{type:"text",value:c.name,onChange:j=>h({...c,name:j.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Ex: Restaurante do João"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Descrição"}),e.jsx("textarea",{value:c.description,onChange:j=>h({...c,description:j.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",rows:2,placeholder:"Descrição do restaurante"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Telefone"}),e.jsx("input",{type:"text",value:c.phone||"",onChange:j=>h({...c,phone:j.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"(11) 99999-9999"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Endereço"}),e.jsx("input",{type:"text",value:c.address||"",onChange:j=>h({...c,address:j.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Rua, número, bairro"})]}),e.jsxs("div",{className:"mt-6 pt-4 border-t border-gray-200 dark:border-gray-600",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 dark:text-white mb-3",children:"Credenciais de Login"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Email de Login"}),e.jsx("input",{type:"email",value:d.email,onChange:j=>x({...d,email:j.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"<EMAIL>"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Deixe em branco para manter o email atual"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Nova Senha"}),e.jsx("input",{type:"password",value:d.password,onChange:j=>x({...d,password:j.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Nova senha (opcional)"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Deixe em branco para manter a senha atual"})]})]})]})]}),e.jsxs("div",{className:"flex space-x-3 mt-6",children:[e.jsx("button",{onClick:()=>{h(null),x({email:"",password:""})},className:"flex-1 px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300",children:"Cancelar"}),e.jsx("button",{onClick:v,disabled:!c.name,className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50",children:"Salvar"})]})]})})]})]})},Rl=()=>{const[r,t]=u.useState(null),[s,a]=u.useState(!0),[n,i]=u.useState("30d"),[l,o]=u.useState("overview");u.useEffect(()=>{c()},[n]);const c=async()=>{var d;try{a(!0);const{data:x}=await Be.client.get("/admin/analytics");if(x!=null&&x.success&&((d=x.analytics)!=null&&d.overview)){const p=x.analytics.overview,m=x.analytics.topRestaurants||[];t({totalRestaurants:p.totalRestaurants??0,activeRestaurants:p.activeRestaurants??0,totalRevenue:p.totalRevenue??0,monthlyRevenue:p.monthlyGrowth??0,totalPayments:p.totalPayments??0,totalVotes:p.totalVotes??0,topRestaurants:m.map(f=>({id:f.id,name:f.name,revenue:f.revenue??0,transactions:f.transactions??f.payments??0})),revenueByMonth:[]})}else throw new Error("Resposta inválida do servidor")}catch(x){console.error("Erro ao carregar analytics globais:",x),b.error("Erro ao carregar dados de analytics")}finally{a(!1)}},h=async()=>{try{b.success("Exportando dados...")}catch{b.error("Erro ao exportar dados")}};return s?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx(Wt,{})}):r?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Analytics Globais"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Métricas consolidadas de todos os restaurantes"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("select",{value:n,onChange:d=>i(d.target.value),className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm",children:[e.jsx("option",{value:"7d",children:"Últimos 7 dias"}),e.jsx("option",{value:"30d",children:"Últimos 30 dias"}),e.jsx("option",{value:"90d",children:"Últimos 90 dias"})]}),e.jsxs("button",{onClick:h,className:"flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",title:"Exportar dados de analytics",children:[e.jsx(Ur,{className:"w-4 h-4"}),e.jsx("span",{children:"Exportar"})]}),e.jsxs("button",{onClick:c,disabled:s,className:"flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Recarregar dados de analytics",children:[e.jsx(Qe,{className:`w-4 h-4 ${s?"animate-spin":""}`}),e.jsx("span",{children:"Recarregar"})]}),e.jsxs("button",{onClick:()=>o("revenue"),className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",children:[e.jsx(Va,{className:"w-4 h-4"}),e.jsx("span",{children:"Relatório"})]})]})]}),e.jsx("div",{className:"flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg",children:[{key:"overview",label:"Visão Geral",icon:vt},{key:"revenue",label:"Receitas",icon:Gt},{key:"restaurants",label:"Restaurantes",icon:Ot}].map(d=>e.jsxs("button",{onClick:()=>o(d.key),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${l===d.key?"bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"}`,children:[e.jsx(d.icon,{className:"w-4 h-4"}),e.jsx("span",{children:d.label})]},d.key))}),l==="overview"&&e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[{title:"Total de Restaurantes",value:r.totalRestaurants,subtitle:`${r.activeRestaurants} ativos`,icon:Ot,color:"text-blue-600",bgColor:"bg-blue-100 dark:bg-blue-900/20"},{title:"Receita Total",value:`R$ ${r.totalRevenue.toFixed(2)}`,subtitle:`+R$ ${r.monthlyRevenue.toFixed(2)} este mês`,icon:Gt,color:"text-green-600",bgColor:"bg-green-100 dark:bg-green-900/20"},{title:"Pagamentos (Supervoto)",value:r.totalPayments.toLocaleString(),subtitle:"Últimos 30 dias",icon:Gt,color:"text-purple-600",bgColor:"bg-purple-100 dark:bg-purple-900/20"},{title:"Votos Totais",value:r.totalVotes.toLocaleString(),subtitle:"Engajamento na plataforma",icon:Da,color:"text-yellow-600",bgColor:"bg-yellow-100 dark:bg-yellow-900/20"}].map((d,x)=>e.jsx(q.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:x*.1},className:"card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-1",children:d.title}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:d.value}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:d.subtitle})]}),e.jsx("div",{className:`p-3 rounded-lg ${d.bgColor}`,children:e.jsx(d.icon,{className:`w-6 h-6 ${d.color}`})})]})},x))}),l==="restaurants"&&e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Top Restaurantes por Receita"}),e.jsx("div",{className:"space-y-4",children:r.topRestaurants.map((d,x)=>e.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"flex items-center justify-center w-8 h-8 bg-blue-100 dark:bg-blue-900/20 text-blue-600 rounded-full text-sm font-bold",children:x+1}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:d.name}),typeof d.transactions=="number"&&e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[d.transactions," transações"]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:"font-semibold text-gray-900 dark:text-white",children:["R$ ",d.revenue.toFixed(2)]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"receita"})]})]},d.id))})]}),l==="revenue"&&e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Receita por Mês"}),e.jsx("div",{className:"space-y-3",children:r.revenueByMonth.map(d=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:d.month}),e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:"font-semibold text-gray-900 dark:text-white",children:["R$ ",d.revenue.toFixed(2)]}),e.jsxs("p",{className:"text-sm text-gray-500",children:[d.payments," pagamentos"]})]})]},d.month))})]}),e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Atividade de Pagamentos"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Em breve: distribuição por método de pagamento, horários de pico e mais."})]})]})]}):e.jsx("div",{className:"text-center py-12",children:e.jsx("p",{className:"text-gray-500",children:"Nenhum dado disponível"})})},Pl=()=>{const[r,t]=u.useState(null),[s,a]=u.useState(!0),[n,i]=u.useState("30d"),[l,o]=u.useState("overview"),c=10;u.useEffect(()=>{h()},[n]);const h=async()=>{try{a(!0);const p=await(await fetch(`/api/v1/admin/revenue?period=${n}`)).json();if(p.success)t(p.revenue);else throw new Error("Erro ao carregar dados de receita")}catch(x){console.error("Erro ao carregar dados de receita:",x),b.error("Erro ao carregar dados de receita"),t({totalRevenue:12450.75,platformRevenue:1245.08,restaurantRevenue:11205.67,monthlyGrowth:15.3,totalTransactions:623,averageTransactionValue:2,revenueByRestaurant:[{restaurantId:"1",restaurantName:"Restaurante A",totalRevenue:3250.5,platformShare:325.05,transactions:162,averageValue:2.01},{restaurantId:"2",restaurantName:"Restaurante B",totalRevenue:2890.3,platformShare:289.03,transactions:144,averageValue:2.01},{restaurantId:"3",restaurantName:"Restaurante C",totalRevenue:2150.2,platformShare:215.02,transactions:107,averageValue:2.01},{restaurantId:"4",restaurantName:"Restaurante D",totalRevenue:1980.15,platformShare:198.02,transactions:99,averageValue:2},{restaurantId:"5",restaurantName:"Restaurante E",totalRevenue:1679.6,platformShare:167.96,transactions:84,averageValue:1.99}],revenueByMonth:[{month:"Jan",totalRevenue:8200,platformRevenue:820,transactions:410},{month:"Fev",totalRevenue:9350,platformRevenue:935,transactions:467},{month:"Mar",totalRevenue:10180,platformRevenue:1018,transactions:509},{month:"Abr",totalRevenue:11420,platformRevenue:1142,transactions:571},{month:"Mai",totalRevenue:12450,platformRevenue:1245,transactions:623}],paymentMethods:[{method:"PIX",count:589,revenue:11780,percentage:94.6},{method:"Cartão",count:34,revenue:670.75,percentage:5.4}]})}finally{a(!1)}},d=async()=>{try{b.success("Exportando relatório de receitas...");const x=await fetch(`/api/v1/admin/revenue/export?period=${n}`);if(!x.ok)throw new Error("Falha ao exportar");const p=await x.blob(),m=window.URL.createObjectURL(p),f=document.createElement("a");f.href=m,f.download=`revenue_${n}_${new Date().toISOString().slice(0,10)}.json`,document.body.appendChild(f),f.click(),f.remove(),window.URL.revokeObjectURL(m)}catch{b.error("Erro ao exportar dados")}};return s?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx(Wt,{})}):r?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Dashboard de Receitas"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Controle financeiro e receitas compartilhadas"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("select",{value:n,onChange:x=>i(x.target.value),className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm",children:[e.jsx("option",{value:"7d",children:"Últimos 7 dias"}),e.jsx("option",{value:"30d",children:"Últimos 30 dias"}),e.jsx("option",{value:"90d",children:"Últimos 90 dias"}),e.jsx("option",{value:"1y",children:"Último ano"})]}),e.jsxs("button",{onClick:d,className:"flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",title:"Exportar dados de receita",children:[e.jsx(Ur,{className:"w-4 h-4"}),e.jsx("span",{children:"Exportar"})]}),e.jsxs("button",{onClick:h,disabled:s,className:"flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Recarregar dados de receita",children:[e.jsx(Qe,{className:`w-4 h-4 ${s?"animate-spin":""}`}),e.jsx("span",{children:"Recarregar"})]}),e.jsxs("button",{onClick:()=>o("overview"),className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",children:[e.jsx(Va,{className:"w-4 h-4"}),e.jsx("span",{children:"Relatório"})]})]})]}),e.jsx("div",{className:"flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg",children:[{key:"overview",label:"Visão Geral",icon:vt},{key:"restaurants",label:"Por Restaurante",icon:Ot},{key:"trends",label:"Tendências",icon:De}].map(x=>e.jsxs("button",{onClick:()=>o(x.key),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${l===x.key?"bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"}`,children:[e.jsx(x.icon,{className:"w-4 h-4"}),e.jsx("span",{children:x.label})]},x.key))}),l==="overview"&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[{title:"Receita Total",value:`R$ ${r.totalRevenue.toFixed(2)}`,subtitle:`${r.totalTransactions} transações`,icon:Gt,color:"text-green-600",bgColor:"bg-green-100 dark:bg-green-900/20"},{title:"Nossa Receita",value:`R$ ${r.platformRevenue.toFixed(2)}`,subtitle:`${c}% da receita total`,icon:De,color:"text-blue-600",bgColor:"bg-blue-100 dark:bg-blue-900/20"},{title:"Receita Restaurantes",value:`R$ ${r.restaurantRevenue.toFixed(2)}`,subtitle:`${100-c}% da receita total`,icon:Ot,color:"text-purple-600",bgColor:"bg-purple-100 dark:bg-purple-900/20"},{title:"Ticket Médio",value:`R$ ${r.averageTransactionValue.toFixed(2)}`,subtitle:`+${r.monthlyGrowth}% este mês`,icon:Ye,color:"text-orange-600",bgColor:"bg-orange-100 dark:bg-orange-900/20"}].map((x,p)=>e.jsx(q.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:p*.1},className:"card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-1",children:x.title}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:x.value}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:x.subtitle})]}),e.jsx("div",{className:`p-3 rounded-lg ${x.bgColor}`,children:e.jsx(x.icon,{className:`w-6 h-6 ${x.color}`})})]})},p))}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Métodos de Pagamento"}),e.jsx("div",{className:"space-y-4",children:r.paymentMethods.map(x=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-3 h-3 bg-blue-500 rounded-full"}),e.jsx("span",{className:"text-gray-900 dark:text-white font-medium",children:x.method})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:"font-semibold text-gray-900 dark:text-white",children:["R$ ",x.revenue.toFixed(2)]}),e.jsxs("p",{className:"text-sm text-gray-500",children:[x.count," transações (",x.percentage,"%)"]})]})]},x.method))})]}),e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Distribuição de Receita"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),e.jsxs("span",{className:"text-gray-900 dark:text-white font-medium",children:["Plataforma (",c,"%)"]})]}),e.jsxs("span",{className:"font-semibold text-gray-900 dark:text-white",children:["R$ ",r.platformRevenue.toFixed(2)]})]}),e.jsxs("div",{className:"flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-3 h-3 bg-blue-500 rounded-full"}),e.jsxs("span",{className:"text-gray-900 dark:text-white font-medium",children:["Restaurantes (",100-c,"%)"]})]}),e.jsxs("span",{className:"font-semibold text-gray-900 dark:text-white",children:["R$ ",r.restaurantRevenue.toFixed(2)]})]})]})]})]})]}),l==="restaurants"&&e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Receita por Restaurante"}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b border-gray-200 dark:border-gray-700",children:[e.jsx("th",{className:"text-left py-3 px-4 font-medium text-gray-900 dark:text-white",children:"Restaurante"}),e.jsx("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white",children:"Receita Total"}),e.jsx("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white",children:"Nossa Parte"}),e.jsx("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white",children:"Transações"}),e.jsx("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white",children:"Ticket Médio"})]})}),e.jsx("tbody",{children:r.revenueByRestaurant.map(x=>e.jsxs("tr",{className:"border-b border-gray-100 dark:border-gray-800",children:[e.jsx("td",{className:"py-3 px-4",children:e.jsx("div",{className:"font-medium text-gray-900 dark:text-white",children:x.restaurantName})}),e.jsxs("td",{className:"py-3 px-4 text-right font-semibold text-gray-900 dark:text-white",children:["R$ ",x.totalRevenue.toFixed(2)]}),e.jsxs("td",{className:"py-3 px-4 text-right font-semibold text-green-600",children:["R$ ",x.platformShare.toFixed(2)]}),e.jsx("td",{className:"py-3 px-4 text-right text-gray-600 dark:text-gray-400",children:x.transactions}),e.jsxs("td",{className:"py-3 px-4 text-right text-gray-600 dark:text-gray-400",children:["R$ ",x.averageValue.toFixed(2)]})]},x.restaurantId))})]})})]}),l==="trends"&&e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Evolução da Receita"}),e.jsx("div",{className:"space-y-4",children:r.revenueByMonth.map(x=>e.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("span",{className:"font-medium text-gray-900 dark:text-white",children:x.month}),e.jsxs("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[x.transactions," transações"]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:"font-semibold text-gray-900 dark:text-white",children:["R$ ",x.totalRevenue.toFixed(2)]}),e.jsxs("p",{className:"text-sm text-green-600",children:["+R$ ",x.platformRevenue.toFixed(2)," nossa parte"]})]})]},x.month))})]})]}):e.jsx("div",{className:"text-center py-12",children:e.jsx("p",{className:"text-gray-500",children:"Nenhum dado de receita disponível"})})},Il=()=>{const[r,t]=u.useState(null),[s,a]=u.useState(!0),[n,i]=u.useState(!1),[l,o]=u.useState("platform");u.useEffect(()=>{c()},[]);const c=async()=>{try{a(!0);const{data:m}=await Be.client.get("/admin/settings");if(m.success)t({platform:{name:"Restaurant Playlist System",version:"2.1.0",maintenanceMode:m.settings.system.maintenanceMode,maxRestaurants:m.settings.system.maxRestaurantsPerPlan,defaultLanguage:"pt-BR",allowNewRegistrations:!!m.settings.system.allowNewRegistrations,defaultTrialDays:Number(m.settings.system.defaultTrialDays||30)},revenue:{platformFeePercentage:m.settings.payments.commissionRate,minimumPaymentAmount:2,paymentMethods:["pix","credit_card"],autoPayoutEnabled:!0,payoutFrequency:"weekly",defaultCurrency:m.settings.payments.defaultCurrency||"BRL"},notifications:{emailEnabled:m.settings.notifications.emailNotifications,smsEnabled:m.settings.notifications.smsNotifications,webhookEnabled:!!m.settings.notifications.webhookUrl,webhookUrl:m.settings.notifications.webhookUrl||"",adminNotifications:{newRestaurant:!0,paymentIssues:!0,systemErrors:!0,maintenanceAlerts:!0}},security:{sessionTimeout:3600,maxLoginAttempts:5,requireTwoFactor:!1,allowedDomains:["localhost","yourdomain.com"],rateLimitEnabled:!0,rateLimitRequests:100},integrations:{mercadoPago:{enabled:m.settings.payments.pixEnabled,environment:"sandbox",webhookUrl:m.settings.notifications.webhookUrl||""},youtube:{enabled:m.settings.features.analyticsEnabled,apiQuotaLimit:1e4,cacheEnabled:!0}}});else throw new Error("Erro ao carregar configurações")}catch(m){console.error("Erro ao carregar configurações:",m),b.error("Erro ao carregar configurações"),t({platform:{name:"Restaurant Playlist System",version:"2.1.0",maintenanceMode:!1,maxRestaurants:100,defaultLanguage:"pt-BR",allowNewRegistrations:!0,defaultTrialDays:30},revenue:{platformFeePercentage:10,minimumPaymentAmount:2,paymentMethods:["pix","credit_card"],autoPayoutEnabled:!0,payoutFrequency:"weekly",defaultCurrency:"BRL"},notifications:{emailEnabled:!0,smsEnabled:!1,webhookEnabled:!0,webhookUrl:"https://yourdomain.com/api/v1/payments/webhook",adminNotifications:{newRestaurant:!0,paymentIssues:!0,systemErrors:!0,maintenanceAlerts:!0}},security:{sessionTimeout:3600,maxLoginAttempts:5,requireTwoFactor:!1,allowedDomains:["localhost","yourdomain.com"],rateLimitEnabled:!0,rateLimitRequests:100},integrations:{mercadoPago:{enabled:!0,environment:"sandbox",webhookUrl:"https://yourdomain.com/api/v1/payments/webhook"},youtube:{enabled:!0,apiQuotaLimit:1e4,cacheEnabled:!0}}})}finally{a(!1)}},h=async()=>{if(r)try{i(!0);const m={settings:{system:{maintenanceMode:r.platform.maintenanceMode,allowNewRegistrations:!!r.platform.allowNewRegistrations,maxRestaurantsPerPlan:r.platform.maxRestaurants,defaultTrialDays:r.platform.defaultTrialDays??30},notifications:{emailNotifications:r.notifications.emailEnabled,smsNotifications:r.notifications.smsEnabled,webhookUrl:r.notifications.webhookEnabled&&r.notifications.webhookUrl||""},payments:{stripeEnabled:r.revenue.paymentMethods.includes("credit_card"),pixEnabled:r.integrations.mercadoPago.enabled,defaultCurrency:r.revenue.defaultCurrency||"BRL",commissionRate:r.revenue.platformFeePercentage},features:{analyticsEnabled:r.integrations.youtube.enabled,competitiveVotingEnabled:!0,playlistSchedulingEnabled:!0,qrCodeGenerationEnabled:!0}}};await Be.client.put("/admin/settings",m),b.success("Configurações salvas com sucesso!")}catch(m){console.error("Erro ao salvar configurações:",m),b.error("Erro ao salvar configurações")}finally{i(!1)}},d=(m,f,y)=>{r&&t({...r,[m]:{...r[m],[f]:y}})},x=(m,f,y,v)=>{r&&t({...r,[m]:{...r[m],[f]:{...r[m][f],[y]:v}}})};if(s)return e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx(Wt,{})});if(!r)return e.jsx("div",{className:"text-center py-12",children:e.jsx("p",{className:"text-gray-500",children:"Erro ao carregar configurações"})});const p=[{key:"platform",label:"Plataforma",icon:Kn},{key:"revenue",label:"Receitas",icon:Gt},{key:"notifications",label:"Notificações",icon:Yn},{key:"security",label:"Segurança",icon:Ta},{key:"integrations",label:"Integrações",icon:qt}];return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Configurações do Sistema"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Configurações globais da plataforma"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("button",{onClick:c,disabled:s,className:"flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Recarregar configurações",children:[e.jsx(Qe,{className:`w-4 h-4 ${s?"animate-spin":""}`}),e.jsx("span",{children:"Recarregar"})]}),e.jsxs("button",{onClick:h,disabled:n||s,className:"flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed",title:"Salvar todas as configurações",children:[e.jsx($a,{className:`w-4 h-4 ${n?"animate-pulse":""}`}),e.jsx("span",{children:n?"Salvando...":"Salvar"})]}),e.jsxs("button",{onClick:()=>window.location.href="/admin/backup",className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",children:[e.jsx(Hn,{className:"w-4 h-4"}),e.jsx("span",{children:"Backup"})]})]})]}),e.jsx("div",{className:"flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg",children:p.map(m=>e.jsxs("button",{onClick:()=>o(m.key),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${l===m.key?"bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"}`,children:[e.jsx(m.icon,{className:"w-4 h-4"}),e.jsx("span",{children:m.label})]},m.key))}),l==="platform"&&e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Configurações da Plataforma"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Nome da Plataforma"}),e.jsx("input",{type:"text",value:r.platform.name,onChange:m=>d("platform","name",m.target.value),className:"input-field"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Versão"}),e.jsx("input",{type:"text",value:r.platform.version,onChange:m=>d("platform","version",m.target.value),className:"input-field",readOnly:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Máximo de Restaurantes"}),e.jsx("input",{type:"number",value:r.platform.maxRestaurants,onChange:m=>d("platform","maxRestaurants",parseInt(m.target.value)),className:"input-field"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Idioma Padrão"}),e.jsxs("select",{value:r.platform.defaultLanguage,onChange:m=>d("platform","defaultLanguage",m.target.value),className:"input-field",children:[e.jsx("option",{value:"pt-BR",children:"Português (Brasil)"}),e.jsx("option",{value:"en-US",children:"English (US)"}),e.jsx("option",{value:"es-ES",children:"Español"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Dias de Teste (Trial)"}),e.jsx("input",{type:"number",min:0,value:r.platform.defaultTrialDays??30,onChange:m=>d("platform","defaultTrialDays",parseInt(m.target.value||"0")),className:"input-field"})]})]}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:r.platform.maintenanceMode,onChange:m=>d("platform","maintenanceMode",m.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Modo de Manutenção"})]}),e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Quando ativado, apenas administradores podem acessar o sistema"}),e.jsxs("div",{className:"mt-4",children:[e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:!!r.platform.allowNewRegistrations,onChange:m=>d("platform","allowNewRegistrations",m.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Permitir novos cadastros"})]}),e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Controla se novos restaurantes podem se registrar na plataforma"})]})]})]}),l==="revenue"&&e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Configurações de Receita"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Taxa da Plataforma (%)"}),e.jsx("input",{type:"number",min:0,max:100,step:.1,value:r.revenue.platformFeePercentage,onChange:m=>d("revenue","platformFeePercentage",parseFloat(m.target.value)),className:"input-field"}),e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Porcentagem que a plataforma recebe de cada transação"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Moeda Padrão"}),e.jsxs("select",{value:r.revenue.defaultCurrency||"BRL",onChange:m=>d("revenue","defaultCurrency",m.target.value),className:"input-field",children:[e.jsx("option",{value:"BRL",children:"BRL (R$)"}),e.jsx("option",{value:"USD",children:"USD ($)"}),e.jsx("option",{value:"EUR",children:"EUR (€)"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Valor Mínimo de Pagamento (R$)"}),e.jsx("input",{type:"number",min:0,step:.01,value:r.revenue.minimumPaymentAmount,onChange:m=>d("revenue","minimumPaymentAmount",parseFloat(m.target.value)),className:"input-field"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Frequência de Repasse"}),e.jsxs("select",{value:r.revenue.payoutFrequency,onChange:m=>d("revenue","payoutFrequency",m.target.value),className:"input-field",children:[e.jsx("option",{value:"daily",children:"Diário"}),e.jsx("option",{value:"weekly",children:"Semanal"}),e.jsx("option",{value:"monthly",children:"Mensal"})]})]})]}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:r.revenue.autoPayoutEnabled,onChange:m=>d("revenue","autoPayoutEnabled",m.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Repasse Automático"})]}),e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Quando ativado, os repasses são feitos automaticamente"})]})]}),l==="notifications"&&e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Notificações"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"p-4 rounded-lg bg-gray-50 dark:bg-gray-700/40",children:[e.jsxs("label",{className:"flex items-center space-x-3 mb-3",children:[e.jsx("input",{type:"checkbox",checked:r.notifications.emailEnabled,onChange:m=>d("notifications","emailEnabled",m.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{children:"Email"})]}),e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:r.notifications.smsEnabled,onChange:m=>d("notifications","smsEnabled",m.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{children:"SMS"})]})]}),e.jsxs("div",{className:"p-4 rounded-lg bg-gray-50 dark:bg-gray-700/40",children:[e.jsxs("label",{className:"flex items-center space-x-3 mb-3",children:[e.jsx("input",{type:"checkbox",checked:r.notifications.webhookEnabled,onChange:m=>d("notifications","webhookEnabled",m.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{children:"Webhook"})]}),e.jsxs("div",{className:"mt-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"URL do Webhook"}),e.jsx("input",{type:"url",placeholder:"https://seuservico.com/webhook",value:r.notifications.webhookUrl||"",onChange:m=>d("notifications","webhookUrl",m.target.value),className:"input-field",disabled:!r.notifications.webhookEnabled})]})]})]})]}),l==="security"&&e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Configurações de Segurança"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Timeout de Sessão (segundos)"}),e.jsx("input",{type:"number",min:"300",value:r.security.sessionTimeout,onChange:m=>d("security","sessionTimeout",parseInt(m.target.value)),className:"input-field"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Máximo de Tentativas de Login"}),e.jsx("input",{type:"number",min:"1",max:"10",value:r.security.maxLoginAttempts,onChange:m=>d("security","maxLoginAttempts",parseInt(m.target.value)),className:"input-field"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Rate Limit (requisições/minuto)"}),e.jsx("input",{type:"number",min:"10",value:r.security.rateLimitRequests,onChange:m=>d("security","rateLimitRequests",parseInt(m.target.value)),className:"input-field"})]})]}),e.jsxs("div",{className:"mt-6 space-y-4",children:[e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:r.security.requireTwoFactor,onChange:m=>d("security","requireTwoFactor",m.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Exigir Autenticação de Dois Fatores"})]}),e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:r.security.rateLimitEnabled,onChange:m=>d("security","rateLimitEnabled",m.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Ativar Rate Limiting"})]})]})]}),l==="integrations"&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Mercado Pago"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Ambiente"}),e.jsxs("select",{value:r.integrations.mercadoPago.environment,onChange:m=>x("integrations","mercadoPago","environment",m.target.value),className:"input-field",children:[e.jsx("option",{value:"sandbox",children:"Sandbox (Teste)"}),e.jsx("option",{value:"production",children:"Produção"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Webhook URL"}),e.jsx("input",{type:"url",value:r.integrations.mercadoPago.webhookUrl,onChange:m=>x("integrations","mercadoPago","webhookUrl",m.target.value),className:"input-field"})]})]}),e.jsx("div",{className:"mt-4",children:e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:r.integrations.mercadoPago.enabled,onChange:m=>x("integrations","mercadoPago","enabled",m.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Ativar Integração Mercado Pago"})]})})]}),e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"YouTube API"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Limite de Quota da API"}),e.jsx("input",{type:"number",min:"1000",value:r.integrations.youtube.apiQuotaLimit,onChange:m=>x("integrations","youtube","apiQuotaLimit",parseInt(m.target.value)),className:"input-field"})]})}),e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:r.integrations.youtube.enabled,onChange:m=>x("integrations","youtube","enabled",m.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Ativar Integração YouTube"})]}),e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:r.integrations.youtube.cacheEnabled,onChange:m=>x("integrations","youtube","cacheEnabled",m.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Ativar Cache de Dados"})]})]})]})]})]})},er=()=>{var c;const[r,t]=u.useState(null),[s,a]=u.useState(!0),[n,i]=u.useState(null);u.useEffect(()=>{l()},[]);const l=async()=>{var h,d;try{a(!0);const[x,p]=await Promise.all([fetch("/api/v1/admin/analytics"),fetch("/api/v1/admin/revenue?period=30d")]),[m,f]=await Promise.all([x.json(),p.json()]);if(m!=null&&m.success&&((h=m.analytics)!=null&&h.overview))t({totalRestaurants:m.analytics.overview.totalRestaurants||0,activeRestaurants:m.analytics.overview.activeRestaurants||0,totalRevenue:m.analytics.overview.totalRevenue||0,monthlyRevenue:m.analytics.overview.monthlyGrowth||0,totalSuggestions:m.analytics.overview.totalSuggestions||0,totalPayments:m.analytics.overview.totalPayments||0,averageRating:m.analytics.overview.averageRating||0,systemUptime:m.analytics.overview.systemUptime||"—"}),f!=null&&f.success&&((d=f.revenue)==null?void 0:d.totalRevenue)!=null&&i({totalRevenue:f.revenue.totalRevenue});else throw new Error("Resposta inválida de /admin/analytics")}catch(x){console.error("Erro ao carregar estatísticas globais:",x),b.error("Erro ao carregar dados do dashboard")}finally{a(!1)}},o=[{title:"Restaurantes Ativos",value:(r==null?void 0:r.activeRestaurants)||"0",total:(r==null?void 0:r.totalRestaurants)||"0",change:"+2 este mês",icon:Ot,color:"text-blue-600 dark:text-blue-400",bgColor:"bg-blue-100 dark:bg-blue-900/20"},{title:"Receita Total",value:`R$ ${((n==null?void 0:n.totalRevenue)??(r==null?void 0:r.totalRevenue)??0).toFixed(2)}`,change:`+R$ ${((c=r==null?void 0:r.monthlyRevenue)==null?void 0:c.toFixed(2))||"0,00"} este mês`,icon:De,color:"text-green-600 dark:text-green-400",bgColor:"bg-green-100 dark:bg-green-900/20"},{title:"Pagamentos (Supervoto)",value:(r==null?void 0:r.totalPayments)||"0",change:"Últimos 30 dias",icon:De,color:"text-purple-600 dark:text-purple-400",bgColor:"bg-purple-100 dark:bg-purple-900/20"},{title:"Uptime do Sistema",value:(r==null?void 0:r.systemUptime)||"—",change:"Últimas 24h",icon:Qe,color:"text-yellow-600 dark:text-yellow-400",bgColor:"bg-yellow-100 dark:bg-yellow-900/20"}];return s?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx(Wt,{})}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Admin Dashboard"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Visão geral do sistema e controle de receitas"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("button",{onClick:l,disabled:s,className:"flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Recarregar dados do dashboard",children:[e.jsx(Qe,{className:`w-4 h-4 ${s?"animate-spin":""}`}),e.jsx("span",{children:"Recarregar"})]}),e.jsxs("button",{onClick:()=>window.location.href="/admin/analytics",className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",children:[e.jsx(vt,{className:"w-4 h-4"}),e.jsx("span",{children:"Ver Analytics"})]})]})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:o.map((h,d)=>e.jsx(q.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:d*.1},className:"card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-1",children:h.title}),e.jsxs("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[h.value,h.total&&e.jsxs("span",{className:"text-sm text-gray-500 ml-1",children:["/ ",h.total]})]}),e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[h.change," vs ontem"]})]}),e.jsx("div",{className:`w-12 h-12 rounded-lg ${h.bgColor} flex items-center justify-center`,children:e.jsx(h.icon,{className:`w-6 h-6 ${h.color}`})})]})},d))}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Transações Recentes (Supervoto)"}),e.jsx("div",{className:"space-y-3",children:[1,2,3,4].map(h=>e.jsxs("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[e.jsx("div",{className:"w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center",children:e.jsx(De,{className:"w-4 h-4 text-green-600 dark:text-green-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:["R$ ",(5*h).toFixed(2)," • Restaurante #",h]}),e.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:["há ",h," min • Supervoto"]})]})]},h))})]}),e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Top Restaurantes por Receita (30d)"}),e.jsx("div",{className:"space-y-3",children:[1,2,3,4].map(h=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-6 h-6 bg-blue-100 dark:bg-blue-900/20 rounded text-xs flex items-center justify-center font-medium",children:h}),e.jsxs("div",{children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:["Restaurante #",h]}),e.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:["ID: demo-",h]})]})]}),e.jsxs("div",{className:"text-sm font-semibold text-gray-900 dark:text-white",children:["R$ ",(1e3*h).toFixed(2)]})]},h))})]})]})]})},Al=()=>e.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[e.jsx("header",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center h-16",children:[e.jsxs($t,{to:"/admin",className:"flex items-center space-x-3 hover:opacity-80 transition-opacity",children:[e.jsx("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:e.jsx(we,{className:"w-5 h-5 text-white"})}),e.jsx("h1",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Restaurant Playlist Admin"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Admin"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center",children:e.jsx(Ft,{className:"w-4 h-4 text-gray-600 dark:text-gray-300"})}),e.jsxs($t,{to:"/",className:"flex items-center space-x-1 px-3 py-2 text-sm text-white bg-red-500 hover:bg-red-600 rounded-lg transition-colors font-medium",title:"Sair do Admin",children:[e.jsx(La,{className:"w-4 h-4"}),e.jsx("span",{children:"Sair"})]})]})]})]})})}),e.jsx("nav",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsx("div",{className:"flex space-x-8",children:[{name:"Dashboard",icon:Fa,path:"/admin/dashboard"},{name:"Restaurantes",icon:Ot,path:"/admin/restaurants"},{name:"Analytics Globais",icon:vt,path:"/admin/analytics"},{name:"Receitas",icon:De,path:"/admin/revenue"},{name:"Configurações",icon:qt,path:"/admin/settings"}].map(r=>e.jsxs($t,{to:r.path,className:"flex items-center space-x-2 py-4 px-1 border-b-2 text-sm font-medium transition-colors border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300",children:[e.jsx(r.icon,{className:"w-4 h-4 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:r.name})]},r.name))})})}),e.jsx("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsxs(Or,{children:[e.jsx(ge,{path:"/",element:e.jsx(er,{})}),e.jsx(ge,{path:"/dashboard",element:e.jsx(er,{})}),e.jsx(ge,{path:"/restaurants",element:e.jsx(El,{})}),e.jsx(ge,{path:"/analytics",element:e.jsx(Rl,{})}),e.jsx(ge,{path:"/revenue",element:e.jsx(Pl,{})}),e.jsx(ge,{path:"/settings",element:e.jsx(Il,{})}),e.jsx(ge,{path:"*",element:e.jsx(er,{})})]})})]}),_l="modulepreload",Tl=function(r){return"/"+r},ya={},dt=function(t,s,a){if(!s||s.length===0)return t();const n=document.getElementsByTagName("link");return Promise.all(s.map(i=>{if(i=Tl(i),i in ya)return;ya[i]=!0;const l=i.endsWith(".css"),o=l?'[rel="stylesheet"]':"";if(!!a)for(let d=n.length-1;d>=0;d--){const x=n[d];if(x.href===i&&(!l||x.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${i}"]${o}`))return;const h=document.createElement("link");if(h.rel=l?"stylesheet":_l,l||(h.as="script",h.crossOrigin=""),h.href=i,document.head.appendChild(h),l)return new Promise((d,x)=>{h.addEventListener("load",d),h.addEventListener("error",()=>x(new Error(`Unable to preload CSS for ${i}`)))})})).then(()=>t()).catch(i=>{const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=i,window.dispatchEvent(l),!l.defaultPrevented)throw i})},mt=Object.create(null);mt.open="0";mt.close="1";mt.ping="2";mt.pong="3";mt.message="4";mt.upgrade="5";mt.noop="6";const Es=Object.create(null);Object.keys(mt).forEach(r=>{Es[mt[r]]=r});const pr={type:"error",data:"parser error"},pn=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",gn=typeof ArrayBuffer=="function",yn=r=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(r):r&&r.buffer instanceof ArrayBuffer,Wr=({type:r,data:t},s,a)=>pn&&t instanceof Blob?s?a(t):ba(t,a):gn&&(t instanceof ArrayBuffer||yn(t))?s?a(t):ba(new Blob([t]),a):a(mt[r]+(t||"")),ba=(r,t)=>{const s=new FileReader;return s.onload=function(){const a=s.result.split(",")[1];t("b"+(a||""))},s.readAsDataURL(r)};function va(r){return r instanceof Uint8Array?r:r instanceof ArrayBuffer?new Uint8Array(r):new Uint8Array(r.buffer,r.byteOffset,r.byteLength)}let tr;function Ol(r,t){if(pn&&r.data instanceof Blob)return r.data.arrayBuffer().then(va).then(t);if(gn&&(r.data instanceof ArrayBuffer||yn(r.data)))return t(va(r.data));Wr(r,!1,s=>{tr||(tr=new TextEncoder),t(tr.encode(s))})}const wa="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Jt=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let r=0;r<wa.length;r++)Jt[wa.charCodeAt(r)]=r;const Vl=r=>{let t=r.length*.75,s=r.length,a,n=0,i,l,o,c;r[r.length-1]==="="&&(t--,r[r.length-2]==="="&&t--);const h=new ArrayBuffer(t),d=new Uint8Array(h);for(a=0;a<s;a+=4)i=Jt[r.charCodeAt(a)],l=Jt[r.charCodeAt(a+1)],o=Jt[r.charCodeAt(a+2)],c=Jt[r.charCodeAt(a+3)],d[n++]=i<<2|l>>4,d[n++]=(l&15)<<4|o>>2,d[n++]=(o&3)<<6|c&63;return h},Dl=typeof ArrayBuffer=="function",Hr=(r,t)=>{if(typeof r!="string")return{type:"message",data:bn(r,t)};const s=r.charAt(0);return s==="b"?{type:"message",data:$l(r.substring(1),t)}:Es[s]?r.length>1?{type:Es[s],data:r.substring(1)}:{type:Es[s]}:pr},$l=(r,t)=>{if(Dl){const s=Vl(r);return bn(s,t)}else return{base64:!0,data:r}},bn=(r,t)=>{switch(t){case"blob":return r instanceof Blob?r:new Blob([r]);case"arraybuffer":default:return r instanceof ArrayBuffer?r:r.buffer}},vn=String.fromCharCode(30),Ll=(r,t)=>{const s=r.length,a=new Array(s);let n=0;r.forEach((i,l)=>{Wr(i,!1,o=>{a[l]=o,++n===s&&t(a.join(vn))})})},Fl=(r,t)=>{const s=r.split(vn),a=[];for(let n=0;n<s.length;n++){const i=Hr(s[n],t);if(a.push(i),i.type==="error")break}return a};function Ul(){return new TransformStream({transform(r,t){Ol(r,s=>{const a=s.length;let n;if(a<126)n=new Uint8Array(1),new DataView(n.buffer).setUint8(0,a);else if(a<65536){n=new Uint8Array(3);const i=new DataView(n.buffer);i.setUint8(0,126),i.setUint16(1,a)}else{n=new Uint8Array(9);const i=new DataView(n.buffer);i.setUint8(0,127),i.setBigUint64(1,BigInt(a))}r.data&&typeof r.data!="string"&&(n[0]|=128),t.enqueue(n),t.enqueue(s)})}})}let sr;function gs(r){return r.reduce((t,s)=>t+s.length,0)}function ys(r,t){if(r[0].length===t)return r.shift();const s=new Uint8Array(t);let a=0;for(let n=0;n<t;n++)s[n]=r[0][a++],a===r[0].length&&(r.shift(),a=0);return r.length&&a<r[0].length&&(r[0]=r[0].slice(a)),s}function Ml(r,t){sr||(sr=new TextDecoder);const s=[];let a=0,n=-1,i=!1;return new TransformStream({transform(l,o){for(s.push(l);;){if(a===0){if(gs(s)<1)break;const c=ys(s,1);i=(c[0]&128)===128,n=c[0]&127,n<126?a=3:n===126?a=1:a=2}else if(a===1){if(gs(s)<2)break;const c=ys(s,2);n=new DataView(c.buffer,c.byteOffset,c.length).getUint16(0),a=3}else if(a===2){if(gs(s)<8)break;const c=ys(s,8),h=new DataView(c.buffer,c.byteOffset,c.length),d=h.getUint32(0);if(d>Math.pow(2,53-32)-1){o.enqueue(pr);break}n=d*Math.pow(2,32)+h.getUint32(4),a=3}else{if(gs(s)<n)break;const c=ys(s,n);o.enqueue(Hr(i?c:sr.decode(c),t)),a=0}if(n===0||n>r){o.enqueue(pr);break}}}})}const wn=4;function Te(r){if(r)return ql(r)}function ql(r){for(var t in Te.prototype)r[t]=Te.prototype[t];return r}Te.prototype.on=Te.prototype.addEventListener=function(r,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+r]=this._callbacks["$"+r]||[]).push(t),this};Te.prototype.once=function(r,t){function s(){this.off(r,s),t.apply(this,arguments)}return s.fn=t,this.on(r,s),this};Te.prototype.off=Te.prototype.removeListener=Te.prototype.removeAllListeners=Te.prototype.removeEventListener=function(r,t){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var s=this._callbacks["$"+r];if(!s)return this;if(arguments.length==1)return delete this._callbacks["$"+r],this;for(var a,n=0;n<s.length;n++)if(a=s[n],a===t||a.fn===t){s.splice(n,1);break}return s.length===0&&delete this._callbacks["$"+r],this};Te.prototype.emit=function(r){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),s=this._callbacks["$"+r],a=1;a<arguments.length;a++)t[a-1]=arguments[a];if(s){s=s.slice(0);for(var a=0,n=s.length;a<n;++a)s[a].apply(this,t)}return this};Te.prototype.emitReserved=Te.prototype.emit;Te.prototype.listeners=function(r){return this._callbacks=this._callbacks||{},this._callbacks["$"+r]||[]};Te.prototype.hasListeners=function(r){return!!this.listeners(r).length};const Ks=(()=>typeof Promise=="function"&&typeof Promise.resolve=="function"?t=>Promise.resolve().then(t):(t,s)=>s(t,0))(),st=(()=>typeof self<"u"?self:typeof window<"u"?window:Function("return this")())(),Bl="arraybuffer";function jn(r,...t){return t.reduce((s,a)=>(r.hasOwnProperty(a)&&(s[a]=r[a]),s),{})}const Ql=st.setTimeout,zl=st.clearTimeout;function Ys(r,t){t.useNativeTimers?(r.setTimeoutFn=Ql.bind(st),r.clearTimeoutFn=zl.bind(st)):(r.setTimeoutFn=st.setTimeout.bind(st),r.clearTimeoutFn=st.clearTimeout.bind(st))}const Wl=1.33;function Hl(r){return typeof r=="string"?Kl(r):Math.ceil((r.byteLength||r.size)*Wl)}function Kl(r){let t=0,s=0;for(let a=0,n=r.length;a<n;a++)t=r.charCodeAt(a),t<128?s+=1:t<2048?s+=2:t<55296||t>=57344?s+=3:(a++,s+=4);return s}function Nn(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}function Yl(r){let t="";for(let s in r)r.hasOwnProperty(s)&&(t.length&&(t+="&"),t+=encodeURIComponent(s)+"="+encodeURIComponent(r[s]));return t}function Jl(r){let t={},s=r.split("&");for(let a=0,n=s.length;a<n;a++){let i=s[a].split("=");t[decodeURIComponent(i[0])]=decodeURIComponent(i[1])}return t}class Gl extends Error{constructor(t,s,a){super(t),this.description=s,this.context=a,this.type="TransportError"}}class Kr extends Te{constructor(t){super(),this.writable=!1,Ys(this,t),this.opts=t,this.query=t.query,this.socket=t.socket,this.supportsBinary=!t.forceBase64}onError(t,s,a){return super.emitReserved("error",new Gl(t,s,a)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(t){this.readyState==="open"&&this.write(t)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(t){const s=Hr(t,this.socket.binaryType);this.onPacket(s)}onPacket(t){super.emitReserved("packet",t)}onClose(t){this.readyState="closed",super.emitReserved("close",t)}pause(t){}createUri(t,s={}){return t+"://"+this._hostname()+this._port()+this.opts.path+this._query(s)}_hostname(){const t=this.opts.hostname;return t.indexOf(":")===-1?t:"["+t+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(t){const s=Yl(t);return s.length?"?"+s:""}}class Xl extends Kr{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(t){this.readyState="pausing";const s=()=>{this.readyState="paused",t()};if(this._polling||!this.writable){let a=0;this._polling&&(a++,this.once("pollComplete",function(){--a||s()})),this.writable||(a++,this.once("drain",function(){--a||s()}))}else s()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(t){const s=a=>{if(this.readyState==="opening"&&a.type==="open"&&this.onOpen(),a.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(a)};Fl(t,this.socket.binaryType).forEach(s),this.readyState!=="closed"&&(this._polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this._poll())}doClose(){const t=()=>{this.write([{type:"close"}])};this.readyState==="open"?t():this.once("open",t)}write(t){this.writable=!1,Ll(t,s=>{this.doWrite(s,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const t=this.opts.secure?"https":"http",s=this.query||{};return this.opts.timestampRequests!==!1&&(s[this.opts.timestampParam]=Nn()),!this.supportsBinary&&!s.sid&&(s.b64=1),this.createUri(t,s)}}let kn=!1;try{kn=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch{}const Zl=kn;function ec(){}class tc extends Xl{constructor(t){if(super(t),typeof location<"u"){const s=location.protocol==="https:";let a=location.port;a||(a=s?"443":"80"),this.xd=typeof location<"u"&&t.hostname!==location.hostname||a!==t.port}}doWrite(t,s){const a=this.request({method:"POST",data:t});a.on("success",s),a.on("error",(n,i)=>{this.onError("xhr post error",n,i)})}doPoll(){const t=this.request();t.on("data",this.onData.bind(this)),t.on("error",(s,a)=>{this.onError("xhr poll error",s,a)}),this.pollXhr=t}}class ht extends Te{constructor(t,s,a){super(),this.createRequest=t,Ys(this,a),this._opts=a,this._method=a.method||"GET",this._uri=s,this._data=a.data!==void 0?a.data:null,this._create()}_create(){var t;const s=jn(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");s.xdomain=!!this._opts.xd;const a=this._xhr=this.createRequest(s);try{a.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){a.setDisableHeaderCheck&&a.setDisableHeaderCheck(!0);for(let n in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(n)&&a.setRequestHeader(n,this._opts.extraHeaders[n])}}catch{}if(this._method==="POST")try{a.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch{}try{a.setRequestHeader("Accept","*/*")}catch{}(t=this._opts.cookieJar)===null||t===void 0||t.addCookies(a),"withCredentials"in a&&(a.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(a.timeout=this._opts.requestTimeout),a.onreadystatechange=()=>{var n;a.readyState===3&&((n=this._opts.cookieJar)===null||n===void 0||n.parseCookies(a.getResponseHeader("set-cookie"))),a.readyState===4&&(a.status===200||a.status===1223?this._onLoad():this.setTimeoutFn(()=>{this._onError(typeof a.status=="number"?a.status:0)},0))},a.send(this._data)}catch(n){this.setTimeoutFn(()=>{this._onError(n)},0);return}typeof document<"u"&&(this._index=ht.requestsCount++,ht.requests[this._index]=this)}_onError(t){this.emitReserved("error",t,this._xhr),this._cleanup(!0)}_cleanup(t){if(!(typeof this._xhr>"u"||this._xhr===null)){if(this._xhr.onreadystatechange=ec,t)try{this._xhr.abort()}catch{}typeof document<"u"&&delete ht.requests[this._index],this._xhr=null}}_onLoad(){const t=this._xhr.responseText;t!==null&&(this.emitReserved("data",t),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}ht.requestsCount=0;ht.requests={};if(typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",ja);else if(typeof addEventListener=="function"){const r="onpagehide"in st?"pagehide":"unload";addEventListener(r,ja,!1)}}function ja(){for(let r in ht.requests)ht.requests.hasOwnProperty(r)&&ht.requests[r].abort()}const sc=function(){const r=Sn({xdomain:!1});return r&&r.responseType!==null}();class rc extends tc{constructor(t){super(t);const s=t&&t.forceBase64;this.supportsBinary=sc&&!s}request(t={}){return Object.assign(t,{xd:this.xd},this.opts),new ht(Sn,this.uri(),t)}}function Sn(r){const t=r.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!t||Zl))return new XMLHttpRequest}catch{}if(!t)try{return new st[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch{}}const Cn=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative";class ac extends Kr{get name(){return"websocket"}doOpen(){const t=this.uri(),s=this.opts.protocols,a=Cn?{}:jn(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(a.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(t,s,a)}catch(n){return this.emitReserved("error",n)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=t=>this.onClose({description:"websocket connection closed",context:t}),this.ws.onmessage=t=>this.onData(t.data),this.ws.onerror=t=>this.onError("websocket error",t)}write(t){this.writable=!1;for(let s=0;s<t.length;s++){const a=t[s],n=s===t.length-1;Wr(a,this.supportsBinary,i=>{try{this.doWrite(a,i)}catch{}n&&Ks(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const t=this.opts.secure?"wss":"ws",s=this.query||{};return this.opts.timestampRequests&&(s[this.opts.timestampParam]=Nn()),this.supportsBinary||(s.b64=1),this.createUri(t,s)}}const rr=st.WebSocket||st.MozWebSocket;class nc extends ac{createSocket(t,s,a){return Cn?new rr(t,s,a):s?new rr(t,s):new rr(t)}doWrite(t,s){this.ws.send(s)}}class ic extends Kr{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(t){return this.emitReserved("error",t)}this._transport.closed.then(()=>{this.onClose()}).catch(t=>{this.onError("webtransport error",t)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(t=>{const s=Ml(Number.MAX_SAFE_INTEGER,this.socket.binaryType),a=t.readable.pipeThrough(s).getReader(),n=Ul();n.readable.pipeTo(t.writable),this._writer=n.writable.getWriter();const i=()=>{a.read().then(({done:o,value:c})=>{o||(this.onPacket(c),i())}).catch(o=>{})};i();const l={type:"open"};this.query.sid&&(l.data=`{"sid":"${this.query.sid}"}`),this._writer.write(l).then(()=>this.onOpen())})})}write(t){this.writable=!1;for(let s=0;s<t.length;s++){const a=t[s],n=s===t.length-1;this._writer.write(a).then(()=>{n&&Ks(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var t;(t=this._transport)===null||t===void 0||t.close()}}const oc={websocket:nc,webtransport:ic,polling:rc},lc=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,cc=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function gr(r){if(r.length>8e3)throw"URI too long";const t=r,s=r.indexOf("["),a=r.indexOf("]");s!=-1&&a!=-1&&(r=r.substring(0,s)+r.substring(s,a).replace(/:/g,";")+r.substring(a,r.length));let n=lc.exec(r||""),i={},l=14;for(;l--;)i[cc[l]]=n[l]||"";return s!=-1&&a!=-1&&(i.source=t,i.host=i.host.substring(1,i.host.length-1).replace(/;/g,":"),i.authority=i.authority.replace("[","").replace("]","").replace(/;/g,":"),i.ipv6uri=!0),i.pathNames=dc(i,i.path),i.queryKey=uc(i,i.query),i}function dc(r,t){const s=/\/{2,9}/g,a=t.replace(s,"/").split("/");return(t.slice(0,1)=="/"||t.length===0)&&a.splice(0,1),t.slice(-1)=="/"&&a.splice(a.length-1,1),a}function uc(r,t){const s={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(a,n,i){n&&(s[n]=i)}),s}const yr=typeof addEventListener=="function"&&typeof removeEventListener=="function",Rs=[];yr&&addEventListener("offline",()=>{Rs.forEach(r=>r())},!1);class Ct extends Te{constructor(t,s){if(super(),this.binaryType=Bl,this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,t&&typeof t=="object"&&(s=t,t=null),t){const a=gr(t);s.hostname=a.host,s.secure=a.protocol==="https"||a.protocol==="wss",s.port=a.port,a.query&&(s.query=a.query)}else s.host&&(s.hostname=gr(s.host).host);Ys(this,s),this.secure=s.secure!=null?s.secure:typeof location<"u"&&location.protocol==="https:",s.hostname&&!s.port&&(s.port=this.secure?"443":"80"),this.hostname=s.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=s.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},s.transports.forEach(a=>{const n=a.prototype.name;this.transports.push(n),this._transportsByName[n]=a}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},s),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=Jl(this.opts.query)),yr&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},Rs.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(t){const s=Object.assign({},this.opts.query);s.EIO=wn,s.transport=t,this.id&&(s.sid=this.id);const a=Object.assign({},this.opts,{query:s,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[t]);return new this._transportsByName[t](a)}_open(){if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}const t=this.opts.rememberUpgrade&&Ct.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1?"websocket":this.transports[0];this.readyState="opening";const s=this.createTransport(t);s.open(),this.setTransport(s)}setTransport(t){this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",s=>this._onClose("transport close",s))}onOpen(){this.readyState="open",Ct.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush()}_onPacket(t){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const s=new Error("server error");s.code=t.data,this._onError(s);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data);break}}onHandshake(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this._pingInterval=t.pingInterval,this._pingTimeout=t.pingTimeout,this._maxPayload=t.maxPayload,this.onOpen(),this.readyState!=="closed"&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const t=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+t,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},t),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const t=this._getWritablePackets();this.transport.send(t),this._prevBufferLen=t.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let s=1;for(let a=0;a<this.writeBuffer.length;a++){const n=this.writeBuffer[a].data;if(n&&(s+=Hl(n)),a>0&&s>this._maxPayload)return this.writeBuffer.slice(0,a);s+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const t=Date.now()>this._pingTimeoutTime;return t&&(this._pingTimeoutTime=0,Ks(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),t}write(t,s,a){return this._sendPacket("message",t,s,a),this}send(t,s,a){return this._sendPacket("message",t,s,a),this}_sendPacket(t,s,a,n){if(typeof s=="function"&&(n=s,s=void 0),typeof a=="function"&&(n=a,a=null),this.readyState==="closing"||this.readyState==="closed")return;a=a||{},a.compress=a.compress!==!1;const i={type:t,data:s,options:a};this.emitReserved("packetCreate",i),this.writeBuffer.push(i),n&&this.once("flush",n),this.flush()}close(){const t=()=>{this._onClose("forced close"),this.transport.close()},s=()=>{this.off("upgrade",s),this.off("upgradeError",s),t()},a=()=>{this.once("upgrade",s),this.once("upgradeError",s)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?a():t()}):this.upgrading?a():t()),this}_onError(t){if(Ct.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&this.readyState==="opening")return this.transports.shift(),this._open();this.emitReserved("error",t),this._onClose("transport error",t)}_onClose(t,s){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing"){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),yr&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const a=Rs.indexOf(this._offlineEventListener);a!==-1&&Rs.splice(a,1)}this.readyState="closed",this.id=null,this.emitReserved("close",t,s),this.writeBuffer=[],this._prevBufferLen=0}}}Ct.protocol=wn;class hc extends Ct{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),this.readyState==="open"&&this.opts.upgrade)for(let t=0;t<this._upgrades.length;t++)this._probe(this._upgrades[t])}_probe(t){let s=this.createTransport(t),a=!1;Ct.priorWebsocketSuccess=!1;const n=()=>{a||(s.send([{type:"ping",data:"probe"}]),s.once("packet",x=>{if(!a)if(x.type==="pong"&&x.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",s),!s)return;Ct.priorWebsocketSuccess=s.name==="websocket",this.transport.pause(()=>{a||this.readyState!=="closed"&&(d(),this.setTransport(s),s.send([{type:"upgrade"}]),this.emitReserved("upgrade",s),s=null,this.upgrading=!1,this.flush())})}else{const p=new Error("probe error");p.transport=s.name,this.emitReserved("upgradeError",p)}}))};function i(){a||(a=!0,d(),s.close(),s=null)}const l=x=>{const p=new Error("probe error: "+x);p.transport=s.name,i(),this.emitReserved("upgradeError",p)};function o(){l("transport closed")}function c(){l("socket closed")}function h(x){s&&x.name!==s.name&&i()}const d=()=>{s.removeListener("open",n),s.removeListener("error",l),s.removeListener("close",o),this.off("close",c),this.off("upgrading",h)};s.once("open",n),s.once("error",l),s.once("close",o),this.once("close",c),this.once("upgrading",h),this._upgrades.indexOf("webtransport")!==-1&&t!=="webtransport"?this.setTimeoutFn(()=>{a||s.open()},200):s.open()}onHandshake(t){this._upgrades=this._filterUpgrades(t.upgrades),super.onHandshake(t)}_filterUpgrades(t){const s=[];for(let a=0;a<t.length;a++)~this.transports.indexOf(t[a])&&s.push(t[a]);return s}}let mc=class extends hc{constructor(t,s={}){const a=typeof t=="object"?t:s;(!a.transports||a.transports&&typeof a.transports[0]=="string")&&(a.transports=(a.transports||["polling","websocket","webtransport"]).map(n=>oc[n]).filter(n=>!!n)),super(t,a)}};function xc(r,t="",s){let a=r;s=s||typeof location<"u"&&location,r==null&&(r=s.protocol+"//"+s.host),typeof r=="string"&&(r.charAt(0)==="/"&&(r.charAt(1)==="/"?r=s.protocol+r:r=s.host+r),/^(https?|wss?):\/\//.test(r)||(typeof s<"u"?r=s.protocol+"//"+r:r="https://"+r),a=gr(r)),a.port||(/^(http|ws)$/.test(a.protocol)?a.port="80":/^(http|ws)s$/.test(a.protocol)&&(a.port="443")),a.path=a.path||"/";const i=a.host.indexOf(":")!==-1?"["+a.host+"]":a.host;return a.id=a.protocol+"://"+i+":"+a.port+t,a.href=a.protocol+"://"+i+(s&&s.port===a.port?"":":"+a.port),a}const fc=typeof ArrayBuffer=="function",pc=r=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(r):r.buffer instanceof ArrayBuffer,En=Object.prototype.toString,gc=typeof Blob=="function"||typeof Blob<"u"&&En.call(Blob)==="[object BlobConstructor]",yc=typeof File=="function"||typeof File<"u"&&En.call(File)==="[object FileConstructor]";function Yr(r){return fc&&(r instanceof ArrayBuffer||pc(r))||gc&&r instanceof Blob||yc&&r instanceof File}function Ps(r,t){if(!r||typeof r!="object")return!1;if(Array.isArray(r)){for(let s=0,a=r.length;s<a;s++)if(Ps(r[s]))return!0;return!1}if(Yr(r))return!0;if(r.toJSON&&typeof r.toJSON=="function"&&arguments.length===1)return Ps(r.toJSON(),!0);for(const s in r)if(Object.prototype.hasOwnProperty.call(r,s)&&Ps(r[s]))return!0;return!1}function bc(r){const t=[],s=r.data,a=r;return a.data=br(s,t),a.attachments=t.length,{packet:a,buffers:t}}function br(r,t){if(!r)return r;if(Yr(r)){const s={_placeholder:!0,num:t.length};return t.push(r),s}else if(Array.isArray(r)){const s=new Array(r.length);for(let a=0;a<r.length;a++)s[a]=br(r[a],t);return s}else if(typeof r=="object"&&!(r instanceof Date)){const s={};for(const a in r)Object.prototype.hasOwnProperty.call(r,a)&&(s[a]=br(r[a],t));return s}return r}function vc(r,t){return r.data=vr(r.data,t),delete r.attachments,r}function vr(r,t){if(!r)return r;if(r&&r._placeholder===!0){if(typeof r.num=="number"&&r.num>=0&&r.num<t.length)return t[r.num];throw new Error("illegal attachments")}else if(Array.isArray(r))for(let s=0;s<r.length;s++)r[s]=vr(r[s],t);else if(typeof r=="object")for(const s in r)Object.prototype.hasOwnProperty.call(r,s)&&(r[s]=vr(r[s],t));return r}const wc=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],jc=5;var fe;(function(r){r[r.CONNECT=0]="CONNECT",r[r.DISCONNECT=1]="DISCONNECT",r[r.EVENT=2]="EVENT",r[r.ACK=3]="ACK",r[r.CONNECT_ERROR=4]="CONNECT_ERROR",r[r.BINARY_EVENT=5]="BINARY_EVENT",r[r.BINARY_ACK=6]="BINARY_ACK"})(fe||(fe={}));class Nc{constructor(t){this.replacer=t}encode(t){return(t.type===fe.EVENT||t.type===fe.ACK)&&Ps(t)?this.encodeAsBinary({type:t.type===fe.EVENT?fe.BINARY_EVENT:fe.BINARY_ACK,nsp:t.nsp,data:t.data,id:t.id}):[this.encodeAsString(t)]}encodeAsString(t){let s=""+t.type;return(t.type===fe.BINARY_EVENT||t.type===fe.BINARY_ACK)&&(s+=t.attachments+"-"),t.nsp&&t.nsp!=="/"&&(s+=t.nsp+","),t.id!=null&&(s+=t.id),t.data!=null&&(s+=JSON.stringify(t.data,this.replacer)),s}encodeAsBinary(t){const s=bc(t),a=this.encodeAsString(s.packet),n=s.buffers;return n.unshift(a),n}}function Na(r){return Object.prototype.toString.call(r)==="[object Object]"}class Jr extends Te{constructor(t){super(),this.reviver=t}add(t){let s;if(typeof t=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");s=this.decodeString(t);const a=s.type===fe.BINARY_EVENT;a||s.type===fe.BINARY_ACK?(s.type=a?fe.EVENT:fe.ACK,this.reconstructor=new kc(s),s.attachments===0&&super.emitReserved("decoded",s)):super.emitReserved("decoded",s)}else if(Yr(t)||t.base64)if(this.reconstructor)s=this.reconstructor.takeBinaryData(t),s&&(this.reconstructor=null,super.emitReserved("decoded",s));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+t)}decodeString(t){let s=0;const a={type:Number(t.charAt(0))};if(fe[a.type]===void 0)throw new Error("unknown packet type "+a.type);if(a.type===fe.BINARY_EVENT||a.type===fe.BINARY_ACK){const i=s+1;for(;t.charAt(++s)!=="-"&&s!=t.length;);const l=t.substring(i,s);if(l!=Number(l)||t.charAt(s)!=="-")throw new Error("Illegal attachments");a.attachments=Number(l)}if(t.charAt(s+1)==="/"){const i=s+1;for(;++s&&!(t.charAt(s)===","||s===t.length););a.nsp=t.substring(i,s)}else a.nsp="/";const n=t.charAt(s+1);if(n!==""&&Number(n)==n){const i=s+1;for(;++s;){const l=t.charAt(s);if(l==null||Number(l)!=l){--s;break}if(s===t.length)break}a.id=Number(t.substring(i,s+1))}if(t.charAt(++s)){const i=this.tryParse(t.substr(s));if(Jr.isPayloadValid(a.type,i))a.data=i;else throw new Error("invalid payload")}return a}tryParse(t){try{return JSON.parse(t,this.reviver)}catch{return!1}}static isPayloadValid(t,s){switch(t){case fe.CONNECT:return Na(s);case fe.DISCONNECT:return s===void 0;case fe.CONNECT_ERROR:return typeof s=="string"||Na(s);case fe.EVENT:case fe.BINARY_EVENT:return Array.isArray(s)&&(typeof s[0]=="number"||typeof s[0]=="string"&&wc.indexOf(s[0])===-1);case fe.ACK:case fe.BINARY_ACK:return Array.isArray(s)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class kc{constructor(t){this.packet=t,this.buffers=[],this.reconPack=t}takeBinaryData(t){if(this.buffers.push(t),this.buffers.length===this.reconPack.attachments){const s=vc(this.reconPack,this.buffers);return this.finishedReconstruction(),s}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}const Sc=Object.freeze(Object.defineProperty({__proto__:null,Decoder:Jr,Encoder:Nc,get PacketType(){return fe},protocol:jc},Symbol.toStringTag,{value:"Module"}));function lt(r,t,s){return r.on(t,s),function(){r.off(t,s)}}const Cc=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class Rn extends Te{constructor(t,s,a){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=t,this.nsp=s,a&&a.auth&&(this.auth=a.auth),this._opts=Object.assign({},a),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const t=this.io;this.subs=[lt(t,"open",this.onopen.bind(this)),lt(t,"packet",this.onpacket.bind(this)),lt(t,"error",this.onerror.bind(this)),lt(t,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(...t){return t.unshift("message"),this.emit.apply(this,t),this}emit(t,...s){var a,n,i;if(Cc.hasOwnProperty(t))throw new Error('"'+t.toString()+'" is a reserved event name');if(s.unshift(t),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(s),this;const l={type:fe.EVENT,data:s};if(l.options={},l.options.compress=this.flags.compress!==!1,typeof s[s.length-1]=="function"){const d=this.ids++,x=s.pop();this._registerAckCallback(d,x),l.id=d}const o=(n=(a=this.io.engine)===null||a===void 0?void 0:a.transport)===null||n===void 0?void 0:n.writable,c=this.connected&&!(!((i=this.io.engine)===null||i===void 0)&&i._hasPingExpired());return this.flags.volatile&&!o||(c?(this.notifyOutgoingListeners(l),this.packet(l)):this.sendBuffer.push(l)),this.flags={},this}_registerAckCallback(t,s){var a;const n=(a=this.flags.timeout)!==null&&a!==void 0?a:this._opts.ackTimeout;if(n===void 0){this.acks[t]=s;return}const i=this.io.setTimeoutFn(()=>{delete this.acks[t];for(let o=0;o<this.sendBuffer.length;o++)this.sendBuffer[o].id===t&&this.sendBuffer.splice(o,1);s.call(this,new Error("operation has timed out"))},n),l=(...o)=>{this.io.clearTimeoutFn(i),s.apply(this,o)};l.withError=!0,this.acks[t]=l}emitWithAck(t,...s){return new Promise((a,n)=>{const i=(l,o)=>l?n(l):a(o);i.withError=!0,s.push(i),this.emit(t,...s)})}_addToQueue(t){let s;typeof t[t.length-1]=="function"&&(s=t.pop());const a={id:this._queueSeq++,tryCount:0,pending:!1,args:t,flags:Object.assign({fromQueue:!0},this.flags)};t.push((n,...i)=>a!==this._queue[0]?void 0:(n!==null?a.tryCount>this._opts.retries&&(this._queue.shift(),s&&s(n)):(this._queue.shift(),s&&s(null,...i)),a.pending=!1,this._drainQueue())),this._queue.push(a),this._drainQueue()}_drainQueue(t=!1){if(!this.connected||this._queue.length===0)return;const s=this._queue[0];s.pending&&!t||(s.pending=!0,s.tryCount++,this.flags=s.flags,this.emit.apply(this,s.args))}packet(t){t.nsp=this.nsp,this.io._packet(t)}onopen(){typeof this.auth=="function"?this.auth(t=>{this._sendConnectPacket(t)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(t){this.packet({type:fe.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},t):t})}onerror(t){this.connected||this.emitReserved("connect_error",t)}onclose(t,s){this.connected=!1,delete this.id,this.emitReserved("disconnect",t,s),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(t=>{if(!this.sendBuffer.some(a=>String(a.id)===t)){const a=this.acks[t];delete this.acks[t],a.withError&&a.call(this,new Error("socket has been disconnected"))}})}onpacket(t){if(t.nsp===this.nsp)switch(t.type){case fe.CONNECT:t.data&&t.data.sid?this.onconnect(t.data.sid,t.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case fe.EVENT:case fe.BINARY_EVENT:this.onevent(t);break;case fe.ACK:case fe.BINARY_ACK:this.onack(t);break;case fe.DISCONNECT:this.ondisconnect();break;case fe.CONNECT_ERROR:this.destroy();const a=new Error(t.data.message);a.data=t.data.data,this.emitReserved("connect_error",a);break}}onevent(t){const s=t.data||[];t.id!=null&&s.push(this.ack(t.id)),this.connected?this.emitEvent(s):this.receiveBuffer.push(Object.freeze(s))}emitEvent(t){if(this._anyListeners&&this._anyListeners.length){const s=this._anyListeners.slice();for(const a of s)a.apply(this,t)}super.emit.apply(this,t),this._pid&&t.length&&typeof t[t.length-1]=="string"&&(this._lastOffset=t[t.length-1])}ack(t){const s=this;let a=!1;return function(...n){a||(a=!0,s.packet({type:fe.ACK,id:t,data:n}))}}onack(t){const s=this.acks[t.id];typeof s=="function"&&(delete this.acks[t.id],s.withError&&t.data.unshift(null),s.apply(this,t.data))}onconnect(t,s){this.id=t,this.recovered=s&&this._pid===s,this._pid=s,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(t=>this.emitEvent(t)),this.receiveBuffer=[],this.sendBuffer.forEach(t=>{this.notifyOutgoingListeners(t),this.packet(t)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(t=>t()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:fe.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(t){return this.flags.compress=t,this}get volatile(){return this.flags.volatile=!0,this}timeout(t){return this.flags.timeout=t,this}onAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(t),this}prependAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(t),this}offAny(t){if(!this._anyListeners)return this;if(t){const s=this._anyListeners;for(let a=0;a<s.length;a++)if(t===s[a])return s.splice(a,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(t),this}prependAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(t),this}offAnyOutgoing(t){if(!this._anyOutgoingListeners)return this;if(t){const s=this._anyOutgoingListeners;for(let a=0;a<s.length;a++)if(t===s[a])return s.splice(a,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(t){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const s=this._anyOutgoingListeners.slice();for(const a of s)a.apply(this,t.data)}}}function Ht(r){r=r||{},this.ms=r.min||100,this.max=r.max||1e4,this.factor=r.factor||2,this.jitter=r.jitter>0&&r.jitter<=1?r.jitter:0,this.attempts=0}Ht.prototype.duration=function(){var r=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),s=Math.floor(t*this.jitter*r);r=Math.floor(t*10)&1?r+s:r-s}return Math.min(r,this.max)|0};Ht.prototype.reset=function(){this.attempts=0};Ht.prototype.setMin=function(r){this.ms=r};Ht.prototype.setMax=function(r){this.max=r};Ht.prototype.setJitter=function(r){this.jitter=r};class wr extends Te{constructor(t,s){var a;super(),this.nsps={},this.subs=[],t&&typeof t=="object"&&(s=t,t=void 0),s=s||{},s.path=s.path||"/socket.io",this.opts=s,Ys(this,s),this.reconnection(s.reconnection!==!1),this.reconnectionAttempts(s.reconnectionAttempts||1/0),this.reconnectionDelay(s.reconnectionDelay||1e3),this.reconnectionDelayMax(s.reconnectionDelayMax||5e3),this.randomizationFactor((a=s.randomizationFactor)!==null&&a!==void 0?a:.5),this.backoff=new Ht({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(s.timeout==null?2e4:s.timeout),this._readyState="closed",this.uri=t;const n=s.parser||Sc;this.encoder=new n.Encoder,this.decoder=new n.Decoder,this._autoConnect=s.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(t){return arguments.length?(this._reconnection=!!t,t||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(t){return t===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=t,this)}reconnectionDelay(t){var s;return t===void 0?this._reconnectionDelay:(this._reconnectionDelay=t,(s=this.backoff)===null||s===void 0||s.setMin(t),this)}randomizationFactor(t){var s;return t===void 0?this._randomizationFactor:(this._randomizationFactor=t,(s=this.backoff)===null||s===void 0||s.setJitter(t),this)}reconnectionDelayMax(t){var s;return t===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=t,(s=this.backoff)===null||s===void 0||s.setMax(t),this)}timeout(t){return arguments.length?(this._timeout=t,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(t){if(~this._readyState.indexOf("open"))return this;this.engine=new mc(this.uri,this.opts);const s=this.engine,a=this;this._readyState="opening",this.skipReconnect=!1;const n=lt(s,"open",function(){a.onopen(),t&&t()}),i=o=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",o),t?t(o):this.maybeReconnectOnOpen()},l=lt(s,"error",i);if(this._timeout!==!1){const o=this._timeout,c=this.setTimeoutFn(()=>{n(),i(new Error("timeout")),s.close()},o);this.opts.autoUnref&&c.unref(),this.subs.push(()=>{this.clearTimeoutFn(c)})}return this.subs.push(n),this.subs.push(l),this}connect(t){return this.open(t)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const t=this.engine;this.subs.push(lt(t,"ping",this.onping.bind(this)),lt(t,"data",this.ondata.bind(this)),lt(t,"error",this.onerror.bind(this)),lt(t,"close",this.onclose.bind(this)),lt(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(t){try{this.decoder.add(t)}catch(s){this.onclose("parse error",s)}}ondecoded(t){Ks(()=>{this.emitReserved("packet",t)},this.setTimeoutFn)}onerror(t){this.emitReserved("error",t)}socket(t,s){let a=this.nsps[t];return a?this._autoConnect&&!a.active&&a.connect():(a=new Rn(this,t,s),this.nsps[t]=a),a}_destroy(t){const s=Object.keys(this.nsps);for(const a of s)if(this.nsps[a].active)return;this._close()}_packet(t){const s=this.encoder.encode(t);for(let a=0;a<s.length;a++)this.engine.write(s[a],t.options)}cleanup(){this.subs.forEach(t=>t()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(t,s){var a;this.cleanup(),(a=this.engine)===null||a===void 0||a.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",t,s),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const t=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const s=this.backoff.duration();this._reconnecting=!0;const a=this.setTimeoutFn(()=>{t.skipReconnect||(this.emitReserved("reconnect_attempt",t.backoff.attempts),!t.skipReconnect&&t.open(n=>{n?(t._reconnecting=!1,t.reconnect(),this.emitReserved("reconnect_error",n)):t.onreconnect()}))},s);this.opts.autoUnref&&a.unref(),this.subs.push(()=>{this.clearTimeoutFn(a)})}}onreconnect(){const t=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",t)}}const Kt={};function Is(r,t){typeof r=="object"&&(t=r,r=void 0),t=t||{};const s=xc(r,t.path||"/socket.io"),a=s.source,n=s.id,i=s.path,l=Kt[n]&&i in Kt[n].nsps,o=t.forceNew||t["force new connection"]||t.multiplex===!1||l;let c;return o?c=new wr(a,t):(Kt[n]||(Kt[n]=new wr(a,t)),c=Kt[n]),s.query&&!t.query&&(t.query=s.queryKey),c.socket(s.path,t)}Object.assign(Is,{Manager:wr,Socket:Rn,io:Is,connect:Is});function Ec(){const r=window.location.hostname==="localhost"||window.location.hostname==="127.0.0.1",t="http://localhost:8001";return{wsUrl:(t&&t.trim()!==""?t:r?"http://localhost:8001":window.location.origin).replace(/\/$/,""),isLocal:r}}var Aa,_a;const Rc=((_a=(Aa=import.meta)==null?void 0:Aa.env)==null?void 0:_a.DEV)??!1;function bs(r,...t){var a;if(!Rc&&r==="debug")return;const s=`[WS:${r.toUpperCase()}]`;(a=console[r==="debug"?"log":r])==null||a.call(console,s,...t)}const Ve={debug:(...r)=>bs("debug",...r),info:(...r)=>bs("info",...r),warn:(...r)=>bs("warn",...r),error:(...r)=>bs("error",...r)};class Pc{constructor(){Ce(this,"socket",null);Ce(this,"connectionStatus","disconnected");Ce(this,"reconnectAttempts",0);Ce(this,"maxReconnectAttempts",5);Ce(this,"listeners",new Map);Ce(this,"statusListeners",new Set);Ce(this,"desiredRestaurantId",null);Ce(this,"joinedRestaurantId",null);Ce(this,"reconnectTimer",null);Ce(this,"lastWsUrl",null);Ce(this,"lastAuthToken",null);Ce(this,"storageListenerAttached",!1);Ce(this,"reconnecting",!1);Ce(this,"eventAliases",{newSuggestion:"new-suggestion",voteUpdate:"vote-update",queueUpdate:"queue-update",queue_updated:"queue-update","playback-state-changed":"playback-state-update",track_ended:"song-ended"});try{this.desiredRestaurantId=typeof localStorage<"u"?localStorage.getItem("currentRestaurantId"):null}catch{}this.connect()}getWsConfig(){return Ec()}connect(){const{wsUrl:t,isLocal:s}=this.getWsConfig();this.setConnectionStatus("connecting");try{const a=this.getAuthToken();this.lastWsUrl=t,this.lastAuthToken=a,this.socket=Is(t,{transports:["websocket"],timeout:2e4,reconnection:!0,reconnectionAttempts:1/0,reconnectionDelay:1e3,reconnectionDelayMax:5e3,randomizationFactor:.5,autoConnect:!0,auth:a?{token:a,restaurantId:this.desiredRestaurantId||void 0}:void 0,withCredentials:!0}),this.setupEventListeners()}catch(a){console.warn("WebSocket não disponível:",a),this.setConnectionStatus("disconnected")}}setupEventListeners(){this.socket&&(this.socket.on("connect",()=>{Ve.info("✅ WebSocket conectado"),this.setConnectionStatus("connected"),this.reconnectAttempts=0,this.ensureJoinedRestaurant()}),this.socket.on("disconnect",t=>{var s;Ve.warn("❌ WebSocket desconectado:",t),this.setConnectionStatus("disconnected"),t==="io server disconnect"&&((s=this.socket)==null||s.connect()),this.joinedRestaurantId=null}),this.socket.on("connect_error",t=>{Ve.warn("WebSocket connection failed:",t.message),this.setConnectionStatus("error"),this.reconnectAttempts++}),this.socket.on("reconnect",t=>{Ve.info(`🔄 WebSocket reconectado após ${t} tentativas`),this.setConnectionStatus("connected"),b.success("Reconectado ao servidor")}),this.socket.on("reconnect_attempt",t=>{Ve.debug(`🔄 Tentativa de reconexão ${t}`),this.setConnectionStatus("connecting")}),this.socket.on("reconnect_error",t=>{Ve.error("❌ Erro na reconexão:",t),this.setConnectionStatus("error")}),this.socket.on("reconnect_failed",()=>{Ve.error("❌ Falha na reconexão após máximo de tentativas"),this.setConnectionStatus("error"),b.error("Falha na conexão com o servidor")}),this.setupApplicationEvents(),this.storageListenerAttached||(window.addEventListener("storage",t=>{t.key==="authToken"&&this.reconnect(),t.key==="currentRestaurantId"&&(this.desiredRestaurantId=t.newValue,this.connectionStatus==="connected"&&this.ensureJoinedRestaurant())}),this.storageListenerAttached=!0))}setupApplicationEvents(){if(this.socket){this.socket.on("notification",t=>{this.emit("notification",t)}),this.socket.on("joined-restaurant",t=>{t&&(this.joinedRestaurantId=t)}),this.socket.on("left-restaurant",t=>{this.joinedRestaurantId===t&&(this.joinedRestaurantId=null)});for(const[t,s]of Object.entries(this.eventAliases))this.socket.on(t,a=>{this.emit(s,a)});this.socket.on("new-suggestion",t=>{!t||!("title"in t)||(Ve.info("🎵 Nova sugestão recebida:",t),this.emit("new-suggestion",t),b.success(`Nova música sugerida: ${t.title}`,{duration:4e3}))}),this.socket.on("vote-update",t=>{t&&(Ve.debug("👍 Atualização de votos:",t),this.emit("vote-update",t))}),this.socket.on("queue-update",t=>{t&&(Ve.debug("📋 Fila atualizada:",t),this.emit("queue-update",t))}),this.socket.on("playback-state-update",t=>{Ve.debug("🎚️ Estado do player atualizado:",t),this.emit("playback-state-update",t)}),this.socket.on("now-playing",t=>{Ve.info("🎵 Tocando agora:",t),this.emit("now-playing",t),b(`Tocando agora: ${t.suggestion.title}`,{icon:"🎵",duration:6e3})}),this.socket.on("playbackStart",t=>{Ve.info("▶️ (camelCase) Início de reprodução:",t),this.emit("now-playing",{suggestion:t.track})}),this.socket.on("playlistReordered",t=>{console.log("🔄 Playlist reordenada:",t),this.emit("playlistReordered",t)}),this.socket.on("playback_started",t=>{var n;Ve.info("▶️ (snake) Início de reprodução:",t);const s=(t==null?void 0:t.track)||((n=t==null?void 0:t.state)==null?void 0:n.currentTrack)||t,a={id:s==null?void 0:s.id,title:s==null?void 0:s.title,artist:s==null?void 0:s.artist,youtubeId:(s==null?void 0:s.youtubeVideoId)||(s==null?void 0:s.youtubeId),thumbnailUrl:s==null?void 0:s.thumbnailUrl,duration:s==null?void 0:s.duration};this.emit("now-playing",a)}),this.socket.on("playlistReorderedAdmin",t=>{console.log("🔄 (admin) Playlist reordenada (admin):",t),this.emit("playlistReordered",t),this.emit("playlistReorderedAdmin",t)}),this.socket.on("ranking-snapshot",t=>{console.log("📸 Snapshot de ranking:",t),this.emit("ranking-snapshot",t)}),this.socket.on("reorderSelected",t=>{try{Ve.info("🎯 Próxima selecionada (server):",t),this.emit("reorderSelected",t)}catch(s){console.warn("Falha ao tratar reorderSelected:",s)}}),this.socket.on("reorder-selected",t=>{try{Ve.info("🎯 (kebab) Próxima selecionada (server):",t),this.emit("reorderSelected",t)}catch{}}),this.socket.on("song-ended",t=>{Ve.info("⏭️ Música finalizada:",t),this.emit("song-ended",t)}),this.socket.on("superVoteReceived",t=>{Ve.info("⭐ SuperVoto recebido:",t),this.emit("superVoteReceived",t)})}}getAuthToken(){try{return typeof localStorage<"u"?localStorage.getItem("authToken"):null}catch{return null}}setConnectionStatus(t){this.connectionStatus=t,this.statusListeners.forEach(s=>s(t))}offConnectionStatusChange(t){this.statusListeners.delete(t)}joinRestaurant(t){this.desiredRestaurantId=t,this.ensureJoinedRestaurant()}leaveRestaurant(t){this.socket&&this.connectionStatus==="connected"&&(this.socket.emit("leave-restaurant",t),console.log(`🚪 Saiu da sala do restaurante: ${t}`)),this.desiredRestaurantId===t&&(this.desiredRestaurantId=null),this.joinedRestaurantId===t&&(this.joinedRestaurantId=null)}on(t,s){this.listeners.has(t)||this.listeners.set(t,new Set);const a=this.listeners.get(t);a.size>100&&Ve.warn("Muitos listeners no evento",String(t),a.size),a.add(s)}off(t,s){const a=this.listeners.get(t);a&&a.delete(s)}emit(t,s){const a=this.listeners.get(t);a&&a.forEach(n=>{try{n(s)}catch(i){console.warn("Erro em listener de evento WS",t,i)}})}publicEmit(t,s){if(!(!this.socket||this.connectionStatus!=="connected"))try{this.socket.emit(t,s)}catch(a){console.warn("Falha ao emitir evento via WS:",t,a)}}onConnectionStatusChange(t){return this.statusListeners.add(t),()=>{this.statusListeners.delete(t)}}subscribe(t,s){return this.on(t,s),()=>this.off(t,s)}getConnectionStatus(){return this.connectionStatus}isConnected(){return this.connectionStatus==="connected"}onConnectionChange(t){this.onConnectionStatusChange(s=>{t(s==="connected")})}reconnect(){this.reconnecting||(this.reconnecting=!0,this.reconnectTimer&&(window.clearTimeout(this.reconnectTimer),this.reconnectTimer=null),this.reconnectTimer=window.setTimeout(()=>{this.reconnectTimer=null;const{wsUrl:t}=this.getWsConfig(),s=this.getAuthToken(),a=this.lastWsUrl===t,n=this.lastAuthToken===s;if(this.socket&&a&&n)if(this.socket.connected)this.ensureJoinedRestaurant();else try{this.socket.connect()}catch{}else this.safeTeardown(),this.connect();this.reconnecting=!1},300))}disconnect(){this.safeTeardown(),this.setConnectionStatus("disconnected")}removeAllListeners(){this.listeners.clear(),this.statusListeners.clear()}safeTeardown(){var t,s;if(this.socket){try{(s=(t=this.socket).removeAllListeners)==null||s.call(t)}catch{}try{this.socket.disconnect()}catch{}this.socket=null}this.joinedRestaurantId=null}ensureJoinedRestaurant(){if(!this.socket||this.connectionStatus!=="connected")return;const t=this.desiredRestaurantId;if(!(!t||this.joinedRestaurantId===t))try{const s=this.getAuthToken();s&&this.socket.emit("authenticate",{token:s,restaurantId:t}),this.socket.emit("join-restaurant",t),this.joinedRestaurantId=t,console.log(`🏪 Entrou na sala do restaurante: ${t}`)}catch(s){console.warn("Falha ao entrar na sala do restaurante via WS:",s)}}getDebugInfo(){var t,s,a,n,i;return{connected:this.isConnected(),status:this.connectionStatus,reconnectAttempts:this.reconnectAttempts,socketId:(t=this.socket)==null?void 0:t.id,transport:(i=(n=(a=(s=this.socket)==null?void 0:s.io)==null?void 0:a.engine)==null?void 0:n.transport)==null?void 0:i.name,listenersCount:Array.from(this.listeners.entries()).reduce((l,[o,c])=>({...l,[o]:c.size}),{})}}}const Ae=new Pc,Pt=r=>{const t=u.useSyncExternalStore(a=>Ae.onConnectionStatusChange(a),()=>Ae.getConnectionStatus(),()=>"disconnected"),s=t==="connected";return u.useEffect(()=>{if(r)return Ae.joinRestaurant(r),()=>{Ae.leaveRestaurant(r)}},[r]),u.useMemo(()=>({service:Ae,isConnected:s,status:t,joinRestaurant:Ae.joinRestaurant.bind(Ae),leaveRestaurant:Ae.leaveRestaurant.bind(Ae),on:Ae.on.bind(Ae),off:Ae.off.bind(Ae),reconnect:Ae.reconnect.bind(Ae),emit:Ae.publicEmit.bind(Ae),onConnectionStatusChange:Ae.onConnectionStatusChange.bind(Ae)}),[t,s,r])},Ic=Object.freeze(Object.defineProperty({__proto__:null,default:Ae,useWebSocket:Pt,wsService:Ae},Symbol.toStringTag,{value:"Module"})),Ac=u.lazy(()=>dt(()=>import("./PlaylistManager-87d19449.js"),["assets/PlaylistManager-87d19449.js","assets/vendor-66b0ef43.js","assets/utils-08f61814.js","assets/ui-1cb796d3.js","assets/router-f729e475.js"])),_c=u.lazy(()=>dt(()=>import("./MusicPlayer-bc235a29.js"),["assets/MusicPlayer-bc235a29.js","assets/vendor-66b0ef43.js","assets/ui-1cb796d3.js","assets/router-f729e475.js","assets/utils-08f61814.js"])),Tc=u.lazy(()=>dt(()=>Promise.resolve().then(()=>Xc),void 0)),Oc=u.lazy(()=>dt(()=>import("./UnifiedAnalytics-a45d3510.js"),["assets/UnifiedAnalytics-a45d3510.js","assets/vendor-66b0ef43.js","assets/ui-1cb796d3.js","assets/router-f729e475.js","assets/utils-08f61814.js"])),Vc=u.lazy(()=>dt(()=>import("./GenreManager-afd8429c.js"),["assets/GenreManager-afd8429c.js","assets/vendor-66b0ef43.js","assets/ui-1cb796d3.js","assets/router-f729e475.js","assets/utils-08f61814.js"])),ka=u.lazy(()=>dt(()=>import("./RestaurantProfile-d1c07026.js"),["assets/RestaurantProfile-d1c07026.js","assets/vendor-66b0ef43.js","assets/YouTubeAuthManager-d052a128.js","assets/ui-1cb796d3.js","assets/router-f729e475.js","assets/utils-08f61814.js"])),Dc=u.lazy(()=>dt(()=>import("./QRCodeManager-2f65a890.js"),["assets/QRCodeManager-2f65a890.js","assets/vendor-66b0ef43.js","assets/router-f729e475.js","assets/ui-1cb796d3.js","assets/utils-08f61814.js"])),$c=u.lazy(()=>dt(()=>import("./ProblematicTracksAlert-71e8e181.js"),["assets/ProblematicTracksAlert-71e8e181.js","assets/vendor-66b0ef43.js","assets/ui-1cb796d3.js","assets/router-f729e475.js","assets/utils-08f61814.js"]));u.lazy(()=>dt(()=>import("./RestaurantSettings-dec5b9cc.js"),["assets/RestaurantSettings-dec5b9cc.js","assets/vendor-66b0ef43.js","assets/YouTubeAuthManager-d052a128.js","assets/ui-1cb796d3.js","assets/router-f729e475.js","assets/utils-08f61814.js"]));const Lc=u.lazy(()=>dt(()=>import("./EnhancedRestaurantProfile-39002e11.js"),["assets/EnhancedRestaurantProfile-39002e11.js","assets/vendor-66b0ef43.js","assets/ui-1cb796d3.js","assets/router-f729e475.js","assets/utils-08f61814.js"])),Pn=u.createContext({restaurantId:"",isConnected:!1,lastUpdate:new Date}),In=()=>u.useContext(Pn);class Nt{static setCache(t,s,a=5*60*1e3){const n={data:s,timestamp:Date.now(),ttl:a};localStorage.setItem(this.CACHE_PREFIX+t,JSON.stringify(n))}static getCache(t){try{const s=localStorage.getItem(this.CACHE_PREFIX+t);if(!s)return null;const{data:a,timestamp:n,ttl:i}=JSON.parse(s);return Date.now()-n>i?(localStorage.removeItem(this.CACHE_PREFIX+t),null):a}catch{return null}}static clearCache(){Object.keys(localStorage).filter(t=>t.startsWith(this.CACHE_PREFIX)).forEach(t=>localStorage.removeItem(t))}}Ce(Nt,"CACHE_PREFIX","restaurant_dashboard_");const jr=()=>e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 animate-pulse",children:[e.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-4"}),e.jsx("div",{className:"h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-2"}),e.jsx("div",{className:"h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4"})]}),Fc=ne.memo(({title:r,value:t,change:s,icon:a,color:n,bgColor:i,description:l})=>e.jsx(q.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow",children:e.jsx("div",{className:"flex items-center justify-between mb-4",children:e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:r}),e.jsx("div",{className:`w-10 h-10 rounded-lg ${i} flex items-center justify-center`,children:e.jsx(a,{className:`w-5 h-5 ${n}`})})]}),e.jsx("p",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-1",children:t}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mb-2",children:l}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:`text-sm font-medium ${s.startsWith("+")?"text-green-600 dark:text-green-400":"text-gray-600 dark:text-gray-400"}`,children:s}),e.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400 ml-1",children:"hoje"})]})]})})})),Uc=()=>{const{restaurantId:r,isConnected:t}=In(),s=zr(),{on:a,off:n,emit:i,joinRestaurant:l,leaveRestaurant:o}=Pt(),[c,h]=u.useState({}),[d,x]=u.useState([]),[p,m]=u.useState([]),[f,y]=u.useState(new Date),{data:v,isLoading:g,error:k}=Ss(["analytics",r],async()=>{try{const S=await Be.getAnalytics(r);return Nt.setCache(`stats_${r}`,S),S}catch(S){const V=Nt.getCache(`stats_${r}`);if(V)return b("Usando dados em cache devido à falha de conexão",{icon:"ℹ️"}),V;throw S}},{staleTime:2*60*1e3,retry:3,retryDelay:S=>Math.min(1e3*2**S,3e4)}),{data:T,isLoading:ee}=Ss(["suggestions",r],async()=>{try{const S=await Be.getSuggestions(r,{limit:10});return Nt.setCache(`suggestions_${r}`,S),S}catch(S){const V=Nt.getCache(`suggestions_${r}`);if(V)return V;throw S}},{staleTime:30*1e3,retry:2}),{data:se,isLoading:te}=Ss(["queue",r],async()=>{try{const S=await Be.getPlayQueue(r);return Nt.setCache(`queue_${r}`,S),S}catch(S){const V=Nt.getCache(`queue_${r}`);if(V)return V;throw S}},{staleTime:15*1e3,retry:2}),j=u.useCallback(S=>S.reduce((V,J,Q)=>Q===0?0:V+(J.duration||180),0),[]);u.useEffect(()=>{if(!r||!t)return;const S=z=>{x(L=>[{...z,type:"suggestion"},...L.slice(0,9)]),h(L=>{var re,P;return{...L,totalSuggestions:(L.totalSuggestions||0)+1,dailyStats:{...L.dailyStats,suggestions:(((re=L.dailyStats)==null?void 0:re.suggestions)||0)+1,votes:((P=L.dailyStats)==null?void 0:P.votes)||0}}}),b.success(`🎵 Nova sugestão: ${z.title}`,{duration:3e3,position:"bottom-right"})},V=z=>{m(z);const L=j(z);h(re=>({...re,estimatedWaitTime:L})),s.setQueryData(["queue",r],{queue:z})},J=z=>{h(L=>{var re,P;return{...L,totalVotes:(L.totalVotes||0)+1,dailyStats:{suggestions:((re=L.dailyStats)==null?void 0:re.suggestions)||0,votes:(((P=L.dailyStats)==null?void 0:P.votes)||0)+1}}})},Q=z=>{h(L=>({...L,currentlyPlaying:z?{title:z.title,artist:z.artist,remainingTime:z.remainingTime}:void 0}))};return a("new-suggestion",S),a("queue-update",V),a("vote-update",J),a("now-playing",Q),l(r),()=>{n("new-suggestion",S),n("queue-update",V),n("vote-update",J),n("now-playing",Q),o(r)}},[r,t,a,n,i,s,j]);const $=ne.useMemo(()=>{var V,J;const S=(v==null?void 0:v.summary)||{};return{totalSuggestions:c.totalSuggestions??S.totalSuggestions??0,totalVotes:c.totalVotes??S.totalVotes??0,pendingSuggestions:c.pendingSuggestions??S.pendingSuggestions??0,dailyStats:{suggestions:((V=c.dailyStats)==null?void 0:V.suggestions)??S.dailySuggestions??0,votes:((J=c.dailyStats)==null?void 0:J.votes)??S.dailyVotes??0},totalPlays:c.totalPlays??S.totalPlays??0,activeUsers:c.activeUsers??S.activeUsers??0,averageRating:c.averageRating??S.averageRating??0,growthRate:c.growthRate??S.growthRate??0,peakHour:c.peakHour??S.peakHour??"0:00",topGenre:c.topGenre??S.topGenre??"N/A",estimatedWaitTime:c.estimatedWaitTime,currentlyPlaying:c.currentlyPlaying}},[v,c]),A=ne.useMemo(()=>{const S=(T==null?void 0:T.suggestions)||[];return[...d,...S.map(Q=>({id:Q.id||`suggestion-${Date.now()}-${Math.random()}`,title:Q.title||"Título não disponível",artist:Q.artist||"Artista desconhecido",createdAt:Q.createdAt||new Date().toISOString(),upvotes:Q.upvotes||0,type:"suggestion"}))].filter((Q,z,L)=>L.findIndex(re=>re.id===Q.id)===z).slice(0,10)},[T,d]),O=ne.useMemo(()=>{const S=(se==null?void 0:se.queue)||[];return(p.length>0?p:S).slice(0,10).map((J,Q)=>{const z=new Date;return z.setSeconds(z.getSeconds()+Q*180),{id:J.id||`queue-${Date.now()}-${Math.random()}`,title:J.title||"Título não disponível",artist:J.artist||"Artista desconhecido",upvotes:J.upvotes||0,downvotes:J.downvotes||0,duration:J.duration||180,priority:J.priority||"normal",estimatedPlayTime:z}})},[se,p]),N=ne.useMemo(()=>[{title:"Sugestões Hoje",value:$.dailyStats.suggestions.toString(),change:$.growthRate>0?`+${$.growthRate.toFixed(1)}%`:"0%",icon:we,color:"text-blue-600 dark:text-blue-400",bgColor:"bg-blue-100 dark:bg-blue-900/20",description:"Novas sugestões recebidas hoje",trend:$.growthRate>0?"up":"stable"},{title:"Total de Votos",value:$.totalVotes.toString(),change:`+${$.dailyStats.votes}`,icon:Xt,color:"text-green-600 dark:text-green-400",bgColor:"bg-green-100 dark:bg-green-900/20",description:"Votos acumulados de clientes",trend:$.dailyStats.votes>0?"up":"stable"},{title:"Fila de Espera",value:O.length.toString(),change:$.estimatedWaitTime?`${Math.round($.estimatedWaitTime/60)}min`:"0min",icon:yt,color:"text-yellow-600 dark:text-yellow-400",bgColor:"bg-yellow-100 dark:bg-yellow-900/20",description:$.estimatedWaitTime?"Tempo estimado de espera":"Fila vazia",trend:O.length>5?"up":O.length>0?"stable":"down"},{title:"Usuários Ativos",value:$.activeUsers.toString(),change:"+0.2",icon:rt,color:"text-purple-600 dark:text-purple-400",bgColor:"bg-purple-100 dark:bg-purple-900/20",description:"Clientes interagindo agora",trend:$.activeUsers>0?"up":"stable"}],[$,O.length]),B=g||ee||te;return k&&!v?e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(ss,{className:"w-8 h-8 text-red-500"}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-red-900 dark:text-red-100",children:"Erro ao carregar dados"}),e.jsx("p",{className:"text-red-700 dark:text-red-300 mt-1",children:"Não foi possível conectar ao servidor. Tentando usar dados em cache..."}),e.jsxs("button",{onClick:()=>{Nt.clearCache(),window.location.reload()},className:"mt-3 flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:[e.jsx(Qe,{className:"w-4 h-4"}),e.jsx("span",{children:"Tentar Novamente"})]})]})]})})}):B?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white shadow-lg animate-pulse",children:[e.jsx("div",{className:"h-8 bg-white/20 rounded w-1/2 mb-2"}),e.jsx("div",{className:"h-4 bg-white/20 rounded w-3/4"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:Array(4).fill(0).map((S,V)=>e.jsx(jr,{},V))})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white shadow-lg",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h1",{className:"text-3xl font-bold mb-2",children:"Dashboard do Restaurante"}),e.jsx("p",{className:"text-blue-100 text-lg",children:"Visão geral das atividades e estatísticas em tempo real"}),e.jsxs("div",{className:"flex items-center space-x-6 mt-4",children:[e.jsx("div",{className:"flex items-center space-x-2",children:t?e.jsxs(e.Fragment,{children:[e.jsx(Gn,{className:"w-4 h-4 text-green-300"}),e.jsx("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-pulse"}),e.jsx("span",{className:"text-sm text-green-100 font-medium",children:"Sistema Online"})]}):e.jsxs(e.Fragment,{children:[e.jsx(Xn,{className:"w-4 h-4 text-red-300"}),e.jsx("div",{className:"w-2 h-2 bg-red-400 rounded-full"}),e.jsx("span",{className:"text-sm text-red-100",children:"Modo Offline"})]})}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(yt,{className:"w-4 h-4 text-blue-200"}),e.jsxs("span",{className:"text-sm text-blue-100",children:["Atualizado:"," ",f.toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit",second:"2-digit"})]})]}),$.currentlyPlaying&&e.jsxs("div",{className:"flex items-center space-x-2 bg-white/10 rounded-lg px-3 py-1",children:[e.jsx(Je,{className:"w-4 h-4 text-green-300"}),e.jsxs("div",{className:"text-sm",children:[e.jsx("span",{className:"text-green-100 font-medium",children:"Tocando:"}),e.jsxs("span",{className:"text-blue-100 ml-2",children:[$.currentlyPlaying.title," -"," ",$.currentlyPlaying.artist]})]})]})]})]}),e.jsx("div",{className:"hidden md:block",children:e.jsxs("div",{className:"w-24 h-24 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm relative",children:[e.jsx(vt,{className:"w-12 h-12 text-white"}),!t&&e.jsx("div",{className:"absolute -top-1 -right-1 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center",children:e.jsx(ss,{className:"w-4 h-4 text-white"})})]})})]})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:N.map((S,V)=>e.jsx(Fc,{...S},S.title))}),e.jsx(u.Suspense,{fallback:e.jsx(jr,{}),children:e.jsx($c,{})}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Atividade Recente"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),e.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Ao vivo"})]})]}),e.jsx("div",{className:"space-y-3",children:A.length>0?A.map(S=>e.jsxs(q.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-sm",children:e.jsx(we,{className:"w-5 h-5 text-white"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:['Nova sugestão: "',S.title,'"']}),e.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 truncate",children:S.artist}),e.jsx("span",{className:"text-xs text-gray-400",children:"•"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:new Date(S.createdAt).toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit"})})]})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(Xt,{className:"w-3 h-3 text-green-500"}),e.jsx("span",{className:"text-xs text-gray-500",children:S.upvotes||0})]})]},S.id)):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(we,{className:"w-12 h-12 text-gray-400 mx-auto mb-3"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:"Nenhuma sugestão recente"}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"As sugestões mais recentes dos clientes aparecerão aqui"}),e.jsxs("div",{className:"mt-4 space-y-2",children:[e.jsx("p",{className:"text-xs text-blue-600 dark:text-blue-400",children:"🎵 Compartilhe o QR Code para clientes sugerirem músicas"}),e.jsx("p",{className:"text-xs text-gray-500",children:'Acesse "QR Code" no menu principal'})]})]})})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Fila Atual"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Je,{className:"w-4 h-4 text-green-500"}),e.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:[O.length," músicas"]})]})]}),e.jsx("div",{className:"space-y-3",children:O.length>0?O.map((S,V)=>e.jsxs(q.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:`flex items-center space-x-3 p-3 rounded-lg transition-colors ${V===0?"bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 border border-green-200 dark:border-green-700":"bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600"}`,children:[e.jsx("div",{className:`w-8 h-8 rounded-full text-xs flex items-center justify-center font-bold ${V===0?"bg-gradient-to-r from-green-500 to-blue-500 text-white shadow-sm":"bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300"}`,children:V===0?e.jsx(Je,{className:"w-3 h-3"}):V+1}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("p",{className:`text-sm font-medium truncate ${V===0?"text-gray-900 dark:text-white font-semibold":"text-gray-900 dark:text-white"}`,children:S.title}),e.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 truncate",children:S.artist}),e.jsx("span",{className:"text-xs text-gray-400",children:"•"}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(Xt,{className:"w-3 h-3 text-green-500"}),e.jsx("span",{className:"text-xs text-gray-500",children:S.upvotes-S.downvotes})]})]})]}),V===0&&e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),e.jsx("span",{className:"text-xs text-green-600 dark:text-green-400 font-medium",children:"Tocando"})]})]},S.id)):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(Je,{className:"w-12 h-12 text-gray-400 mx-auto mb-3"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:"Fila de reprodução vazia"}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"Aguardando músicas serem aprovadas e adicionadas à fila"}),e.jsx("div",{className:"mt-4 space-y-2",children:e.jsx("p",{className:"text-xs text-blue-600 dark:text-blue-400",children:'💡 Dica: Use "Controle de Reprodução" para acompanhar fila e votação'})})]})})]})]})]})},Mc=()=>{var p,m;const r=cs(),t=Un(),{restaurantId:s}=Et(),{user:a,setUser:n,setAuthToken:i}=xs(),{status:l}=Pt(),{data:o}=Ss(["restaurant",s],()=>Be.getRestaurant(s),{enabled:!!s,staleTime:5*60*1e3,retry:2}),c=(o==null?void 0:o.name)||(o==null?void 0:o.displayName)||((p=a==null?void 0:a.restaurant)==null?void 0:p.name)||(s?`Restaurante ${s}`:"Restaurante"),h=(o==null?void 0:o.logo)||(o==null?void 0:o.logoUrl)||(o==null?void 0:o.brandLogo)||((m=a==null?void 0:a.restaurant)==null?void 0:m.logo);if(!s)return e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-2xl font-bold text-red-600",children:"Erro de Rota"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"ID do restaurante não fornecido na URL"})]})});const d=()=>{n(null),i(null),localStorage.removeItem("authToken"),b.success("Logout realizado com sucesso!"),r("/")},x=ne.useMemo(()=>[{name:"Player",icon:Jn,path:`/restaurant/${s}/dashboard/player`},{name:"Controle de Reprodução",icon:qt,path:`/restaurant/${s}/dashboard/playback-control`},{name:"Playlists",icon:we,path:`/restaurant/${s}/dashboard/playlists`},{name:"Gêneros",icon:Ut,path:`/restaurant/${s}/dashboard/genres`},{name:"Analytics",icon:vt,path:`/restaurant/${s}/dashboard/analytics`},{name:"QR Code",icon:ts,path:`/restaurant/${s}/dashboard/qrcode`}],[s]);return e.jsx(Pn.Provider,{value:{restaurantId:s,isConnected:l==="connected",lastUpdate:new Date},children:e.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[e.jsx("header",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center h-16",children:[e.jsxs($t,{to:`/restaurant/${s}/dashboard`,className:"flex items-center space-x-3 hover:opacity-80 transition-all duration-200 hover:scale-105 cursor-pointer",title:"Voltar ao Dashboard Principal",children:[h?e.jsx("div",{className:"w-10 h-10 rounded-lg overflow-hidden shadow-lg border border-gray-200 dark:border-gray-700 bg-white",children:e.jsx("img",{src:h,alt:`${c} logo`,className:"w-full h-full object-cover",onError:f=>{f.currentTarget.style.display="none"}})}):e.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center shadow-lg",children:e.jsx(we,{className:"w-6 h-6 text-white"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:c}),e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Dashboard de Gerenciamento"})]})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:c}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx($t,{to:"profile",className:"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center hover:from-blue-600 hover:to-purple-700 transition-all duration-200 cursor-pointer",title:"Perfil do Restaurante",children:e.jsx(Ft,{className:"w-4 h-4 text-white"})}),e.jsxs("button",{onClick:d,className:"flex items-center space-x-1 px-3 py-2 text-sm text-white bg-red-500 hover:bg-red-600 rounded-lg transition-colors font-medium",title:"Sair",children:[e.jsx(La,{className:"w-4 h-4"}),e.jsx("span",{children:"Sair"})]})]})]})]})})}),e.jsx("nav",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsx("div",{className:"flex space-x-8 overflow-x-auto",children:x.map(f=>e.jsxs($t,{to:f.path,className:`flex items-center space-x-2 py-4 px-1 border-b-2 text-sm font-medium transition-colors whitespace-nowrap ${t.pathname===f.path?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"}`,children:[e.jsx(f.icon,{className:"w-4 h-4 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:f.name})]},f.name))})})}),e.jsx("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsx(u.Suspense,{fallback:e.jsx(jr,{}),children:e.jsxs(Or,{children:[e.jsx(ge,{index:!0,element:e.jsx(Uc,{})}),e.jsx(ge,{path:"player",element:e.jsx(_c,{})}),e.jsx(ge,{path:"playback-control",element:e.jsx(Tc,{})}),e.jsx(ge,{path:"playlists",element:e.jsx(Ac,{})}),e.jsx(ge,{path:"genres",element:e.jsx(Vc,{})}),e.jsx(ge,{path:"qrcode",element:e.jsx(Dc,{})}),e.jsx(ge,{path:"analytics",element:e.jsx(Oc,{})}),e.jsx(ge,{path:"settings",element:e.jsx(ka,{})}),e.jsx(ge,{path:"profile",element:e.jsx(ka,{})}),e.jsx(ge,{path:"enhanced-profile",element:e.jsx(Lc,{})}),e.jsx(ge,{path:"suggestions",element:e.jsx(Tt,{to:"playback-control",replace:!0})}),e.jsx(ge,{path:"*",element:e.jsx(Tt,{to:".",replace:!0})})]})})})]})})};class ot{static setCache(t,s,a=2*60*1e3){const n={data:s,timestamp:Date.now(),ttl:a};localStorage.setItem(this.CACHE_PREFIX+t,JSON.stringify(n))}static getCache(t){try{const s=localStorage.getItem(this.CACHE_PREFIX+t);if(!s)return null;const{data:a,timestamp:n,ttl:i}=JSON.parse(s);return Date.now()-n>i?(localStorage.removeItem(this.CACHE_PREFIX+t),null):a}catch{return null}}}Ce(ot,"CACHE_PREFIX","playback_controller_");const qc=()=>{const{restaurantId:r}=Et(),{restaurantId:t,isConnected:s}=In(),a=r||t,{on:n,off:i,emit:l,onConnectionStatusChange:o}=Pt(),[c,h]=u.useState(null),[d,x]=u.useState(!0),[p,m]=u.useState(null),[f,y]=u.useState([]),[v,g]=u.useState([]),[k,T]=u.useState({totalItems:0,paidItems:0,freeItems:0,estimatedWaitTime:0}),[ee,se]=u.useState(new Date),[te,j]=u.useState(0),[$,A]=u.useState([]),[O,N]=u.useState([]),[B,S]=u.useState(null),[V,J]=u.useState(0),[Q,z]=u.useState(!1),[L,re]=u.useState([]),[P,X]=u.useState(0),[ue,ke]=u.useState([]),[Se,ye]=u.useState(null),[Fe,Ue]=u.useState({totalSuperVotes:0,totalNormalVotes:0,paidItems:0,freeItems:0}),[He,W]=u.useState(0),ae=u.useMemo(()=>{const w={};for(const C of[...$,...O])C!=null&&C.youtubeVideoId&&(w[C.youtubeVideoId]=C);return w},[$,O]),Ee=u.useMemo(()=>[...f||[],...v||[]],[f,v]),ie=u.useMemo(()=>{const w=C=>{const R=ae[C.youtubeVideoId],F=Number((R==null?void 0:R.isPaid)??C.isPaid??!1),_=Number((R==null?void 0:R.paymentAmount)??0),H=Number((R==null?void 0:R.voteCount)??0);return{isPaid:F,payment:_,votes:H}};return[...Ee].sort((C,R)=>{const F=w(C),_=w(R);return F.isPaid!==_.isPaid?_.isPaid-F.isPaid:F.payment!==_.payment?_.payment-F.payment:_.votes-F.votes})},[Ee,ae]),[Oe,at]=u.useState({shuffle:!1,repeat:!1,autoPlay:!1,crossfade:0,lockVoting:!1}),[Ge,nt]=u.useState("");u.useState(!1);const xt=u.useRef(null),Me=u.useRef(null),ft=u.useRef(null),Xe=u.useRef(null),he=u.useCallback(async(w,C,R=3)=>{for(let F=0;F<R;F++)try{const _={"Content-Type":"application/json",...(C==null?void 0:C.headers)||{}},H=localStorage.getItem("authToken");H&&(_.Authorization=`Bearer ${H}`);const le=await fetch(w,{...C,headers:_});if(!le.ok)throw new Error(`HTTP ${le.status}: ${le.statusText}`);const be=await le.json();return j(0),be}catch(_){if(console.error(`API call failed (attempt ${F+1}):`,_),F===R-1)throw j(H=>H+1),b.error(`Falha na conexão. Tentativa ${te+1}`),_;await new Promise(H=>setTimeout(H,Math.pow(2,F)*1e3))}},[te]),_e=u.useCallback(async()=>{if(a)try{console.log(`[PlaybackController] Loading playback state for restaurant: ${a}`);const w=await he(ce(`/playback/${a}/state`));console.log("[PlaybackController] Playback state response:",w),w!=null&&w.state?(w.state.currentTrack&&w.state.currentTrack.createdAt&&(w.state.currentTrack.createdAt=new Date(w.state.currentTrack.createdAt)),h(w.state),ot.setCache(`playback_${a}`,w.state),console.log("[PlaybackController] Playback state loaded successfully:",w.state)):(console.log("[PlaybackController] No playback state found, setting default state"),h({isPlaying:!1,currentTrack:null,volume:50,currentTime:0,queue:[],priorityQueue:[],normalQueue:[],history:[],connectionStatus:s?"connected":"disconnected"}))}catch(w){console.error("Erro ao carregar estado de reprodução:",w);const C=ot.getCache(`playback_${a}`);C&&(h(C),b("Usando dados em cache devido à falha de conexão",{icon:"ℹ️"}))}finally{x(!1),se(new Date)}},[a,s,he]),ut=u.useCallback(w=>w.reduce((C,R)=>C+(R.duration||180),0),[]),xe=u.useCallback(async()=>{if(a)try{const w=await he(ce(`/playback/${a}/state`)),C=(w==null?void 0:w.state)||{},R=(C.priorityQueue||[]).map(H=>({...H,createdAt:H.createdAt?new Date(H.createdAt):new Date,isPaid:!0})),F=(C.normalQueue||[]).map(H=>({...H,createdAt:H.createdAt?new Date(H.createdAt):new Date,isPaid:!1}));y(R),g(F);const _=ut([...R||[],...F||[]]);T({totalItems:((R==null?void 0:R.length)||0)+((F==null?void 0:F.length)||0),paidItems:(R==null?void 0:R.length)||0,freeItems:(F==null?void 0:F.length)||0,estimatedWaitTime:_}),ot.setCache(`queue_${a}`,{priorityQueue:R,normalQueue:F})}catch(w){console.error("Erro ao carregar filas pelo playback/state:",w);const C=ot.getCache(`queue_${a}`);C&&(y(C.priorityQueue||[]),g(C.normalQueue||[]),b("Usando dados de fila em cache",{icon:"ℹ️"}))}},[a,he,ut]),Re=u.useCallback(async()=>{if(a)try{const w=ce(`/collaborative-playlist/${a}/ranking`,{limit:"50"}),C=await fetch(w,{headers:_t()});if(!C.ok)throw new Error(`Falha ao carregar ranking (${C.status})`);const R=await C.json(),F=(R==null?void 0:R.data)||[],_=ve=>({...ve,paymentAmount:Number(ve.paymentAmount)||0,voteCount:Number(ve.voteCount)||0,superVoteCount:Number(ve.superVoteCount)||0,normalVoteCount:Number(ve.normalVoteCount)||0,totalRevenue:Number(ve.totalRevenue)||0,isPaid:!!ve.isPaid}),H=F.map(_),le=H.filter(ve=>ve.isPaid).sort((ve,et)=>{const Gr=et.paymentAmount-ve.paymentAmount;return Gr!==0?Gr:et.voteCount-ve.voteCount}),be=H.filter(ve=>!ve.isPaid).sort((ve,et)=>et.voteCount-ve.voteCount);A(le),N(be)}catch(w){console.warn("Não foi possível obter ranking colaborativo:",w)}},[a]),Ze=u.useCallback(async()=>{if(a)try{const w=ce(`/collaborative-playlist/${a}/stats`),C=await fetch(w,{headers:_t()});if(!C.ok)throw new Error(`Falha ao carregar stats (${C.status})`);const R=await C.json();ye((R==null?void 0:R.data)??R??null)}catch(w){console.warn("Não foi possível obter stats colaborativas:",w)}},[a]);u.useEffect(()=>{const w=[...$,...O].sort((_,H)=>{const le=Number(H.isPaid)-Number(_.isPaid);if(le!==0)return le;const be=H.paymentAmount-_.paymentAmount;return be!==0?be:H.voteCount-_.voteCount});re(w),X(w.reduce((_,H)=>_+H.voteCount,0));const C=w.reduce((_,H)=>_+H.superVoteCount,0),R=w.reduce((_,H)=>_+H.normalVoteCount,0);Ue({totalSuperVotes:C,totalNormalVotes:R,paidItems:$.length,freeItems:O.length});const F=$.reduce((_,H)=>_+H.paymentAmount,0);W(F)},[$,O]);const qe=u.useCallback(w=>{Xe.current&&clearInterval(Xe.current);const C=()=>{const R=Math.max(0,Math.floor((w.getTime()-Date.now())/1e3));J(R),R<=0&&(Xe.current&&clearInterval(Xe.current),Xe.current=null,Q&&We())};C(),Xe.current=setInterval(C,1e3)},[Q]),We=u.useCallback(async()=>{if(a)try{const w=await he(ce(`/collaborative-playlist/${a}/reorder`),{method:"POST"});b.success("Playlist reordenada por votos"),await Promise.all([xe(),Re(),_e()])}catch(w){console.error("Erro ao reordenar por votos:",w),b.error("Erro ao reordenar por votos")}finally{const w=new Date(Date.now()+3e5);S(w),qe(w)}},[a,he,xe,Re,_e,qe]);u.useEffect(()=>{if(!a||!s)return;const w=_=>{h(H=>H&&{...H,..._}),se(new Date),ot.setCache(`playback_${a}`,_)},C=_=>{let H=[],le=[];if(_.priorityQueue||_.normalQueue)H=_.priorityQueue||[],le=_.normalQueue||[];else if(_.queue){const ve=_.queue||[];H=ve.filter(et=>et==null?void 0:et.isPaid),le=ve.filter(et=>!(et!=null&&et.isPaid))}y(H),g(le);const be=ut([...H||[],...le||[]]);T(ve=>({...ve,estimatedWaitTime:be})),ot.setCache(`queue_${a}`,{priorityQueue:H,normalQueue:le})},R=o(_=>{h(H=>H&&{...H,connectionStatus:_})});n("playback-state-update",w),n("queue-update",C);const F=_=>{b.success((_==null?void 0:_.message)||"Playlist reordenada por votos"),ke(le=>{var be,ve;return[{time:new Date().toISOString(),playlistName:((be=_==null?void 0:_.playlist)==null?void 0:be.name)||(_==null?void 0:_.playlistName),count:((ve=_==null?void 0:_.playlist)==null?void 0:ve.tracksReordered)||(_==null?void 0:_.tracksReordered)||0,details:((_==null?void 0:_.topTracks)||[]).slice(0,5)},...le.slice(0,19)]}),xe(),_e(),Re(),Ze();const H=new Date(Date.now()+5*60*1e3);S(H),qe(H)};return n("playlistReordered",F),l("join-restaurant-playback",{restaurantId:a}),()=>{i("playback-state-update",w),i("queue-update",C),R==null||R(),i("playlistReordered",F),l("leave-restaurant-playback",{restaurantId:a})}},[a,s,n,i,l,xe,_e,Re,Ze,ut,qe]),u.useEffect(()=>{const w=async()=>{const R=ot.getCache(`playback_${a}`),F=ot.getCache(`queue_${a}`);R&&(h(R),b("Carregando dados em cache...",{duration:2e3,icon:"ℹ️"})),F&&(y(F.priorityQueue||[]),g(F.normalQueue||[])),await Promise.all([_e(),xe()])},C=async()=>{await w(),Re(),Ze()};if(C(),setTimeout(()=>{C()},1e3),s||(xt.current=setInterval(()=>{_e(),xe()},1e4)),ft.current=setInterval(()=>{Re(),Ze()},15e3),!B){const R=new Date(Date.now()+3e5);S(R),qe(R)}return()=>{xt.current&&clearInterval(xt.current),Me.current&&clearTimeout(Me.current),ft.current&&clearInterval(ft.current),Xe.current&&clearInterval(Xe.current)}},[a,s,_e,xe,Re,Ze,B,qe]);const Ie=u.useCallback(async w=>{if(a)try{await he(ce(`/collaborative-playlist/${a}/vote`),{method:"POST",body:JSON.stringify({youtubeVideoId:w,clientSessionId:"admin_panel"})}),b.success("Voto registrado (normal)"),await Promise.all([Re(),xe()])}catch(C){console.error(C),b.error("Falha ao registrar voto (normal)")}},[a,he,Re,xe]),Ke=u.useCallback(async(w,C)=>{if(a)try{await he(ce(`/collaborative-playlist/${a}/supervote`),{method:"POST",body:JSON.stringify({youtubeVideoId:w,paymentAmount:C,paymentId:`admin_test_${Date.now()}`,clientSessionId:"admin_panel"})}),b.success(`Supervoto R$ ${C} registrado`),await Promise.all([Re(),xe()])}catch(R){console.error(R),b.error("Falha ao registrar supervoto")}},[a,he,Re,xe]),it=u.useCallback(w=>{if(!w)return null;if(/^[a-zA-Z0-9_-]{11}$/.test(w))return w;try{const C=new URL(w);return C.hostname.includes("youtu.be")?C.pathname.slice(1):C.searchParams.get("v")}catch{return null}},[]),Dt=u.useCallback(w=>{const C=it(Ge.trim());if(!C){b.error("Informe um link ou ID válido do YouTube");return}const R={id:`local_${Date.now()}`,youtubeVideoId:C,title:`Música (${C})`,artist:"Artista Desconhecido",duration:180,thumbnailUrl:`https://img.youtube.com/vi/${C}/mqdefault.jpg`,upvotes:0,downvotes:0,score:0,createdAt:new Date,isPaid:w};w?(y(F=>[R,...F]),b.success("Adicionada à fila prioritária (somente UI)")):(g(F=>[R,...F]),b.success("Adicionada à fila normal (somente UI)")),nt("")},[Ge,it]),jt=u.useCallback(()=>{y([]),g([]),T({totalItems:0,paidItems:0,freeItems:0,estimatedWaitTime:0}),b.success("Filas limpas (somente UI)")},[]),pt=u.useCallback((w,C,R)=>{(w==="priority"?y:g)(_=>{const H=[..._],le=H.findIndex(ve=>ve.id===C);if(le<0)return _;const be=R==="up"?le-1:le+1;return be<0||be>=H.length?_:([H[le],H[be]]=[H[be],H[le]],H)})},[]),E=u.useCallback(w=>{y(C=>{const R=C.findIndex(H=>H.id===w);if(R<0)return C;const F={...C[R],isPaid:!1},_=C.filter((H,le)=>le!==R);return g(H=>[F,...H]),b("Movida para fila normal (somente UI)"),_})},[]),M=u.useCallback(w=>{at(C=>({...C,[w]:!C[w]}))},[]),D=u.useCallback(async()=>{var w;if(!(!c||!a)){m("playpause");try{const C=c.isPlaying?"pause":"resume";await he(ce(`/playback/${a}/${C}`),{method:"POST"});const R={...c,isPlaying:!c.isPlaying};h(R),ot.setCache(`playback_${a}`,R),b.success(c.isPlaying?"Reprodução pausada":"Reprodução retomada"),pe("playback_toggle",{action:C,trackId:(w=c.currentTrack)==null?void 0:w.id})}catch{b.error("Erro ao controlar reprodução")}finally{m(null)}}},[c,a,he]),K=u.useCallback(async()=>{var w;if(a){m("skip");try{await he(ce(`/playback/${a}/skip`),{method:"POST"}),b.success("Música pulada"),pe("track_skip",{trackId:(w=c==null?void 0:c.currentTrack)==null?void 0:w.id,skipTime:c==null?void 0:c.currentTime}),await Promise.all([_e(),xe()])}catch{b.error("Erro ao pular música")}finally{m(null)}}},[a,c,he,_e,xe]),Y=u.useCallback(async()=>{if(!a)return;m("start");let w=null,C="";if(f.length>0?(w=f[0],C="priority",b.success(`Tocando da fila prioritária: ${w.title}`)):v.length>0&&(w=[...v].sort((F,_)=>_.upvotes-_.downvotes-(F.upvotes-F.downvotes))[0],C="normal",b.success(`Tocando da fila normal: ${w.title}`)),!w){b.error("Nenhuma música na fila"),m(null);return}try{await he(ce(`/playback/${a}/play`),{method:"POST",body:JSON.stringify({songId:w.id})}),b.success(`Tocando: ${w.title}`),pe("track_start",{trackId:w.id,queueType:C,trackTitle:w.title}),await Promise.all([_e(),xe()])}catch(R){console.error("Erro ao iniciar próxima música:",R),b.error("Erro ao iniciar próxima música")}finally{m(null)}},[a,f,v,he,_e,xe]),je=u.useCallback(async w=>{if(!(!a||!c))try{await he(ce(`/playback/${a}/volume`),{method:"POST",body:JSON.stringify({volume:w})});const C={...c,volume:w};h(C),ot.setCache(`playback_${a}`,C),pe("volume_change",{previousVolume:c.volume,newVolume:w})}catch{b.error("Erro ao ajustar volume")}},[a,c,he]),Ne=u.useCallback(()=>{if(!c)return;const w=c.volume===0?50:0;je(w),pe("volume_mute_toggle",{action:w===0?"mute":"unmute"})},[c,je]),U=u.useCallback(async w=>{if(a){m(`remove-${w}`);try{await he(ce(`/playback-queue/${a}/remove`),{method:"POST",body:JSON.stringify({trackId:w})}),b.success("Música removida da fila"),pe("track_remove",{trackId:w}),await xe()}catch{b.error("Erro ao remover música da fila")}finally{m(null)}}},[a,he,xe]),oe=u.useCallback(async w=>{if(a){m(`promote-${w}`);try{await he(ce(`/playback-queue/${a}/promote`),{method:"POST",body:JSON.stringify({trackId:w})}),b.success("Música promovida para fila prioritária"),pe("track_promote",{trackId:w}),await xe()}catch{b.error("Erro ao promover música")}finally{m(null)}}},[a,he,xe]),pe=u.useCallback((w,C)=>{try{const R={timestamp:new Date().toISOString(),restaurantId:a,action:w,data:C};let F=JSON.parse(localStorage.getItem("playback_analytics")||"[]");F.push(R),F.length>100&&(F=F.slice(-100)),localStorage.setItem("playback_analytics",JSON.stringify(F))}catch(R){console.error("Erro ao registrar analytics:",R)}},[a]),I=w=>{const C=Math.floor(w/60),R=Math.floor(w%60);return`${C}:${R.toString().padStart(2,"0")}`},G=w=>w<60?`${w}s`:w<3600?`${Math.floor(w/60)}min`:`${Math.floor(w/3600)}h`,Z=w=>w.toLocaleString("pt-BR",{style:"currency",currency:"BRL"}).slice(3),me=()=>{var w;return(w=c==null?void 0:c.currentTrack)!=null&&w.duration?c.currentTime/c.currentTrack.duration*100:0};return d?e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm",children:e.jsx("div",{className:"flex justify-center items-center h-32",children:e.jsx(Qe,{className:"w-8 h-8 animate-spin text-blue-600"})})}):e.jsx("div",{className:"min-h-screen bg-white text-gray-900",children:e.jsxs("div",{className:"max-w-6xl mx-auto px-4 py-6 space-y-6",children:[e.jsx(ln,{position:"top-left"}),e.jsx(Bc,{autoReorder:Q,setAutoReorder:z,handleReorderByVotes:We,onRefresh:()=>{_e(),xe()},onStartNext:Y,onClearQueues:jt,actionLoading:p,localSettings:Oe,toggleLocal:M}),e.jsx(Qc,{countdown:V,totalVotes:(Se==null?void 0:Se.totalVotes)??P,computedRevenue:(Se==null?void 0:Se.totalRevenue)??He,voteAggregates:Fe,formatEstimatedTime:G}),e.jsx(zc,{playbackState:c,actionLoading:p,handlePlayPause:D,handleSkip:K,handleVolumeChange:je,toggleMute:Ne,getProgressPercentage:me,formatTime:I,localSettings:Oe,toggleLocal:M,handleClearQueues:jt,handleReorderByVotes:We,handleStartNext:Y,manualVideoInput:Ge,setManualVideoInput:nt,handleManualAdd:Dt,loadInitialData:()=>{_e(),xe()},rankingMap:ae}),e.jsx(Wc,{queueStats:k,formatEstimatedTime:G}),e.jsx(Hc,{reorderHistory:ue}),e.jsx(Kc,{priorityQueue:f,normalQueue:v,actionLoading:p,handleMoveInQueue:pt,handleDemoteToNormal:E,handlePromoteTrack:oe,handleRemoveFromQueue:U,formatTime:I,rankingMap:ae,predictedReorder:ie}),e.jsx(Yc,{rankingPaid:$,rankingFree:O,voteNormalFromController:Ie,superVoteFromController:Ke,formatBRL:Z}),e.jsx(Jc,{autoPreview:L,formatBRL:Z}),e.jsx(Gc,{lastUpdateTime:ee})]})})},Bc=ne.memo(({autoReorder:r,setAutoReorder:t,handleReorderByVotes:s,onRefresh:a,onStartNext:n,onClearQueues:i,actionLoading:l,localSettings:o,toggleLocal:c})=>e.jsx("header",{className:"bg-white/70 backdrop-blur-md border border-gray-200 p-4 sm:p-6 rounded-xl",children:e.jsxs("div",{className:"flex flex-col gap-3",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3",children:[e.jsxs("div",{className:"text-gray-900",children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Controle da Playlist (Colaborativa)"}),e.jsx("p",{className:"text-gray-600 mt-1",children:"Gerencie a reprodução e acompanhe votos, supervotos e reordenações automáticas."})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("label",{className:"flex items-center gap-2 text-sm text-gray-800 bg-white/70 border border-gray-300 px-3 py-1.5 rounded-lg",children:[e.jsx("input",{type:"checkbox",checked:r,onChange:h=>t(h.target.checked)}),"Auto-reordenar"]}),e.jsx("button",{onClick:s,className:"px-3 py-2 bg-gradient-to-r from-indigo-600 to-blue-600 text-white rounded-lg hover:from-indigo-700 hover:to-blue-700 text-sm",children:"Reordenar agora"}),e.jsxs("button",{onClick:a,disabled:l==="refresh",className:"flex items-center gap-2 px-3 py-2 bg-white/70 border border-gray-300 rounded-lg hover:bg-white text-sm disabled:opacity-50","aria-label":"Atualizar dados de reprodução",children:[l==="refresh"?e.jsx(Lt,{className:"w-4 h-4 animate-spin"}):e.jsx(Qe,{className:"w-4 h-4"}),"Atualizar"]})]})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-2",children:[e.jsxs("button",{onClick:n,className:"px-3 py-1.5 bg-white border border-gray-300 text-gray-800 rounded hover:bg-gray-50 text-sm flex items-center gap-2",children:[e.jsx(Ua,{className:"w-4 h-4"})," Tocar próxima da fila"]}),e.jsxs("button",{onClick:i,className:"px-3 py-1.5 bg-white border border-red-300 text-red-700 rounded hover:bg-red-50 text-sm flex items-center gap-2",children:[e.jsx(Oa,{className:"w-4 h-4"})," Limpar Filas"]}),e.jsx("div",{className:"h-5 w-px bg-gray-300"}),e.jsxs("button",{onClick:()=>c("shuffle"),className:`px-3 py-1.5 rounded text-sm flex items-center gap-1 border ${o.shuffle?"bg-indigo-50 border-indigo-200 text-indigo-700":"bg-white border-gray-300 text-gray-700"}`,children:[e.jsx(Zn,{className:"w-4 h-4"})," Shuffle"]}),e.jsxs("button",{onClick:()=>c("repeat"),className:`px-3 py-1.5 rounded text-sm flex items-center gap-1 border ${o.repeat?"bg-indigo-50 border-indigo-200 text-indigo-700":"bg-white border-gray-300 text-gray-700"}`,children:[e.jsx(ei,{className:"w-4 h-4"})," Repeat"]}),e.jsxs("button",{onClick:()=>c("lockVoting"),className:`px-3 py-1.5 rounded text-sm flex items-center gap-1 border ${o.lockVoting?"bg-yellow-50 border-yellow-300 text-yellow-700":"bg-white border-gray-300 text-gray-700"}`,children:[o.lockVoting?e.jsx(Bs,{className:"w-4 h-4"}):e.jsx(ti,{className:"w-4 h-4"})," ",o.lockVoting?"Votação Travada":"Votação Liberada"]})]})]})})),Qc=ne.memo(({countdown:r,totalVotes:t,computedRevenue:s,voteAggregates:a,formatEstimatedTime:n})=>e.jsx("section",{className:"bg-white/70 backdrop-blur-md rounded-xl p-4 border border-gray-200",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsx("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Próxima reordenação"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:n(r)})]}),e.jsx(yt,{className:"w-7 h-7 text-indigo-500"})]})}),e.jsx("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Total de votos"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:t})]}),e.jsx(De,{className:"w-7 h-7 text-green-500"})]})}),e.jsx("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-yellow-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-yellow-600",children:"Fila Prioritária"}),e.jsx("p",{className:"text-2xl font-bold text-yellow-700",children:a.paidItems})]}),e.jsx(rt,{className:"w-7 h-7 text-yellow-500"})]})}),e.jsx("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-green-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-green-600",children:"Fila Normal"}),e.jsx("p",{className:"text-2xl font-bold text-green-700",children:a.freeItems})]}),e.jsx(rt,{className:"w-7 h-7 text-green-500"})]})})]})})),zc=ne.memo(({playbackState:r,actionLoading:t,handlePlayPause:s,handleSkip:a,handleVolumeChange:n,toggleMute:i,getProgressPercentage:l,formatTime:o,localSettings:c,toggleLocal:h,handleClearQueues:d,handleReorderByVotes:x,handleStartNext:p,manualVideoInput:m,setManualVideoInput:f,handleManualAdd:y,loadInitialData:v,rankingMap:g})=>e.jsx("section",{className:"bg-white/70 backdrop-blur-md rounded-xl p-6 border border-gray-200",children:r!=null&&r.currentTrack?e.jsxs(q.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("img",{src:r.currentTrack.thumbnailUrl,alt:r.currentTrack.title,className:"w-16 h-12 object-cover rounded-lg shadow-sm"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:r.currentTrack.title}),e.jsx("p",{className:"text-gray-600",children:r.currentTrack.artist}),e.jsxs("div",{className:"flex items-center space-x-4 mt-1",children:[e.jsxs("div",{className:"flex items-center space-x-1 text-green-600",children:[e.jsx(De,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{className:"text-sm",children:r.currentTrack.upvotes})]}),e.jsxs("div",{className:"flex items-center space-x-1 text-red-600",children:[e.jsx(Mr,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{className:"text-sm",children:r.currentTrack.downvotes})]}),e.jsxs("div",{className:"text-sm text-gray-600",children:["Score: ",r.currentTrack.score]})]}),e.jsx("div",{className:"flex items-center space-x-3 mt-1 text-xs text-gray-600",children:(()=>{const k=r.currentTrack.youtubeVideoId,T=k?g==null?void 0:g[k]:void 0;return T?e.jsxs(e.Fragment,{children:[e.jsxs("span",{className:"px-1.5 py-0.5 rounded bg-white/70 border border-gray-300 text-gray-800",children:["Votos: ",T.voteCount]}),T.isPaid&&e.jsxs("span",{className:"px-1.5 py-0.5 rounded bg-yellow-100 text-yellow-800 border border-yellow-300",children:["R$ ",((T.paymentAmount??0)/100).toFixed(2)]})]}):null})()})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between text-sm text-gray-500",children:[e.jsx("span",{children:o(r.currentTime)}),e.jsx("span",{children:o(r.currentTrack.duration)})]}),e.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:e.jsx(q.div,{className:"bg-blue-600 h-2 rounded-full",initial:{width:0},animate:{width:`${l()}%`},transition:{duration:1},role:"progressbar","aria-valuenow":l(),"aria-valuemin":0,"aria-valuemax":100,"aria-label":"Progresso da música"})})]}),e.jsxs("div",{className:"flex items-center justify-center space-x-4",children:[e.jsx("button",{onClick:s,disabled:t==="playpause",className:"flex items-center justify-center w-12 h-12 bg-blue-600 text-white rounded-full hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors","aria-label":r.isPlaying?"Pausar":"Reproduzir",children:t==="playpause"?e.jsx(Lt,{className:"w-6 h-6 animate-spin"}):r.isPlaying?e.jsx(Fr,{className:"w-6 h-6"}):e.jsx(Je,{className:"w-6 h-6 ml-1"})}),e.jsx("button",{onClick:a,disabled:t==="skip",className:"flex items-center justify-center w-10 h-10 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-300 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors","aria-label":"Pular música",children:t==="skip"?e.jsx(Lt,{className:"w-5 h-5 animate-spin"}):e.jsx(Ua,{className:"w-5 h-5"})}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{onClick:i,"aria-label":r.volume===0?"Ativar som":"Silenciar",children:r.volume===0?e.jsx(Ma,{className:"w-5 h-5 text-gray-600"}):e.jsx(us,{className:"w-5 h-5 text-gray-600"})}),e.jsx("input",{type:"range",min:"0",max:"100",value:r.volume,onChange:k=>n(parseInt(k.target.value)),className:"w-20 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer","aria-label":"Controle de volume"}),e.jsx("span",{className:"text-sm text-gray-500 w-8",children:r.volume})]})]})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"w-16 h-12 bg-white/10 rounded-lg"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Sem reprodução ativa no momento"}),e.jsx("p",{className:"text-gray-600 text-sm",children:"A playlist do restaurante toca normalmente até que a votação reordene a fila."})]})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-900/40 rounded-lg p-4 border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center gap-2 text-gray-800 dark:text-gray-200 mb-3",children:[e.jsx(Lr,{className:"w-4 h-4"}),e.jsx("span",{className:"font-medium text-sm",children:"Adicionar música manualmente"})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-2",children:[e.jsx("input",{value:m,onChange:k=>f(k.target.value),placeholder:"Cole o link ou ID do YouTube",className:"flex-1 px-3 py-2 rounded border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:()=>y(!1),className:"px-3 py-2 bg-green-600 text-white rounded hover:bg-green-700 text-sm",children:"Adicionar na Fila Normal"}),e.jsx("button",{onClick:()=>y(!0),className:"px-3 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 text-sm",children:"Adicionar na Fila Prioritária"})]})]})]})]})})),Wc=ne.memo(({queueStats:r,formatEstimatedTime:t})=>e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[e.jsx("div",{className:"bg-white rounded-xl p-4 border border-gray-200 shadow-sm",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Total na Fila"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:r.totalItems})]}),e.jsx(we,{className:"w-8 h-8 text-blue-500"})]})}),e.jsx("div",{className:"bg-white rounded-xl p-4 border border-gray-200 shadow-sm",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Fila Prioritária"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:r.paidItems})]}),e.jsx(De,{className:"w-8 h-8 text-yellow-500"})]})}),e.jsx("div",{className:"bg-white rounded-xl p-4 border border-gray-200 shadow-sm",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Fila Normal"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:r.freeItems})]}),e.jsx(rt,{className:"w-8 h-8 text-green-500"})]})})]})),Hc=ne.memo(({reorderHistory:r})=>e.jsxs("section",{className:"bg-white rounded-xl p-6 border border-gray-200 shadow-sm mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Histórico de Reordenação"}),e.jsxs("span",{className:"text-xs px-2 py-1 rounded bg-gray-100 text-gray-700 border border-gray-200",children:[r.length," eventos"]})]}),r.length===0?e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Nenhum evento de reordenação ainda"}):e.jsx("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:r.map((t,s)=>e.jsxs("div",{className:"p-3 rounded border border-indigo-200 dark:border-indigo-700 bg-indigo-50/60 dark:bg-indigo-900/20",children:[e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsxs("div",{className:"font-medium text-gray-800 dark:text-gray-200",children:[new Date(t.time).toLocaleString("pt-BR")," • ",t.playlistName||"Playlist"]}),e.jsxs("div",{className:"text-xs text-indigo-700 dark:text-indigo-300",children:[t.count," músicas impactadas"]})]}),e.jsxs("div",{className:"mt-2 text-xs text-gray-700 dark:text-gray-300",children:["Top 5:",e.jsx("ul",{className:"list-disc list-inside",children:(t.details||[]).map((a,n)=>e.jsxs("li",{className:"truncate",children:[a.title||a.videoId," ",a.isPaid?"(Paga)":""," — votos: ",a.voteCount??"?"]},n))})]})]},s))})]})),Kc=ne.memo(({priorityQueue:r,normalQueue:t,actionLoading:s,handleMoveInQueue:a,handleDemoteToNormal:n,handlePromoteTrack:i,handleRemoveFromQueue:l,formatTime:o,rankingMap:c,predictedReorder:h})=>e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("section",{className:"bg-white/70 backdrop-blur-md rounded-xl p-6 border border-gray-200",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h3",{className:"text-lg font-semibold flex items-center text-gray-900",children:["🔥 Fila Prioritária (",r.length,")"]}),e.jsx("div",{className:"bg-yellow-100 text-yellow-800 border border-yellow-300 px-2 py-1 rounded text-xs font-medium",children:"SuperVoto (R$ 5, 20, 50)"})]}),e.jsx("div",{className:"text-xs text-gray-600 mb-2",children:(()=>{const d=r.reduce((p,m)=>{var f;return p+(((f=c==null?void 0:c[m.youtubeVideoId])==null?void 0:f.voteCount)??0)},0),x=r.length;return e.jsxs("span",{children:["Itens pagos: ",x," • Votos somados: ",d]})})()}),e.jsxs("div",{className:"space-y-3 max-h-64 overflow-y-auto",children:[r.length>0?e.jsx(ze,{children:r.map((d,x)=>{var p,m,f;return e.jsxs(q.div,{layout:!0,initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{duration:.2},className:"flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-full text-xs flex items-center justify-center font-bold",children:x+1}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 truncate",children:d.title}),e.jsx("p",{className:"text-xs text-gray-600 truncate",children:d.artist})]}),e.jsxs("div",{className:"flex items-center space-x-3 mt-1",children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(De,{className:"w-3 h-3 text-yellow-600","aria-hidden":"true"}),e.jsxs("span",{className:"text-xs text-yellow-700",children:[((p=c==null?void 0:c[d.youtubeVideoId])==null?void 0:p.voteCount)??0," votos"]})]}),((m=c==null?void 0:c[d.youtubeVideoId])==null?void 0:m.isPaid)&&e.jsxs("div",{className:"text-xs text-green-700",children:["R$ ",((((f=c==null?void 0:c[d.youtubeVideoId])==null?void 0:f.paymentAmount)??0)/100).toFixed(2)]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"text-xs text-gray-500",children:o(d.duration)}),e.jsx("button",{onClick:()=>a("priority",d.id,"up"),className:"p-1 text-gray-600 hover:text-gray-800",title:"Mover para cima","aria-label":"Mover para cima",children:e.jsx(ea,{className:"w-3 h-3"})}),e.jsx("button",{onClick:()=>a("priority",d.id,"down"),className:"p-1 text-gray-600 hover:text-gray-800",title:"Mover para baixo","aria-label":"Mover para baixo",children:e.jsx(ta,{className:"w-3 h-3"})}),e.jsx("button",{onClick:()=>n(d.id),className:"p-1 text-blue-600 hover:text-blue-800",title:"Mover para fila normal","aria-label":"Mover para fila normal",children:e.jsx(rs,{className:"w-3 h-3 rotate-180"})}),e.jsx("button",{onClick:()=>l(d.id),disabled:s===`remove-${d.id}`,className:"p-1 text-red-600 hover:text-red-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors","aria-label":"Remover da fila prioritária",title:"Remover",children:s===`remove-${d.id}`?e.jsx(Lt,{className:"w-3 h-3 animate-spin"}):e.jsx(bt,{className:"w-3 h-3"})})]})]},d.id)})}):e.jsxs(q.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center py-8",children:[e.jsx("div",{className:"w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(De,{className:"w-6 h-6 text-yellow-600"})}),e.jsx("p",{className:"text-gray-600 text-sm",children:"Nenhuma música na fila prioritária"})]}),h&&h.length>0&&e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-indigo-200 dark:border-indigo-700 lg:col-span-2",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("h4",{className:"text-md font-semibold text-indigo-700 dark:text-indigo-300",children:"Prévia de Reordenação (por votos)"}),e.jsx("span",{className:"text-xs text-gray-500",children:"Pagas primeiro, depois por valor e votos"})]}),e.jsx("div",{className:"flex flex-wrap gap-2",children:h.map((d,x)=>{var p,m,f;return e.jsxs("div",{className:"px-2 py-1 rounded text-xs border border-indigo-200 dark:border-indigo-700 bg-indigo-50/60 dark:bg-indigo-900/20",children:[e.jsxs("span",{className:"font-medium mr-1",children:[x+1,"."]}),e.jsx("span",{className:"mr-1",children:d.title}),e.jsxs("span",{className:"text-gray-500",children:["(",((p=c==null?void 0:c[d.youtubeVideoId])==null?void 0:p.voteCount)??0," votos",(m=c==null?void 0:c[d.youtubeVideoId])!=null&&m.isPaid?` · R$ ${((((f=c==null?void 0:c[d.youtubeVideoId])==null?void 0:f.paymentAmount)??0)/100).toFixed(2)}`:"",")"]})]},`${d.id}_${x}`)})})]})]})]}),e.jsxs("section",{className:"bg-white/70 backdrop-blur-md rounded-xl p-6 border border-gray-200",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h3",{className:"text-lg font-semibold flex items-center text-gray-900",children:["📋 Fila Normal (",t.length,")"]}),e.jsx("div",{className:"bg-green-100 text-green-800 border border-green-300 px-2 py-1 rounded text-xs font-medium",children:"Gratuito"})]}),e.jsx("div",{className:"space-y-3 max-h-64 overflow-y-auto",children:t.length>0?e.jsx(ze,{children:t.slice().sort((d,x)=>x.upvotes-x.downvotes-(d.upvotes-d.downvotes)).map((d,x)=>{var p;return e.jsxs(q.div,{layout:!0,initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{duration:.2},className:"flex items-center space-x-3 p-3 bg-green-50 rounded-lg border border-green-200",children:[e.jsx("div",{className:"w-6 h-6 bg-green-600 text-white rounded text-xs flex items-center justify-center font-medium",children:x+1}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 truncate",children:d.title}),e.jsx("p",{className:"text-xs text-gray-600 truncate",children:d.artist}),e.jsxs("div",{className:"flex items-center space-x-3 mt-1",children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(De,{className:"w-3 h-3 text-green-600","aria-hidden":"true"}),e.jsx("span",{className:"text-xs text-green-600",children:d.upvotes})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(Mr,{className:"w-3 h-3 text-red-600","aria-hidden":"true"}),e.jsx("span",{className:"text-xs text-red-600",children:d.downvotes})]}),e.jsx("div",{className:"flex items-center space-x-3 mt-1",children:e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(De,{className:"w-3 h-3 text-green-600","aria-hidden":"true"}),e.jsxs("span",{className:"text-xs text-green-700",children:[((p=c==null?void 0:c[d.youtubeVideoId])==null?void 0:p.voteCount)??0," votos"]})]})}),e.jsxs("div",{className:"text-xs text-gray-500",children:["Score: ",d.score||d.upvotes-d.downvotes]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"text-xs text-gray-500",children:o(d.duration)}),e.jsx("button",{onClick:()=>a("normal",d.id,"up"),className:"p-1 text-gray-600 hover:text-gray-800",title:"Mover para cima","aria-label":"Mover para cima",children:e.jsx(ea,{className:"w-3 h-3"})}),e.jsx("button",{onClick:()=>a("normal",d.id,"down"),className:"p-1 text-gray-600 hover:text-gray-800",title:"Mover para baixo","aria-label":"Mover para baixo",children:e.jsx(ta,{className:"w-3 h-3"})}),e.jsx("button",{onClick:()=>i(d.id),disabled:s===`promote-${d.id}`,className:"p-1 text-blue-600 hover:text-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors","aria-label":"Promover para fila prioritária",title:"Promover",children:s===`promote-${d.id}`?e.jsx(Lt,{className:"w-3 h-3 animate-spin"}):e.jsx(rs,{className:"w-3 h-3"})}),e.jsx("button",{onClick:()=>l(d.id),disabled:s===`remove-${d.id}`,className:"p-1 text-red-600 hover:text-red-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors","aria-label":"Remover da fila normal",title:"Remover",children:s===`remove-${d.id}`?e.jsx(Lt,{className:"w-3 h-3 animate-spin"}):e.jsx(bt,{className:"w-3 h-3"})})]})]},d.id)})}):e.jsxs(q.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center py-8",children:[e.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(rt,{className:"w-6 h-6 text-green-600"})}),e.jsx("p",{className:"text-gray-600 text-sm",children:"Nenhuma música na fila normal"})]})})]})]})),Yc=ne.memo(({rankingPaid:r,rankingFree:t,voteNormalFromController:s,superVoteFromController:a,formatBRL:n})=>e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("section",{className:"bg-white/70 backdrop-blur-md rounded-xl p-6 border border-gray-200",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900",children:"SuperVotos (Pagas)"}),e.jsxs("span",{className:"text-xs px-2 py-1 rounded bg-yellow-100 text-yellow-800 border border-yellow-300",children:[r.length," itens"]})]}),e.jsx("div",{className:"space-y-2 max-h-72 overflow-y-auto",children:r.length===0?e.jsx("div",{className:"text-sm text-gray-600",children:"Nenhum supervoto no momento"}):r.map((i,l)=>e.jsxs("div",{className:"flex items-center gap-3 p-3 rounded border border-gray-200 bg-white/50",children:[e.jsx("div",{className:"w-7 h-7 bg-yellow-500 text-white rounded flex items-center justify-center text-xs font-bold",children:l+1}),e.jsx("img",{src:`https://img.youtube.com/vi/${i.youtubeVideoId}/mqdefault.jpg`,alt:i.title||i.youtubeVideoId,className:"w-10 h-8 object-cover rounded"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("div",{className:"text-sm font-medium truncate text-gray-900",children:i.title||i.youtubeVideoId}),e.jsx("div",{className:"text-xs text-gray-600 truncate",children:i.artist||"—"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"text-xs text-purple-200 mr-1",children:["Votos: ",i.voteCount]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("button",{onClick:()=>s(i.youtubeVideoId),className:"px-2 py-0.5 border border-white/20 rounded text-xs hover:bg-white/10",title:"Voto normal",children:"+1"}),e.jsx("button",{onClick:()=>a(i.youtubeVideoId,5),className:"px-2 py-0.5 border border-yellow-500/40 rounded text-xs hover:bg-yellow-500/10",title:"Supervoto R$5",children:"5"}),e.jsx("button",{onClick:()=>a(i.youtubeVideoId,20),className:"px-2 py-0.5 border border-yellow-500/50 rounded text-xs hover:bg-yellow-500/10",title:"Supervoto R$20",children:"20"}),e.jsx("button",{onClick:()=>a(i.youtubeVideoId,50),className:"px-2 py-0.5 border border-yellow-500/60 rounded text-xs hover:bg-yellow-500/10",title:"Supervoto R$50",children:"50"})]}),e.jsxs("div",{className:"text-xs text-green-200 ml-1",children:["R$ ",n(i.paymentAmount)]})]})]},i.youtubeVideoId))})]}),e.jsxs("section",{className:"bg-white/70 backdrop-blur-md rounded-xl p-6 border border-gray-200",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900",children:"Votos (Grátis)"}),e.jsxs("span",{className:"text-xs px-2 py-1 rounded bg-green-100 text-green-800 border border-green-300",children:[t.length," itens"]})]}),e.jsx("div",{className:"space-y-2 max-h-72 overflow-y-auto",children:t.length===0?e.jsx("div",{className:"text-sm text-gray-600",children:"Nenhum voto gratuito no momento"}):t.map((i,l)=>e.jsxs("div",{className:"flex items-center gap-3 p-3 rounded border border-gray-200 bg-white/50",children:[e.jsx("div",{className:"w-7 h-7 bg-green-600 text-white rounded flex items-center justify-center text-xs font-bold",children:l+1}),e.jsx("img",{src:`https://img.youtube.com/vi/${i.youtubeVideoId}/mqdefault.jpg`,alt:i.title||i.youtubeVideoId,className:"w-10 h-8 object-cover rounded"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("div",{className:"text-sm font-medium truncate text-gray-900",children:i.title||i.youtubeVideoId}),e.jsx("div",{className:"text-xs text-gray-600 truncate",children:i.artist||"—"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"text-xs text-gray-600 mr-1",children:["Votos: ",i.voteCount]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("button",{onClick:()=>s(i.youtubeVideoId),className:"px-2 py-0.5 border border-gray-300 rounded text-xs hover:bg-gray-100",title:"Voto normal",children:"+1"}),e.jsx("button",{onClick:()=>a(i.youtubeVideoId,5),className:"px-2 py-0.5 border border-yellow-400 rounded text-xs hover:bg-yellow-100",title:"Supervoto R$5",children:"5"}),e.jsx("button",{onClick:()=>a(i.youtubeVideoId,20),className:"px-2 py-0.5 border border-yellow-500 rounded text-xs hover:bg-yellow-100",title:"Supervoto R$20",children:"20"}),e.jsx("button",{onClick:()=>a(i.youtubeVideoId,50),className:"px-2 py-0.5 border border-yellow-600 rounded text-xs hover:bg-yellow-100",title:"Supervoto R$50",children:"50"})]}),e.jsxs("div",{className:"text-xs text-gray-600 ml-1",children:["Mesa: ",i.tableNumber??"—"]})]})]},i.youtubeVideoId))})]})]})),Jc=ne.memo(({autoPreview:r,formatBRL:t})=>e.jsxs("section",{className:"bg-white/70 backdrop-blur-md rounded-xl p-6 border border-gray-200",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900",children:"Fila Automática (Prévia)"}),e.jsx("div",{className:"text-xs text-gray-600",children:"Baseada em votos e pagamentos"})]}),r.length>0?e.jsx("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:r.map((s,a)=>e.jsxs("div",{className:"flex items-center justify-between p-2 rounded bg-white/50 border border-gray-200",children:[e.jsxs("div",{className:"truncate mr-2",children:[e.jsx("span",{className:"text-sm font-medium text-gray-900",children:s.title}),e.jsxs("span",{className:"text-xs text-gray-600 ml-2",children:[s.voteCount," votos"]})]}),e.jsx("div",{className:"text-xs",children:s.isPaid?e.jsxs("span",{className:"text-green-600",children:["R$ ",t(s.paymentAmount??0)]}):e.jsx("span",{className:"text-gray-600",children:"Grátis"})})]},a))}):e.jsx("div",{className:"text-sm text-gray-600",children:"Nenhuma prévia disponível."})]})),Gc=ne.memo(({lastUpdateTime:r})=>e.jsx(q.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.8},className:"mt-6",children:e.jsx("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Da,{className:"w-4 h-4 text-blue-500"}),e.jsxs("span",{className:"text-sm text-gray-600",children:["Sistema ativo - Última atualização: ",r.toLocaleTimeString("pt-BR")]})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),e.jsx("span",{className:"text-xs text-green-600",children:"Online"})]})]})})})),Xc=Object.freeze(Object.defineProperty({__proto__:null,default:qc},Symbol.toStringTag,{value:"Module"})),Zc=()=>{const{restaurantId:r}=Et(),[t,s]=u.useState(null),[a,n]=u.useState([]),[i,l]=u.useState(null),[o,c]=u.useState("7d"),[h,d]=u.useState(!0),[x,p]=u.useState(null),m=r||"demo-restaurant";u.useEffect(()=>{f()},[o,m]);const f=async()=>{d(!0),p(null);try{const g=await(await fetch(`http://localhost:8001/api/v1/analytics/dashboard/${m}?period=${o}`)).json();s(g);const T=await(await fetch(`http://localhost:8001/api/v1/analytics/popular-songs/${m}?limit=5`)).json();n(T.songs);const se=await(await fetch(`http://localhost:8001/api/v1/analytics/engagement/${m}?period=${o}`)).json();l(se)}catch(v){p("Erro ao carregar dados de analytics"),console.error("Analytics error:",v)}finally{d(!1)}},y=async()=>{try{await fetch(`http://localhost:8001/api/v1/analytics/generate-test-data/${m}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({days:7})}),f()}catch(v){console.error("Error generating test data:",v)}};return h?e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-600",children:"Carregando analytics..."})]})}):x?e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-red-600 mb-4",children:x}),e.jsx("button",{onClick:f,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700",children:"Tentar Novamente"})]})}):e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("div",{className:"bg-white shadow-sm border-b",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center py-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Analytics Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"Restaurante Demo - Métricas e Relatórios"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("select",{value:o,onChange:v=>c(v.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[e.jsx("option",{value:"24h",children:"Últimas 24h"}),e.jsx("option",{value:"7d",children:"Últimos 7 dias"}),e.jsx("option",{value:"30d",children:"Últimos 30 dias"}),e.jsx("option",{value:"90d",children:"Últimos 90 dias"})]}),e.jsxs("button",{onClick:y,className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2",children:[e.jsx(vt,{className:"w-4 h-4"}),e.jsx("span",{children:"Gerar Dados Teste"})]})]})]})})}),e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[t&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(we,{className:"w-8 h-8 text-blue-600"}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total de Plays"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:t.summary.totalPlays})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(De,{className:"w-8 h-8 text-green-600"}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total de Votos"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:t.summary.totalVotes})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(rt,{className:"w-8 h-8 text-purple-600"}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Sugestões"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:t.summary.totalSuggestions})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(vt,{className:"w-8 h-8 text-orange-600"}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Engajamento"}),e.jsxs("p",{className:"text-2xl font-bold text-gray-900",children:[t.summary.engagementRate,"%"]})]})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Músicas Mais Populares"})}),e.jsx("div",{className:"p-6",children:a.length>0?e.jsx("div",{className:"space-y-4",children:a.map((v,g)=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold",children:g+1}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-gray-900",children:v.title}),e.jsx("p",{className:"text-sm text-gray-600",children:v.artist})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900",children:["Score: ",v.stats.popularityScore]}),e.jsxs("p",{className:"text-xs text-gray-600",children:[v.stats.plays," plays • ",v.stats.upvotes," ","upvotes"]})]})]},v.id))}):e.jsx("p",{className:"text-gray-500 text-center py-8",children:"Nenhum dado disponível"})})]}),i&&e.jsxs("div",{className:"bg-white rounded-lg shadow",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Métricas de Engajamento"})}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600",children:"Taxa de Engajamento"}),e.jsxs("span",{className:"font-semibold text-green-600",children:[i.metrics.engagementRate,"%"]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600",children:"Taxa de Positividade"}),e.jsxs("span",{className:"font-semibold text-blue-600",children:[i.metrics.positivityRate,"%"]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600",children:"Votos por Sugestão"}),e.jsx("span",{className:"font-semibold text-purple-600",children:i.metrics.averageVotesPerSuggestion})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600",children:"Upvotes"}),e.jsx("span",{className:"font-semibold text-green-600",children:i.metrics.upvotes})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600",children:"Downvotes"}),e.jsx("span",{className:"font-semibold text-red-600",children:i.metrics.downvotes})]})]})})]})]}),e.jsx("div",{className:"mt-8 bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Exportar Dados"}),e.jsx("p",{className:"text-gray-600",children:"Baixe relatórios detalhados em formato JSON"})]}),e.jsx("div",{className:"flex space-x-3",children:e.jsxs("a",{href:`http://localhost:8001/api/v1/analytics/export/${m}?type=all&format=json`,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2",target:"_blank",rel:"noopener noreferrer",children:[e.jsx(Ur,{className:"w-4 h-4"}),e.jsx("span",{children:"Exportar Tudo"})]})})]})})]})]})},ed=()=>{const r=cs();return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center px-4",children:e.jsxs(q.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center max-w-md mx-auto",children:[e.jsx(q.div,{initial:{scale:.8},animate:{scale:1},transition:{delay:.2},className:"mb-8",children:e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"text-8xl md:text-9xl font-bold text-gray-200 dark:text-gray-700 select-none",children:"404"}),e.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:e.jsx(q.div,{animate:{rotate:[0,10,-10,0]},transition:{duration:2,repeat:1/0,ease:"easeInOut"},className:"w-16 h-16 bg-primary-500 rounded-full flex items-center justify-center shadow-lg",children:e.jsx(we,{className:"w-8 h-8 text-white"})})})]})}),e.jsxs(q.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},className:"mb-8",children:[e.jsx("h1",{className:"text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"Página Não Encontrada"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-2",children:"Ops! A página que você está procurando não existe ou foi movida."}),e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Que tal voltar e descobrir uma nova música?"})]}),e.jsxs(q.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},className:"space-y-4",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[e.jsx(tt,{onClick:()=>r("/"),icon:e.jsx(Fa,{className:"w-4 h-4"}),size:"lg",children:"Página Inicial"}),e.jsx(tt,{onClick:()=>r(-1),variant:"outline",icon:e.jsx(si,{className:"w-4 h-4"}),size:"lg",children:"Voltar"})]}),e.jsx("div",{className:"pt-4",children:e.jsxs("button",{onClick:()=>r("/"),className:"inline-flex items-center space-x-2 text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors",children:[e.jsx(_s,{className:"w-4 h-4"}),e.jsx("span",{className:"text-sm",children:"Procurar restaurantes"})]})})]}),e.jsxs(q.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.8},className:"mt-12 p-4 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-200 dark:border-gray-700",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-900 dark:text-white mb-2",children:"Você pode estar procurando por:"}),e.jsxs("ul",{className:"text-sm text-gray-600 dark:text-gray-300 space-y-1",children:[e.jsx("li",{children:"• Página inicial do sistema"}),e.jsx("li",{children:"• Painel administrativo (/admin)"}),e.jsx("li",{children:"• Página de um restaurante específico"})]})]}),e.jsxs("div",{className:"absolute inset-0 -z-10 overflow-hidden",children:[e.jsx(q.div,{animate:{x:[0,100,0],y:[0,-100,0]},transition:{duration:20,repeat:1/0,ease:"linear"},className:"absolute top-1/4 left-1/4 w-32 h-32 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-10"}),e.jsx(q.div,{animate:{x:[0,-100,0],y:[0,100,0]},transition:{duration:15,repeat:1/0,ease:"linear"},className:"absolute bottom-1/4 right-1/4 w-32 h-32 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-10"})]})]})})};class td{constructor(){Ce(this,"sessionToken",null);Ce(this,"session",null);Ce(this,"apiUrl",fn.BASE_URL+"/api/v1");this.restoreSession()}async createSession(t,s,a){try{const n=Gs(),i=this.getDeviceInfo(),l=await fetch(`${this.apiUrl}/client/session`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({sessionToken:n,restaurantId:t,tableNumber:s,clientName:a,deviceInfo:i})});if(l.ok){const o=await l.json();return this.session=o,this.sessionToken=o.sessionToken,this.saveSession(),o}else return this.createLocalSession(t,s,a)}catch(n){return console.error("Error creating session:",n),this.createLocalSession(t,s,a)}}createLocalSession(t,s,a){const n=Gs(),i=new Date().toISOString(),l={id:Gs(),sessionToken:n,restaurantId:t,tableNumber:s,clientName:a,deviceInfo:this.getDeviceInfo(),lastActivity:i,suggestionsCount:0,votesCount:0,pageViews:1,sessionDuration:0,formattedDuration:"0m",isActive:!0,isSessionActive:!0,isNewSession:!0,engagementLevel:"low",createdAt:i,updatedAt:i,points:0,level:1,badges:[],streak:0};return this.session=l,this.sessionToken=n,this.saveSession(),l}getSession(){return this.session}getSessionToken(){return this.sessionToken}updateStats(t){this.session&&(this.session={...this.session,...t},this.saveSession())}incrementSuggestions(){this.session&&(this.session.suggestionsCount++,this.session.points+=20,this.updateLevel(),this.saveSession())}incrementVotes(){this.session&&(this.session.votesCount++,this.session.points+=10,this.updateLevel(),this.saveSession())}updateLevel(){if(this.session){const t=Math.floor(this.session.points/100)+1;t>this.session.level&&(this.session.level=t)}}awardBadge(t){return this.session&&!this.session.badges.includes(t)?(this.session.badges.push(t),this.saveSession(),!0):!1}getDeviceInfo(){const t=navigator.userAgent,s=`${screen.width}x${screen.height}`,a=navigator.language,n=Intl.DateTimeFormat().resolvedOptions().timeZone;let i="desktop";return/Mobile|Android|iPhone|iPad/.test(t)&&(i=/iPad/.test(t)?"tablet":"mobile"),{type:i,screenResolution:s,language:a,timezone:n}}saveSession(){this.session&&this.sessionToken&&(localStorage.setItem("clientSession",JSON.stringify(this.session)),localStorage.setItem("sessionToken",this.sessionToken))}restoreSession(){try{const t=localStorage.getItem("clientSession"),s=localStorage.getItem("sessionToken");t&&s&&(this.session=JSON.parse(t),this.sessionToken=s)}catch(t){console.error("Error restoring session:",t),this.clearSession()}}clearSession(){this.session=null,this.sessionToken=null,localStorage.removeItem("clientSession"),localStorage.removeItem("sessionToken")}isSessionValid(){if(!this.session||!this.sessionToken)return!1;const t=Date.now()-new Date(this.session.createdAt).getTime(),s=24*60*60*1e3;return t<s}async forceNewSession(t,s,a){return this.clearSession(),this.createSession(t,s,a)}}const ar=new td;class sd{constructor(){Ce(this,"cooldownCache",new Map);Ce(this,"cacheExpiry",3e4);Ce(this,"intervalId",null)}async checkSongCooldown(t,s){const a=`${t}:${s}`,n=this.cooldownCache.get(a);if(n&&Date.now()<n.expiresAt){if(!n.isInCooldown)return{isInCooldown:!1};const i=Math.floor((Date.now()-n.cachedAt)/1e3),l=Math.max(0,n.ttlAtCache-i);return n.lastTimeLeft=l,this.cooldownCache.set(a,n),{isInCooldown:l>0,cooldownTimeLeft:l>0?l:void 0}}try{const i=await fetch(ce(`/collaborative-playlist/${t}/cooldown/${s}`),{method:"GET",headers:{"Content-Type":"application/json"}});if(i.ok){const l=await i.json(),o=!!l.isInCooldown,c=Math.max(0,Number(l.cooldownTimeLeft||0));return this.cooldownCache.set(a,{isInCooldown:o,cachedAt:Date.now(),ttlAtCache:c,expiresAt:Date.now()+this.cacheExpiry,lastTimeLeft:c}),{isInCooldown:o,cooldownTimeLeft:o?c:void 0}}}catch(i){console.warn("Erro ao verificar cooldown:",i)}return{isInCooldown:!1}}async checkMultipleSongsCooldown(t,s){const a=s.map(async n=>{const i=await this.checkSongCooldown(t,n.youtubeVideoId);return{...n,isInCooldown:i.isInCooldown,cooldownTimeLeft:i.cooldownTimeLeft}});return Promise.all(a)}markSongInCooldown(t,s,a=10){const n=`${t}:${s}`,i=a*60;this.cooldownCache.set(n,{isInCooldown:!0,cachedAt:Date.now(),ttlAtCache:i,expiresAt:Date.now()+this.cacheExpiry,lastTimeLeft:i})}removeSongFromCooldown(t,s){const a=`${t}:${s}`;this.cooldownCache.delete(a)}clearCache(){this.cooldownCache.clear()}updateCooldownTimes(){const t=Date.now();for(const[s,a]of this.cooldownCache.entries()){if(!a.isInCooldown)continue;const n=Math.floor((t-a.cachedAt)/1e3),i=Math.max(0,a.ttlAtCache-n);i<=0?this.cooldownCache.set(s,{...a,isInCooldown:!1,lastTimeLeft:0}):this.cooldownCache.set(s,{...a,lastTimeLeft:i})}}startCooldownTimer(){this.intervalId||(this.intervalId=setInterval(()=>{this.updateCooldownTimes()},1e3))}}const ct=new sd,rd=[{label:"R$ 5,00",amount:500},{label:"R$ 20,00",amount:2e3},{label:"R$ 50,00",amount:5e3}],ad=({isOpen:r,onClose:t,suggestion:s,sessionId:a,onPaymentSuccess:n,restaurantId:i})=>{const{restaurantId:l}=Et(),o=i||l,[c]=Vr(),h=c.get("table"),[d,x]=u.useState("Cliente"),[p,m]=u.useState(""),[f,y]=u.useState("confirm"),[v,g]=u.useState(null),[k,T]=u.useState(!1),[ee,se]=u.useState(1800),[te,j]=u.useState(!1),[$,A]=u.useState(!1),[O,N]=u.useState(null);if(!o)return console.error("❌ RestaurantId não encontrado na URL"),null;u.useEffect(()=>{if(f==="payment"&&ee>0){const L=setInterval(()=>{se(re=>re<=1?(y("error"),b.error("O tempo para pagamento expirou"),0):re-1)},1e3);return()=>clearInterval(L)}},[f,ee]),u.useEffect(()=>{if(f==="payment"&&v&&!te){const L=setInterval(async()=>{await V()},5e3);return()=>clearInterval(L)}},[f,v,te]);const B=L=>{const re=Math.floor(L/60),P=L%60;return`${re.toString().padStart(2,"0")}:${P.toString().padStart(2,"0")}`},S=async L=>{try{T(!0),N(L);const re=await fetch(ce("/payments/pix/suggestion"),{method:"POST",headers:_t("application/json"),body:JSON.stringify({restaurantId:o,youtubeId:s.youtubeVideoId||s.id,title:s.title,artist:s.artist,clientName:d,clientMessage:p,tableNumber:h?parseInt(h):1,sessionId:a,amount:L})});if(re.ok){const P=await re.json();console.log("💳 DEBUG: Dados do pagamento recebidos:",P);const X=P.payment.qrCodeData==="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";(!P.payment.qrCodeData.startsWith("data:image/")||X)&&(console.warn("⚠️ QR Code placeholder ou inválido, mas continuando..."),A(!0)),g({paymentId:P.payment.id,qrCode:P.payment.pixCode,qrCodeBase64:P.payment.qrCodeData,ticketUrl:P.payment.ticketUrl||"",amount:P.payment.amount}),y("payment"),b.success("Pagamento Pix gerado com sucesso!")}else{const P=await re.json();console.error("❌ Erro na resposta do servidor:",P),b.error(P.error||"Erro ao gerar pagamento"),y("error")}}catch(re){console.error("Erro ao criar pagamento:",re),b.error("Erro ao conectar com o servidor de pagamento"),y("error")}finally{T(!1)}},V=async()=>{var L,re;if(!(!v||te))try{j(!0);const P=await fetch(ce(`/payments/${v.paymentId}/status`),{headers:_t()});if(P.ok){const X=await P.json(),ue=((L=X.payment)==null?void 0:L.status)||((re=X.status)==null?void 0:re.status);if(ue==="approved"||ue==="paid"){y("success"),b.success("Pagamento aprovado! Sua música foi adicionada à fila.");const ke=O??Math.round((v.amount||0)*100);n==null||n({paymentId:v.paymentId,amountCents:ke,clientMessage:p,clientName:d})}else(ue==="rejected"||ue==="cancelled")&&(y("error"),b.error("Pagamento rejeitado ou cancelado"))}else console.error("❌ Erro ao verificar status do pagamento:",P.status)}catch(P){console.error("Erro ao verificar pagamento:",P)}finally{j(!1)}},J=()=>{v!=null&&v.qrCode&&(navigator.clipboard.writeText(v.qrCode),b.success("Código Pix copiado!"))},Q=()=>{v!=null&&v.ticketUrl&&window.open(v.ticketUrl,"_blank")},z=()=>{y("confirm"),g(null),se(1800),A(!1),t()};return r?e.jsx(ze,{children:e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] p-4 overflow-y-auto",children:e.jsxs(q.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9},className:"bg-white dark:bg-gray-800 rounded-lg max-w-md w-full my-4",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700",children:[e.jsxs("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:[f==="confirm"&&"Escolha seu SuperVoto",f==="payment"&&"Pagamento Pix",f==="success"&&"Pagamento Aprovado!",f==="error"&&"Erro no Pagamento"]}),e.jsx("button",{onClick:z,className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors","aria-label":"Fechar modal",children:e.jsx(bt,{className:"w-5 h-5"})})]}),e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex items-center space-x-4 mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[s.thumbnailUrl&&e.jsx("img",{src:s.thumbnailUrl,alt:`Capa de ${s.title} por ${s.artist}`,className:"w-16 h-16 rounded-lg object-cover"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"font-medium text-gray-900 dark:text-white truncate",children:s.title}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 truncate",children:s.artist}),s.duration&&e.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-500",children:[Math.floor(s.duration/60),":",(s.duration%60).toString().padStart(2,"0")]})]})]}),f==="confirm"&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(Ye,{className:"w-8 h-8 text-green-600 dark:text-green-400"})}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Escolha seu SuperVoto"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"Seu SuperVoto prioriza a música na fila. Selecione um valor:"})]}),e.jsx("div",{className:"grid grid-cols-3 gap-3",children:rd.map(L=>e.jsxs("button",{onClick:()=>S(L.amount),disabled:k,className:"px-3 py-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 flex items-center justify-center gap-1 text-xs","aria-label":`Gerar Pix de ${L.label}`,children:[e.jsx(ts,{className:"w-4 h-4"}),e.jsx("span",{children:L.label})]},L.amount))}),e.jsx("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(ri,{className:"w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5"}),e.jsxs("div",{className:"text-sm",children:[e.jsx("p",{className:"font-medium text-blue-900 dark:text-blue-100 mb-1",children:"Como funciona:"}),e.jsxs("ul",{className:"text-blue-800 dark:text-blue-200 space-y-1",children:[e.jsx("li",{children:"• Pague via Pix"}),e.jsx("li",{children:"• Sua música vai para a fila prioritária"}),e.jsx("li",{children:'• Acompanhe as letras no "Cante Comigo"'}),e.jsx("li",{children:"• Outros clientes podem votar na sua performance"})]})]})]})})]}),f==="payment"&&v&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Mensagem para o restaurante (opcional)"}),e.jsx("textarea",{value:p,onChange:L=>m(L.target.value),rows:2,maxLength:200,placeholder:"Escreva uma mensagem para acompanhar seu Supervoto...",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"}),e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Máx. 200 caracteres"})]}),e.jsxs("div",{className:"flex items-center justify-center space-x-2 text-orange-600 dark:text-orange-400",children:[e.jsx(yt,{className:"w-5 h-5"}),e.jsx("span",{className:"font-mono text-lg",children:B(ee)}),e.jsx("span",{className:"text-sm",children:"para pagar"})]}),e.jsxs("div",{className:"text-center",children:[$?e.jsxs("div",{className:"bg-yellow-100 dark:bg-yellow-900/20 p-4 rounded-lg mb-4",children:[e.jsx("div",{className:"flex items-center justify-center mb-2",children:e.jsx(ts,{className:"w-8 h-8 text-yellow-600 dark:text-yellow-400"})}),e.jsx("p",{className:"text-sm text-yellow-800 dark:text-yellow-200 font-medium mb-2",children:"QR Code temporariamente indisponível"}),e.jsx("p",{className:"text-xs text-yellow-700 dark:text-yellow-300",children:"Use o código PIX abaixo para fazer o pagamento manualmente no seu app do banco"})]}):e.jsx("div",{className:"bg-white p-4 rounded-lg inline-block mb-4",children:e.jsx("img",{src:v.qrCodeBase64,alt:`QR Code para pagamento de ${s.title}`,className:"w-48 h-48 mx-auto",onError:L=>{console.error("❌ Erro ao carregar QR Code:",v.qrCodeBase64),A(!0),L.currentTarget.style.display="none"},onLoad:()=>{console.log("✅ QR Code carregado com sucesso!")}})}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-4",children:"Escaneie o QR Code com seu app do banco"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Ou copie o código Pix:"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"text",value:v.qrCode,readOnly:!0,className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-sm font-mono text-gray-900 dark:text-gray-100","aria-label":"Código Pix"}),e.jsx("button",{onClick:J,className:"px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors","aria-label":"Copiar código Pix",children:e.jsx(ai,{className:"w-4 h-4"})})]})]}),v.ticketUrl&&e.jsxs("button",{onClick:Q,className:"w-full flex items-center justify-center space-x-2 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors","aria-label":"Abrir pagamento no Mercado Pago",children:[e.jsx(Lr,{className:"w-4 h-4"}),e.jsx("span",{children:"Abrir no Mercado Pago"})]}),e.jsx("div",{className:"text-center",children:e.jsxs("div",{className:"flex items-center justify-center space-x-2 text-blue-600 dark:text-blue-400",children:[e.jsx("div",{className:"w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"}),e.jsx("span",{className:"text-sm",children:"Aguardando pagamento..."})]})})]}),f==="success"&&e.jsxs("div",{className:"text-center space-y-6",children:[e.jsx("div",{className:"w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto",children:e.jsx(qa,{className:"w-8 h-8 text-green-600 dark:text-green-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Pagamento Aprovado!"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Sua música foi adicionada à fila prioritária e tocará em breve."})]}),e.jsx("button",{onClick:z,className:"w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors","aria-label":"Continuar após pagamento aprovado",children:"Continuar"})]}),f==="error"&&e.jsxs("div",{className:"text-center space-y-6",children:[e.jsx("div",{className:"w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto",children:e.jsx(ss,{className:"w-8 h-8 text-red-600 dark:text-red-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Erro no Pagamento"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Não foi possível processar o pagamento. Tente novamente."})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("button",{onClick:()=>{y("confirm"),g(null),se(1800),A(!1)},className:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors","aria-label":"Tentar pagamento novamente",children:"Tentar Novamente"}),e.jsx("button",{onClick:z,className:"w-full py-3 px-4 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors","aria-label":"Cancelar pagamento",children:"Cancelar"})]})]})]})]})})}):null};var An={exports:{}},nd="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",id=nd,od=id;function _n(){}function Tn(){}Tn.resetWarningCache=_n;var ld=function(){function r(a,n,i,l,o,c){if(c!==od){var h=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw h.name="Invariant Violation",h}}r.isRequired=r;function t(){return r}var s={array:r,bigint:r,bool:r,func:r,number:r,object:r,string:r,symbol:r,any:r,arrayOf:t,element:r,elementType:r,instanceOf:t,node:r,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:Tn,resetWarningCache:_n};return s.PropTypes=s,s};An.exports=ld();var cd=An.exports;const $e=qs(cd);var dd=function r(t,s){if(t===s)return!0;if(t&&s&&typeof t=="object"&&typeof s=="object"){if(t.constructor!==s.constructor)return!1;var a,n,i;if(Array.isArray(t)){if(a=t.length,a!=s.length)return!1;for(n=a;n--!==0;)if(!r(t[n],s[n]))return!1;return!0}if(t.constructor===RegExp)return t.source===s.source&&t.flags===s.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===s.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===s.toString();if(i=Object.keys(t),a=i.length,a!==Object.keys(s).length)return!1;for(n=a;n--!==0;)if(!Object.prototype.hasOwnProperty.call(s,i[n]))return!1;for(n=a;n--!==0;){var l=i[n];if(!r(t[l],s[l]))return!1}return!0}return t!==t&&s!==s};const ud=qs(dd);var Nr={exports:{}},On;/**
* @link https://github.com/gajus/sister for the canonical source repository
* @license https://github.com/gajus/sister/blob/master/LICENSE BSD 3-Clause
*/On=function(){var r={},t={};return r.on=function(s,a){var n={name:s,handler:a};return t[s]=t[s]||[],t[s].unshift(n),n},r.off=function(s){var a=t[s.name].indexOf(s);a!==-1&&t[s.name].splice(a,1)},r.trigger=function(s,a){var n=t[s],i;if(n)for(i=n.length;i--;)n[i].handler(a)},r};var hd=On,kr={exports:{}},md=function(t,s,a){var n=document.head||document.getElementsByTagName("head")[0],i=document.createElement("script");typeof s=="function"&&(a=s,s={}),s=s||{},a=a||function(){},i.type=s.type||"text/javascript",i.charset=s.charset||"utf8",i.async="async"in s?!!s.async:!0,i.src=t,s.attrs&&xd(i,s.attrs),s.text&&(i.text=""+s.text);var l="onload"in i?Sa:fd;l(i,a),i.onload||Sa(i,a),n.appendChild(i)};function xd(r,t){for(var s in t)r.setAttribute(s,t[s])}function Sa(r,t){r.onload=function(){this.onerror=this.onload=null,t(null,r)},r.onerror=function(){this.onerror=this.onload=null,t(new Error("Failed to load "+this.src),r)}}function fd(r,t){r.onreadystatechange=function(){this.readyState!="complete"&&this.readyState!="loaded"||(this.onreadystatechange=null,t(null,r))}}(function(r,t){Object.defineProperty(t,"__esModule",{value:!0});var s=md,a=n(s);function n(i){return i&&i.__esModule?i:{default:i}}t.default=function(i){var l=new Promise(function(o){if(window.YT&&window.YT.Player&&window.YT.Player instanceof Function){o(window.YT);return}else{var c=window.location.protocol==="http:"?"http:":"https:";(0,a.default)(c+"//www.youtube.com/iframe_api",function(d){d&&i.trigger("error",d)})}var h=window.onYouTubeIframeAPIReady;window.onYouTubeIframeAPIReady=function(){h&&h(),o(window.YT)}});return l},r.exports=t.default})(kr,kr.exports);var pd=kr.exports,Sr={exports:{}},Cr={exports:{}},Er={exports:{}},ns=1e3,is=ns*60,os=is*60,ls=os*24,gd=ls*365.25,yd=function(r,t){t=t||{};var s=typeof r;if(s==="string"&&r.length>0)return bd(r);if(s==="number"&&isNaN(r)===!1)return t.long?wd(r):vd(r);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(r))};function bd(r){if(r=String(r),!(r.length>100)){var t=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(r);if(t){var s=parseFloat(t[1]),a=(t[2]||"ms").toLowerCase();switch(a){case"years":case"year":case"yrs":case"yr":case"y":return s*gd;case"days":case"day":case"d":return s*ls;case"hours":case"hour":case"hrs":case"hr":case"h":return s*os;case"minutes":case"minute":case"mins":case"min":case"m":return s*is;case"seconds":case"second":case"secs":case"sec":case"s":return s*ns;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return s;default:return}}}}function vd(r){return r>=ls?Math.round(r/ls)+"d":r>=os?Math.round(r/os)+"h":r>=is?Math.round(r/is)+"m":r>=ns?Math.round(r/ns)+"s":r+"ms"}function wd(r){return vs(r,ls,"day")||vs(r,os,"hour")||vs(r,is,"minute")||vs(r,ns,"second")||r+" ms"}function vs(r,t,s){if(!(r<t))return r<t*1.5?Math.floor(r/t)+" "+s:Math.ceil(r/t)+" "+s+"s"}(function(r,t){t=r.exports=n.debug=n.default=n,t.coerce=c,t.disable=l,t.enable=i,t.enabled=o,t.humanize=yd,t.names=[],t.skips=[],t.formatters={};var s;function a(h){var d=0,x;for(x in h)d=(d<<5)-d+h.charCodeAt(x),d|=0;return t.colors[Math.abs(d)%t.colors.length]}function n(h){function d(){if(d.enabled){var x=d,p=+new Date,m=p-(s||p);x.diff=m,x.prev=s,x.curr=p,s=p;for(var f=new Array(arguments.length),y=0;y<f.length;y++)f[y]=arguments[y];f[0]=t.coerce(f[0]),typeof f[0]!="string"&&f.unshift("%O");var v=0;f[0]=f[0].replace(/%([a-zA-Z%])/g,function(k,T){if(k==="%%")return k;v++;var ee=t.formatters[T];if(typeof ee=="function"){var se=f[v];k=ee.call(x,se),f.splice(v,1),v--}return k}),t.formatArgs.call(x,f);var g=d.log||t.log||console.log.bind(console);g.apply(x,f)}}return d.namespace=h,d.enabled=t.enabled(h),d.useColors=t.useColors(),d.color=a(h),typeof t.init=="function"&&t.init(d),d}function i(h){t.save(h),t.names=[],t.skips=[];for(var d=(typeof h=="string"?h:"").split(/[\s,]+/),x=d.length,p=0;p<x;p++)d[p]&&(h=d[p].replace(/\*/g,".*?"),h[0]==="-"?t.skips.push(new RegExp("^"+h.substr(1)+"$")):t.names.push(new RegExp("^"+h+"$")))}function l(){t.enable("")}function o(h){var d,x;for(d=0,x=t.skips.length;d<x;d++)if(t.skips[d].test(h))return!1;for(d=0,x=t.names.length;d<x;d++)if(t.names[d].test(h))return!0;return!1}function c(h){return h instanceof Error?h.stack||h.message:h}})(Er,Er.exports);var jd=Er.exports;(function(r,t){t=r.exports=jd,t.log=n,t.formatArgs=a,t.save=i,t.load=l,t.useColors=s,t.storage=typeof chrome<"u"&&typeof chrome.storage<"u"?chrome.storage.local:o(),t.colors=["lightseagreen","forestgreen","goldenrod","dodgerblue","darkorchid","crimson"];function s(){return typeof window<"u"&&window.process&&window.process.type==="renderer"?!0:typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}t.formatters.j=function(c){try{return JSON.stringify(c)}catch(h){return"[UnexpectedJSONParseError]: "+h.message}};function a(c){var h=this.useColors;if(c[0]=(h?"%c":"")+this.namespace+(h?" %c":" ")+c[0]+(h?"%c ":" ")+"+"+t.humanize(this.diff),!!h){var d="color: "+this.color;c.splice(1,0,d,"color: inherit");var x=0,p=0;c[0].replace(/%[a-zA-Z%]/g,function(m){m!=="%%"&&(x++,m==="%c"&&(p=x))}),c.splice(p,0,d)}}function n(){return typeof console=="object"&&console.log&&Function.prototype.apply.call(console.log,console,arguments)}function i(c){try{c==null?t.storage.removeItem("debug"):t.storage.debug=c}catch{}}function l(){var c;try{c=t.storage.debug}catch{}return!c&&typeof process<"u"&&"env"in process&&(c={}.DEBUG),c}t.enable(l());function o(){try{return window.localStorage}catch{}}})(Cr,Cr.exports);var Nd=Cr.exports,Rr={exports:{}};(function(r,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=["cueVideoById","loadVideoById","cueVideoByUrl","loadVideoByUrl","playVideo","pauseVideo","stopVideo","getVideoLoadedFraction","cuePlaylist","loadPlaylist","nextVideo","previousVideo","playVideoAt","setShuffle","setLoop","getPlaylist","getPlaylistIndex","setOption","mute","unMute","isMuted","setVolume","getVolume","seekTo","getPlayerState","getPlaybackRate","setPlaybackRate","getAvailablePlaybackRates","getPlaybackQuality","setPlaybackQuality","getAvailableQualityLevels","getCurrentTime","getDuration","removeEventListener","getVideoUrl","getVideoEmbedCode","getOptions","getOption","addEventListener","destroy","setSize","getIframe"],r.exports=t.default})(Rr,Rr.exports);var kd=Rr.exports,Pr={exports:{}};(function(r,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=["ready","stateChange","playbackQualityChange","playbackRateChange","error","apiChange","volumeChange"],r.exports=t.default})(Pr,Pr.exports);var Sd=Pr.exports,Ir={exports:{}},Ar={exports:{}};(function(r,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default={BUFFERING:3,ENDED:0,PAUSED:2,PLAYING:1,UNSTARTED:-1,VIDEO_CUED:5},r.exports=t.default})(Ar,Ar.exports);var Cd=Ar.exports;(function(r,t){Object.defineProperty(t,"__esModule",{value:!0});var s=Cd,a=n(s);function n(i){return i&&i.__esModule?i:{default:i}}t.default={pauseVideo:{acceptableStates:[a.default.ENDED,a.default.PAUSED],stateChangeRequired:!1},playVideo:{acceptableStates:[a.default.ENDED,a.default.PLAYING],stateChangeRequired:!1},seekTo:{acceptableStates:[a.default.ENDED,a.default.PLAYING,a.default.PAUSED],stateChangeRequired:!0,timeout:3e3}},r.exports=t.default})(Ir,Ir.exports);var Ed=Ir.exports;(function(r,t){Object.defineProperty(t,"__esModule",{value:!0});var s=Nd,a=d(s),n=kd,i=d(n),l=Sd,o=d(l),c=Ed,h=d(c);function d(m){return m&&m.__esModule?m:{default:m}}var x=(0,a.default)("youtube-player"),p={};p.proxyEvents=function(m){var f={},y=function(j){var $="on"+j.slice(0,1).toUpperCase()+j.slice(1);f[$]=function(A){x('event "%s"',$,A),m.trigger(j,A)}},v=!0,g=!1,k=void 0;try{for(var T=o.default[Symbol.iterator](),ee;!(v=(ee=T.next()).done);v=!0){var se=ee.value;y(se)}}catch(te){g=!0,k=te}finally{try{!v&&T.return&&T.return()}finally{if(g)throw k}}return f},p.promisifyPlayer=function(m){var f=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,y={},v=function($){f&&h.default[$]?y[$]=function(){for(var A=arguments.length,O=Array(A),N=0;N<A;N++)O[N]=arguments[N];return m.then(function(B){var S=h.default[$],V=B.getPlayerState(),J=B[$].apply(B,O);return S.stateChangeRequired||Array.isArray(S.acceptableStates)&&S.acceptableStates.indexOf(V)===-1?new Promise(function(Q){var z=function L(){var re=B.getPlayerState(),P=void 0;typeof S.timeout=="number"&&(P=setTimeout(function(){B.removeEventListener("onStateChange",L),Q()},S.timeout)),Array.isArray(S.acceptableStates)&&S.acceptableStates.indexOf(re)!==-1&&(B.removeEventListener("onStateChange",L),clearTimeout(P),Q())};B.addEventListener("onStateChange",z)}).then(function(){return J}):J})}:y[$]=function(){for(var A=arguments.length,O=Array(A),N=0;N<A;N++)O[N]=arguments[N];return m.then(function(B){return B[$].apply(B,O)})}},g=!0,k=!1,T=void 0;try{for(var ee=i.default[Symbol.iterator](),se;!(g=(se=ee.next()).done);g=!0){var te=se.value;v(te)}}catch(j){k=!0,T=j}finally{try{!g&&ee.return&&ee.return()}finally{if(k)throw T}}return y},t.default=p,r.exports=t.default})(Sr,Sr.exports);var Rd=Sr.exports;(function(r,t){Object.defineProperty(t,"__esModule",{value:!0});var s=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(x){return typeof x}:function(x){return x&&typeof Symbol=="function"&&x.constructor===Symbol&&x!==Symbol.prototype?"symbol":typeof x},a=hd,n=h(a),i=pd,l=h(i),o=Rd,c=h(o);function h(x){return x&&x.__esModule?x:{default:x}}var d=void 0;t.default=function(x){var p=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},m=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,f=(0,n.default)();if(d||(d=(0,l.default)(f)),p.events)throw new Error("Event handlers cannot be overwritten.");if(typeof x=="string"&&!document.getElementById(x))throw new Error('Element "'+x+'" does not exist.');p.events=c.default.proxyEvents(f);var y=new Promise(function(g){if((typeof x>"u"?"undefined":s(x))==="object"&&x.playVideo instanceof Function){var k=x;g(k)}else d.then(function(T){var ee=new T.Player(x,p);return f.on("ready",function(){g(ee)}),null})}),v=c.default.promisifyPlayer(y,m);return v.on=f.on,v.off=f.off,v},r.exports=t.default})(Nr,Nr.exports);var Pd=Nr.exports;const Id=qs(Pd);var Ad=Object.defineProperty,_d=Object.defineProperties,Td=Object.getOwnPropertyDescriptors,Ca=Object.getOwnPropertySymbols,Od=Object.prototype.hasOwnProperty,Vd=Object.prototype.propertyIsEnumerable,Ea=(r,t,s)=>t in r?Ad(r,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):r[t]=s,_r=(r,t)=>{for(var s in t||(t={}))Od.call(t,s)&&Ea(r,s,t[s]);if(Ca)for(var s of Ca(t))Vd.call(t,s)&&Ea(r,s,t[s]);return r},Tr=(r,t)=>_d(r,Td(t)),Dd=(r,t,s)=>new Promise((a,n)=>{var i=c=>{try{o(s.next(c))}catch(h){n(h)}},l=c=>{try{o(s.throw(c))}catch(h){n(h)}},o=c=>c.done?a(c.value):Promise.resolve(c.value).then(i,l);o((s=s.apply(r,t)).next())});function $d(r,t){var s,a;if(r.videoId!==t.videoId)return!0;const n=((s=r.opts)==null?void 0:s.playerVars)||{},i=((a=t.opts)==null?void 0:a.playerVars)||{};return n.start!==i.start||n.end!==i.end}function Ra(r={}){return Tr(_r({},r),{height:0,width:0,playerVars:Tr(_r({},r.playerVars),{autoplay:0,start:0,end:0})})}function Ld(r,t){return r.videoId!==t.videoId||!ud(Ra(r.opts),Ra(t.opts))}function Fd(r,t){var s,a,n,i;return r.id!==t.id||r.className!==t.className||((s=r.opts)==null?void 0:s.width)!==((a=t.opts)==null?void 0:a.width)||((n=r.opts)==null?void 0:n.height)!==((i=t.opts)==null?void 0:i.height)||r.iframeClassName!==t.iframeClassName||r.title!==t.title}var Ud={videoId:"",id:"",className:"",iframeClassName:"",style:{},title:"",loading:void 0,opts:{},onReady:()=>{},onError:()=>{},onPlay:()=>{},onPause:()=>{},onEnd:()=>{},onStateChange:()=>{},onPlaybackRateChange:()=>{},onPlaybackQualityChange:()=>{}},Md={videoId:$e.string,id:$e.string,className:$e.string,iframeClassName:$e.string,style:$e.object,title:$e.string,loading:$e.oneOf(["lazy","eager"]),opts:$e.objectOf($e.any),onReady:$e.func,onError:$e.func,onPlay:$e.func,onPause:$e.func,onEnd:$e.func,onStateChange:$e.func,onPlaybackRateChange:$e.func,onPlaybackQualityChange:$e.func},As=class extends ne.Component{constructor(r){super(r),this.destroyPlayerPromise=void 0,this.onPlayerReady=t=>{var s,a;return(a=(s=this.props).onReady)==null?void 0:a.call(s,t)},this.onPlayerError=t=>{var s,a;return(a=(s=this.props).onError)==null?void 0:a.call(s,t)},this.onPlayerStateChange=t=>{var s,a,n,i,l,o,c,h;switch((a=(s=this.props).onStateChange)==null||a.call(s,t),t.data){case As.PlayerState.ENDED:(i=(n=this.props).onEnd)==null||i.call(n,t);break;case As.PlayerState.PLAYING:(o=(l=this.props).onPlay)==null||o.call(l,t);break;case As.PlayerState.PAUSED:(h=(c=this.props).onPause)==null||h.call(c,t);break}},this.onPlayerPlaybackRateChange=t=>{var s,a;return(a=(s=this.props).onPlaybackRateChange)==null?void 0:a.call(s,t)},this.onPlayerPlaybackQualityChange=t=>{var s,a;return(a=(s=this.props).onPlaybackQualityChange)==null?void 0:a.call(s,t)},this.destroyPlayer=()=>this.internalPlayer?(this.destroyPlayerPromise=this.internalPlayer.destroy().then(()=>this.destroyPlayerPromise=void 0),this.destroyPlayerPromise):Promise.resolve(),this.createPlayer=()=>{if(typeof document>"u")return;if(this.destroyPlayerPromise){this.destroyPlayerPromise.then(this.createPlayer);return}const t=Tr(_r({},this.props.opts),{videoId:this.props.videoId});this.internalPlayer=Id(this.container,t),this.internalPlayer.on("ready",this.onPlayerReady),this.internalPlayer.on("error",this.onPlayerError),this.internalPlayer.on("stateChange",this.onPlayerStateChange),this.internalPlayer.on("playbackRateChange",this.onPlayerPlaybackRateChange),this.internalPlayer.on("playbackQualityChange",this.onPlayerPlaybackQualityChange),(this.props.title||this.props.loading)&&this.internalPlayer.getIframe().then(s=>{this.props.title&&s.setAttribute("title",this.props.title),this.props.loading&&s.setAttribute("loading",this.props.loading)})},this.resetPlayer=()=>this.destroyPlayer().then(this.createPlayer),this.updatePlayer=()=>{var t;(t=this.internalPlayer)==null||t.getIframe().then(s=>{this.props.id?s.setAttribute("id",this.props.id):s.removeAttribute("id"),this.props.iframeClassName?s.setAttribute("class",this.props.iframeClassName):s.removeAttribute("class"),this.props.opts&&this.props.opts.width?s.setAttribute("width",this.props.opts.width.toString()):s.removeAttribute("width"),this.props.opts&&this.props.opts.height?s.setAttribute("height",this.props.opts.height.toString()):s.removeAttribute("height"),this.props.title?s.setAttribute("title",this.props.title):s.setAttribute("title","YouTube video player"),this.props.loading?s.setAttribute("loading",this.props.loading):s.removeAttribute("loading")})},this.getInternalPlayer=()=>this.internalPlayer,this.updateVideo=()=>{var t,s,a,n;if(typeof this.props.videoId>"u"||this.props.videoId===null){(t=this.internalPlayer)==null||t.stopVideo();return}let i=!1;const l={videoId:this.props.videoId};if((s=this.props.opts)!=null&&s.playerVars&&(i=this.props.opts.playerVars.autoplay===1,"start"in this.props.opts.playerVars&&(l.startSeconds=this.props.opts.playerVars.start),"end"in this.props.opts.playerVars&&(l.endSeconds=this.props.opts.playerVars.end)),i){(a=this.internalPlayer)==null||a.loadVideoById(l);return}(n=this.internalPlayer)==null||n.cueVideoById(l)},this.refContainer=t=>{this.container=t},this.container=null,this.internalPlayer=null}componentDidMount(){this.createPlayer()}componentDidUpdate(r){return Dd(this,null,function*(){Fd(r,this.props)&&this.updatePlayer(),Ld(r,this.props)&&(yield this.resetPlayer()),$d(r,this.props)&&this.updateVideo()})}componentWillUnmount(){this.destroyPlayer()}render(){return ne.createElement("div",{className:this.props.className,style:this.props.style},ne.createElement("div",{id:this.props.id,className:this.props.iframeClassName,ref:this.refContainer}))}},Js=As;Js.propTypes=Md;Js.defaultProps=Ud;Js.PlayerState={UNSTARTED:-1,ENDED:0,PLAYING:1,PAUSED:2,BUFFERING:3,CUED:5};var ws=Js;const qd=({isOpen:r,onClose:t,suggestion:s,sessionId:a,onVoteRequest:n})=>{var Ue,He;const[i,l]=u.useState(null),[o,c]=u.useState(0),[h,d]=u.useState(!1),[x,p]=u.useState(!1),[m,f]=u.useState(!1),[y,v]=u.useState(!1),[g,k]=u.useState(28),[T,ee]=u.useState("#3B82F6"),[se,te]=u.useState(!1),[j,$]=u.useState(-1),[A,O]=u.useState(0),N=u.useRef(null),B=u.useRef(null),S=u.useRef(null),V=u.useCallback(async()=>{var W;if(!s.youtubeVideoId){b.error("ID do vídeo não fornecido");return}try{te(!0),console.log(`🎵 Carregando letras para: ${s.title} - ${s.artist}`);const ae=await fetch(ce(`/lyrics/search?title=${encodeURIComponent(s.title)}&artist=${encodeURIComponent(s.artist)}&youtubeVideoId=${s.youtubeVideoId}`));if(ae.ok){const ie=await ae.json();if(ie.success&&ie.lyrics){l(ie.lyrics),b.success("Letras sincronizadas carregadas! 🎤"),console.log(`✅ Letras carregadas com ${((W=ie.lyrics.lines)==null?void 0:W.length)||0} linhas`);return}}try{const ie=await fetch(ce(`/lyrics/youtube/${s.youtubeVideoId}`));if(ie.ok){const Oe=await ie.json();if(Oe.success&&Oe.lyrics){l(Oe.lyrics),b.success("Letras encontradas pelo YouTube ID! 🎤");return}}}catch(ie){console.warn("Fallback YouTube ID falhou:",ie)}const Ee=await fetch(ce(`/lyrics/test?title=${encodeURIComponent(s.title)}&artist=${encodeURIComponent(s.artist)}`));if(Ee.ok){const ie=await Ee.json();if(ie.success&&ie.lyrics){l(ie.lyrics),b("Usando letras de demonstração 🎵",{icon:"ℹ️",duration:4e3}),console.log("📝 Usando letras de teste");return}}throw new Error("Nenhuma fonte de letras disponível")}catch(ae){console.error("Erro ao carregar letras:",ae),b.error("Não foi possível carregar as letras. Modo karaokê sem sincronização."),l({id:`fallback_${s.youtubeVideoId}`,title:s.title,artist:s.artist,duration:s.duration||180,language:"pt",lines:[{time:0,text:"🎤 Modo Karaokê Ativo!"},{time:5,text:"Cante junto com a música!"},{time:10,text:"Letras sincronizadas não disponíveis"},{time:15,text:"Mas você pode cantar do mesmo jeito! 🎵"}],source:"fallback",isExplicit:!1,hasTimestamps:!1})}finally{te(!1)}},[s.title,s.artist,s.youtubeVideoId,s.duration]),J=u.useCallback(W=>{if(!i)return;let ae=-1,Ee=0;for(let ie=0;ie<i.lines.length&&W>=i.lines[ie].time;ie++)ae=ie;if(ae>=0&&ae<i.lines.length-1){const ie=i.lines[ae],at=i.lines[ae+1].time-ie.time,Ge=W-ie.time;Ee=Math.min(1,Math.max(0,Ge/at))}$(ae),O(Ee)},[i]),Q=u.useCallback(()=>{if(!N.current||!i)return;const W=N.current.getInternalPlayer();W&&typeof W.getCurrentTime=="function"&&W.getCurrentTime().then(ae=>{c(ae),J(ae)}).catch(ae=>{console.warn("Erro ao obter tempo atual do player:",ae)})},[i,J]),z=u.useCallback(()=>{var ae;const W=(ae=N.current)==null?void 0:ae.getInternalPlayer();W&&(h?W.pauseVideo():W.playVideo(),d(!h))},[h]),L=u.useCallback(()=>{var ae;const W=(ae=N.current)==null?void 0:ae.getInternalPlayer();W&&(x?(W.unMute(),W.setVolume(50)):W.mute(),p(!x))},[x]),re=u.useCallback(()=>{var ae;const W=(ae=N.current)==null?void 0:ae.getInternalPlayer();W&&(W.seekTo(0),h||(W.playVideo(),d(!0))),c(0),$(-1),O(0)},[h]),P=u.useCallback(()=>{var W,ae,Ee;B.current&&(m?(Ee=document.exitFullscreen)==null||Ee.call(document):(ae=(W=B.current).requestFullscreen)==null||ae.call(W),f(!m))},[m]),X=u.useCallback(W=>{W.target.setVolume(x?0:50)},[x]),ue=u.useCallback(W=>{W.data===ws.PlayerState.PLAYING?d(!0):W.data===ws.PlayerState.PAUSED?d(!1):W.data===ws.PlayerState.ENDED&&(d(!1),c(0),$(-1),O(0))},[]),ke=u.useCallback(W=>{const ae=Math.floor(W/60),Ee=Math.floor(W%60);return`${ae}:${Ee.toString().padStart(2,"0")}`},[]),Se=u.useCallback(()=>!i||j<0?null:i.lines[j]||null,[i,j]),ye=u.useCallback(()=>!i||j<0||j>=i.lines.length-1?null:i.lines[j+1]||null,[i,j]),Fe=u.useCallback(()=>{if(!i||j<0)return[];const W=Math.max(0,j-2),ae=Math.min(i.lines.length,j+3);return i.lines.slice(W,ae)},[i,j]);return u.useEffect(()=>(r&&s.youtubeVideoId&&V(),()=>{l(null),c(0),$(-1),O(0),d(!1),S.current&&(clearInterval(S.current),S.current=null)}),[r,s.youtubeVideoId,V]),u.useEffect(()=>(h&&i?S.current=setInterval(Q,100):S.current&&(clearInterval(S.current),S.current=null),()=>{S.current&&(clearInterval(S.current),S.current=null)}),[h,i,Q]),u.useEffect(()=>{const W=()=>{f(!!document.fullscreenElement)};return document.addEventListener("fullscreenchange",W),()=>document.removeEventListener("fullscreenchange",W)},[]),r?e.jsx(ze,{children:e.jsxs(q.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex flex-col z-50 overflow-hidden",ref:B,role:"dialog","aria-modal":"true","aria-labelledby":"karaoke-title",children:[e.jsxs("header",{className:"flex items-center justify-between p-3 sm:p-4 bg-black/40 backdrop-blur-md border-b border-white/10",children:[e.jsxs("div",{className:"flex items-center gap-2 sm:gap-3 overflow-hidden flex-1 min-w-0",children:[s.thumbnailUrl&&e.jsx("img",{src:s.thumbnailUrl,alt:`Capa de ${s.title}`,className:"w-8 h-8 sm:w-10 sm:h-10 rounded-lg object-cover flex-shrink-0 shadow-lg","aria-hidden":"true"}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("h2",{id:"karaoke-title",className:"text-sm sm:text-lg font-bold text-white truncate leading-tight",children:s.title}),e.jsx("p",{className:"text-xs sm:text-sm text-purple-200 truncate",children:s.artist})]})]}),e.jsxs("div",{className:"flex items-center gap-1 sm:gap-2 flex-shrink-0",children:[e.jsx("button",{onClick:()=>v(!y),className:"p-2 text-white hover:bg-white/20 rounded-full transition-colors active:scale-95","aria-label":y?"Fechar configurações":"Abrir configurações",children:e.jsx(qt,{className:"w-4 h-4 sm:w-5 sm:h-5","aria-hidden":"true"})}),e.jsx("button",{onClick:P,className:"p-2 text-white hover:bg-white/20 rounded-full transition-colors active:scale-95","aria-label":m?"Sair da tela cheia":"Entrar na tela cheia",children:m?e.jsx(Ba,{className:"w-4 h-4 sm:w-5 sm:h-5","aria-hidden":"true"}):e.jsx(qr,{className:"w-4 h-4 sm:w-5 sm:h-5","aria-hidden":"true"})}),e.jsx("button",{onClick:t,className:"p-2 text-white hover:bg-white/20 rounded-full transition-colors active:scale-95","aria-label":"Fechar player de karaokê",children:e.jsx(bt,{className:"w-4 h-4 sm:w-5 sm:h-5","aria-hidden":"true"})})]})]}),y&&e.jsx(q.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"bg-black/60 backdrop-blur-md p-3 sm:p-4 mx-3 sm:mx-4 rounded-xl border border-white/20 shadow-2xl",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3",children:[e.jsx("span",{className:"text-white text-sm font-medium",children:"Tamanho da fonte:"}),e.jsxs("div",{className:"flex items-center gap-3 flex-1",children:[e.jsx("input",{type:"range",min:"16",max:"40",value:g,onChange:W=>k(Number(W.target.value)),className:"flex-1 h-2 bg-white/20 rounded-lg appearance-none cursor-pointer","aria-label":"Ajustar tamanho da fonte"}),e.jsxs("span",{className:"text-white text-sm font-mono bg-white/10 px-2 py-1 rounded min-w-[50px] text-center",children:[g,"px"]})]})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3",children:[e.jsx("span",{className:"text-white text-sm font-medium",children:"Cor de destaque:"}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"color",value:T,onChange:W=>ee(W.target.value),className:"w-10 h-10 rounded-lg border-2 border-white/20 cursor-pointer","aria-label":"Escolher cor de destaque"}),e.jsx("span",{className:"text-white text-sm font-mono bg-white/10 px-2 py-1 rounded",children:T})]})]})]})}),e.jsxs("div",{className:"flex-1 flex flex-col overflow-hidden",children:[s.youtubeVideoId&&e.jsx("div",{className:"hidden sm:block w-full px-3 sm:px-4 pt-2",children:e.jsx("div",{className:"w-full max-w-md mx-auto aspect-video",children:e.jsx(ws,{videoId:s.youtubeVideoId,opts:{playerVars:{autoplay:0,controls:0,rel:0,showinfo:0,modestbranding:1}},onReady:X,onStateChange:ue,ref:N,className:"w-full h-full rounded-xl overflow-hidden shadow-2xl border border-white/20"})})}),e.jsx("div",{className:"flex-1 flex flex-col justify-center px-4 sm:px-6 py-4 overflow-hidden",children:se?e.jsxs("div",{className:"text-center flex flex-col items-center justify-center h-full",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"w-16 h-16 border-4 border-purple-400 border-t-transparent rounded-full animate-spin mb-6"}),e.jsx("div",{className:"absolute inset-0 w-16 h-16 border-4 border-blue-400 border-b-transparent rounded-full animate-spin animate-reverse"})]}),e.jsx("p",{className:"text-white text-lg sm:text-xl font-medium",children:"Carregando letras..."}),e.jsx("p",{className:"text-purple-200 text-sm mt-2",children:"Preparando experiência de karaokê"})]}):i?e.jsxs("div",{className:"h-full flex flex-col justify-center text-center space-y-4 sm:space-y-6",children:[e.jsx(q.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5,ease:"easeOut"},className:"flex-1 flex items-center justify-center px-2","aria-live":"polite",children:e.jsx("div",{className:"max-w-full",children:e.jsx("div",{className:"font-bold leading-tight text-center break-words",style:{fontSize:`${Math.max(g*.8,18)}px`,color:Se()?T:"white",textShadow:"2px 2px 8px rgba(0,0,0,0.9)",filter:Se()?`drop-shadow(0 0 20px ${T}40)`:"none",lineHeight:"1.2"},children:((Ue=Se())==null?void 0:Ue.text)||"🎵 Aguardando início..."})})},`current-${j}`),Se()&&i.hasTimestamps&&e.jsxs(q.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.3},className:"px-6 sm:px-12",children:[e.jsx("div",{className:"w-full bg-white/20 rounded-full h-2 shadow-inner",children:e.jsx(q.div,{className:"h-2 rounded-full bg-gradient-to-r from-purple-400 via-blue-400 to-cyan-400 shadow-lg",style:{width:`${A*100}%`},transition:{duration:.1}})}),e.jsxs("div",{className:"flex justify-between text-xs text-white/60 mt-1",children:[e.jsx("span",{children:"Linha atual"}),e.jsxs("span",{children:[Math.round(A*100),"%"]})]})]}),ye()&&e.jsx(q.div,{initial:{opacity:0,y:20},animate:{opacity:.8,y:0},transition:{duration:.4,delay:.2},className:"px-4",children:e.jsxs("div",{className:"bg-white/5 rounded-xl p-3 sm:p-4 border border-white/10",children:[e.jsx("div",{className:"text-xs text-blue-300 font-medium mb-1",children:"Próxima linha:"}),e.jsx("div",{className:"text-white/80 leading-relaxed break-words",style:{fontSize:`${Math.max(g*.6,14)}px`,textShadow:"1px 1px 4px rgba(0,0,0,0.8)"},children:(He=ye())==null?void 0:He.text})]})},`next-${j+1}`),e.jsx("div",{className:"hidden sm:block space-y-1 opacity-30 max-h-24 overflow-y-auto",children:Fe().slice(0,3).map((W,ae)=>{const Ee=i.lines.indexOf(W),ie=Ee===j,Oe=Ee<j;return ie||Ee===j+1?null:e.jsx(q.div,{initial:{opacity:0},animate:{opacity:Oe?.4:.6},transition:{duration:.3},className:"text-white/50 text-sm px-4 truncate",children:W.text},`context-${Ee}`)})})]}):e.jsxs("div",{className:"h-full flex flex-col items-center justify-center text-center px-4",children:[e.jsx(q.div,{initial:{scale:0},animate:{scale:1},transition:{duration:.6,ease:"easeOut"},className:"mb-6",children:e.jsxs("div",{className:"relative",children:[e.jsx(Ts,{className:"w-16 h-16 sm:w-20 sm:h-20 text-purple-400 mx-auto mb-4"}),e.jsx("div",{className:"absolute inset-0 w-16 h-16 sm:w-20 sm:h-20 border-4 border-purple-400/30 rounded-full animate-ping"})]})}),e.jsxs(q.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.3},className:"space-y-3",children:[e.jsx("h3",{className:"text-white text-xl sm:text-2xl font-bold",children:"🎤 Modo Karaokê Ativo!"}),e.jsx("p",{className:"text-purple-200 text-base sm:text-lg",children:"Letras sincronizadas não disponíveis"}),e.jsx("p",{className:"text-white/80 text-sm sm:text-base",children:"Mas você pode cantar junto com o vídeo! 🎵"}),e.jsx("div",{className:"mt-6 p-4 bg-white/5 rounded-xl border border-white/10",children:e.jsxs("p",{className:"text-white/70 text-sm",children:["💡 ",e.jsx("strong",{children:"Dica:"})," Use os controles abaixo para ajustar o volume e pausar a música"]})})]})]})})]}),e.jsxs("footer",{className:"bg-black/50 backdrop-blur-md border-t border-white/10",children:[i&&e.jsxs("div",{className:"px-4 pt-3",children:[e.jsxs("div",{className:"flex items-center justify-between text-white text-xs sm:text-sm mb-2",children:[e.jsx("span",{className:"font-mono",children:ke(o)}),e.jsx("span",{className:"font-mono",children:ke(s.duration||i.duration)})]}),e.jsx("div",{className:"w-full bg-white/20 rounded-full h-2 shadow-inner",children:e.jsx("div",{className:"bg-gradient-to-r from-purple-400 to-blue-400 h-2 rounded-full transition-all duration-200 shadow-lg",style:{width:`${o/(s.duration||i.duration)*100}%`},role:"progressbar","aria-valuenow":o/(s.duration||i.duration)*100,"aria-valuemin":0,"aria-valuemax":100})})]}),e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"flex items-center justify-center gap-3 sm:gap-6",children:[e.jsx("button",{onClick:re,className:"p-3 bg-white/10 text-white rounded-full hover:bg-white/20 active:scale-95 transition-all border border-white/20","aria-label":"Reiniciar música",children:e.jsx(ni,{className:"w-5 h-5 sm:w-6 sm:h-6","aria-hidden":"true"})}),e.jsx("button",{onClick:z,className:"p-4 sm:p-5 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-full hover:from-purple-700 hover:to-blue-700 active:scale-95 transition-all shadow-lg border border-white/20","aria-label":h?"Pausar música":"Tocar música",children:h?e.jsx(Fr,{className:"w-6 h-6 sm:w-8 sm:h-8","aria-hidden":"true"}):e.jsx(Je,{className:"w-6 h-6 sm:w-8 sm:h-8","aria-hidden":"true"})}),e.jsx("button",{onClick:L,className:"p-3 bg-white/10 text-white rounded-full hover:bg-white/20 active:scale-95 transition-all border border-white/20","aria-label":x?"Ativar som":"Silenciar som",children:x?e.jsx(Ma,{className:"w-5 h-5 sm:w-6 sm:h-6","aria-hidden":"true"}):e.jsx(us,{className:"w-5 h-5 sm:w-6 sm:h-6","aria-hidden":"true"})}),n&&e.jsx("button",{onClick:n,className:"p-3 bg-pink-600/80 text-white rounded-full hover:bg-pink-600 active:scale-95 transition-all border border-white/20","aria-label":"Solicitar votação da performance",children:e.jsx(Qs,{className:"w-5 h-5 sm:w-6 sm:h-6","aria-hidden":"true"})})]}),e.jsxs("div",{className:"flex items-center justify-center gap-4 sm:gap-6 mt-4 text-white/70 text-xs sm:text-sm",children:[e.jsxs("div",{className:"flex items-center gap-1.5",children:[e.jsx(Ts,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{className:"hidden sm:inline",children:"Cante Comigo"}),e.jsx("span",{className:"sm:hidden",children:"Karaokê"})]}),(i==null?void 0:i.hasTimestamps)&&e.jsxs("div",{className:"flex items-center gap-1.5",children:[e.jsx(Ut,{className:"w-4 h-4 text-yellow-400","aria-hidden":"true"}),e.jsx("span",{className:"hidden sm:inline",children:"Sincronizado"}),e.jsx("span",{className:"sm:hidden",children:"Sync"})]}),e.jsxs("div",{className:"flex items-center gap-1.5",children:[e.jsx(rt,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{className:"hidden sm:inline",children:"Interativo"}),e.jsx("span",{className:"sm:hidden",children:"Live"})]})]})]})]})]})}):null},Bd=({restaurantId:r,sessionId:t,isCollapsed:s=!1,onToggleCollapse:a})=>{var te,j,$;const[n,i]=u.useState([]),[l,o]=u.useState(null),[c,h]=u.useState(!1),[d,x]=u.useState(null),[p,m]=u.useState([]),{on:f,off:y,joinRestaurant:v}=Pt(),g=u.useCallback(async()=>{try{h(!0);const[A,O]=await Promise.all([fetch(ce(`/playback-queue/${r}`)),fetch(ce(`/collaborative-playlist/${r}/ranking`))]);if(A.ok){const N=await A.json(),B=Array.isArray(N.queue)?[...N.queue].sort((S,V)=>{const J=typeof S.position=="number"&&S.position>0,Q=typeof V.position=="number"&&V.position>0;if(J&&Q)return S.position-V.position;if(J)return-1;if(Q)return 1;if(S.isPaid!==V.isPaid)return S.isPaid?-1:1;const z=S.addedAt?new Date(S.addedAt).getTime():0,L=V.addedAt?new Date(V.addedAt).getTime():0;return z-L}):[];i(B),o(N.stats),x(N.currentlyPlaying||null)}if(O.ok){const N=await O.json();m(Array.isArray(N==null?void 0:N.data)?N.data:[])}}catch(A){console.error("Erro ao carregar fila/ranking:",A)}finally{h(!1)}},[r]),k=u.useCallback(async()=>{try{const A=await fetch(ce(`/collaborative-playlist/${r}/ranking`));if(A.ok){const O=await A.json();m(Array.isArray(O==null?void 0:O.data)?O.data:[])}}catch(A){console.warn("Falha ao recarregar ranking:",A)}},[r]);u.useEffect(()=>{g(),v(r)},[r]),u.useEffect(()=>{const A=N=>{try{if(N!=null&&N.queue){const B=Array.isArray(N.queue)?[...N.queue].sort((S,V)=>{const J=typeof S.position=="number"&&S.position>0,Q=typeof V.position=="number"&&V.position>0;if(J&&Q)return S.position-V.position;if(J)return-1;if(Q)return 1;if(S.isPaid!==V.isPaid)return S.isPaid?-1:1;const z=S.addedAt?new Date(S.addedAt).getTime():0,L=V.addedAt?new Date(V.addedAt).getTime():0;return z-L}):[];i(B)}N!=null&&N.stats&&o(N.stats),N!=null&&N.currentlyPlaying&&x(N.currentlyPlaying)}catch(B){console.warn("Falha ao aplicar queue-update:",B)}k()},O=()=>{g(),k()};return f("queue-update",A),f("reorderSelected",O),f("playlistReordered",O),f("reorder-selected",O),()=>{y("queue-update",A),y("reorderSelected",O),y("playlistReordered",O),y("reorder-selected",O)}},[f,y,g,k]);const T=A=>{const O=Math.floor(A/60),N=A%60;return`${O}:${N.toString().padStart(2,"0")}`},ee=A=>{const O=Math.floor(A/60);if(O>60){const N=Math.floor(O/60),B=O%60;return`${N}h ${B}m`}return`${O}m`},se=A=>!1;return c&&n.length===0?e.jsx("div",{className:"bg-white/5 rounded-lg p-4 border border-white/10",children:e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsx("div",{className:"w-4 h-4 border-2 border-purple-400 border-t-transparent rounded-full animate-spin"}),e.jsx("span",{className:"text-white",children:"Carregando fila..."})]})}):e.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-xl border border-white/20 overflow-hidden shadow-xl",children:[e.jsx("div",{className:"p-6 bg-gradient-to-r from-purple-500/20 to-indigo-500/20 cursor-pointer hover:from-purple-500/30 hover:to-indigo-500/30 transition-all",onClick:a,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full flex items-center justify-center shadow-lg",children:e.jsx(we,{className:"w-6 h-6 text-white"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-bold text-white",children:"Fila de Reprodução"}),l&&e.jsxs("div",{className:"flex items-center gap-4 mt-1 text-sm",children:[e.jsxs("span",{className:"text-purple-200",children:[l.totalItems," música",l.totalItems!==1?"s":""]}),l.paidItems>0&&e.jsxs("span",{className:"flex items-center gap-1 text-yellow-300",children:[e.jsx(Ye,{className:"w-3 h-3"}),l.paidItems," paga",l.paidItems!==1?"s":""]}),e.jsxs("span",{className:"text-indigo-300",children:[ee(l.estimatedWaitTime)," total"]})]}),p.length>0&&e.jsxs("div",{className:"mt-2 p-3 bg-emerald-500/20 rounded-lg border border-emerald-400/30",children:[e.jsxs("p",{className:"text-emerald-300 text-sm font-medium",children:["🎯 Próxima por votação: ",((te=p[0])==null?void 0:te.title)||((j=p[0])==null?void 0:j.youtubeVideoId)]}),e.jsxs("p",{className:"text-emerald-200 text-xs",children:[($=p[0])==null?void 0:$.voteCount," votos • Será tocada automaticamente"]})]})]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[l&&l.paidItems>0&&e.jsxs("div",{className:"flex items-center space-x-1 bg-yellow-500/20 px-3 py-1 rounded-full",children:[e.jsx(Ye,{className:"w-4 h-4 text-yellow-400"}),e.jsx("span",{className:"text-yellow-300 font-medium",children:l.paidItems})]}),a&&e.jsx("button",{className:"p-2 bg-white/10 rounded-lg text-purple-300 hover:text-white hover:bg-white/20 transition-all",children:s?e.jsx(or,{className:"w-5 h-5"}):e.jsx(rs,{className:"w-5 h-5"})})]})]})}),d&&!s&&e.jsx("div",{className:"p-6 bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-y border-green-400/30",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:d.thumbnailUrl||`https://img.youtube.com/vi/${d.suggestionId}/mqdefault.jpg`,alt:d.title,className:"w-16 h-16 rounded-xl object-cover shadow-lg"}),e.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-black/40 rounded-xl",children:e.jsx(us,{className:"w-5 h-5 text-green-400 animate-pulse"})})]}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[e.jsx(Je,{className:"w-4 h-4 text-green-400"}),e.jsx("span",{className:"text-green-400 text-sm font-bold uppercase tracking-wide",children:"Tocando Agora"}),d.isPaid&&e.jsxs("div",{className:"flex items-center gap-1 bg-yellow-500/20 px-2 py-1 rounded-full",children:[e.jsx(Ye,{className:"w-3 h-3 text-yellow-400"}),e.jsx("span",{className:"text-yellow-300 text-xs font-medium",children:"SuperVoto"})]})]}),e.jsx("h4",{className:"text-white font-bold text-lg truncate",children:d.title}),e.jsx("p",{className:"text-gray-300 text-sm truncate",children:d.artist}),d.tableName&&e.jsxs("p",{className:"text-purple-300 text-xs",children:["Por ",d.tableName]})]}),e.jsx("div",{className:"text-right",children:e.jsx("div",{className:"text-white text-sm",children:T(d.duration)})})]})}),!s&&p.length>0&&e.jsxs("div",{className:"p-6 border-t border-white/20 bg-gradient-to-r from-indigo-500/10 to-purple-500/10",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-4",children:[e.jsx(Ut,{className:"w-5 h-5 text-yellow-400"}),e.jsx("h4",{className:"text-lg font-bold text-white",children:"Mais Votadas Agora"})]}),e.jsx("div",{className:"space-y-3",children:p.slice(0,5).map((A,O)=>e.jsx("div",{className:`p-3 rounded-lg border transition-all ${O===0?"bg-gradient-to-r from-emerald-500/20 to-green-500/20 border-emerald-400/30":"bg-white/5 border-white/10 hover:bg-white/10"}`,children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[e.jsxs("div",{className:`w-8 h-8 rounded-full flex items-center justify-center font-bold text-sm ${O===0?"bg-emerald-500 text-white":O===1?"bg-gray-400 text-black":O===2?"bg-amber-600 text-white":"bg-indigo-500/30 text-indigo-300"}`,children:["#",O+1]}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"font-medium text-white truncate",children:[A.title||A.youtubeVideoId,O===0&&e.jsx("span",{className:"ml-2 px-2 py-1 bg-emerald-500/30 text-emerald-300 rounded-full text-xs font-bold",children:"🎯 Próxima"})]}),e.jsxs("div",{className:"flex items-center gap-2 mt-1 text-xs",children:[e.jsxs("span",{className:"flex items-center gap-1 text-indigo-300",children:[e.jsx(rt,{className:"w-3 h-3"}),A.voteCount," votos"]}),A.isPaid&&e.jsxs("span",{className:"flex items-center gap-1 text-yellow-300 bg-yellow-500/20 px-2 py-1 rounded",children:[e.jsx(Ye,{className:"w-3 h-3"}),"SuperVoto"]})]})]})]})})},A.youtubeVideoId))})]}),e.jsx(ze,{children:!s&&e.jsx(q.div,{initial:{height:0},animate:{height:"auto"},exit:{height:0},className:"overflow-hidden",children:n.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(we,{className:"w-16 h-16 text-gray-500 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-400 text-lg font-medium",children:"Fila vazia"}),e.jsx("p",{className:"text-gray-500 text-sm",children:"Seja o primeiro a sugerir uma música!"})]}):e.jsx("div",{className:"p-4 space-y-3 max-h-96 overflow-y-auto",children:n.map((A,O)=>e.jsx(q.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:O*.05},className:"p-4 rounded-lg border transition-all hover:shadow-lg bg-white/5 border-white/10 hover:bg-white/10",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsxs("div",{className:`w-10 h-10 rounded-full flex items-center justify-center font-bold ${A.isPaid?"bg-yellow-500 text-black":"bg-purple-500/30 text-purple-300"}`,children:["#",A.position]})}),e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:A.thumbnailUrl||`https://img.youtube.com/vi/${A.suggestionId}/mqdefault.jpg`,alt:A.title,className:"w-14 h-14 rounded-lg object-cover shadow-md"}),A.isPaid&&e.jsx("div",{className:"absolute -top-1 -right-1 w-5 h-5 bg-yellow-500 rounded-full flex items-center justify-center shadow-lg",children:e.jsx(Ye,{className:"w-3 h-3 text-black"})})]}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"font-bold text-lg truncate text-white",children:A.title}),e.jsx("p",{className:"text-purple-200 text-sm truncate mb-2",children:A.artist}),e.jsxs("div",{className:"flex flex-wrap items-center gap-2",children:[A.tableName&&e.jsxs("div",{className:"flex items-center gap-1 bg-purple-500/20 px-2 py-1 rounded-full",children:[e.jsx(rt,{className:"w-3 h-3 text-purple-400"}),e.jsx("span",{className:"text-purple-300 text-xs font-medium",children:A.tableName})]}),A.isPaid&&A.paymentAmount&&e.jsxs("div",{className:"flex items-center gap-1 bg-yellow-500/20 px-2 py-1 rounded-full",children:[e.jsx(Ye,{className:"w-3 h-3 text-yellow-400"}),e.jsxs("span",{className:"text-yellow-300 text-xs font-bold",children:["R$ ",Number(A.paymentAmount||0).toFixed(2)]})]}),typeof A.voteCount=="number"&&e.jsxs("div",{className:"flex items-center gap-1 bg-indigo-500/20 px-2 py-1 rounded-full",children:[e.jsx(Ut,{className:"w-3 h-3 text-indigo-400"}),e.jsxs("span",{className:"text-indigo-300 text-xs font-medium",children:[A.voteCount," votos"]})]}),se()]})]}),e.jsxs("div",{className:"text-right flex-shrink-0",children:[e.jsx("div",{className:"text-white text-sm",children:T(A.duration)}),A.estimatedPlayTime&&e.jsxs("div",{className:"flex items-center space-x-1 text-gray-400 text-xs",children:[e.jsx(yt,{className:"w-3 h-3"}),e.jsx("span",{children:new Date(A.estimatedPlayTime).toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit"})})]})]})]})},A.id))})})}),!s&&l&&l.totalItems>0&&e.jsx("div",{className:"p-3 bg-white/5 border-t border-white/10",children:e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{className:"flex items-center space-x-1 text-purple-300",children:[e.jsx(we,{className:"w-3 h-3"}),e.jsxs("span",{children:[l.totalItems," total"]})]}),l.paidItems>0&&e.jsxs("div",{className:"flex items-center space-x-1 text-yellow-400",children:[e.jsx(Ye,{className:"w-3 h-3"}),e.jsxs("span",{children:[l.paidItems," pagas"]})]})]}),e.jsxs("div",{className:"flex items-center space-x-1 text-gray-400",children:[e.jsx(yt,{className:"w-3 h-3"}),e.jsxs("span",{children:[ee(l.totalDuration)," total"]})]})]})})]})},Qd=({isOpen:r,onClose:t,restaurantId:s,sessionId:a})=>{const[n,i]=u.useState(!1),[l,o]=u.useState(!1),[c,h]=u.useState({name:"",avatar:"",joinedAt:new Date().toISOString(),level:1,experience:0,nextLevelExp:100,title:"Novo Ouvinte",preferences:{favoriteGenres:[],notifications:!0,autoShare:!1}}),[d,x]=u.useState([]),p=u.useCallback(()=>{o(!0);try{const g=localStorage.getItem(`clientProfile_${a}`);if(g)h(JSON.parse(g));else{const k={name:`Cliente ${a.slice(0,8)}`,avatar:"",joinedAt:new Date().toISOString(),level:1,experience:0,nextLevelExp:100,title:"Novo Ouvinte",preferences:{favoriteGenres:[],notifications:!0,autoShare:!1}};h(k),localStorage.setItem(`clientProfile_${a}`,JSON.stringify(k))}}catch(g){console.error("Error loading profile:",g)}finally{o(!1)}},[a]),m=u.useCallback(async()=>{o(!0);try{const g=await fetch(ce(`/collaborative-playlist/${s}/ranking`));if(g.ok){const{ranking:k}=await g.json();x(k)}else throw new Error("Falha ao buscar ranking")}catch(g){console.error("Erro ao carregar ranking:",g),x([{title:"Música Exemplo",artist:"Artista",points:61,voteCount:12,superVote5:3,superVote20:1,superVote40:1},{title:"Outra Música",artist:"Outro",points:40,voteCount:5,superVote5:2,superVote20:1,superVote40:0}]),b.error("Erro ao carregar ranking, usando dados de exemplo")}finally{o(!1)}},[s]),f=u.useCallback(()=>{try{localStorage.setItem(`clientProfile_${a}`,JSON.stringify(c)),i(!1),b.success("Perfil salvo com sucesso!")}catch(g){console.error("Error saving profile:",g),b.error("Erro ao salvar perfil")}},[c,a]),y=u.useCallback(g=>{var T;const k=(T=g.target.files)==null?void 0:T[0];if(k&&k.type.startsWith("image/")){const ee=new FileReader;ee.onload=()=>{h(se=>({...se,avatar:ee.result}))},ee.readAsDataURL(k)}else b.error("Por favor, selecione uma imagem válida")},[]),v=u.useCallback(()=>Math.min(c.experience/c.nextLevelExp*100,100),[c.experience,c.nextLevelExp]);return u.useCallback(g=>{const k={Iniciante:"text-gray-600 bg-gray-100 dark:bg-gray-700",Ativo:"text-blue-600 bg-blue-100 dark:bg-blue-900/30",Engajado:"text-green-600 bg-green-100 dark:bg-green-900/30",Expert:"text-purple-600 bg-purple-100 dark:bg-purple-900/30"};return k[g]||k.Iniciante},[]),u.useEffect(()=>{r&&a&&(p(),m())},[r,a,p,m]),r?e.jsx(ze,{children:e.jsx(q.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",onClick:t,role:"dialog","aria-modal":"true","aria-labelledby":"profile-modal-title",children:e.jsxs(q.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},onClick:g=>g.stopPropagation(),className:"bg-white dark:bg-gray-900 rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden",children:[e.jsx("header",{className:"bg-gradient-to-r from-purple-600 to-blue-600 p-4 sm:p-6 text-white",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center",children:c.avatar?e.jsx("img",{src:c.avatar,alt:`Avatar de ${c.name}`,className:"w-full h-full rounded-full object-cover"}):e.jsx(Ft,{className:"w-8 h-8","aria-hidden":"true"})}),e.jsxs("div",{children:[e.jsx("h2",{id:"profile-modal-title",className:"text-xl sm:text-2xl font-bold",children:c.name}),e.jsx("p",{className:"text-blue-100 text-sm",children:c.title}),e.jsxs("div",{className:"flex items-center gap-2 mt-1",children:[e.jsxs("span",{className:"text-sm",children:["Nível ",c.level]}),e.jsx("div",{className:"w-24 h-2 bg-white/20 rounded-full",children:e.jsx("div",{className:"h-full bg-white rounded-full transition-all duration-300",style:{width:`${v()}%`},role:"progressbar","aria-valuenow":Math.round(v()),"aria-valuemin":0,"aria-valuemax":100})})]})]})]}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsx("button",{onClick:t,className:"p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors","aria-label":"Fechar perfil",children:e.jsx(bt,{className:"w-5 h-5","aria-hidden":"true"})})})]})}),e.jsx("div",{className:"p-4 sm:p-6 max-h-[60vh] overflow-y-auto",children:l?e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4","aria-hidden":"true"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"Carregando perfil..."})]}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Nome"}),n?e.jsx("input",{type:"text",value:c.name,onChange:g=>h({...c,name:g.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500","aria-label":"Editar nome do perfil"}):e.jsx("p",{className:"text-gray-900 dark:text-white",children:c.name})]}),n&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Avatar"}),e.jsx("input",{type:"file",accept:"image/*",onChange:y,className:"w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:bg-blue-100 file:text-blue-700 hover:file:bg-blue-200","aria-label":"Fazer upload de avatar"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Membro desde"}),e.jsx("p",{className:"text-gray-900 dark:text-white",children:new Date(c.joinedAt).toLocaleDateString("pt-BR",{day:"2-digit",month:"long",year:"numeric"})})]})]}),e.jsx("div",{className:"flex justify-end items-center",children:n?e.jsxs("button",{onClick:f,className:"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors","aria-label":"Salvar alterações do perfil",children:[e.jsx($a,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{children:"Salvar"})]}):e.jsxs("button",{onClick:()=>i(!0),className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors","aria-label":"Editar perfil",children:[e.jsx(Ft,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{children:"Editar"})]})})]})})})]})})}):null},zd=({restaurantId:r,currentTableNumber:t,isVisible:s,onClose:a})=>{const[n,i]=u.useState([]),[l,o]=u.useState(!1),[c,h]=u.useState("points");u.useEffect(()=>{if(s){d();const f=setInterval(d,3e4);return()=>clearInterval(f)}},[s,r]);const d=async()=>{o(!0);try{const f=await fetch(`http://localhost:8001/api/v1/analytics/tables/${r}/leaderboard`);if(f.ok){const v=(await f.json()).tables.map((g,k)=>({tableNumber:g.tableNumber||`Mesa ${k+1}`,totalSuggestions:g.totalSuggestions||0,totalVotes:g.totalVotes||0,averageScore:g.averageScore||0,paidSuggestions:g.paidSuggestions||0,points:g.points||0,rank:k+1,isCurrentTable:g.tableNumber===t}));i(v)}}catch(f){console.error("Erro ao carregar estatísticas das mesas:",f),i([{tableNumber:"Mesa 5",totalSuggestions:8,totalVotes:45,averageScore:4.2,paidSuggestions:3,points:850,rank:1,isCurrentTable:t==="5"},{tableNumber:"Mesa 10",totalSuggestions:6,totalVotes:38,averageScore:3.8,paidSuggestions:2,points:720,rank:2,isCurrentTable:t==="10"},{tableNumber:"Mesa 3",totalSuggestions:5,totalVotes:32,averageScore:3.5,paidSuggestions:1,points:650,rank:3,isCurrentTable:t==="3"},{tableNumber:"Mesa 7",totalSuggestions:4,totalVotes:28,averageScore:3.2,paidSuggestions:1,points:580,rank:4,isCurrentTable:t==="7"},{tableNumber:"Mesa 12",totalSuggestions:3,totalVotes:22,averageScore:2.9,paidSuggestions:0,points:450,rank:5,isCurrentTable:t==="12"}])}finally{o(!1)}},x=[...n].sort((f,y)=>{switch(c){case"suggestions":return y.totalSuggestions-f.totalSuggestions;case"votes":return y.totalVotes-f.totalVotes;default:return y.points-f.points}}),p=f=>{switch(f){case 1:return e.jsx(oi,{className:"w-5 h-5 text-yellow-500"});case 2:return e.jsx(ii,{className:"w-5 h-5 text-gray-400"});case 3:return e.jsx(Qa,{className:"w-5 h-5 text-orange-600"});default:return e.jsx(Os,{className:"w-5 h-5 text-gray-500"})}},m=f=>{switch(f){case 1:return"from-yellow-500 to-yellow-600";case 2:return"from-gray-400 to-gray-500";case 3:return"from-orange-500 to-orange-600";default:return"from-gray-600 to-gray-700"}};return s?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs(q.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:20},className:"bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-md max-h-[80vh] overflow-hidden",children:[e.jsx("div",{className:"bg-gradient-to-r from-purple-600 to-pink-600 p-6 text-white",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(Os,{className:"w-6 h-6"}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold",children:"Ranking das Mesas"}),e.jsx("p",{className:"text-purple-100 text-sm",children:"Competição em tempo real"})]})]}),e.jsx("button",{onClick:a,className:"text-white/80 hover:text-white transition-colors",children:e.jsx(bt,{className:"w-6 h-6"})})]})}),e.jsx("div",{className:"p-4 border-b border-gray-200 dark:border-gray-700",children:e.jsx("div",{className:"flex space-x-2",children:[{key:"points",label:"Pontos"},{key:"suggestions",label:"Sugestões"},{key:"votes",label:"Votos"}].map(f=>e.jsx("button",{onClick:()=>h(f.key),className:`px-3 py-1 rounded-full text-sm font-medium transition-colors ${c===f.key?"bg-purple-600 text-white":"bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-purple-100 dark:hover:bg-gray-600"}`,children:f.label},f.key))})}),e.jsx("div",{className:"p-4 max-h-96 overflow-y-auto",children:l?e.jsxs("div",{className:"flex items-center justify-center py-8",children:[e.jsx("div",{className:"w-8 h-8 border-4 border-purple-600 border-t-transparent rounded-full animate-spin"}),e.jsx("span",{className:"ml-3 text-gray-600 dark:text-gray-400",children:"Carregando ranking..."})]}):e.jsx("div",{className:"space-y-3",children:e.jsx(ze,{children:x.map((f,y)=>e.jsxs(q.div,{layout:!0,initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3,delay:y*.05},className:`p-4 rounded-lg border-2 transition-all ${f.isCurrentTable?"border-purple-500 bg-purple-50 dark:bg-purple-900/20":"border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50"}`,children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:`p-2 rounded-full bg-gradient-to-r ${m(y+1)}`,children:p(y+1)}),e.jsxs("div",{children:[e.jsxs("h3",{className:`font-bold ${f.isCurrentTable?"text-purple-700 dark:text-purple-300":"text-gray-900 dark:text-white"}`,children:[f.tableNumber,f.isCurrentTable&&e.jsx("span",{className:"ml-2 text-xs bg-purple-600 text-white px-2 py-1 rounded-full",children:"Você"})]}),e.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400",children:[e.jsxs("span",{children:[f.points," pts"]}),e.jsxs("span",{children:[f.totalSuggestions," sugestões"]}),e.jsxs("span",{children:[f.totalVotes," votos"]})]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"flex items-center space-x-1 text-yellow-500",children:[e.jsx(Ut,{className:"w-4 h-4"}),e.jsx("span",{className:"font-medium",children:f.averageScore.toFixed(1)})]}),f.paidSuggestions>0&&e.jsxs("div",{className:"text-xs text-green-600 dark:text-green-400",children:[f.paidSuggestions," pagas"]})]})]}),e.jsx("div",{className:"mt-3",children:e.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2",children:e.jsx("div",{className:`h-2 rounded-full bg-gradient-to-r ${m(y+1)} transition-all duration-500`,style:{width:`${Math.min(f.points/Math.max(...x.map(v=>v.points))*100,100)}%`}})})})]},f.tableNumber))})})}),e.jsx("div",{className:"p-4 bg-gray-50 dark:bg-gray-700 text-center",children:e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"🎵 Continue sugerindo e votando para subir no ranking!"})})]})}):null},Wd=()=>u.useCallback(async(r,t={},s=3,a=1e3)=>{let n;for(let i=0;i<s;i++)try{const l=await fetch(r,t);if(l.ok)return l;let o=`Erro na requisição: ${l.status} ${l.statusText}`;try{const c=await l.json();c.error?o=c.error:c.message&&(o=c.message)}catch{}if(n=new Error(o),l.status===401||l.status===403||l.status===409)throw n}catch(l){if(n=l instanceof Error?l:new Error(String(l)),i===s-1)break;console.warn(`Tentativa ${i+1} falhou, tentando novamente em ${a}ms...`),await new Promise(o=>setTimeout(o,a))}throw n},[]),Hd=()=>({getBadges:u.useCallback(t=>{const s=[];return t.suggestionsCount>=1&&s.push("🎵 Primeira Sugestão"),t.suggestionsCount>=5&&s.push("🎶 Melomaníaco"),t.suggestionsCount>=10&&s.push("🎸 DJ Amador"),t.votesCount>=10&&s.push("👍 Crítico Musical"),t.votesCount>=25&&s.push("⭐ Especialista"),t.streak>=3&&s.push("🔥 Em Chamas"),t.points>=500&&s.push("🏆 Lenda"),s},[])}),Pa=()=>{const{restaurantId:r}=Et(),[t]=Vr(),s=t.get("table"),a=Wd(),{getBadges:n}=Hd(),[i,l]=u.useState(null),[o,c]=u.useState(""),[h,d]=u.useState([]),[x,p]=u.useState(!1),[m,f]=u.useState([]),[y,v]=u.useState(1),[g,k]=u.useState(!1),[T,ee]=u.useState(!0),[se,te]=u.useState(0),[j,$]=u.useState(null),[A,O]=u.useState(!0),[N,B]=u.useState(!1),[S,V]=u.useState(null),[J,Q]=u.useState({points:0,level:1,badges:[],suggestionsCount:0,votesCount:0,streak:0}),[z,L]=u.useState(""),[re,P]=u.useState(!0),[X,ue]=u.useState(!1),[ke,Se]=u.useState(null),[ye,Fe]=u.useState(!1),[Ue,He]=u.useState(!1),[W,ae]=u.useState(null),[Ee,ie]=u.useState(!1),[Oe,at]=u.useState(null),[Ge,nt]=u.useState(!1),[xt,Me]=u.useState(!1),[ft,Xe]=u.useState(!1);u.useState(!1);const[he,_e]=u.useState(!1);u.useState(new Map);const[ut,xe]=u.useState(null),[Re,Ze]=u.useState(""),qe=u.useRef(0),{isConnected:We,joinRestaurant:Ie,on:Ke,off:it,reconnect:Dt}=Pt();u.useEffect(()=>{if(r)try{localStorage.setItem("currentRestaurantId",r),Dt()}catch{}},[r,Dt]),u.useEffect(()=>{We&&r&&Ie(r)},[We,r,Ie]),u.useEffect(()=>{r&&ct.startCooldownTimer()},[r]);const jt=u.useCallback(async()=>{var I;if(!r){b.error("ID do restaurante não encontrado");return}try{const Z=await(await a(ce(`/restaurants/${r}`),{headers:{"Content-Type":"application/json"}})).json();l(Z.restaurant||{id:r,name:"Restaurante Demo",description:"Ambiente acolhedor com música interativa",isActive:!0,isOpen:!0}),console.log(`🏪 Entrou na sala do restaurante: ${((I=Z.restaurant)==null?void 0:I.name)||"Restaurante Demo"}`)}catch(G){console.error("Erro ao carregar restaurante:",G),l({id:r,name:"Restaurante Demo",description:"Ambiente acolhedor com música interativa",isActive:!0,isOpen:!0}),b.error("Erro ao carregar dados do restaurante",{duration:4e3,icon:"⚠️"})}},[r,a]),pt=u.useCallback(async()=>{var I;if(!r){b.error("ID do restaurante não encontrado");return}try{const Z=await(await a(ce(`/playback/${r}/state`),{headers:{"Content-Type":"application/json"}})).json();if(Z.success===!1)throw new Error(Z.message||"Erro ao carregar música atual");$(((I=Z.state)==null?void 0:I.currentTrack)||null)}catch(G){console.error("Erro ao carregar música atual:",G),$(null)}},[r,a]),E=u.useCallback(async(I=1,G=!1)=>{if(!r){b.error("ID do restaurante não encontrado");return}k(!0);try{const me=new URLSearchParams({page:I.toString(),limit:24 .toString()});if(m.length){const R=m.filter(_=>["rock","pop","sertanejo","mpb","eletronica","funk"].includes(_));R.length&&me.append("genres",R.join(","));const F=m.filter(_=>["happy","sad","energetic","calm"].includes(_));F.length&&me.append("moods",F.join(","))}const C=await(await a(ce(`/restaurants/${r}/playlist?${me.toString()}`),{headers:{"Content-Type":"application/json"}})).json();if(C.success&&C.results){const R=C.results.map(_=>({id:_.youtubeVideoId||_.id,title:_.title,artist:_.artist,duration:_.duration||0,formattedDuration:_.formattedDuration||"0:00",thumbnailUrl:_.thumbnailUrl||`https://img.youtube.com/vi/${_.youtubeVideoId||_.id}/mqdefault.jpg`,youtubeVideoId:_.youtubeVideoId||_.id,channelName:_.artist,viewCount:_.viewCount||0,publishedAt:_.addedAt||new Date().toISOString(),genre:Array.isArray(_.genres)&&_.genres.length?_.genres[0]:_.genre}));let F=R;try{const _=await ct.checkMultipleSongsCooldown(r,R.map(le=>({youtubeVideoId:le.youtubeVideoId}))),H=new Map;_.forEach(le=>H.set(le.youtubeVideoId,{isInCooldown:le.isInCooldown,cooldownTimeLeft:le.cooldownTimeLeft})),F=R.map(le=>{var be,ve;return{...le,isInCooldown:(be=H.get(le.youtubeVideoId))==null?void 0:be.isInCooldown,cooldownTimeLeft:(ve=H.get(le.youtubeVideoId))==null?void 0:ve.cooldownTimeLeft}})}catch(_){console.warn("Falha ao enriquecer playlist com cooldown:",_)}d(_=>G?[..._,...F]:F),te(C.total||F.length),ee(F.length===24),v(I)}else G||(d([]),b("Nenhuma música encontrada na playlist",{icon:"ℹ️"}))}catch(Z){console.error("Erro ao carregar playlist:",Z),b.error("Erro ao carregar playlist do restaurante"),G||d([])}finally{k(!1)}},[r,m,a]),M=u.useCallback(async()=>{if(!o.trim()){b.error("Digite uma busca válida");return}if(!r){b.error("ID do restaurante não encontrado");return}p(!0);try{const I=new URLSearchParams({q:encodeURIComponent(o.trim())});if(m.length){const me=m.filter(C=>["rock","pop","sertanejo","mpb","eletronica","funk"].includes(C));me.length&&I.append("genres",me.join(","));const w=m.filter(C=>["happy","sad","energetic","calm"].includes(C));w.length&&I.append("moods",w.join(","))}const Z=await(await a(ce(`/search/music?${I.toString()}`),{headers:{"Content-Type":"application/json"}})).json();if(Z.success&&Z.results){const me=Z.results.map(C=>({id:C.youtubeVideoId||C.id,title:C.title,artist:C.artist,duration:C.duration||0,formattedDuration:C.formattedDuration||"0:00",thumbnailUrl:C.thumbnailUrl||`https://img.youtube.com/vi/${C.youtubeVideoId||C.id}/mqdefault.jpg`,youtubeVideoId:C.youtubeVideoId||C.id,channelName:C.artist,viewCount:C.viewCount||0,publishedAt:C.addedAt||new Date().toISOString(),genre:Array.isArray(C.genres)&&C.genres.length?C.genres[0]:C.genre}));let w=me;try{const C=await ct.checkMultipleSongsCooldown(r,me.map(F=>({youtubeVideoId:F.youtubeVideoId}))),R=new Map;C.forEach(F=>R.set(F.youtubeVideoId,{isInCooldown:F.isInCooldown,cooldownTimeLeft:F.cooldownTimeLeft})),w=me.map(F=>{var _,H;return{...F,isInCooldown:(_=R.get(F.youtubeVideoId))==null?void 0:_.isInCooldown,cooldownTimeLeft:(H=R.get(F.youtubeVideoId))==null?void 0:H.cooldownTimeLeft}})}catch(C){console.warn("Falha ao enriquecer busca com cooldown:",C)}d(w),te(Z.total||w.length),ee(!1),v(1),w.length>0?b.success(`${w.length} música(s) encontrada(s)`,{icon:"🔍"}):b(`Nenhuma música encontrada para "${o}"`,{icon:"🔍"})}else d([]),b(`Nenhuma música encontrada para "${o}"`,{icon:"🔍"})}catch(I){console.error("Erro ao buscar músicas:",I),b.error("Erro ao buscar músicas"),d([])}finally{p(!1)}},[o,r,m,a]),D=u.useCallback(async()=>{if(!r){b.error("ID do restaurante não encontrado");return}P(!0);try{const I=await ar.forceNewSession(r,s||void 0,z||void 0);V(I),Q({points:I.points||0,level:I.level||1,badges:I.badges||[],suggestionsCount:I.suggestionsCount||0,votesCount:I.votesCount||0,streak:I.streak||0}),!I.clientName&&!z&&Fe(!0)}catch(I){console.error("Erro ao inicializar sessão:",I),b.error("Erro ao inicializar sessão",{duration:4e3,icon:"⚠️"})}finally{P(!1)}},[r,s,z]),K=u.useCallback((I,G=10)=>{Q(Z=>{const me={...Z,points:Z.points+G,suggestionsCount:I==="suggest"?Z.suggestionsCount+1:Z.suggestionsCount,votesCount:I==="vote"?Z.votesCount+1:Z.votesCount,streak:Z.streak+1},w=Math.floor(me.points/100)+1,C=n(me);w>Z.level&&(ue(!0),setTimeout(()=>ue(!1),3e3),b.success(`🎉 Level Up! Agora você é nível ${w}!`,{duration:5e3,icon:"🏆"}));const R=C.find(F=>!Z.badges.includes(F));return R&&(ar.awardBadge(R),Se(R),setTimeout(()=>Se(null),3e3),b.success(`🏆 Nova conquista: ${R}!`,{duration:5e3,icon:"🎖️"})),{...me,level:w,badges:C}})},[n]),Y=u.useCallback(I=>{ae(I),He(!0)},[]),je=u.useCallback(async I=>{if(!S||!r){b.error("Sessão ou restaurante não encontrado");return}try{if((await ct.checkSongCooldown(r,I.youtubeVideoId)).isInCooldown){b.error("Música em cooldown no momento. Tente outra ou aguarde.");return}const me=await(await a(ce(`/collaborative-playlist/${r}/vote`),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({youtubeVideoId:I.youtubeVideoId,tableNumber:S.tableNumber,clientSessionId:S.id})})).json();if((me==null?void 0:me.success)===!1)throw new Error((me==null?void 0:me.message)||"Falha ao registrar voto");b.success(`Voto registrado para "${I.title}" ✅`),K("vote",10)}catch(G){console.error("Erro ao registrar voto:",G),b.error(G instanceof Error?G.message:"Erro ao registrar voto")}},[S,r,a,K]),Ne=u.useCallback(async I=>{if(!r){b.error("Restaurante não encontrado");return}try{const G=await fetch(ce(`/playback-queue/${r}/like`),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({youtubeVideoId:I.youtubeVideoId,action:"like"})}),Z=await G.json();if(!G.ok||(Z==null?void 0:Z.success)===!1)throw new Error((Z==null?void 0:Z.message)||"Falha ao registrar curtida");b.success(`Curtida registrada para "${I.title}" ❤`)}catch(G){console.error("Erro ao registrar curtida:",G),b.error(G instanceof Error?G.message:"Erro ao registrar curtida")}},[r]),U=u.useCallback(async({paymentId:I,amountCents:G,clientMessage:Z,clientName:me})=>{if(!W||!S||!r){b.error("Dados insuficientes para processar pagamento");return}try{const C=await(await a(ce(`/collaborative-playlist/${r}/supervote`),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({youtubeVideoId:W.youtubeVideoId,paymentAmount:(G||0)/100,paymentId:I,tableNumber:S.tableNumber,clientSessionId:S.id,clientMessage:Z||void 0,clientName:me||z||void 0})})).json();if(C.success===!1)throw new Error(C.message||"Erro ao processar pagamento");b.success(`SuperVoto aplicado em "${W.title}"! ⭐`),K("vote",25),c(""),o.trim()&&E(1),at({id:W.id,title:W.title,artist:W.artist,thumbnailUrl:W.thumbnailUrl,duration:W.duration,youtubeVideoId:W.youtubeVideoId,status:"approved",upvotes:0,downvotes:0,score:0,createdAt:new Date().toISOString(),isPaid:!0,clientSessionId:S.id}),He(!1),setTimeout(()=>{b(R=>e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("span",{children:'🎤 Quer cantar junto? Ative o "Cante Comigo"!'}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:()=>{ie(!0),b.dismiss(R.id)},className:"px-3 py-1 bg-blue-600 text-white rounded text-sm",children:"Sim, vamos cantar!"}),e.jsx("button",{onClick:()=>b.dismiss(R.id),className:"px-3 py-1 bg-gray-600 text-white rounded text-sm",children:"Não, obrigado"})]})]}),{duration:8e3})},2e3)}catch(w){console.error("Erro após pagamento:",w),b.error("Erro ao processar pagamento",{duration:4e3,icon:"❌"})}finally{ae(null)}},[W,S,r,E,o,a,K]),oe=u.useCallback(async()=>{if(!z.trim()){b.error("Por favor, digite seu nome");return}if(!r){b.error("ID do restaurante não encontrado");return}try{const I=await ar.createSession(r,s||void 0,z);V(I),Fe(!1);const G=`clientProfile_${I.id}`,Z=localStorage.getItem(G);if(Z){const me=JSON.parse(Z);me.name=z,localStorage.setItem(G,JSON.stringify(me))}else localStorage.setItem(G,JSON.stringify({name:z,avatar:"",joinedAt:new Date().toISOString(),level:1,experience:0,nextLevelExp:100,title:"Novo Ouvinte",preferences:{favoriteGenres:[],notifications:!0,autoShare:!1}}));b.success(`Bem-vindo, ${z}! 🎵`,{duration:4e3,icon:"👋"})}catch(I){console.error("Erro ao salvar nome:",I),b.error("Erro ao salvar seu nome")}},[z,r,s]),pe=u.useCallback(()=>{const I=()=>{pt(),b.success("Fila atualizada pela votação!",{icon:"🔄"})},G=C=>{var R;$(C.suggestion),b(`🎵 Tocando agora: ${C.suggestion.title}`,{duration:5e3,icon:"🎧"});try{r&&((R=C.suggestion)!=null&&R.youtubeVideoId)&&(ct.markSongInCooldown(r,C.suggestion.youtubeVideoId),d(F=>F.map(_=>_.youtubeVideoId===C.suggestion.youtubeVideoId?{..._,isInCooldown:!0}:_)))}catch{}},Z=C=>{try{const R=C==null?void 0:C.video;if(!(R!=null&&R.youtubeVideoId))return;$(F=>(F==null?void 0:F.youtubeVideoId)===R.youtubeVideoId?F:{id:R.suggestionId||R.youtubeVideoId,title:R.title,artist:R.artist,youtubeVideoId:R.youtubeVideoId,thumbnailUrl:R.thumbnailUrl,duration:R.duration,status:"approved",upvotes:0,downvotes:0,score:0,createdAt:new Date().toISOString()}),b(`🎯 Próxima selecionada: ${R.title}`,{icon:"⏭️"})}catch{}},me=()=>{pt()},w=C=>{try{const R=C!=null&&C.timestamp?new Date(C.timestamp).getTime():Date.now();if(!Number.isFinite(R)||R<=qe.current)return;qe.current=R;const F=R+5*60*1e3;F>Date.now()&&xe(F)}catch{}};return Ke("now-playing",G),Ke("playlistReordered",I),Ke("reorderSelected",Z),Ke("queue-update",me),Ke("ranking-snapshot",w),()=>{it("now-playing",G),it("playlistReordered",I),it("reorderSelected",Z),it("queue-update",me),it("ranking-snapshot",w)}},[Ke,it,pt,r]);return u.useEffect(()=>{if(!ut){Ze("");return}const I=()=>{const Z=Math.max(0,Math.floor((ut-Date.now())/1e3)),me=String(Math.floor(Z/60)).padStart(2,"0"),w=String(Z%60).padStart(2,"0");Ze(`${me}:${w}`)};I();const G=setInterval(I,1e3);return()=>clearInterval(G)},[ut]),u.useEffect(()=>{if(!r){b.error("ID do restaurante não encontrado");return}(async()=>{P(!0);try{await Promise.all([D(),jt(),pt(),E()]),Ie(r)}catch(Z){console.error("Erro na inicialização:",Z),b.error("Erro ao carregar dados iniciais")}finally{P(!1)}})();const G=pe();return()=>{G()}},[r]),u.useEffect(()=>{r&&(v(1),E(1))},[m,r]),re&&!i?e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center",children:e.jsxs("div",{className:"text-center text-white",children:[e.jsx(Qe,{className:"w-12 h-12 animate-spin mx-auto mb-4","aria-hidden":"true"}),e.jsx("h2",{className:"text-xl font-semibold",children:"Carregando..."}),e.jsx("p",{className:"text-sm text-purple-300 mt-2",children:"Conectando ao restaurante"}),e.jsx("button",{onClick:()=>{jt(),pt(),E()},className:"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors","aria-label":"Tentar novamente",children:"Tentar Novamente"})]})}):e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white",children:[e.jsx("header",{className:"bg-black/30 backdrop-blur-md border-b border-white/10 p-4 sm:p-6 sticky top-0 z-10",children:e.jsxs("div",{className:"max-w-4xl mx-auto text-center",children:[e.jsxs("div",{className:"flex items-center justify-center gap-3 mb-3",children:[e.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-pink-500 to-violet-500 rounded-full flex items-center justify-center",children:e.jsx(we,{className:"w-6 h-6","aria-hidden":"true"})}),e.jsx("div",{children:e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold",children:i==null?void 0:i.name})})]}),e.jsx("p",{className:"text-sm text-purple-200 mb-4",children:"Escolha e vote nas músicas que vão animar seu momento!"}),e.jsxs("div",{className:"flex flex-wrap justify-center gap-2 sm:gap-4 text-sm",children:[s&&e.jsxs("div",{className:"flex items-center gap-1",title:`Mesa ${s}`,children:[e.jsx("span",{className:"text-lg",children:"🪑"}),e.jsxs("span",{children:["Mesa ",s]})]}),e.jsxs("div",{className:"flex items-center gap-1",title:"Votos realizados",children:[e.jsx(Xt,{className:"w-4 h-4 text-blue-400","aria-hidden":"true"}),e.jsx("span",{children:J.votesCount})]}),e.jsxs("button",{onClick:()=>nt(!0),className:"flex items-center gap-1 px-3 py-1 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full hover:from-blue-600 hover:to-indigo-600 transition-transform hover:scale-105","aria-label":"Ver perfil",children:[e.jsx(Ft,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{children:"Perfil"})]}),e.jsxs("button",{onClick:()=>Me(!0),className:"flex items-center gap-1 px-3 py-1 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full hover:from-purple-600 hover:to-pink-600 transition-transform hover:scale-105","aria-label":"Ver ranking",children:[e.jsx(Os,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{children:"Ranking"})]})]}),J.badges.length>0&&e.jsxs("div",{className:"flex flex-wrap justify-center gap-2 mt-3",children:[J.badges.slice(0,3).map((I,G)=>e.jsx("span",{className:"px-2 py-1 bg-white/10 rounded-full text-xs border border-white/20",title:I,children:I},G)),J.badges.length>3&&e.jsxs("span",{className:"px-2 py-1 bg-white/10 rounded-full text-xs text-purple-200 border border-white/20",title:`${J.badges.length-3} badges adicionais`,children:["+",J.badges.length-3," mais"]})]}),e.jsxs("div",{className:"mt-2 flex items-center justify-center gap-2",children:[e.jsx("div",{className:`w-2 h-2 rounded-full ${We?"bg-green-400 animate-pulse":"bg-red-400"}`}),e.jsx("span",{className:"text-xs text-purple-200",children:We?"Conectado ao restaurante":"Reconectando..."}),Re&&e.jsxs("span",{className:"ml-3 text-xs text-purple-200 px-2 py-1 bg-white/10 rounded-full border border-white/20",title:"Próxima reavaliação do ranking",children:["🗳️ Próxima votação em ",Re]})]})]})}),e.jsxs("main",{className:"max-w-4xl mx-auto px-4 py-6 space-y-6",children:[j&&e.jsxs(q.section,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20","aria-labelledby":"now-playing",children:[e.jsxs("h2",{id:"now-playing",className:"flex items-center gap-2 text-xl font-bold mb-4",children:[e.jsx(Je,{className:"w-5 h-5 text-green-400","aria-hidden":"true"}),"Tocando Agora"]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:j.thumbnailUrl||`https://img.youtube.com/vi/${j.youtubeVideoId}/mqdefault.jpg`,alt:`Capa de ${j.title}`,className:"w-16 h-16 rounded-lg object-cover",loading:"lazy"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-semibold truncate",children:j.title}),e.jsx("p",{className:"text-purple-200 truncate",children:j.artist})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-xl font-bold text-green-400",children:[j.score>0?"+":"",j.score]}),e.jsx("div",{className:"text-xs text-purple-200",children:"votos"})]})]})]}),e.jsxs("section",{className:"bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20","aria-labelledby":"search",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h2",{id:"search",className:"flex items-center gap-2 text-xl font-bold",children:[e.jsx(_s,{className:"w-5 h-5","aria-hidden":"true"}),"Buscar Músicas"]}),e.jsx("button",{onClick:()=>Xe(!ft),className:"px-3 py-2 bg-white/10 rounded-lg border border-white/20 text-xs hover:bg-white/20 inline-flex items-center gap-1","aria-label":"Alternar seção de busca",children:ft?e.jsxs(e.Fragment,{children:[e.jsx(or,{className:"w-4 h-4"}),e.jsx("span",{children:"Expandir"})]}):e.jsxs(e.Fragment,{children:[e.jsx(rs,{className:"w-4 h-4"}),e.jsx("span",{children:"Recolher"})]})})]}),!ft&&e.jsxs("div",{className:"flex gap-3 mb-4",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx("input",{type:"text",value:o,onChange:I=>c(I.target.value),onKeyPress:I=>I.key==="Enter"&&M(),placeholder:"Busque por música ou artista...",className:"w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-purple-200 focus:outline-none focus:ring-2 focus:ring-purple-500","aria-label":"Buscar músicas por título ou artista"}),o&&e.jsx("button",{onClick:()=>c(""),className:"absolute right-12 top-1/2 -translate-y-1/2 text-white/60 hover:text-white","aria-label":"Limpar busca",children:e.jsx(bt,{className:"w-4 h-4"})})]}),e.jsxs("button",{onClick:M,disabled:x||!o.trim(),className:"px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 flex items-center gap-2 whitespace-nowrap","aria-label":"Buscar músicas",children:[x?e.jsx(Qe,{className:"w-4 h-4 animate-spin","aria-hidden":"true"}):e.jsx(_s,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{children:"Buscar"})]})]})]}),e.jsxs("section",{className:"bg-gradient-to-br from-purple-500/10 via-blue-500/10 to-indigo-500/10 backdrop-blur-md rounded-xl p-6 border border-purple-400/30 shadow-lg","aria-labelledby":"karaoke-section",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg",children:e.jsx(Ts,{className:"w-6 h-6 text-white","aria-hidden":"true"})}),e.jsxs("div",{children:[e.jsx("h2",{id:"karaoke-section",className:"text-xl font-bold text-white",children:"Cante Comigo (Karaokê)"}),e.jsx("p",{className:"text-purple-200 text-sm",children:"Letras sincronizadas em tempo real"})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"flex items-center gap-1 px-2 py-1 bg-green-500/20 rounded-full border border-green-400/30",children:[e.jsx("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-pulse"}),e.jsx("span",{className:"text-green-300 text-xs font-medium",children:"Melhorado"})]}),e.jsx("button",{onClick:()=>_e(!he),className:"px-3 py-2 bg-white/10 rounded-lg border border-white/20 text-xs hover:bg-white/20 inline-flex items-center gap-1 transition-colors","aria-label":"Alternar seção de karaokê",children:he?e.jsxs(e.Fragment,{children:[e.jsx(or,{className:"w-4 h-4"}),e.jsx("span",{children:"Expandir"})]}):e.jsxs(e.Fragment,{children:[e.jsx(rs,{className:"w-4 h-4"}),e.jsx("span",{children:"Recolher"})]})})]})]}),!he&&e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-white/5 rounded-lg p-4 border border-white/10",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex-1 min-w-0",children:j?e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:j.thumbnailUrl||`https://img.youtube.com/vi/${j.youtubeVideoId}/mqdefault.jpg`,alt:`Capa de ${j.title}`,className:"w-16 h-16 rounded-lg object-cover shadow-md",loading:"lazy"}),e.jsx("div",{className:"absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white animate-pulse"})]}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("p",{className:"font-semibold truncate text-white",children:j.title}),e.jsx("p",{className:"text-purple-200 text-sm truncate",children:j.artist}),e.jsx("div",{className:"flex items-center gap-2 mt-1",children:e.jsxs("div",{className:"flex items-center gap-1 text-xs text-green-300",children:[e.jsx("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),e.jsx("span",{children:"Tocando agora"})]})})]})]}):e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-16 h-16 bg-white/10 rounded-lg flex items-center justify-center",children:e.jsx(we,{className:"w-6 h-6 text-white/50"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-white/70 font-medium",children:"Aguardando música"}),e.jsx("p",{className:"text-purple-200 text-sm",children:"Nenhuma música tocando no momento"})]})]})}),e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsxs("button",{onClick:()=>{console.log("🎤 Botão karaokê clicado",{currentlyPlaying:j}),at(j?{id:j.id,title:j.title,artist:j.artist,thumbnailUrl:j.thumbnailUrl,duration:j.duration,youtubeVideoId:j.youtubeVideoId,status:"approved",upvotes:0,downvotes:0,score:0,createdAt:new Date().toISOString()}:{id:"demo-karaoke",title:"Música de Demonstração",artist:"Artista Demo",thumbnailUrl:"https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg",duration:180,youtubeVideoId:"dQw4w9WgXcQ",status:"approved",upvotes:0,downvotes:0,score:0,createdAt:new Date().toISOString()}),ie(!0),b.success("🎤 Abrindo karaokê!")},className:"px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg hover:from-purple-700 hover:to-blue-700 inline-flex items-center gap-2 font-medium transition-all transform hover:scale-105","aria-label":"Abrir karaokê",children:[e.jsx(Ts,{className:"w-5 h-5","aria-hidden":"true"}),e.jsx("span",{children:j?"Iniciar Karaokê":"Demo Karaokê"})]}),!j&&e.jsx("p",{className:"text-xs text-purple-300 text-center",children:"Clique para testar o karaokê com música demo"})]})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:[e.jsxs("div",{className:"bg-white/5 rounded-lg p-3 border border-white/10",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center",children:e.jsx(we,{className:"w-4 h-4 text-blue-400"})}),e.jsx("span",{className:"text-sm font-medium text-white",children:"Sincronização"})]}),e.jsx("p",{className:"text-xs text-purple-200",children:"Letras sincronizadas em tempo real com a música"})]}),e.jsxs("div",{className:"bg-white/5 rounded-lg p-3 border border-white/10",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("div",{className:"w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center",children:e.jsx(qt,{className:"w-4 h-4 text-green-400"})}),e.jsx("span",{className:"text-sm font-medium text-white",children:"Personalização"})]}),e.jsx("p",{className:"text-xs text-purple-200",children:"Ajuste tamanho da fonte e cores do destaque"})]}),e.jsxs("div",{className:"bg-white/5 rounded-lg p-3 border border-white/10",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("div",{className:"w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center",children:e.jsx(qr,{className:"w-4 h-4 text-purple-400"})}),e.jsx("span",{className:"text-sm font-medium text-white",children:"Tela Cheia"})]}),e.jsx("p",{className:"text-xs text-purple-200",children:"Modo fullscreen para melhor experiência"})]})]})]})]}),e.jsxs("section",{className:"bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20","aria-labelledby":"playlist",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h2",{id:"playlist",className:"flex items-center gap-2 text-xl font-bold",children:[e.jsx(we,{className:"w-5 h-5","aria-hidden":"true"}),o?"Resultados da Busca":"Playlist do Restaurante"]}),se>0&&e.jsxs("div",{className:"text-sm text-purple-200",children:[h.length," de ",se," músicas"]})]}),g&&h.length===0?e.jsxs("div",{className:"flex justify-center items-center py-12",children:[e.jsx(Qe,{className:"w-8 h-8 text-purple-400 animate-spin","aria-hidden":"true"}),e.jsx("span",{className:"ml-3 text-purple-200",children:"Carregando músicas..."})]}):h.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(li,{className:"w-12 h-12 text-purple-400 mx-auto mb-3","aria-hidden":"true"}),e.jsx("p",{className:"text-purple-200",children:"Nenhuma música encontrada."}),e.jsx("p",{className:"text-sm text-purple-300",children:"Use a busca ou filtros para encontrar músicas."})]}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6",children:h.map(I=>e.jsxs(q.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},whileHover:{scale:I.isInCooldown?1:1.03},className:`bg-white/5 rounded-lg p-4 border border-white/10 hover:bg-white/10 transition-all ${I.isInCooldown?"opacity-60 border-orange-400/30 bg-orange-500/5":""}`,role:"article","aria-labelledby":`song-${I.id}`,children:[e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:I.thumbnailUrl,alt:`Capa de ${I.title}`,className:`w-full h-32 object-cover rounded-lg mb-3 ${I.isInCooldown?"grayscale":""}`,loading:"lazy"}),I.isInCooldown&&e.jsx("div",{className:"absolute inset-0 bg-orange-500/20 rounded-lg flex items-center justify-center",children:e.jsx("div",{className:"bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-bold",children:"⏰ Cooldown"})})]}),e.jsx("h3",{id:`song-${I.id}`,className:`font-semibold text-sm truncate ${I.isInCooldown?"text-orange-300":""}`,children:I.title}),e.jsx("p",{className:`text-xs truncate mb-2 ${I.isInCooldown?"text-orange-400":"text-purple-200"}`,children:I.artist}),I.isInCooldown&&I.cooldownTimeLeft&&e.jsxs("p",{className:"text-orange-400 text-xs mb-2 font-medium",children:["🚫 Disponível em ",Math.ceil(I.cooldownTimeLeft/60)," min"]}),e.jsxs("div",{className:"flex justify-between items-center text-xs text-purple-300 mb-3",children:[e.jsx("span",{children:I.formattedDuration}),e.jsxs("button",{onClick:()=>Ne(I),disabled:re,className:"inline-flex items-center gap-1 text-pink-300 hover:text-pink-400 disabled:opacity-50 transition-colors","aria-label":`Curtir ${I.title}`,title:"Curtir",children:[e.jsx(Qs,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{className:"sr-only",children:"Curtir"})]})]}),e.jsx("div",{className:"flex flex-col gap-2",children:I.isInCooldown?e.jsx("div",{className:"px-3 py-2 bg-orange-500/20 border border-orange-400/30 rounded-lg flex items-center justify-center gap-1 text-xs text-orange-300",children:e.jsx("span",{children:"⏰ Em Cooldown"})}):e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>je(I),disabled:re,className:"px-3 py-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 flex items-center justify-center gap-1 text-xs","aria-label":`Registrar voto para ${I.title}`,children:[e.jsx(we,{className:"w-3 h-3","aria-hidden":"true"}),e.jsx("span",{children:"Voto"})]}),e.jsxs("button",{onClick:()=>Y(I),disabled:re,className:"px-3 py-2 bg-gradient-to-r from-green-600 to-emerald-600 rounded-lg hover:from-green-700 hover:to-emerald-700 disabled:opacity-50 flex items-center justify-center gap-1 text-xs","aria-label":`Adicionar ${I.title} com SuperVoto`,children:[e.jsx(Ye,{className:"w-3 h-3","aria-hidden":"true"}),e.jsx("span",{children:"SuperVoto"})]})]})})]},I.id))}),T&&e.jsx("div",{className:"text-center",children:e.jsxs("button",{onClick:()=>E(y+1,!0),disabled:g,className:"px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 flex items-center gap-2 mx-auto","aria-label":"Carregar mais músicas da playlist",children:[g?e.jsx(Qe,{className:"w-4 h-4 animate-spin","aria-hidden":"true"}):e.jsx(we,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{children:g?"Carregando...":"Carregar Mais Músicas"})]})}),!T&&se>24&&e.jsxs("p",{className:"text-center text-sm text-purple-300",children:["🎵 Todas as ",se," músicas foram carregadas!"]})]})]}),A&&e.jsx("section",{className:"bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20","aria-labelledby":"playback-queue",children:e.jsx(Bd,{restaurantId:r||"",sessionId:(S==null?void 0:S.id)||"",isCollapsed:N,onToggleCollapse:()=>B(!N)})}),e.jsxs("footer",{className:"text-center py-6",children:[e.jsx("p",{className:"text-sm text-purple-300",children:"🎵 Powered by Sistema de Playlist Interativa"}),e.jsx("p",{className:"text-xs text-purple-400 mt-1",children:"Sugestões são moderadas e podem levar alguns minutos para aparecer na fila"})]})]}),e.jsxs(ze,{children:[X&&e.jsx(q.div,{initial:{opacity:0,scale:.5,y:50},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.5,y:-50},className:"fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50",role:"alert","aria-live":"polite",children:e.jsxs("div",{className:"bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-8 py-6 rounded-xl shadow-2xl text-center",children:[e.jsx("div",{className:"text-4xl mb-2",children:"🎉"}),e.jsx("h3",{className:"text-xl font-bold mb-1",children:"LEVEL UP!"}),e.jsxs("p",{className:"text-sm",children:["Você alcançou o nível ",J.level,"!"]})]})}),ke&&e.jsx(q.div,{initial:{opacity:0,scale:.5,y:50},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.5,y:-50},className:"fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50",role:"alert","aria-live":"polite",children:e.jsxs("div",{className:"bg-gradient-to-r from-purple-500 to-pink-500 text-white px-8 py-6 rounded-xl shadow-2xl text-center",children:[e.jsx("div",{className:"text-4xl mb-2",children:"🏆"}),e.jsx("h3",{className:"text-xl font-bold mb-1",children:"NOVA CONQUISTA!"}),e.jsx("p",{className:"text-sm",children:ke})]})}),ye&&e.jsx(q.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",role:"dialog","aria-modal":"true","aria-labelledby":"name-modal-title",children:e.jsxs(q.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:20},className:"bg-white dark:bg-gray-900 rounded-xl p-6 max-w-md w-full shadow-2xl",children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx(Ft,{className:"w-12 h-12 text-purple-500 mx-auto mb-3","aria-hidden":"true"}),e.jsx("h3",{id:"name-modal-title",className:"text-xl font-bold text-gray-800 dark:text-white mb-2",children:"Bem-vindo! 🎵"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Como podemos te chamar? (Opcional)"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("input",{type:"text",value:z,onChange:I=>L(I.target.value),placeholder:"Digite seu nome...",className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100",onKeyPress:I=>I.key==="Enter"&&oe(),autoFocus:!0,"aria-label":"Digite seu nome"}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:oe,disabled:!z.trim(),className:"flex-1 bg-purple-500 text-white py-3 rounded-lg hover:bg-purple-600 transition-colors disabled:opacity-50","aria-label":"Confirmar nome",children:"Continuar"}),e.jsx("button",{onClick:()=>Fe(!1),className:"flex-1 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-3 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors","aria-label":"Pular entrada de nome",children:"Pular"})]})]})]})})]}),Ue&&W&&e.jsx(ad,{isOpen:Ue,onClose:()=>{He(!1),ae(null)},suggestion:{id:W.id,title:W.title,artist:W.artist,thumbnailUrl:W.thumbnailUrl,duration:W.duration,youtubeVideoId:W.youtubeVideoId},sessionId:(S==null?void 0:S.sessionToken)||"",restaurantId:r||"",onPaymentSuccess:U}),Oe&&e.jsx(qd,{isOpen:Ee,onClose:()=>{ie(!1),at(null)},suggestion:Oe,sessionId:(S==null?void 0:S.id)||"",onVoteRequest:()=>b.success("Votação solicitada! 🗳️",{duration:4e3})}),Ge&&S&&e.jsx(Qd,{isOpen:Ge,onClose:()=>nt(!1),restaurantId:r||"",sessionId:S.id}),e.jsx(zd,{restaurantId:r||"",currentTableNumber:s||void 0,isVisible:xt,onClose:()=>Me(!1)})]})},nr=r=>{const t=Math.floor(r/60),s=r%60;return`${t}:${String(s).padStart(2,"0")}`},Ia=()=>{const{restaurantId:r}=Et(),{on:t,off:s,joinRestaurant:a}=Pt(),[n,i]=u.useState(null),[l,o]=u.useState([]),[c,h]=u.useState([]),[d,x]=u.useState([]),[p,m]=u.useState(null),[f,y]=u.useState(Date.now()),[v,g]=u.useState([]),[k,T]=u.useState(!1),[ee,se]=u.useState(null);u.useEffect(()=>{r&&ct.startCooldownTimer()},[r]);const te=u.useRef(0),j=u.useRef(0),$=async()=>{if(!r)return;T(!0),se(null);const N=_t();let B=0;const S=3;for(;B<S;)try{const V=await fetch(ce(`/restaurants/${r}/playlist`),{headers:N});if(!V.ok){if((V.status===502||V.status===500)&&B<S-1){B++,await new Promise(z=>setTimeout(z,500*B));continue}throw new Error(`HTTP ${V.status}`)}const J=await V.json(),Q=Array.isArray(J==null?void 0:J.results)?J.results:[];Q.sort((z,L)=>(z.position||0)-(L.position||0)),g(Q),T(!1);return}catch(V){if(B<S-1){B++,await new Promise(J=>setTimeout(J,500*B));continue}se((V==null?void 0:V.message)||"Falha ao carregar playlist"),T(!1);return}};u.useEffect(()=>{if(!r)return;a(r),(async()=>{var B;try{const S=_t(),[V,J]=await Promise.all([fetch(ce(`/playback-queue/${r}`),{headers:S}),fetch(ce(`/collaborative-playlist/${r}/ranking`),{headers:S})]);if(V.ok){const Q=await V.json();o(Q.queue||[]),i(Q.currentlyPlaying||null)}if(J.ok){const Q=await J.json();try{const z=await Promise.all((Array.isArray(Q==null?void 0:Q.data)?Q.data:[]).map(async L=>{const re=L.youtubeVideoId;try{const P=await ct.checkSongCooldown(r,re);return{...L,isInCooldown:P.isInCooldown,cooldownTimeLeft:P.cooldownTimeLeft}}catch{return L}}));h(z)}catch{h(Array.isArray(Q==null?void 0:Q.data)?Q.data:[])}}try{const Q=await fetch(ce("/playlist-reorder/status"),{headers:S});if(Q.ok){const z=await Q.json(),L=(B=z==null?void 0:z.status)==null?void 0:B.nextExecution;L&&!p&&m(new Date(L).getTime())}}catch{}if(await $(),r){try{localStorage.setItem("currentRestaurantId",r)}catch{}a(r)}}catch{}})()},[r,a]),u.useEffect(()=>{const N=P=>{Array.isArray(P==null?void 0:P.queue)&&o(P.queue),P!=null&&P.currentlyPlaying&&i(P.currentlyPlaying)},B=P=>{try{const X=P!=null&&P.timestamp?new Date(P.timestamp).getTime():null;if(X){const ue=X+3e5;(!p||ue>p+1e3)&&(m(ue),j.current=ue)}}catch{}try{const X=Array.isArray(P==null?void 0:P.paidQueue)?P.paidQueue:[],ue=Array.isArray(P==null?void 0:P.freeQueue)?P.freeQueue:[],ke=[...X,...ue].map(ye=>({youtubeVideoId:ye.youtubeVideoId,title:ye.title,artist:ye.artist,voteCount:Number(ye.voteCount||ye.normalVoteCount||0),isPaid:!!ye.isPaid,normalVoteCount:Number(ye.normalVoteCount||0),superVoteCount:Number(ye.superVoteCount||0),thumbnailUrl:ye.thumbnailUrl}));ke.length>0&&h(ke);const Se=P!=null&&P.timestamp?new Date(P.timestamp).getTime():null;if(Se){const ye=Se+3e5;(!p||ye>p+1e3)&&(m(ye),j.current=ye)}}catch{}},S=P=>{if(P!=null&&P.suggestion){const X=P.suggestion;i({id:X.id||X.youtubeVideoId,suggestionId:X.youtubeVideoId,youtubeVideoId:X.youtubeVideoId,title:X.title,artist:X.artist,duration:X.duration||0,thumbnailUrl:X.thumbnailUrl,isPaid:!!X.isPaid,paymentAmount:X.paymentAmount,position:0})}},V=P=>{var ue,ke,Se,ye,Fe,Ue,He,W;const X={id:`${Date.now()}_${Math.random()}`,at:(P==null?void 0:P.timestamp)||new Date().toISOString(),amount:Number((ue=P==null?void 0:P.payment)==null?void 0:ue.amount)||0,voteWeight:Number((ke=P==null?void 0:P.payment)==null?void 0:ke.voteWeight)||0,title:(Se=P==null?void 0:P.suggestion)==null?void 0:Se.title,artist:(ye=P==null?void 0:P.suggestion)==null?void 0:ye.artist,youtubeVideoId:(Fe=P==null?void 0:P.suggestion)==null?void 0:Fe.youtubeVideoId,clientName:(Ue=P==null?void 0:P.payment)==null?void 0:Ue.clientName,tableNumber:(He=P==null?void 0:P.payment)==null?void 0:He.tableNumber,message:(W=P==null?void 0:P.payment)==null?void 0:W.message};x(ae=>[X,...ae].slice(0,50))},J={id:0},Q={t:0},z=async()=>{try{const P=await fetch(ce(`/collaborative-playlist/${r}/ranking`),{headers:_t()});if(P.ok){const X=await P.json();h(Array.isArray(X==null?void 0:X.data)?X.data:[])}}catch{}},L=P=>{const X=Date.now();if(!(X-Q.t<1e3)){Q.t=X,J.id&&clearTimeout(J.id),J.id=setTimeout(()=>{z(),J.id=0},300);try{const ue=P!=null&&P.timestamp?new Date(P.timestamp).getTime():null;if(!ue||ue<=te.current)return;te.current=ue;const ke=ue+5*60*1e3;(!p||ke>p)&&(m(ke),j.current=ke)}catch{}}},re=P=>{var X;try{let ue=null;const ke=(X=P==null?void 0:P.adminDetails)==null?void 0:X.nextReorderTime;ke?ue=new Date(ke).getTime():P!=null&&P.timestamp&&(ue=new Date(P.timestamp).getTime()+5*60*1e3),ue&&(!p||ue>p+2e3)&&(m(ue),j.current=ue)}catch{}$()};return t("queue-update",N),t("now-playing",S),t("superVoteReceived",V),t("vote-update",L),t("playlistReordered",re),t("ranking-snapshot",B),()=>{J.id&&clearTimeout(J.id),s("queue-update",N),s("now-playing",S),s("superVoteReceived",V),s("vote-update",L),s("playlistReordered",re),s("ranking-snapshot",B)}},[r]),u.useEffect(()=>{const N=setInterval(()=>y(Date.now()),1e3);return()=>clearInterval(N)},[]);const A=u.useMemo(()=>{if(!p)return null;const N=p-f;if(N<=0)return"00:00";const B=Math.floor(N/6e4),S=Math.floor(N%6e4/1e3);return`${String(B).padStart(2,"0")}:${String(S).padStart(2,"0")}`},[p,f]),O=u.useMemo(()=>c.slice(0,5),[c]);return e.jsx("div",{className:"min-h-screen px-4 py-6 md:px-8 md:py-10 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white",children:e.jsxs("div",{className:"mx-auto max-w-7xl",children:[e.jsxs(q.header,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"mb-8",children:[e.jsxs("div",{className:"flex items-center justify-between flex-wrap gap-4",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-pink-500 to-violet-500 rounded-full flex items-center justify-center",children:e.jsx(we,{className:"w-6 h-6"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl md:text-4xl font-bold bg-gradient-to-r from-pink-400 to-violet-400 bg-clip-text text-transparent",children:"Painel do Músico"}),e.jsx("p",{className:"text-purple-200 text-sm",children:"Couvert Musical Interativo"})]})]}),e.jsx("p",{className:"text-purple-300 text-sm max-w-2xl",children:"Acompanhe a fila, ranking por votos e recados dos fãs em tempo real. Sistema de votação colaborativa com SuperVotos."})]}),e.jsxs("div",{className:"flex gap-4 text-sm",children:[e.jsxs("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg px-3 py-2 text-center",children:[e.jsx("div",{className:"text-lg font-bold text-green-400",children:l.length}),e.jsx("div",{className:"text-purple-200 text-xs",children:"Na fila"})]}),e.jsxs("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg px-3 py-2 text-center",children:[e.jsx("div",{className:"text-lg font-bold text-blue-400",children:c.length}),e.jsx("div",{className:"text-purple-200 text-xs",children:"Com votos"})]}),e.jsxs("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg px-3 py-2 text-center",children:[e.jsx("div",{className:"text-lg font-bold text-yellow-400",children:d.length}),e.jsx("div",{className:"text-purple-200 text-xs",children:"SuperVotos"})]})]})]}),A&&e.jsxs(q.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},className:"mt-4 inline-flex items-center gap-3 text-sm bg-gradient-to-r from-indigo-500/20 to-purple-500/20 backdrop-blur-sm border border-indigo-400/30 rounded-lg px-4 py-2",children:[e.jsx(sa,{className:"w-5 h-5 text-indigo-300 animate-pulse"}),e.jsx("span",{className:"text-indigo-200",children:"Próxima reordenação automática em"}),e.jsx("span",{className:"font-mono font-bold text-xl text-indigo-100 bg-indigo-500/30 px-2 py-1 rounded",children:A})]})]}),e.jsx(ze,{children:n&&e.jsxs(q.section,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95},className:"mb-6 bg-gradient-to-r from-green-500/20 to-emerald-500/20 backdrop-blur-sm border border-green-400/30 rounded-xl p-6",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[e.jsx(us,{className:"w-5 h-5 text-green-400 animate-pulse"}),e.jsx("h2",{className:"text-lg font-semibold text-green-300",children:"Tocando Agora"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:n.thumbnailUrl||`https://img.youtube.com/vi/${n.youtubeVideoId}/mqdefault.jpg`,alt:n.title,className:"w-20 h-16 rounded-lg object-cover shadow-lg"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"text-xl font-bold text-white truncate",children:n.title}),e.jsx("p",{className:"text-green-200 truncate",children:n.artist}),e.jsxs("div",{className:"flex items-center gap-4 mt-2 text-sm",children:[e.jsxs("span",{className:"flex items-center gap-1 text-green-300",children:[e.jsx(yt,{className:"w-4 h-4"}),nr(n.duration)]}),n.isPaid&&n.paymentAmount&&e.jsxs("span",{className:"flex items-center gap-1 text-yellow-300 bg-yellow-500/20 px-2 py-1 rounded",children:[e.jsx(Ye,{className:"w-4 h-4"}),"SuperVoto R$ ",Number(n.paymentAmount).toFixed(2)]})]})]})]})]})}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6",children:[e.jsxs(q.section,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"lg:col-span-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h2",{className:"text-xl font-semibold flex items-center gap-2",children:[e.jsx(Je,{className:"w-5 h-5 text-purple-400"}),"Sequência (Fila)"]}),e.jsxs("div",{className:"text-sm text-purple-300",children:[l.length," música",l.length!==1?"s":""]})]}),e.jsx("div",{className:"max-h-[500px] overflow-y-auto space-y-2",children:l.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(we,{className:"w-12 h-12 text-gray-500 mx-auto mb-3"}),e.jsx("p",{className:"text-gray-400",children:"Fila vazia"}),e.jsx("p",{className:"text-gray-500 text-sm",children:"Aguardando sugestões dos clientes"})]}):l.map((N,B)=>e.jsx(q.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:B*.05},className:"p-4 bg-white/5 hover:bg-white/10 rounded-lg transition-all duration-200 border border-white/10",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-10 h-10 bg-purple-500/20 rounded-full flex items-center justify-center",children:e.jsxs("span",{className:"text-purple-300 font-bold",children:["#",N.position]})}),e.jsx("img",{src:N.thumbnailUrl||`https://img.youtube.com/vi/${N.youtubeVideoId}/mqdefault.jpg`,alt:N.title,className:"w-16 h-12 rounded-lg object-cover shadow-md"}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-white font-semibold truncate",children:N.title}),e.jsx("div",{className:"text-purple-200 text-sm truncate",children:N.artist}),e.jsxs("div",{className:"flex items-center gap-3 mt-2 text-xs",children:[e.jsxs("span",{className:"flex items-center gap-1 text-purple-300",children:[e.jsx(yt,{className:"w-3 h-3"}),nr(N.duration)]}),N.isPaid&&N.paymentAmount&&e.jsxs("span",{className:"flex items-center gap-1 text-yellow-300 bg-yellow-500/20 px-2 py-1 rounded",children:[e.jsx(ra,{className:"w-3 h-3"}),"R$ ",Number(N.paymentAmount).toFixed(2)]})]})]})]})},N.id))})]}),e.jsxs(q.section,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6",children:[e.jsxs("h2",{className:"text-xl font-semibold mb-4 flex items-center gap-2",children:[e.jsx(Qa,{className:"w-5 h-5 text-yellow-400"}),"Ranking de Votos"]}),O.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(De,{className:"w-12 h-12 text-gray-500 mx-auto mb-3"}),e.jsx("p",{className:"text-gray-400",children:"Sem votos ainda"}),e.jsx("p",{className:"text-gray-500 text-sm",children:"Aguardando votação dos clientes"})]}):e.jsx("div",{className:"space-y-3",children:O.map((N,B)=>e.jsx(q.div,{initial:{opacity:0,x:10},animate:{opacity:1,x:0},transition:{delay:B*.1},className:`p-3 rounded-lg border transition-all duration-200 ${N.isInCooldown?"bg-orange-500/10 border-orange-400/30 opacity-70":B===0?"bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border-yellow-400/30":"bg-white/5 border-white/10 hover:bg-white/10"}`,children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center font-bold ${N.isInCooldown?"bg-orange-500/70 text-white":B===0?"bg-yellow-500 text-black":B===1?"bg-gray-400 text-black":B===2?"bg-amber-600 text-white":"bg-indigo-500/30 text-indigo-300"}`,children:N.isInCooldown?"⏰":`#${B+1}`}),e.jsx("img",{src:N.thumbnailUrl||`https://img.youtube.com/vi/${N.youtubeVideoId}/default.jpg`,className:`w-12 h-9 rounded object-cover shadow-md ${N.isInCooldown?"grayscale":""}`,alt:N.title||N.youtubeVideoId}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:`font-medium truncate ${N.isInCooldown?"text-orange-300":"text-white"}`,children:[N.title||N.youtubeVideoId,B===0&&!N.isInCooldown&&e.jsx("span",{className:"ml-2 text-xs bg-emerald-500/30 text-emerald-300 px-2 py-1 rounded",children:"Próxima"}),N.isInCooldown&&e.jsx("span",{className:"ml-2 text-xs bg-orange-500/30 text-orange-300 px-2 py-1 rounded",children:"Cooldown"})]}),N.artist&&e.jsx("div",{className:`text-sm truncate ${N.isInCooldown?"text-orange-400":"text-purple-200"}`,children:N.artist}),e.jsx("div",{className:"flex items-center gap-2 mt-1 text-xs",children:N.isInCooldown&&N.cooldownTimeLeft?e.jsxs("span",{className:"flex items-center gap-1 text-orange-400",children:[e.jsx(sa,{className:"w-3 h-3"}),Math.ceil(N.cooldownTimeLeft/60),"min restantes"]}):e.jsxs(e.Fragment,{children:[e.jsxs("span",{className:"flex items-center gap-1 text-indigo-300",children:[e.jsx(Qs,{className:"w-3 h-3"}),N.voteCount," votos"]}),N.isPaid&&e.jsxs("span",{className:"flex items-center gap-1 text-yellow-300",children:[e.jsx(Ye,{className:"w-3 h-3"}),"Pago"]})]})})]})]})},N.youtubeVideoId))})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs(q.section,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6 lg:col-span-2",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h2",{className:"text-xl font-semibold flex items-center gap-2",children:[e.jsx(we,{className:"w-5 h-5 text-blue-400"}),"Catálogo Completo"]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("span",{className:"text-sm text-purple-300",children:[v.length," música",v.length!==1?"s":""]}),e.jsxs("button",{onClick:$,disabled:k,className:"flex items-center gap-1 text-xs text-blue-300 hover:text-blue-200 transition-colors disabled:opacity-50",children:[e.jsx(Qe,{className:`w-3 h-3 ${k?"animate-spin":""}`}),k?"Carregando...":"Atualizar"]})]})]}),ee&&e.jsx("div",{className:"mb-4 p-3 bg-red-500/20 border border-red-400/30 rounded-lg text-red-300 text-sm",children:ee}),e.jsx("div",{className:"max-h-[500px] overflow-y-auto space-y-2",children:v.length===0&&!k?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(we,{className:"w-12 h-12 text-gray-500 mx-auto mb-3"}),e.jsx("p",{className:"text-gray-400",children:"Nenhuma música cadastrada"}),e.jsx("p",{className:"text-gray-500 text-sm",children:"Configure a playlist no painel administrativo"})]}):v.map((N,B)=>e.jsx(q.div,{initial:{opacity:0,y:5},animate:{opacity:1,y:0},transition:{delay:B*.02},className:"p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-all duration-200 border border-white/10",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center",children:e.jsxs("span",{className:"text-blue-300 text-sm font-medium",children:["#",N.position&&N.position>0?N.position:B+1]})}),e.jsx("img",{src:N.thumbnailUrl||`https://img.youtube.com/vi/${N.youtubeVideoId}/mqdefault.jpg`,alt:N.title,className:"w-14 h-10 rounded object-cover shadow-md"}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-white font-medium truncate",children:N.title}),e.jsx("div",{className:"text-purple-200 text-sm truncate",children:N.artist}),e.jsxs("div",{className:"flex items-center gap-3 mt-1 text-xs",children:[e.jsxs("span",{className:"flex items-center gap-1 text-purple-300",children:[e.jsx(yt,{className:"w-3 h-3"}),N.formattedDuration||nr(Math.max(0,Math.floor(N.duration||0)))]}),N.isAvailable===!1&&e.jsxs("span",{className:"flex items-center gap-1 text-amber-300 bg-amber-500/20 px-2 py-1 rounded",children:[e.jsx(ds,{className:"w-3 h-3"}),"Indisponível"]})]})]})]})},N.id||N.youtubeVideoId||B))})]}),e.jsxs(q.section,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl overflow-hidden",children:[e.jsx("div",{className:"p-6 border-b border-white/20 bg-gradient-to-r from-emerald-500/10 to-green-500/10",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("h2",{className:"text-xl font-semibold flex items-center gap-2",children:[e.jsx(aa,{className:"w-5 h-5 text-emerald-400"}),"SuperVotos & Recados"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-emerald-400 rounded-full animate-pulse"}),e.jsx("span",{className:"text-xs text-emerald-300",children:"Tempo real"})]})]})}),e.jsx("div",{className:"max-h-[500px] overflow-y-auto",children:d.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(aa,{className:"w-12 h-12 text-gray-500 mx-auto mb-3"}),e.jsx("p",{className:"text-gray-400",children:"Sem SuperVotos ainda"}),e.jsx("p",{className:"text-gray-500 text-sm",children:"Mensagens e pagamentos aparecem aqui quando clientes fazem SuperVotos"})]}):e.jsx("div",{className:"p-4 space-y-4",children:e.jsx(ze,{children:d.map((N,B)=>e.jsx(q.div,{initial:{opacity:0,x:-20,scale:.95},animate:{opacity:1,x:0,scale:1},exit:{opacity:0,x:20,scale:.95},transition:{delay:B*.05},className:"p-4 bg-gradient-to-r from-emerald-500/10 to-green-500/10 border border-emerald-400/20 rounded-lg",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"w-12 h-12 rounded-full bg-gradient-to-r from-emerald-500 to-green-500 flex items-center justify-center text-white font-bold shadow-lg",children:e.jsx(ra,{className:"w-5 h-5"})}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("div",{className:"font-semibold text-white truncate",children:N.title||N.youtubeVideoId||"Música"}),e.jsx("div",{className:"text-xs text-emerald-300 bg-emerald-500/20 px-2 py-1 rounded",children:new Date(N.at).toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit"})})]}),N.artist&&e.jsx("div",{className:"text-sm text-emerald-200 mb-2 truncate",children:N.artist}),N.message&&e.jsxs("div",{className:"text-sm text-gray-200 mb-3 p-2 bg-white/5 rounded border-l-2 border-emerald-400",children:['"',N.message,'"']}),e.jsxs("div",{className:"flex items-center gap-4 text-xs",children:[e.jsxs("span",{className:"flex items-center gap-1 text-yellow-300 bg-yellow-500/20 px-2 py-1 rounded",children:[e.jsx(Ye,{className:"w-3 h-3"}),"R$ ",N.amount.toFixed(2)]}),e.jsxs("span",{className:"flex items-center gap-1 text-indigo-300 bg-indigo-500/20 px-2 py-1 rounded",children:[e.jsx(Ut,{className:"w-3 h-3"}),"+",N.voteWeight," votos"]}),N.clientName&&e.jsxs("span",{className:"flex items-center gap-1 text-purple-300",children:[e.jsx(rt,{className:"w-3 h-3"}),N.clientName]}),N.tableNumber&&e.jsxs("span",{className:"text-purple-300",children:["Mesa ",N.tableNumber]})]})]})]})},N.id))})})})]})]})]})})},Yt=r=>{var a;if(!r)return null;let t=String(r).trim();try{if(/^https?:\/\//i.test(t)||t.includes("youtu")){const n=/[?&#]v=([a-zA-Z0-9_-]{11})/.exec(t);if(n)return n[1];const i=/(?:embed|shorts)\/([a-zA-Z0-9_-]{11})/.exec(t);if(i)return i[1];const l=/youtu\.be\/([a-zA-Z0-9_-]{11})/.exec(t);if(l)return l[1]}}catch{}const s=((a=/([a-zA-Z0-9_-]{11})/.exec(t))==null?void 0:a[1])||null;return s||(/^[a-zA-Z0-9_-]{11}$/.test(t)?t:null)},Kd=()=>{const{restaurantId:r=""}=Et(),[t]=Vr(),s=u.useMemo(()=>`display_state_${r}`,[r]),a=t.get("theme")||"client",n=(t.get("popups")??"on")!=="off",[i,l]=u.useState(null),[o,c]=u.useState(null),[h,d]=u.useState([]),[x,p]=u.useState([]),[m,f]=u.useState([]),[y,v]=u.useState(0),[g,k]=u.useState(null),T=u.useRef(0),[ee,se]=u.useState([]),[te,j]=u.useState(!0),[$,A]=u.useState(null),[O,N]=u.useState(!1),[B,S]=u.useState(!0),[V,J]=u.useState(!1),Q=u.useRef(!1),{on:z,off:L,joinRestaurant:re,isConnected:P}=Pt();u.useEffect(()=>{r&&ct.startCooldownTimer()},[r]);const X=u.useRef(null),ue=u.useRef(null),ke=u.useRef(null),Se=u.useRef(0),ye=u.useRef(!1),Fe=u.useRef(null),Ue=u.useRef(!1),He=u.useRef(null),W=u.useRef(null),ae=u.useRef(null),Ee=u.useMemo(()=>"yt_player_container",[]),ie=u.useRef(()=>{}),Oe=u.useRef(null),at=u.useRef(null),Ge=u.useRef(!1),nt=u.useCallback((E=0)=>{try{const M=X.current;if(!M||!ye.current)return;typeof M.unMute=="function"&&M.unMute(),typeof M.setVolume=="function"&&M.setVolume(80),typeof M.playVideo=="function"&&M.playVideo()}catch{}E<2&&setTimeout(()=>nt(E+1),E===0?100:400)},[]),xt=u.useCallback(()=>{if(Q.current=!0,!V)try{const E=X.current,M=ye.current;E&&M&&(nt(),J(!0))}catch{}},[V,nt]),Me=u.useCallback(async E=>{const M=ce(E);try{const D=await fetch(M,{method:"GET",headers:{"Content-Type":"application/json"}});if(!D.ok)throw new Error(`Erro HTTP: ${D.status}`);return await D.json()}catch(D){throw console.warn(`Falha ao carregar ${E}: ${D instanceof Error?D.message:"Erro desconhecido"}`),D}},[]),ft=u.useCallback(async()=>{const E=await Me(`/restaurants/${r}`);E.success&&E.restaurant&&l(E.restaurant)},[r,Me]),Xe=u.useCallback(async()=>{var E;try{const M=await Me(`/playback/${r}/state`),D=(E=M==null?void 0:M.playbackState)==null?void 0:E.currentSong;if(M.success&&D){c(xe({id:D.id||D.youtubeId,title:D.title,artist:D.artist,youtubeVideoId:D.youtubeId,thumbnailUrl:D.thumbnailUrl,duration:D.duration}));return}}catch(M){console.warn("/playback/state indisponível",M)}},[r,Me]),he=u.useCallback(async()=>{try{const E=await Me(`/collaborative-playlist/${r}/ranking?limit=20`);if(E.success&&E.data){const M=await Promise.all(E.data.map(async D=>{const K=xe(D);try{const Y=await ct.checkSongCooldown(r,K.youtubeVideoId);return{...K,isInCooldown:Y.isInCooldown,cooldownTimeLeft:Y.cooldownTimeLeft}}catch(Y){return console.warn(`Erro ao verificar cooldown para ${K.youtubeVideoId}:`,Y),K}}));d(M)}}catch(E){console.warn("Falha ao carregar ranking colaborativo",E)}},[r,Me]),_e=u.useCallback(async()=>{try{const E=await Me(`/playback-queue/${r}`);if(E&&(Array.isArray(E.queue)||E.currentlyPlaying)){const M=(E.queue||[]).map(xe),D=E.currentlyPlaying?xe({id:E.currentlyPlaying.youtubeVideoId||E.currentlyPlaying.id,title:E.currentlyPlaying.title,artist:E.currentlyPlaying.artist,youtubeVideoId:E.currentlyPlaying.youtubeVideoId||E.currentlyPlaying.suggestionId,thumbnailUrl:E.currentlyPlaying.thumbnailUrl,duration:E.currentlyPlaying.duration,isPaid:E.currentlyPlaying.isPaid,paymentAmount:E.currentlyPlaying.paymentAmount}):M[0]||null;D&&c(D);const K=M[1]||null;k(K)}}catch(E){console.warn("Falha ao carregar playback-queue:",E)}},[r,Me]),ut=u.useCallback(async()=>{const E=new URLSearchParams({page:"1",limit:"50"}),M=await Me(`/restaurants/${r}/playlist?${E.toString()}`);let D=[];return Array.isArray(M.results)&&(D=M.results.map(xe).filter(K=>!!K.youtubeVideoId),f(D)),D},[r,Me]),xe=E=>{const D=Yt(E.youtubeVideoId||E.youtubeId||E.videoId||E.id||E.url||E.link)||"";return{id:D,title:E.title||"Música Sem Título",artist:E.artist||"Artista Desconhecido",duration:E.duration||0,formattedDuration:E.formattedDuration||"0:00",thumbnailUrl:E.thumbnailUrl||(D?`https://img.youtube.com/vi/${D}/mqdefault.jpg`:""),channelName:E.channelName||E.artist||"Canal Desconhecido",youtubeVideoId:D,upvotes:E.upvotes||0,downvotes:E.downvotes||0,voteCount:E.voteCount||0,isPaid:E.isPaid||!1,paymentAmount:E.paymentAmount||0,score:E.score||0}},Re=u.useCallback(E=>{const M=Yt(E);if(!M){console.warn("ID de vídeo inválido, ignorando:",E);return}Ue.current=!1;const D=X.current;if(!D||!ye.current){Fe.current=M;return}if(D){try{const K=Date.now(),Y=typeof D.getVideoData=="function"?D.getVideoData():null,je=(Y==null?void 0:Y.video_id)||ke.current,Ne=typeof D.getPlayerState=="function"?D.getPlayerState():void 0;if(je===M&&(K-Se.current<2e3||Ne===1||Ne===3))return}catch{}console.log("🎵 Tocando vídeo:",M),typeof D.loadVideoById=="function"?D.loadVideoById({videoId:M}):typeof D.cueVideoById=="function"&&(D.cueVideoById({videoId:M}),typeof D.playVideo=="function"&&D.playVideo()),ke.current=M,Se.current=Date.now()}},[]),Ze=u.useCallback(()=>{var D;if(g&&g.youtubeVideoId!==(o==null?void 0:o.youtubeVideoId))return g;const M=[...h].filter(K=>K.youtubeVideoId!==(o==null?void 0:o.youtubeVideoId)&&!K.isInCooldown).sort((K,Y)=>(Y.score||0)-(K.score||0)).find(K=>(K.score||0)>0);if(M)return M;if(m.length>0){if(!o)return m[0];for(let K=1;K<=m.length;K++){const Y=(y+K)%m.length;if(!((D=m[Y])!=null&&D.isInCooldown))return m[Y]}return m[(y+1)%m.length]}if(h.length>0){const K=h.filter(Ne=>!Ne.isInCooldown),Y=K.findIndex(Ne=>Ne.youtubeVideoId===(o==null?void 0:o.youtubeVideoId)),je=Y>=0?(Y+1)%K.length:0;return K[je]||null}return null},[h,o==null?void 0:o.youtubeVideoId,m,y,g]),qe=u.useCallback(()=>{const E=Ze();if(E){c(E);const M=m.findIndex(D=>D.youtubeVideoId===E.youtubeVideoId);M>=0&&v(M),k(D=>D&&D.youtubeVideoId===E.youtubeVideoId?null:D),Re(E.youtubeVideoId)}else console.warn("Nenhuma próxima música disponível")},[Ze,m,Re]),We=u.useCallback(()=>{const E=Date.now();if(E-T.current<5*60*1e3)return;let K=[...h].filter(Y=>Y.youtubeVideoId!==(o==null?void 0:o.youtubeVideoId)).sort((Y,je)=>(je.score||0)-(Y.score||0)).find(Y=>(Y.score||0)>0)||null;if(!K){if(m.length>0){const Y=(y+1)%m.length;K=m[Y]||null}else if(h.length>0){const Y=h.findIndex(Ne=>Ne.youtubeVideoId===(o==null?void 0:o.youtubeVideoId)),je=Y>=0?(Y+1)%h.length:0;K=h[je]||null}}K&&K.youtubeVideoId===(o==null?void 0:o.youtubeVideoId)&&(K=null),k(K||null),T.current=E,K&&console.log("⏭️ Próxima música agendada pelo snapshot:",K.title)},[h,o==null?void 0:o.youtubeVideoId,m,y]),Ie=u.useRef(()=>{});u.useEffect(()=>{ie.current=()=>qe()},[qe]),u.useEffect(()=>{Oe.current=(o==null?void 0:o.youtubeVideoId)||null},[o==null?void 0:o.youtubeVideoId]),u.useEffect(()=>{at.current=g},[g]),u.useEffect(()=>{Ie.current=We},[We]),u.useEffect(()=>{const E=()=>xt();return V||(window.addEventListener("click",E),window.addEventListener("keydown",E),window.addEventListener("touchstart",E)),()=>{window.removeEventListener("click",E),window.removeEventListener("keydown",E),window.removeEventListener("touchstart",E)}},[xt,V]),u.useEffect(()=>{if(!r)return;(async()=>{var D,K;j(!0);try{if(!Ge.current)try{const pe=localStorage.getItem(s);if(pe){const I=JSON.parse(pe),G=Date.now()-((I==null?void 0:I.ts)||0);G>=0&&G<=30*60*1e3&&((D=I.currentlyPlaying)!=null&&D.youtubeVideoId&&c(xe(I.currentlyPlaying)),Number.isInteger(I.baseIndex)&&v(I.baseIndex),(K=I.scheduledNext)!=null&&K.youtubeVideoId&&k(xe(I.scheduledNext)),Ge.current=!0)}}catch{}const[Y,je,Ne,U]=await Promise.all([ft(),Xe(),he(),ut()]);if(await _e(),!(Ge.current&&!!Oe.current)&&!o){const I=U&&U[0]||null||Ze();I&&I.youtubeVideoId&&(c(I),v(()=>{const G=U.findIndex(Z=>Z.youtubeVideoId===I.youtubeVideoId);return G>=0?G:0}),Re(I.youtubeVideoId))}}catch{A("Erro ao carregar dados do restaurante")}finally{j(!1)}})(),We();const M=setInterval(()=>{he().finally(()=>{var D;(D=Ie.current)==null||D.call(Ie)})},3e5);return()=>clearInterval(M)},[r]),u.useEffect(()=>{if(!r)return;const E=()=>{he().finally(()=>{var D;(D=Ie.current)==null||D.call(Ie)})},M=()=>{document.visibilityState==="visible"&&E()};return window.addEventListener("focus",E),document.addEventListener("visibilitychange",M),()=>{window.removeEventListener("focus",E),document.removeEventListener("visibilitychange",M)}},[r,he]),u.useEffect(()=>{if(!r)return;const E=U=>{try{const oe=Array.isArray(U==null?void 0:U.queue)?U.queue:null;if(oe&&oe.length>0){const pe=oe.map(xe),I=pe[0],G=pe[1]||null;I&&c(I),k(G)}else _e()}catch(oe){console.warn("Falha ao aplicar queue-update no Display:",oe)}finally{he().finally(()=>{var oe;(oe=Ie.current)==null||oe.call(Ie)})}},M=U=>{var oe,pe,I,G,Z,me,w,C;try{const R=Yt((oe=U==null?void 0:U.video)==null?void 0:oe.youtubeVideoId);if(!R)return;const F=Oe.current,_=Date.now()-(Se.current||0)<1500;if(F===R&&_)return;const H=xe({id:((pe=U==null?void 0:U.video)==null?void 0:pe.suggestionId)||R,title:(I=U==null?void 0:U.video)==null?void 0:I.title,artist:(G=U==null?void 0:U.video)==null?void 0:G.artist,youtubeVideoId:R,thumbnailUrl:(Z=U==null?void 0:U.video)==null?void 0:Z.thumbnailUrl,duration:(me=U==null?void 0:U.video)==null?void 0:me.duration,isPaid:(w=U==null?void 0:U.video)==null?void 0:w.isPaid,paymentAmount:(C=U==null?void 0:U.video)==null?void 0:C.paymentAmount});c(be=>(be==null?void 0:be.youtubeVideoId)===R&&_?be:H),Re(R);const le=m.findIndex(be=>be.youtubeVideoId===R);le>=0&&v(le),k(be=>(be==null?void 0:be.youtubeVideoId)===R?null:be)}catch(R){console.warn("Falha ao processar reorderSelected:",R)}},D=U=>{const oe={id:`${Date.now()}-${Math.random()}`,type:U.isPaid?"supervote":"vote",songTitle:U.title||"Música",clientName:U.clientName,tableNumber:U.tableNumber,amount:U.amount,timestamp:Date.now()};p(pe=>[oe,...pe.slice(0,9)]),n&&jt(`Novo ${U.isPaid?"SuperVoto":"voto"} em "${U.title}"`,oe.type),he().finally(()=>{var pe;(pe=Ie.current)==null||pe.call(Ie)})},K=U=>{const oe={id:`${Date.now()}-${Math.random()}`,type:"suggestion",songTitle:U.title||"Nova música",timestamp:Date.now()};p(pe=>[oe,...pe.slice(0,9)]),n&&jt(`Nova sugestão: "${U.title}"`,"suggestion"),he().finally(()=>{We()})},Y=U=>{const oe=xe({id:(U==null?void 0:U.id)||(U==null?void 0:U.youtubeId),title:U==null?void 0:U.title,artist:U==null?void 0:U.artist,youtubeVideoId:U==null?void 0:U.youtubeId,thumbnailUrl:U==null?void 0:U.thumbnailUrl,duration:U==null?void 0:U.duration});c(pe=>pe&&pe.youtubeVideoId===oe.youtubeVideoId?pe:oe)},je=U=>{Promise.all([_e(),he()]).finally(()=>{var oe;(oe=Ie.current)==null||oe.call(Ie)})},Ne=U=>{try{const oe=U!=null&&U.nextTrack?xe(U.nextTrack):null;oe&&oe.youtubeVideoId!==Oe.current&&k(oe)}catch{}};return W.current!==r&&(re(r),W.current=r),z("queue-update",E),z("reorderSelected",M),z("vote-update",D),z("new-suggestion",K),z("now-playing",Y),z("playlistReordered",je),z("ranking-snapshot",Ne),()=>{L("queue-update",E),L("reorderSelected",M),L("vote-update",D),L("new-suggestion",K),L("now-playing",Y),L("playlistReordered",je),L("ranking-snapshot",Ne)}},[r,re,z,L,he,_e,n,Re,We]);let Ke=window.__ytApiPromise||null;const it=()=>typeof window<"u"&&window.YT&&window.YT.Player?Promise.resolve():(Ke||(Ke=new Promise(E=>{const M=window.onYouTubeIframeAPIReady;window.onYouTubeIframeAPIReady=()=>{try{typeof M=="function"&&M()}catch{}E()};const D=document.createElement("script");D.src="https://www.youtube.com/iframe_api",document.body.appendChild(D)}),window.__ytApiPromise=Ke),Ke);u.useEffect(()=>{let E=!1;return it().then(()=>{if(E)return;const M=window.YT;if(!M||!M.Player||X.current)return;const D=ue.current||document.getElementById(Ee);if(!D){console.warn("⚠️ Container do player não encontrado, adiando criação");return}X.current=new M.Player(D,{height:"100%",width:"100%",host:"https://www.youtube-nocookie.com",playerVars:{autoplay:1,controls:0,rel:0,modestbranding:1,fs:1,enablejsapi:1,origin:window.location.protocol+"//"+window.location.host,playsinline:1},events:{onReady:()=>{var Ne,U,oe,pe,I;console.log("🎵 Player pronto");try{ye.current=!0}catch{}try{!Q.current&&!V&&((U=(Ne=X.current)==null?void 0:Ne.mute)==null||U.call(Ne));const G=((pe=(oe=X.current)==null?void 0:oe.getIframe)==null?void 0:pe.call(oe))||null;G&&G.setAttribute("allow","autoplay; fullscreen; encrypted-media; picture-in-picture"),(Q.current||V)&&nt()}catch{}const K=Fe.current&&Yt(Fe.current);if(K){Fe.current=null,setTimeout(()=>Re(K),0);return}const Y=Oe.current,je=at.current;if(!Y&&je)(I=ie.current)==null||I.call(ie);else if(Y){const G=Yt(Y);G&&setTimeout(()=>Re(G),0)}},onStateChange:K=>{var Y;if(He.current=(K==null?void 0:K.data)??null,K.data===0){console.log("🎵 Música terminou, escolhendo próxima pelo ranking/base"),Ue.current=!0;try{const je=Oe.current;r&&je&&ct.markSongInCooldown(r,je)}catch{}(Y=ie.current)==null||Y.call(ie)}if(K.data===1&&(Q.current||V)){try{nt()}catch{}V||J(!0)}},onError:K=>{var Ne;const Y=K==null?void 0:K.data;console.error("❌ Erro no player:",Y,"-",Y===2?"Parâmetro inválido (verifique videoId ou formato)":Y===5?"Erro HTML5 (tente recarregar)":Y===100?"Vídeo removido/privado":Y===101||Y===150?"Embed desativado pelo proprietário":"Erro desconhecido"),(Ne=ie.current)==null||Ne.call(ie)}}})}),()=>{var M,D;E=!0;try{(D=(M=X.current)==null?void 0:M.destroy)==null||D.call(M)}catch{}}},[Ee]),u.useEffect(()=>{if(!(o!=null&&o.youtubeVideoId))return;if(Ue.current=!1,!ye.current){Fe.current=o.youtubeVideoId;return}const M=setTimeout(()=>Re(o.youtubeVideoId),50);return()=>clearTimeout(M)},[o==null?void 0:o.youtubeVideoId,Re]),u.useEffect(()=>{if(r)try{const E={ts:Date.now(),currentlyPlaying:o?{id:o.id,title:o.title,artist:o.artist,duration:o.duration,formattedDuration:o.formattedDuration,thumbnailUrl:o.thumbnailUrl,channelName:o.channelName,youtubeVideoId:o.youtubeVideoId,upvotes:o.upvotes,downvotes:o.downvotes,voteCount:o.voteCount,isPaid:o.isPaid,paymentAmount:o.paymentAmount,clientName:o.clientName,tableNumber:o.tableNumber,createdAt:o.createdAt,score:o.score}:null,baseIndex:y,scheduledNext:g?{id:g.id,title:g.title,artist:g.artist,duration:g.duration,formattedDuration:g.formattedDuration,thumbnailUrl:g.thumbnailUrl,channelName:g.channelName,youtubeVideoId:g.youtubeVideoId,upvotes:g.upvotes,downvotes:g.downvotes,voteCount:g.voteCount,isPaid:g.isPaid,paymentAmount:g.paymentAmount,clientName:g.clientName,tableNumber:g.tableNumber,createdAt:g.createdAt,score:g.score}:null};localStorage.setItem(s,JSON.stringify(E))}catch{}},[r,o==null?void 0:o.youtubeVideoId,y,g==null?void 0:g.youtubeVideoId]),u.useEffect(()=>{if(ye.current&&!o&&g){try{r&&g.youtubeVideoId&&ct.markSongInCooldown(r,g.youtubeVideoId)}catch{}qe()}},[g,o,qe]),u.useEffect(()=>{let E;return E=setInterval(()=>{var D,K;try{const Y=X.current;if(!Y||!ye.current)return;const je=typeof Y.getPlayerState=="function"?Y.getPlayerState():void 0;if(je!==1&&je!==2&&je!==3)return;if(Q.current)try{(typeof Y.isMuted=="function"?Y.isMuted():!1)&&((D=Y.unMute)==null||D.call(Y),(K=Y.setVolume)==null||K.call(Y,80))}catch{}const Ne=typeof Y.getDuration=="function"?Y.getDuration():0,U=typeof Y.getCurrentTime=="function"?Y.getCurrentTime():0;Ne&&U&&Ne-U<=1&&(Ue.current||(Ue.current=!0,console.log("⏭️ Avançando pelo watcher (fim detectado)"),qe()))}catch{}},500),()=>clearInterval(E)},[qe]);const Dt=()=>{const E=ae.current;document.fullscreenElement?(document.exitFullscreen(),N(!1)):(E==null||E.requestFullscreen(),N(!0))};u.useEffect(()=>{let E;const M=()=>{S(!0),clearTimeout(E),E=setTimeout(()=>S(!1),8e3)};return document.addEventListener("mousemove",M),document.addEventListener("keypress",M),M(),()=>{document.removeEventListener("mousemove",M),document.removeEventListener("keypress",M),clearTimeout(E)}},[]);const jt=(E,M)=>{const D=`${Date.now()}-${Math.random()}`;se(K=>[...K,{id:D,text:E,type:M}]),setTimeout(()=>se(K=>K.filter(Y=>Y.id!==D)),5e3)},pt=E=>{const M=Date.now()-E,D=Math.floor(M/6e4);return D<1?"agora":D<60?`${D}min`:`${Math.floor(D/60)}h`};return te?e.jsx(Yd,{}):$&&h.length===0&&m.length===0?e.jsx(Jd,{error:$}):e.jsxs("div",{ref:ae,className:`relative w-screen h-screen ${a==="client"?"bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900":"bg-black"} text-white overflow-hidden`,style:{cursor:B?"default":"none"},children:[e.jsx("div",{id:Ee,ref:ue,className:"absolute inset-0 z-10"}),!V&&e.jsx("div",{className:"absolute inset-0 z-30 flex items-center justify-center pointer-events-none",children:e.jsxs("button",{onClick:xt,className:"pointer-events-auto inline-flex items-center gap-2 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white px-6 py-3 rounded-xl shadow-xl border border-white/20 backdrop-blur-md focus:outline-none focus-visible:ring-4 focus-visible:ring-emerald-300/40",children:[e.jsx(us,{className:"w-5 h-5"}),e.jsx("span",{className:"font-semibold",children:"Ativar áudio"})]})}),e.jsx(ze,{children:B&&e.jsxs(q.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"absolute inset-0 z-20 pointer-events-none",children:[e.jsx(Gd,{restaurant:i,restaurantId:r,isConnected:P,playlistLength:h.length,isFullscreen:O,toggleFullscreen:Dt}),e.jsx(Xd,{currentlyPlaying:o,playlistSongs:h,recentActivity:x,formatTimeAgo:pt})]})}),e.jsx("div",{className:"absolute top-20 right-4 z-30 space-y-2",children:e.jsx(ze,{children:ee.map(E=>e.jsx(q.div,{initial:{opacity:0,x:100},animate:{opacity:1,x:0},exit:{opacity:0,x:100},className:`bg-white/20 backdrop-blur-md rounded-lg p-3 border border-white/30 flex items-center space-x-3 text-sm ${E.type==="supervote"?"text-yellow-300":E.type==="vote"?"text-green-300":"text-blue-300"}`,children:e.jsx("span",{children:E.text})},E.id))})})]})},Yd=()=>e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-white mb-4"}),e.jsx("p",{className:"text-white text-xl",children:"Carregando playlist..."})]})}),Jd=({error:r})=>e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-red-900 via-purple-900 to-indigo-900 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-red-400 text-6xl mb-4",children:"⚠️"}),e.jsx("p",{className:"text-white text-xl mb-4",children:r}),e.jsx("button",{onClick:()=>window.location.reload(),className:"bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg transition-colors",children:"Tentar Novamente"})]})}),Gd=ne.memo(({restaurant:r,restaurantId:t,isConnected:s,playlistLength:a,isFullscreen:n,toggleFullscreen:i})=>e.jsx(q.div,{initial:{y:-100},animate:{y:0},exit:{y:-100},className:"absolute top-0 left-0 right-0 bg-gradient-to-b from-black/80 to-transparent p-6 pointer-events-auto",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{className:"flex items-center space-x-6",children:e.jsxs("div",{children:[e.jsxs("h1",{className:"text-3xl font-bold text-white mb-1",children:["🎵 ",(r==null?void 0:r.name)||t.replace("-"," ").toUpperCase()]}),e.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-300",children:[e.jsxs("span",{className:"flex items-center",children:[e.jsx("div",{className:`w-2 h-2 rounded-full mr-2 ${s?"bg-green-400 animate-pulse":"bg-red-400"}`}),s?"AO VIVO":"DESCONECTADO"]}),e.jsxs("span",{children:[a," músicas na fila"]})]})]})}),e.jsxs("button",{onClick:i,className:"bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/20 text-white px-4 py-2 rounded-lg transition-all duration-200 flex items-center space-x-2",children:[n?e.jsx(Ba,{size:20}):e.jsx(qr,{size:20}),e.jsx("span",{children:n?"Sair Fullscreen":"Fullscreen"})]})]})})),Xd=ne.memo(({currentlyPlaying:r,playlistSongs:t,recentActivity:s,formatTimeAgo:a})=>e.jsx(q.div,{initial:{y:100},animate:{y:0},exit:{y:100},className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 to-transparent p-6 pointer-events-auto",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsx(Zd,{song:r}),e.jsx(eu,{songs:t,currentId:r==null?void 0:r.youtubeVideoId}),e.jsx(tu,{activities:s,formatTimeAgo:a})]})})),Zd=({song:r})=>e.jsxs("div",{className:"lg:col-span-1",children:[e.jsxs("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[e.jsx(we,{className:"mr-2",size:20}),"Tocando Agora"]}),r?e.jsx("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("img",{src:r.thumbnailUrl,alt:r.title,className:"w-16 h-16 rounded-lg object-cover"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"text-white font-semibold truncate",children:r.title}),e.jsx("p",{className:"text-gray-300 text-sm truncate",children:r.artist}),e.jsxs("div",{className:"flex items-center space-x-4 mt-2",children:[e.jsxs("div",{className:"flex items-center text-green-400",children:[e.jsx(Xt,{size:16,className:"mr-1"}),e.jsx("span",{className:"text-sm",children:r.upvotes||0})]}),e.jsxs("div",{className:"flex items-center text-red-400",children:[e.jsx(ci,{size:16,className:"mr-1"}),e.jsx("span",{className:"text-sm",children:r.downvotes||0})]}),r.isPaid&&e.jsx("div",{className:"bg-yellow-500 text-black px-2 py-1 rounded text-xs font-bold",children:"💰 SuperVoto"})]})]})]})}):e.jsxs("div",{className:"bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-white/10 text-center text-gray-400",children:[e.jsx(we,{size:32,className:"mx-auto mb-2 opacity-50"}),e.jsx("p",{children:"Aguardando próxima música..."})]})]}),eu=({songs:r,currentId:t})=>e.jsxs("div",{className:"lg:col-span-1",children:[e.jsxs("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[e.jsx(Os,{className:"mr-2",size:20}),"Ranking"]}),e.jsx("div",{className:"space-y-2 max-h-64 overflow-y-auto pr-2",children:r.slice(0,6).map((s,a)=>e.jsxs("div",{className:`bg-white/10 backdrop-blur-sm rounded-lg p-3 border border-white/20 flex items-center space-x-3 ${t===s.youtubeVideoId?"ring-2 ring-green-400 bg-green-400/20":""} ${s.isInCooldown?"opacity-60 border-orange-400/50":""}`,children:[e.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${s.isInCooldown?"bg-orange-500/70 text-white":a<3?["bg-yellow-500 text-black","bg-gray-400 text-black","bg-orange-600 text-white"][a]:"bg-white/20 text-white"}`,children:s.isInCooldown?"⏰":t===s.youtubeVideoId?"▶":a+1}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("p",{className:`text-sm font-medium truncate ${s.isInCooldown?"text-orange-300":"text-white"}`,children:s.title}),e.jsx("p",{className:`text-xs truncate ${s.isInCooldown?"text-orange-400":"text-gray-300"}`,children:s.artist}),s.isInCooldown&&s.cooldownTimeLeft&&e.jsxs("p",{className:"text-orange-400 text-xs mt-1",children:["⏰ Cooldown: ",Math.ceil(s.cooldownTimeLeft/60),"min restantes"]})]}),e.jsx("div",{className:"text-right",children:s.isInCooldown?e.jsx("div",{className:"text-orange-400 text-xs font-bold",children:"🚫 Em Cooldown"}):s.isPaid?e.jsxs("div",{className:"text-yellow-400 text-xs font-bold",children:["💰 R$ ",((s.paymentAmount||0)/100).toFixed(2)]}):e.jsxs("div",{className:"text-green-400 text-xs",children:["👍 ",s.voteCount||0]})})]},s.youtubeVideoId))})]}),tu=({activities:r,formatTimeAgo:t})=>e.jsxs("div",{className:"lg:col-span-1",children:[e.jsxs("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[e.jsx(De,{className:"mr-2",size:20}),"Atividade Recente"]}),e.jsx("div",{className:"space-y-2 max-h-64 overflow-y-auto pr-2",children:r.length>0?r.map(s=>e.jsx(q.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"bg-white/10 backdrop-blur-sm rounded-lg p-3 border border-white/20",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${s.type==="supervote"?"bg-yellow-500":s.type==="vote"?"bg-green-500":"bg-blue-500"}`,children:s.type==="supervote"?"💰":s.type==="vote"?"👍":"🎵"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("p",{className:"text-white text-sm font-medium truncate",children:[s.type==="supervote"&&`SuperVoto R$ ${((s.amount||0)/100).toFixed(2)}`,s.type==="vote"&&"Novo voto",s.type==="suggestion"&&"Nova sugestão"]}),e.jsx("p",{className:"text-gray-300 text-xs truncate",children:s.songTitle})]}),e.jsxs("div",{className:"text-right text-xs text-gray-400",children:[s.clientName&&e.jsx("p",{className:"font-medium",children:s.clientName}),s.tableNumber&&e.jsxs("p",{children:["Mesa ",s.tableNumber]}),e.jsx("p",{children:t(s.timestamp)})]})]})},s.id)):e.jsxs("div",{className:"bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10 text-center text-gray-400",children:[e.jsx(De,{size:24,className:"mx-auto mb-2 opacity-50"}),e.jsx("p",{className:"text-sm",children:"Aguardando atividade..."})]})})]}),su=()=>{var o;const{notifications:r,removeNotification:t,addNotification:s}=bl(),{settings:a}=mn();u.useEffect(()=>{let c;return dt(()=>Promise.resolve().then(()=>Ic),void 0).then(({wsService:h})=>{const d=x=>{const p=x.notification||x;s({type:p.type||"info",title:p.title||"Notificação",message:p.message||String((p==null?void 0:p.message)??""),duration:p.duration||5e3})};h.on("notification",d),c=()=>h.off("notification",d)}).catch(()=>{}),()=>{c&&c()}},[s]);const n=c=>{switch(c){case"success":return{icon:qa,bgColor:"bg-green-50 dark:bg-green-900/20",borderColor:"border-green-200 dark:border-green-800",iconColor:"text-green-600 dark:text-green-400",titleColor:"text-green-900 dark:text-green-100",messageColor:"text-green-700 dark:text-green-300"};case"error":return{icon:ss,bgColor:"bg-red-50 dark:bg-red-900/20",borderColor:"border-red-200 dark:border-red-800",iconColor:"text-red-600 dark:text-red-400",titleColor:"text-red-900 dark:text-red-100",messageColor:"text-red-700 dark:text-red-300"};case"warning":return{icon:Mr,bgColor:"bg-yellow-50 dark:bg-yellow-900/20",borderColor:"border-yellow-200 dark:border-yellow-800",iconColor:"text-yellow-600 dark:text-yellow-400",titleColor:"text-yellow-900 dark:text-yellow-100",messageColor:"text-yellow-700 dark:text-yellow-300"};case"info":default:return{icon:di,bgColor:"bg-blue-50 dark:bg-blue-900/20",borderColor:"border-blue-200 dark:border-blue-800",iconColor:"text-blue-600 dark:text-blue-400",titleColor:"text-blue-900 dark:text-blue-100",messageColor:"text-blue-700 dark:text-blue-300"}}},i=(()=>{switch(a.notificationPosition){case"top-left":return"top-4 left-4";case"bottom-right":return"bottom-4 right-4";case"bottom-left":return"bottom-4 left-4";case"top-right":default:return"top-4 right-4"}})(),l=(o=a.notificationPosition)==null?void 0:o.includes("left");return e.jsx("div",{className:`fixed ${i} z-50 space-y-2 max-w-sm w-full`,children:e.jsx(ze,{children:r.map(c=>{const h=n(c.type),d=h.icon;return e.jsxs(q.div,{initial:{opacity:0,x:l?-300:300,scale:.9},animate:{opacity:1,x:0,scale:1},exit:{opacity:0,x:l?-300:300,scale:.9},transition:{type:"spring",stiffness:500,damping:30},className:`
                ${h.bgColor} ${h.borderColor}
                border rounded-lg shadow-lg p-4 backdrop-blur-sm
              `,children:[e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx(d,{className:`w-5 h-5 ${h.iconColor}`})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:`text-sm font-medium ${h.titleColor}`,children:c.title}),e.jsx("p",{className:`text-sm mt-1 ${h.messageColor}`,children:c.message}),c.action&&e.jsx("button",{onClick:c.action.onClick,className:`
                        text-sm font-medium mt-2 hover:underline
                        ${h.iconColor}
                      `,children:c.action.label})]}),e.jsx("button",{onClick:()=>t(c.id),className:`
                    flex-shrink-0 p-1 rounded-md hover:bg-black/5 dark:hover:bg-white/5
                    ${h.iconColor} transition-colors
                  `,children:e.jsx(bt,{className:"w-4 h-4"})})]}),c.duration&&c.duration>0&&e.jsx(q.div,{initial:{width:"100%"},animate:{width:"0%"},transition:{duration:c.duration/1e3,ease:"linear"},className:`
                    h-1 mt-3 rounded-full
                    ${h.iconColor.replace("text-","bg-")}
                    opacity-30
                  `})]},c.id)})})})},ru=({children:r})=>{const{isAuthenticated:t,user:s,authToken:a}=xs();return Et(),console.log("🔐 ProtectedRoute - Estado:",{isAuthenticated:t,user:!!s,authToken:!!a}),a&&!s?(console.log("🔐 Carregando autenticação..."),e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx(Wt,{size:"lg"}),e.jsx("p",{className:"mt-4 text-gray-600 dark:text-gray-400",children:"Verificando autenticação..."})]})})):!t||!s||!a?(console.log("🔐 Não autenticado, redirecionando para login"),e.jsx(Tt,{to:"/admin/login",replace:!0})):["admin","moderator","staff"].includes(s.role)?(console.log("🔐 Acesso autorizado ao dashboard"),e.jsx(e.Fragment,{children:r})):(console.log("🔐 Sem permissão, redirecionando para home"),e.jsx(Tt,{to:"/",replace:!0}))},au=()=>{const{authToken:r,setUser:t,setAuthToken:s,isAuthenticated:a}=xs();u.useEffect(()=>{(()=>{const i=localStorage.getItem("authToken");if(!i){r&&(s(null),t(null));return}r!==i&&(Be.setAuthToken(i),s(i),t({id:"admin-user",name:"Admin",email:"<EMAIL>",role:"admin",isActive:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}),console.log("🔐 Token de autenticação restaurado"))})()},[])};const nu=new $i({defaultOptions:{queries:{retry:3,retryDelay:r=>Math.min(1e3*2**r,3e4),staleTime:5*60*1e3,cacheTime:10*60*1e3,refetchOnWindowFocus:!1,refetchOnReconnect:!0},mutations:{retry:1}}}),iu=({children:r})=>{const{isAuthenticated:t,user:s}=zt();return!t||!s?e.jsx(Tt,{to:"/admin/login",replace:!0}):["admin","moderator","super_admin"].includes(s.role)?e.jsx(e.Fragment,{children:r}):e.jsx(Tt,{to:"/",replace:!0})},ou=()=>{const{isOnline:r,connectionStatus:t,setOnlineStatus:s,setConnectionStatus:a}=zt(),{settings:n}=mn();return au(),u.useEffect(()=>{vl();const i=Ae.onConnectionStatusChange(c=>{a(c)}),l=()=>s(!0),o=()=>s(!1);return window.addEventListener("online",l),window.addEventListener("offline",o),()=>{i(),window.removeEventListener("online",l),window.removeEventListener("offline",o)}},[s,a]),e.jsxs(Bi,{client:nu,children:[e.jsx(Mn,{children:e.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200",children:[e.jsxs(Or,{children:[e.jsx(ge,{path:"/",element:e.jsx(jl,{})}),e.jsx(ge,{path:"/restaurant/:restaurantId/dashboard/*",element:e.jsx(ru,{children:e.jsx(Mc,{})})}),e.jsx(ge,{path:"/restaurant/dashboard/*",element:e.jsx(Tt,{to:"/restaurant/demo-restaurant/dashboard",replace:!0})}),e.jsx(ge,{path:"/test",element:e.jsx("div",{style:{padding:"20px",fontSize:"24px"},children:"Teste de Rota Funcionando!"})}),!1,e.jsx(ge,{path:"/restaurant/public/:restaurantId",element:e.jsx(Pa,{})}),e.jsx(ge,{path:"/couvert/:restaurantId",element:e.jsx(Ia,{})}),e.jsx(ge,{path:"/cover/:restaurantId",element:e.jsx(Ia,{})}),e.jsx(ge,{path:"/display/:restaurantId",element:e.jsx(Kd,{})}),e.jsx(ge,{path:"/admin/login",element:e.jsx(Sl,{})}),e.jsx(ge,{path:"/admin/*",element:e.jsx(iu,{children:e.jsx(Al,{})})}),e.jsx(ge,{path:"/login",element:e.jsx(Cl,{})}),e.jsx(ge,{path:"/analytics",element:e.jsx(Zc,{})}),e.jsx(ge,{path:"/client/:restaurantId",element:e.jsx(Pa,{})}),e.jsx(ge,{path:"*",element:e.jsx(ed,{})})]}),e.jsx(su,{}),e.jsx(ln,{position:n.notificationPosition||"top-left",toastOptions:{duration:4e3,style:{background:"var(--toast-bg)",color:"var(--toast-color)",border:"1px solid var(--toast-border)"},success:{iconTheme:{primary:"#10B981",secondary:"#FFFFFF"}},error:{iconTheme:{primary:"#EF4444",secondary:"#FFFFFF"}}}})]})}),!1]})};"serviceWorker"in navigator&&window.addEventListener("load",()=>{navigator.serviceWorker.register("/sw.js").then(r=>{console.log("SW registered: ",r)}).catch(r=>{console.log("SW registration failed: ",r)})});const Vn=()=>{const r=document.documentElement;r.style.setProperty("--toast-bg","rgb(255 255 255)"),r.style.setProperty("--toast-color","rgb(17 24 39)"),r.style.setProperty("--toast-border","rgb(229 231 235)"),window.matchMedia("(prefers-color-scheme: dark)").matches&&(r.style.setProperty("--toast-bg","rgb(31 41 55)"),r.style.setProperty("--toast-color","rgb(243 244 246)"),r.style.setProperty("--toast-border","rgb(75 85 99)"))};Vn();window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",Vn);lr.createRoot(document.getElementById("root")).render(e.jsx(ne.StrictMode,{children:e.jsx(ou,{})}));export{fn as A,mu as V,ws as Y,de as _,Bt as a,ce as b,b as c,Be as d,mn as e,_t as g,e as j,In as u};
//# sourceMappingURL=index-3b37f57e.js.map
