import { io, Socket } from "socket.io-client";
import { useEffect, useMemo, useSyncExternalStore } from "react";
import { toast } from "react-hot-toast";
import { WebSocketEvents, ConnectionStatus } from "@/types";
import { getWsConfig as getWsConfigExternal } from "./wsConfig";
import { logger } from "@/utils/logger";

type EventCallback<T = any> = (data: T) => void;

class WebSocketService {
  private socket: Socket | null = null;
  private connectionStatus: ConnectionStatus = "disconnected";
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private listeners: Map<string, Set<EventCallback>> = new Map();
  private statusListeners: Set<(status: ConnectionStatus) => void> = new Set();
  // Controle de sala / reconexão
  private desiredRestaurantId: string | null = null;
  private joinedRestaurantId: string | null = null;
  private reconnectTimer: number | null = null;
  private lastWsUrl: string | null = null;
  private lastAuthToken: string | null = null;
  private storageListenerAttached = false;
  private reconnecting = false;
  // Aliases de eventos (variações -> evento padrão tipado)
  private eventAliases: Record<string, keyof WebSocketEvents> = {
    newSuggestion: "new-suggestion",
    voteUpdate: "vote-update",
    queueUpdate: "queue-update",
    queue_updated: "queue-update",
    "playback-state-changed": "playback-state-update",
    track_ended: "song-ended",
  };

  constructor() {
    // Pré-carregar alvo inicial de restaurante (se existir)
    try {
      this.desiredRestaurantId =
        typeof localStorage !== "undefined"
          ? localStorage.getItem("currentRestaurantId")
          : null;
    } catch {}
    this.connect();
  }

  // Mantido por compatibilidade interna, delega ao módulo de config
  private getWsConfig() { return getWsConfigExternal(); }

  private connect() {
    // Detectar ambiente e base URL
    const { wsUrl, isLocal } = this.getWsConfig();

    this.setConnectionStatus("connecting");

    try {
      const token = this.getAuthToken();
      this.lastWsUrl = wsUrl;
      this.lastAuthToken = token;
      this.socket = io(wsUrl, {
        transports: ["websocket"],
        timeout: 15000,
        reconnection: true,
        reconnectionAttempts: Infinity,
        reconnectionDelay: 1500,
        reconnectionDelayMax: 6000,
        randomizationFactor: 0.25,
        autoConnect: true,
        auth: token ? { token, restaurantId: this.desiredRestaurantId || undefined } : undefined,
        withCredentials: true, // permitir cookies em dev/prod se usados
      });

      this.setupEventListeners();
    } catch (error) {
      console.warn("WebSocket não disponível:", error);
      this.setConnectionStatus("disconnected");
    }
  }

  private setupEventListeners() {
    if (!this.socket) return;

    // Eventos de conexão
    this.socket.on("connect", () => {
      logger.info("✅ WebSocket conectado");
      this.setConnectionStatus("connected");
      this.reconnectAttempts = 0;
      // Ao conectar, garantir autenticação/entrada na sala desejada
      this.ensureJoinedRestaurant();
    });

    this.socket.on("disconnect", (reason) => {
      logger.warn("❌ WebSocket desconectado:", reason);
      this.setConnectionStatus("disconnected");

      if (reason === "io server disconnect") {
        // Servidor forçou desconexão, reconectar manualmente
        this.socket?.connect();
      }
      // Reset de sala atual
      this.joinedRestaurantId = null;
    });

    this.socket.on("connect_error", (error) => {
      logger.warn("WebSocket connection failed:", error.message);
      this.setConnectionStatus("error");
      this.reconnectAttempts++;
    });

    this.socket.on("reconnect", (attemptNumber) => {
      logger.info(`🔄 WebSocket reconectado após ${attemptNumber} tentativas`);
      this.setConnectionStatus("connected");
    });

    this.socket.on("reconnect_attempt", (attemptNumber) => {
      logger.debug(`🔄 Tentativa de reconexão ${attemptNumber}`);
      this.setConnectionStatus("connecting");
    });

    this.socket.on("reconnect_error", (error) => {
      logger.error("❌ Erro na reconexão:", error);
      this.setConnectionStatus("error");
    });

    this.socket.on("reconnect_failed", () => {
      logger.error("❌ Falha na reconexão após máximo de tentativas");
      this.setConnectionStatus("error");
    });

    // Eventos customizados da aplicação
    this.setupApplicationEvents();

    // Reagir a mudanças de token/restaurante em outras abas (uma única vez)
    if (!this.storageListenerAttached) {
      window.addEventListener("storage", (e) => {
        if (e.key === "authToken") {
          this.reconnect();
        }
        if (e.key === "currentRestaurantId") {
          this.desiredRestaurantId = e.newValue;
          if (this.connectionStatus === "connected") this.ensureJoinedRestaurant();
        }
      });
      this.storageListenerAttached = true;
    }
  }

  private setupApplicationEvents() {
    if (!this.socket) return;
    // Encaminhar canal de notificação genérica se existir
    this.socket.on("notification", (data: any) => {
      this.emit("notification" as any, data);
    });

    // ACKs do servidor para join/leave (se existirem)
    this.socket.on("joined-restaurant", (restaurantId: string) => {
      if (restaurantId) this.joinedRestaurantId = restaurantId;
    });
    this.socket.on("left-restaurant", (restaurantId: string) => {
      if (this.joinedRestaurantId === restaurantId) this.joinedRestaurantId = null;
    });

    // Registrar aliases de eventos genéricos (evita duplicação)
    for (const [alias, standardEvent] of Object.entries(this.eventAliases)) {
      this.socket.on(alias, (data: any) => {
        this.emit(standardEvent as any, data as any);
      });
    }

    // Eventos de sugestões
    this.socket.on(
      "new-suggestion",
      (data: WebSocketEvents["new-suggestion"]) => {
        if (!data || !("title" in (data as any))) return;
        logger.info("🎵 Nova sugestão recebida:", data);
        this.emit("new-suggestion", data);

        toast.success(`Nova música sugerida: ${data.title}`, {
          duration: 4000,
        });
      }
    );

    this.socket.on("vote-update", (data: WebSocketEvents["vote-update"]) => {
      if (!data) return;
      logger.debug("👍 Atualização de votos:", data);
      this.emit("vote-update", data);
    });

    this.socket.on("queue-update", (data: WebSocketEvents["queue-update"]) => {
      if (!data) return;
      logger.debug("📋 Fila atualizada:", data);
      this.emit("queue-update", data);
    });

    // Alguns ambientes emitem atualizações de estado do player
    this.socket.on(
      "playback-state-update",
      (data: WebSocketEvents["playback-state-update"]) => {
  logger.debug("🎚️ Estado do player atualizado:", data);
        // Repassa direto para listeners locais
        this.emit("playback-state-update", data);
      }
    );

    this.socket.on("now-playing", (data: WebSocketEvents["now-playing"]) => {
      logger.info("🎵 Tocando agora:", data);
      this.emit("now-playing", data);

      toast(`Tocando agora: ${data.suggestion.title}`, {
        icon: "🎵",
        duration: 6000,
      });
    });

  // Mapear eventos camelCase com carga especial quando necessário
    this.socket.on("playbackStart", (data: any) => {
      logger.info("▶️ (camelCase) Início de reprodução:", data);
      this.emit("now-playing", { suggestion: data.track } as any);
    });
    this.socket.on("playlistReordered", (data: any) => {
      console.log("🔄 Playlist reordenada:", data);
      this.emit("playlistReordered", data);
    });

  // Compatibilidade com variações com payloads especiais
    this.socket.on("playback_started", (data: any) => {
      logger.info("▶️ (snake) Início de reprodução:", data);
      const t = (data as any)?.track || (data as any)?.state?.currentTrack || data;
      const mapped = {
        id: t?.id,
        title: t?.title,
        artist: t?.artist,
        youtubeId: t?.youtubeVideoId || t?.youtubeId,
        thumbnailUrl: t?.thumbnailUrl,
        duration: t?.duration,
      };
      this.emit("now-playing", mapped as any);
    });
    this.socket.on("playlistReorderedAdmin", (data: any) => {
      console.log("🔄 (admin) Playlist reordenada (admin):", data);
      // Reemitir como evento padrão para telas que só escutam playlistReordered
      this.emit("playlistReordered" as any, data);
      // E também manter o canal admin específico, se houver listeners tipados
      this.emit("playlistReorderedAdmin" as any, data);
    });

    // Snapshot periódico de ranking (autoridade para próxima música e countdown)
    this.socket.on("ranking-snapshot", (data: any) => {
      console.log("📸 Snapshot de ranking:", data);
      this.emit("ranking-snapshot" as any, data);
    });

    // Evento autoritativo de próxima música selecionada
  this.socket.on("reorderSelected", (data: WebSocketEvents["reorderSelected"]) => {
      try {
    logger.info("🎯 Próxima selecionada (server):", data);
        this.emit("reorderSelected", data);
      } catch (e) {
    console.warn("Falha ao tratar reorderSelected:", e);
      }
    });
    // Alias kebab-case se existir em algum ambiente legado
  this.socket.on("reorder-selected", (data: any) => {
      try {
    logger.info("🎯 (kebab) Próxima selecionada (server):", data);
        this.emit("reorderSelected", data as any);
      } catch {}
    });

    this.socket.on("song-ended", (data: WebSocketEvents["song-ended"]) => {
      logger.info("⏭️ Música finalizada:", data);
      this.emit("song-ended", data);
    });

    // SuperVoto recebido (cliente pagou um PIX/Supervoto)
    this.socket.on("superVoteReceived", (data: WebSocketEvents["superVoteReceived"]) => {
      logger.info("⭐ SuperVoto recebido:", data);
      this.emit("superVoteReceived", data);
    });
  }

  // Centraliza a leitura segura do token
  private getAuthToken(): string | null {
    try {
      return typeof localStorage !== "undefined"
        ? localStorage.getItem("authToken")
        : null;
    } catch {
      return null;
    }
  }

  private setConnectionStatus(status: ConnectionStatus) {
    this.connectionStatus = status;
    this.statusListeners.forEach((listener) => listener(status));
  }

  // Remover listener de status de conexão
  offConnectionStatusChange(callback: (status: ConnectionStatus) => void) {
    this.statusListeners.delete(callback);
  }

  // Métodos públicos

  // Entrar em uma sala específica do restaurante
  joinRestaurant(restaurantId: string) {
    // Atualiza alvo desejado e, se conectado, garante join idempotente
    this.desiredRestaurantId = restaurantId;
    this.ensureJoinedRestaurant();
  }

  // Sair de uma sala do restaurante
  leaveRestaurant(restaurantId: string) {
    if (this.socket && this.connectionStatus === "connected") {
      this.socket.emit("leave-restaurant", restaurantId);
      console.log(`🚪 Saiu da sala do restaurante: ${restaurantId}`);
    }
    if (this.desiredRestaurantId === restaurantId) {
      this.desiredRestaurantId = null;
    }
    if (this.joinedRestaurantId === restaurantId) {
      this.joinedRestaurantId = null;
    }
  }

  // Adicionar listener para eventos
  on<K extends keyof WebSocketEvents>(
    event: K,
    callback: EventCallback<WebSocketEvents[K]>
  ) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    const set = this.listeners.get(event)!;
    if (set.size > 100) {
      logger.warn("Muitos listeners no evento", String(event), set.size);
    }
    set.add(callback);
  }

  // Remover listener para eventos
  off<K extends keyof WebSocketEvents>(
    event: K,
    callback: EventCallback<WebSocketEvents[K]>
  ) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.delete(callback);
    }
  }

  // Emitir evento para listeners locais
  private emit<K extends keyof WebSocketEvents>(
    event: K,
    data: WebSocketEvents[K]
  ) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach((callback) => {
        try {
          callback(data);
        } catch (err) {
          console.warn("Erro em listener de evento WS", event, err);
        }
      });
    }
  }

  // Emissão pública e limitada para solicitar ações suportadas pelo backend
  publicEmit<T extends string>(event: T, payload?: any) {
    if (!this.socket || this.connectionStatus !== "connected") return;
    try {
      this.socket.emit(event as any, payload);
    } catch (e) {
      console.warn("Falha ao emitir evento via WS:", event, e);
    }
  }

  // Adicionar listener para mudanças de status de conexão
  onConnectionStatusChange(callback: (status: ConnectionStatus) => void) {
    this.statusListeners.add(callback);

    // Retornar função para remover o listener
    return () => {
      this.statusListeners.delete(callback);
    };
  }

  // Atalho de inscrição que já retorna unsubscribe
  subscribe<K extends keyof WebSocketEvents>(event: K, callback: EventCallback<WebSocketEvents[K]>) {
    this.on(event, callback);
    return () => this.off(event, callback);
  }

  // Obter status atual da conexão
  getConnectionStatus(): ConnectionStatus {
    return this.connectionStatus;
  }

  // Verificar se está conectado
  isConnected(): boolean {
    return this.connectionStatus === "connected";
  }

  // Escutar mudanças de conexão
  onConnectionChange(callback: (connected: boolean) => void): void {
    this.onConnectionStatusChange((status) => {
      callback(status === "connected");
    });
  }

  // Reconectar manualmente
  reconnect() {
  if (this.reconnecting) return;
  this.reconnecting = true;
    // Debounce para evitar tempestade de reconexões
    if (this.reconnectTimer) {
      window.clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    this.reconnectTimer = window.setTimeout(() => {
      this.reconnectTimer = null;
      const { wsUrl } = this.getWsConfig();
      const token = this.getAuthToken();

      // Se a URL e o token não mudaram, não derruba a conexão à toa
      const sameEndpoint = this.lastWsUrl === wsUrl;
      const sameAuth = this.lastAuthToken === token;

  if (this.socket && sameEndpoint && sameAuth) {
        if (this.socket.connected) {
          // Apenas garantir que está na sala correta
          this.ensureJoinedRestaurant();
        } else {
          // Tentar reconectar o socket existente
          try {
            this.socket.connect();
          } catch {}
        }
      } else {
        // Endpoint/token mudou: recriar conexão de forma segura
        this.safeTeardown();
        this.connect();
      }
  this.reconnecting = false;
    }, 300);
  }

  // Desconectar
  disconnect() {
    this.safeTeardown();
    this.setConnectionStatus("disconnected");
  }

  // Limpar todos os listeners
  removeAllListeners() {
    this.listeners.clear();
    this.statusListeners.clear();
  }

  // Teardown seguro evitando vazamento de listeners
  private safeTeardown() {
    if (this.socket) {
      try {
        (this.socket as any).removeAllListeners?.();
      } catch {}
      try {
        this.socket.disconnect();
      } catch {}
      this.socket = null;
    }
    this.joinedRestaurantId = null;
  }

  // Garante que a sala desejada está ativa; idempotente
  private ensureJoinedRestaurant() {
    if (!this.socket || this.connectionStatus !== "connected") return;

    const target = this.desiredRestaurantId;
    if (!target || this.joinedRestaurantId === target) return;

    try {
      const authToken = this.getAuthToken();
      if (authToken) {
        this.socket.emit("authenticate", { token: authToken, restaurantId: target });
      }
      // Usa callback ACK para confirmar entrada e evitar duplicação
      this.socket.emit("join-restaurant", target, (ack: any) => {
        if (ack === false) {
          // servidor rejeitou ou não suporta ACK; ainda assim assume join para evitar loop
          this.joinedRestaurantId = target;
          return;
        }
        this.joinedRestaurantId = target;
      });
      console.log(`🏪 Entrou na sala do restaurante: ${target}`);
    } catch (e) {
      console.warn("Falha ao entrar na sala do restaurante via WS:", e);
    }
  }

  // Obter informações de debug
  getDebugInfo() {
    return {
      connected: this.isConnected(),
      status: this.connectionStatus,
      reconnectAttempts: this.reconnectAttempts,
      socketId: this.socket?.id,
  transport: this.socket?.io?.engine?.transport?.name,
      listenersCount: Array.from(this.listeners.entries()).reduce(
        (acc, [event, listeners]) => ({ ...acc, [event]: listeners.size }),
        {}
      ),
    };
  }
}

// Instância singleton do WebSocket
export const wsService = new WebSocketService();

// Hook personalizado para React
export const useWebSocket = (restaurantId?: string) => {
  const status = useSyncExternalStore(
    (callback) => wsService.onConnectionStatusChange(callback),
    () => wsService.getConnectionStatus(),
    () => "disconnected" as ConnectionStatus
  );
  const isConnected = status === "connected";

  // Join/leave automático se restaurantId for informado
  useEffect(() => {
    if (!restaurantId) return;
    wsService.joinRestaurant(restaurantId);
    return () => {
      wsService.leaveRestaurant(restaurantId);
    };
  }, [restaurantId]);

  return useMemo(
    () => ({
      service: wsService,
      isConnected,
      status,
      joinRestaurant: wsService.joinRestaurant.bind(wsService),
      leaveRestaurant: wsService.leaveRestaurant.bind(wsService),
      on: wsService.on.bind(wsService),
      off: wsService.off.bind(wsService),
      reconnect: wsService.reconnect.bind(wsService),
      emit: wsService.publicEmit.bind(wsService),
      onConnectionStatusChange: wsService.onConnectionStatusChange.bind(wsService),
    }),
  [status, isConnected, restaurantId]
  );
};

export default wsService;
