#!/bin/bash

# ============================================
# SCRIPT DE ATUALIZAÇÃO DO BANCO DE DADOS
# Aplica migração de execution_order em containers existentes
# ============================================

echo "🔄 Iniciando atualização do banco de dados..."

# Verificar se o container do banco está rodando
if ! docker ps | grep -q "restaurant-playlist-db"; then
    echo "❌ Container do banco de dados não está rodando!"
    echo "Execute: docker-compose up -d"
    exit 1
fi

echo "✅ Container do banco encontrado"

# Verificar se a coluna execution_order já existe
echo "🔍 Verificando se a coluna execution_order já existe..."
COLUMN_EXISTS=$(docker exec restaurant-playlist-db psql -U restaurant_user -d restaurant_playlist -tAc "SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='execution_order';")

if [ "$COLUMN_EXISTS" = "1" ]; then
    echo "✅ Coluna execution_order já existe"
else
    echo "➕ Adicionando coluna execution_order..."
    docker exec restaurant-playlist-db psql -U restaurant_user -d restaurant_playlist -c "ALTER TABLE playlists ADD COLUMN execution_order INTEGER DEFAULT NULL;"
    
    echo "📊 Criando índice para execution_order..."
    docker exec restaurant-playlist-db psql -U restaurant_user -d restaurant_playlist -c "CREATE INDEX IF NOT EXISTS idx_playlists_execution_order ON playlists(restaurant_id, execution_order);"
    
    echo "✅ Coluna e índice criados com sucesso"
fi

# Executar migração dos dados
echo "🔄 Executando migração de dados..."
docker exec -i restaurant-playlist-db psql -U restaurant_user -d restaurant_playlist < database/migrate_execution_order.sql

if [ $? -eq 0 ]; then
    echo "✅ Migração concluída com sucesso!"
else
    echo "❌ Erro durante a migração"
    exit 1
fi

# Verificar resultados
echo "📊 Verificando resultados da migração..."
UPDATED_COUNT=$(docker exec restaurant-playlist-db psql -U restaurant_user -d restaurant_playlist -tAc "SELECT COUNT(*) FROM playlists WHERE execution_order IS NOT NULL;")
echo "✅ Total de playlists com execution_order: $UPDATED_COUNT"

echo ""
echo "🎉 Atualização do banco de dados concluída!"
echo "📝 Agora reinicie o backend para aplicar as mudanças:"
echo "   docker-compose restart backend"
