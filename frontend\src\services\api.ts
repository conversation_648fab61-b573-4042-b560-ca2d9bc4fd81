import axios, { AxiosInstance, AxiosResponse, AxiosError } from "axios";
import { toast } from "react-hot-toast";
import {
  ApiError,
  AuthResponse,
  Restaurant,
  Suggestion,
  SearchResult,
  PlayQueue,
  VideoInfo,
  PaginatedResponse,
  SuggestionFilters,
} from "@/types";

// Configuração base da API
const API_BASE_URL = (import.meta as any).env?.VITE_API_URL || "http://localhost:8001";

class ApiService {
  public client: AxiosInstance;
  private sessionId: string | null = null;

  constructor() {
    this.client = axios.create({
      baseURL: `${API_BASE_URL}/api/v1`,
      timeout: 30000,
      headers: {
        "Content-Type": "application/json",
      },
    });

    this.setupInterceptors();
    this.initializeSessionId();
  }

  private setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        // Adicionar token de autenticação se disponível
        const token = this.getAuthToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // Adicionar session ID se disponível
        if (this.sessionId) {
          config.headers["X-Session-ID"] = this.sessionId;
        }

        // Adicionar informações do dispositivo
        config.headers["X-Device-Info"] = JSON.stringify({
          userAgent: navigator.userAgent,
          language: navigator.language,
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          screenResolution: `${screen.width}x${screen.height}`,
        });

        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      (error: AxiosError) => {
        this.handleApiError(error);
        return Promise.reject(error);
      }
    );
  }

  private initializeSessionId() {
    // Obter session ID do localStorage ou gerar novo
    let sessionId = localStorage.getItem("sessionId");

    if (!sessionId) {
      sessionId = this.generateSessionId();
      localStorage.setItem("sessionId", sessionId);
    }

    this.sessionId = sessionId;
  }

  private generateSessionId(): string {
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
      /[xy]/g,
      function (c) {
        const r = (Math.random() * 16) | 0;
        const v = c === "x" ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      }
    );
  }

  private getAuthToken(): string | null {
    return localStorage.getItem("authToken");
  }

  public setAuthToken(token: string) {
    localStorage.setItem("authToken", token);
  }

  private removeAuthToken() {
    localStorage.removeItem("authToken");
  }

  private handleApiError(error: AxiosError) {
    const apiError = error.response?.data as ApiError;

    // Tratar diferentes tipos de erro
    switch (error.response?.status) {
      case 401:
        // Token expirado ou inválido
        this.removeAuthToken();
        if (window.location.pathname.includes("/admin")) {
          window.location.href = "/admin/login";
        }
        break;

      case 403:
        toast.error("Acesso negado");
        break;

      case 404:
        toast.error("Recurso não encontrado");
        break;

      case 429:
        toast.error("Muitas requisições. Tente novamente em alguns minutos.");
        break;

      case 500:
        toast.error("Erro interno do servidor");
        break;

      default:
        if (apiError?.message) {
          toast.error(apiError.message);
        } else if (error.message) {
          toast.error(error.message);
        } else {
          toast.error("Erro inesperado");
        }
    }
  }

  // Métodos de autenticação
  async login(email: string, password: string): Promise<AuthResponse> {
    const response = await this.client.post<AuthResponse>("/auth/login", {
      email,
      password,
    });

    this.setAuthToken(response.data.token);
    return response.data;
  }

  async logout(): Promise<void> {
    this.removeAuthToken();
  }

  async getCurrentUser(): Promise<{
    user: any;
    restaurant?: Restaurant;
    permissions: string[];
  }> {
    const response = await this.client.get("/auth/me");
    return response.data;
  }

  // Métodos para restaurantes
  async getRestaurant(restaurantId: string): Promise<Restaurant> {
    const response = await this.client.get<Restaurant>(
      `/restaurants/${restaurantId}`
    );
    return response.data;
  }

  // Métodos para busca no YouTube
  async searchYouTube(
    query: string,
    restaurantId: string,
    maxResults: number = 10,
    pageToken?: string,
    useYouTubeAPI: boolean = false
  ): Promise<SearchResult> {
    const response = await this.client.get<SearchResult>("/youtube/search", {
      params: {
        q: query,
  maxResults,
  pageToken,
  useYouTubeAPI: useYouTubeAPI.toString(),
  restaurantId,
      },
    });
    return response.data;
  }

  async getVideoInfo(videoId: string): Promise<{ video: VideoInfo }> {
    const response = await this.client.get(`/youtube/video/${videoId}`);
    return response.data;
  }

  // Métodos para sugestões
  async createSuggestion(data: {
    youtubeVideoId: string;
    restaurantId: string;
    playlistId?: string;
  }): Promise<{ suggestion: Suggestion }> {
    const response = await this.client.post("/suggestions", data);
    return response.data;
  }

  async getSuggestions(
    restaurantId: string,
    filters?: SuggestionFilters & { page?: number; limit?: number }
  ): Promise<PaginatedResponse<Suggestion>> {
    // Corrigir status=all para status válido
    const params = { ...filters };
  if ((params as any).status === "all") {
      delete params.status; // Remove status=all, deixa buscar todos
    }

    const response = await this.client.get(`/suggestions/${restaurantId}`, {
      params,
    });
    return response.data;
  }

  async getPlayQueue(restaurantId: string): Promise<PlayQueue> {
    // Corrigir endpoint para playback-queue
    const response = await this.client.get<PlayQueue>(
      `/playback-queue/${restaurantId}`
    );
    return response.data;
  }

  async voteSuggestion(
    suggestionId: string,
    voteType: "up" | "down"
  ): Promise<{ message: string; suggestion: Suggestion }> {
    const response = await this.client.post(
      `/suggestions/${suggestionId}/vote`,
      {
        voteType,
      }
    );
    return response.data;
  }

  // Métodos para playlists
  async getPlaylists(restaurantId: string): Promise<any[]> {
    const response = await this.client.get(`/playlists/${restaurantId}`);
    return response.data;
  }

  async getPlaylist(playlistId: string): Promise<any> {
    const response = await this.client.get(`/playlists/${playlistId}`);
    return response.data;
  }

  // Métodos para analytics
  async getAnalytics(
    restaurantId: string,
    dateRange?: { start: string; end: string }
  ): Promise<any> {
    // Corrigir endpoint para analytics/dashboard
    const response = await this.client.get(
      `/analytics/dashboard/${restaurantId}`,
      {
        params: dateRange,
      }
    );
    return response.data;
  }

  // Métodos para quota do YouTube
  async getYouTubeQuota(): Promise<{
    quotaUsed: number;
    quotaRemaining: number;
    quotaLimit: number;
    canSearch: boolean;
    canGetVideoDetails: boolean;
  }> {
    const response = await this.client.get("/youtube/quota");
    return response.data;
  }

  // Métodos utilitários
  getSessionId(): string | null {
    return this.sessionId;
  }

  isAuthenticated(): boolean {
    return !!this.getAuthToken();
  }

  // Método para fazer requisições customizadas
  async request<T>(config: any): Promise<T> {
    const response = await this.client.request<T>(config);
    return response.data;
  }
}

// Instância singleton da API
export const apiService = new ApiService();

// Hooks personalizados para React Query
export const apiQueries = {
  restaurant: (restaurantId: string) => ({
    queryKey: ["restaurant", restaurantId],
    queryFn: () => apiService.getRestaurant(restaurantId),
    staleTime: 5 * 60 * 1000, // 5 minutos
  }),

  suggestions: (restaurantId: string, filters?: SuggestionFilters) => ({
    queryKey: ["suggestions", restaurantId, filters],
    queryFn: () => apiService.getSuggestions(restaurantId, filters),
    refetchInterval: 30000, // 30 segundos
  }),

  playQueue: (restaurantId: string) => ({
    queryKey: ["playQueue", restaurantId],
    queryFn: () => apiService.getPlayQueue(restaurantId),
    refetchInterval: 10000, // 10 segundos
  }),

  playlists: (restaurantId: string) => ({
    queryKey: ["playlists", restaurantId],
    queryFn: () => apiService.getPlaylists(restaurantId),
    staleTime: 10 * 60 * 1000, // 10 minutos
  }),

  youtubeSearch: (
    query: string,
    restaurantId: string,
    maxResults?: number,
    pageToken?: string,
    useYouTubeAPI?: boolean
  ) => ({
    queryKey: [
      "youtubeSearch",
      query,
      restaurantId,
      maxResults,
      pageToken,
      useYouTubeAPI,
    ],
    queryFn: () =>
      apiService.searchYouTube(
        query,
        restaurantId!,
        maxResults,
        pageToken,
        useYouTubeAPI
      ),
    enabled: !!query && query.length >= 2 && !!restaurantId,
    staleTime: 5 * 60 * 1000, // 5 minutos
  }),

  youtubeQuota: () => ({
    queryKey: ["youtubeQuota"],
    queryFn: () => apiService.getYouTubeQuota(),
    refetchInterval: 60000, // 1 minuto
  }),

  currentUser: () => ({
    queryKey: ["currentUser"],
    queryFn: () => apiService.getCurrentUser(),
    staleTime: 10 * 60 * 1000, // 10 minutos
  }),
};

export default apiService;
