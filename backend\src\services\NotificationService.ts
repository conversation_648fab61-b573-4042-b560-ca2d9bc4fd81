import { EventEmitter } from "events";
import {
  IsString,
  IsOptional,
  IsE<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsBoolean,
  IsN<PERSON>ber,
  validate,
} from "class-validator";
import { plainToClass } from "class-transformer";
import { Socket } from "socket.io";
import { WebSocketService } from "./WebSocketService";
import { logger } from "../utils/logger";
import { redisClient } from "../config/redis";

export enum NotificationType {
  SUCCESS = "success",
  ERROR = "error",
  WARNING = "warning",
  INFO = "info",
  MUSIC = "music",
  VOTE = "vote",
  VOTING = "voting",
  VOTING_UPDATE = "voting_update",
  BADGE = "badge",
  SYSTEM = "system",
  PAYMENT = "payment",
  PLAYLIST_REORDERED = "playlist_reordered",
  PLAYLIST_UPDATE = "playlist_update",
}

export enum NotificationPriority {
  LOW = "low",
  NORMAL = "normal",
  HIGH = "high",
  URGENT = "urgent",
}

export enum NotificationChannel {
  WEBSOCKET = "websocket",
  EMAIL = "email",
  SMS = "sms",
  PUSH = "push",
}

export interface INotificationData {
  type: NotificationType;
  title: string;
  message: string;
  priority?: NotificationPriority;
  category?: string;
  data?: any;
  targetUsers?: string[];
  targetRestaurants?: string[];
  autoClose?: boolean;
  duration?: number;
  actions?: INotificationAction[];
  channels?: NotificationChannel[];
  metadata?: {
    source?: string;
    correlationId?: string;
    timestamp?: Date;
  };
}

export interface INotificationAction {
  id: string;
  label: string;
  endpoint?: string;
  method?: "GET" | "POST" | "PUT" | "DELETE";
  data?: any;
  style?: "primary" | "secondary" | "danger";
}

export interface INotificationHistory {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  category?: string;
  priority?: NotificationPriority;
  notification: INotificationData;
  timestamp: Date;
  deliveryStatus: {
    websocket?: boolean;
    email?: boolean;
    sms?: boolean;
    push?: boolean;
  };
  recipients: string[];
}

export interface INotificationStats {
  totalSent: number;
  totalDelivered: number;
  totalFailed: number;
  deliveryRate: number;
  averageDeliveryTime: number;
  byType: Record<NotificationType, number>;
  byPriority: Record<NotificationPriority, number>;
}

export interface ConnectedClient {
  id: string;
  ws: Socket;
  type: string;
  userId?: string;
  sessionId?: string;
  restaurantId?: string;
  connectedAt: Date;
  lastActivity: Date;
}

export class SendNotificationDto {
  @IsEnum(NotificationType)
  type: NotificationType;

  @IsString()
  title: string;

  @IsString()
  message: string;

  @IsOptional()
  @IsEnum(NotificationPriority)
  priority: NotificationPriority = NotificationPriority.NORMAL;

  @IsOptional()
  @IsString()
  category?: string;

  @IsOptional()
  @IsArray()
  targetUsers?: string[];

  @IsOptional()
  @IsArray()
  targetRestaurants?: string[];

  @IsOptional()
  @IsBoolean()
  autoClose: boolean = false;

  @IsOptional()
  @IsNumber()
  duration?: number;

  @IsOptional()
  @IsArray()
  channels?: NotificationChannel[];
}

export class NotificationQueryDto {
  @IsOptional()
  @IsString()
  restaurantId?: string;

  @IsOptional()
  @IsEnum(NotificationType)
  type?: NotificationType;

  @IsOptional()
  @IsNumber()
  limit: number = 50;

  @IsOptional()
  @IsNumber()
  offset: number = 0;
}

export class NotificationError extends Error {
  public readonly code: string;
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly notificationId?: string;

  constructor(
    message: string,
    code: string = "NOTIFICATION_ERROR",
    statusCode: number = 500,
    isOperational: boolean = true,
    notificationId?: string
  ) {
    super(message);
    this.name = "NotificationError";
    this.code = code;
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.notificationId = notificationId;

    Error.captureStackTrace(this, this.constructor);
  }
}

class NotificationService extends EventEmitter {
  private static instance: NotificationService;
  private wsService: WebSocketService;
  private notificationHistory: INotificationHistory[] = [];
  private notificationQueue: INotificationData[] = [];
  private clients: Map<string, ConnectedClient> = new Map();
  private readonly maxHistorySize = 1000;
  private readonly maxQueueSize = 500;
  private stats: INotificationStats = {
    totalSent: 0,
    totalDelivered: 0,
    totalFailed: 0,
    deliveryRate: 0,
    averageDeliveryTime: 0,
    byType: {} as Record<NotificationType, number>,
    byPriority: {} as Record<NotificationPriority, number>,
  };

  constructor() {
    super();
    this.wsService = WebSocketService.getInstance();
    this.setupEventListeners();
    this.startQueueProcessor();
    this.initializeStats();
  }

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  private initializeStats(): void {
    Object.values(NotificationType).forEach((type) => {
      this.stats.byType[type] = 0;
    });

    Object.values(NotificationPriority).forEach((priority) => {
      this.stats.byPriority[priority] = 0;
    });
  }

  private startQueueProcessor(): void {
    setInterval(async () => {
      if (this.notificationQueue.length > 0) {
        const notifications = [...this.notificationQueue];
        this.notificationQueue = [];

        for (const notification of notifications) {
          try {
            await this.deliverNotification(notification);
          } catch (error) {
            logger.error("Erro ao processar notificação da fila:", error);
          }
        }
      }
    }, 1000);
  }

  private setupEventListeners(): void {
    this.on("suggestion:created", this.handleSuggestionCreated.bind(this));
    this.on("suggestion:approved", this.handleSuggestionApproved.bind(this));
    this.on("suggestion:rejected", this.handleSuggestionRejected.bind(this));
    this.on("vote:cast", this.handleVoteCast.bind(this));
    this.on("playback:changed", this.handlePlaybackChanged.bind(this));
    this.on("badge:earned", this.handleBadgeEarned.bind(this));
    this.on("payment:approved", this.handlePaymentApproved.bind(this));
    this.on("system:maintenance", this.handleSystemMaintenance.bind(this));
  }

  private async validateInput<T extends object>(
    dto: new () => T,
    data: any
  ): Promise<T> {
    const instance = plainToClass(dto, data);
    const errors = await validate(instance);

    if (errors.length > 0) {
      const errorMessages = errors
        .map((error) => Object.values(error.constraints || {}).join(", "))
        .join("; ");

      throw new NotificationError(
        `Dados inválidos: ${errorMessages}`,
        "VALIDATION_ERROR",
        400
      );
    }

    return instance;
  }

  async sendNotification(notification: INotificationData): Promise<string> {
    try {
      await this.validateInput(SendNotificationDto, notification);

      const notificationId = this.generateNotificationId();

      const enrichedNotification: INotificationData = {
        ...notification,
        priority: notification.priority || NotificationPriority.NORMAL,
        autoClose: notification.autoClose || false,
        metadata: {
          ...notification.metadata,
          timestamp: new Date(),
          correlationId: notificationId,
        },
      };

      if (enrichedNotification.priority === NotificationPriority.URGENT) {
        await this.deliverNotification(enrichedNotification);
      } else {
        this.addToQueue(enrichedNotification);
      }

      logger.info(`Notificação ${notificationId} enviada`, {
        type: notification.type,
        priority: notification.priority,
        targets: notification.targetRestaurants?.length || 0,
      });

      return notificationId;
    } catch (error) {
      logger.error("Erro ao enviar notificação:", error);

      if (error instanceof NotificationError) {
        throw error;
      }

      throw new NotificationError(
        `Falha ao enviar notificação: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
        "SEND_FAILED",
        500,
        true,
        undefined
      );
    }
  }

  addClient(clientId: string, ws: Socket, metadata: Partial<ConnectedClient>) {
    const client: ConnectedClient = {
      id: clientId,
      ws,
      type: metadata.type || "unknown",
      userId: metadata.userId,
      sessionId: metadata.sessionId,
      restaurantId: metadata.restaurantId,
      connectedAt: new Date(),
      lastActivity: new Date(),
    };

    this.clients.set(clientId, client);

    ws.on("disconnect", () => {
      this.clients.delete(clientId);
    });

    ws.on("error", () => {
      this.clients.delete(clientId);
    });
  }

  async sendToUser(
    userId: string,
    notification: INotificationData
  ): Promise<string> {
    return this.sendNotification({
      ...notification,
      targetUsers: [userId],
    });
  }

  async sendToRestaurant(
    restaurantId: string,
    notification: INotificationData
  ): Promise<string> {
    return this.sendNotification({
      ...notification,
      targetRestaurants: [restaurantId],
    });
  }

  async sendToSession(
    sessionId: string,
    notification: INotificationData
  ): Promise<string> {
    return this.sendNotification({
      ...notification,
      targetUsers: [sessionId],
    });
  }

  async sendToClients(
    restaurantId: string,
    notification: INotificationData
  ): Promise<string> {
    return this.sendToRestaurant(restaurantId, notification);
  }

  async sendToAllClients(
    restaurantId: string,
    notification: INotificationData
  ): Promise<string> {
    return this.sendToRestaurant(restaurantId, notification);
  }

  async broadcast(notification: INotificationData): Promise<string> {
    return this.sendNotification(notification);
  }

  private async handleSuggestionCreated(data: any): Promise<void> {
    await this.sendToRestaurant(data.restaurantId, {
      type: NotificationType.MUSIC,
      title: "Nova Sugestão",
      message: `${data.clientName || "Um cliente"} sugeriu "${data.songTitle}"`,
      priority: NotificationPriority.NORMAL,
      category: "suggestions",
      data: data,
      actions: [
        {
          id: "approve",
          label: "Aprovar",
          endpoint: `/api/v1/suggestions/${data.suggestionId}/approve`,
          method: "POST",
          style: "primary",
        },
        {
          id: "reject",
          label: "Rejeitar",
          endpoint: `/api/v1/suggestions/${data.suggestionId}/reject`,
          method: "POST",
          style: "danger",
        },
      ],
    });
  }

  private async handleSuggestionApproved(data: any): Promise<void> {
    if (data.clientSessionId) {
      await this.sendNotification({
        type: NotificationType.SUCCESS,
        title: "Sugestão Aprovada!",
        message: `Sua música "${data.songTitle}" foi aprovada e adicionada à fila`,
        priority: NotificationPriority.HIGH,
        category: "suggestions",
        targetUsers: [data.clientSessionId],
        data: data,
      });
    }

    await this.sendToRestaurant(data.restaurantId, {
      type: NotificationType.MUSIC,
      title: "Nova Música na Fila",
      message: `"${data.songTitle}" foi adicionada à playlist`,
      priority: NotificationPriority.NORMAL,
      category: "playlist",
      data: data,
    });
  }

  private async handleSuggestionRejected(data: any): Promise<void> {
    if (data.clientSessionId) {
      await this.sendNotification({
        type: NotificationType.WARNING,
        title: "Sugestão Rejeitada",
        message: `Sua música "${data.songTitle}" não foi aprovada`,
        priority: NotificationPriority.NORMAL,
        category: "suggestions",
        targetUsers: [data.clientSessionId],
        data: data,
      });
    }
  }

  private async handleVoteCast(data: any): Promise<void> {
    if (data.positionChanged) {
      await this.sendToRestaurant(data.restaurantId, {
        type: NotificationType.VOTE,
        title: "Fila Atualizada",
        message: `"${data.songTitle}" ${
          data.voteType === "up" ? "subiu" : "desceu"
        } na fila`,
        priority: NotificationPriority.LOW,
        category: "voting",
        data: data,
        autoClose: true,
        duration: 3000,
      });
    }
  }

  private async handleBadgeEarned(data: any): Promise<void> {
    await this.sendNotification({
      type: NotificationType.BADGE,
      title: "Novo Badge Desbloqueado!",
      message: `Você ganhou o badge "${data.badgeName}"`,
      priority: NotificationPriority.HIGH,
      category: "gamification",
      targetUsers: [data.clientSessionId],
      data: data,
      autoClose: false,
    });
  }

  private async handlePlaybackChanged(data: any): Promise<void> {
    await this.sendToRestaurant(data.restaurantId, {
      type: NotificationType.MUSIC,
      title: "Reprodução Alterada",
      message:
        data.action === "play"
          ? `Tocando: "${data.songTitle}"`
          : data.action === "pause"
          ? "Reprodução pausada"
          : `Volume alterado para ${data.volume}%`,
      priority: NotificationPriority.LOW,
      category: "playback",
      data: data,
      autoClose: true,
      duration: 2000,
    });
  }

  private async handleSystemMaintenance(data: any): Promise<void> {
    await this.broadcast({
      type: NotificationType.WARNING,
      title: "Manutenção do Sistema",
      message: data.message || "O sistema entrará em manutenção em breve",
      priority: NotificationPriority.URGENT,
      category: "system",
      data: data,
      autoClose: false,
    });
  }

  private async handlePaymentApproved(data: any): Promise<void> {
    if (data.clientSessionId) {
      await this.sendNotification({
        type: NotificationType.PAYMENT,
        title: "Pagamento Aprovado!",
        message: `Seu pagamento de R$ ${data.amount} foi aprovado. Sua música terá prioridade na fila!`,
        priority: NotificationPriority.HIGH,
        category: "payment",
        targetUsers: [data.clientSessionId],
        autoClose: false,
        data: data,
      });
    }

    await this.sendToRestaurant(data.restaurantId, {
      type: NotificationType.PAYMENT,
      title: "Pagamento Recebido",
      message: `Pagamento de R$ ${data.amount} aprovado para "${data.songTitle}"`,
      priority: NotificationPriority.NORMAL,
      category: "payment",
      autoClose: true,
      duration: 5000,
      data: data,
    });
  }

  private getTargetClients(notification: INotificationData): ConnectedClient[] {
    const allClients = Array.from(this.clients.values());

    if (!notification.targetUsers && !notification.targetRestaurants) {
      return allClients;
    }

    return allClients.filter((client) => {
      if (notification.targetUsers && client.userId) {
        return notification.targetUsers.includes(client.userId);
      }

      if (notification.targetRestaurants && client.restaurantId) {
        return notification.targetRestaurants.includes(client.restaurantId);
      }

      return false;
    });
  }

  private addToHistory(notification: INotificationData): void {
    const historyEntry: INotificationHistory = {
      id: this.generateNotificationId(),
      type: notification.type,
      title: notification.title,
      message: notification.message,
      category: notification.category,
      priority: notification.priority,
      notification,
      timestamp: new Date(),
      deliveryStatus: { websocket: true },
      recipients: this.getRecipients(notification),
    };

    this.notificationHistory.unshift(historyEntry);

    if (this.notificationHistory.length > this.maxHistorySize) {
      this.notificationHistory = this.notificationHistory.slice(
        0,
        this.maxHistorySize
      );
    }
  }

  private generateNotificationId(): string {
    return `notification-${Date.now()}-${Math.random()
      .toString(36)
      .substr(2, 9)}`;
  }

  private addToQueue(notification: INotificationData): void {
    if (this.notificationQueue.length >= this.maxQueueSize) {
      logger.warn("Fila de notificações cheia, removendo mais antiga");
      this.notificationQueue.shift();
    }

    this.notificationQueue.push(notification);
    logger.debug(
      `Notificação adicionada à fila. Tamanho atual: ${this.notificationQueue.length}`
    );
  }

  private async deliverNotification(
    notification: INotificationData
  ): Promise<void> {
    const startTime = Date.now();
    const notificationId = this.generateNotificationId();
    const channels = notification.channels || [NotificationChannel.WEBSOCKET];

    const deliveryStatus = {
      websocket: false,
      email: false,
      sms: false,
      push: false,
    };

    const targetClients = this.getTargetClients(notification);

    if (channels.includes(NotificationChannel.WEBSOCKET)) {
      try {
        for (const client of targetClients) {
          if (client.ws.connected) {
            client.ws.emit("notification", {
              type: "notification",
              notification: { ...notification, id: notificationId },
            });
            client.lastActivity = new Date();
          }
        }
        deliveryStatus.websocket = true;
      } catch (error) {
        logger.error("Erro na entrega via WebSocket:", error);
      }
    }

    const historyEntry: INotificationHistory = {
      id: notificationId,
      type: notification.type,
      title: notification.title,
      message: notification.message,
      category: notification.category,
      priority: notification.priority,
      notification,
      timestamp: new Date(),
      deliveryStatus,
      recipients: this.getRecipients(notification),
    };

    this.addToHistory(historyEntry);
    this.updateStats(notification, Date.now() - startTime, deliveryStatus);
    this.emit("notification:sent", historyEntry);
  }

  private async deliverViaWebSocket(
    notification: INotificationData
  ): Promise<void> {
    // Verificar se o WebSocketService está disponível
    if (!this.wsService || typeof this.wsService.emitToRestaurant !== 'function') {
      logger.warn('WebSocketService não está disponível para entrega de notificação');
      return;
    }

    try {
      if (
        notification.targetRestaurants &&
        notification.targetRestaurants.length > 0
      ) {
        for (const restaurantId of notification.targetRestaurants) {
          await this.wsService.emitToRestaurant(
            restaurantId,
            "notification",
            notification
          );
        }
      } else if (
        notification.targetUsers &&
        notification.targetUsers.length > 0
      ) {
        for (const userId of notification.targetUsers) {
          await this.wsService.emitToUser(userId, "notification", notification);
        }
      } else {
        await this.wsService.broadcast("notification", notification);
      }
    } catch (error) {
      logger.error('Erro ao entregar notificação via WebSocket:', error);
      throw error;
    }
  }

  private getRecipients(notification: INotificationData): string[] {
    const recipients: string[] = [];

    if (notification.targetRestaurants) {
      recipients.push(...notification.targetRestaurants);
    }

    if (notification.targetUsers) {
      recipients.push(...notification.targetUsers);
    }

    return recipients;
  }

  private updateStats(
    notification: INotificationData,
    deliveryTime: number,
    deliveryStatus: {
      websocket?: boolean;
      email?: boolean;
      sms?: boolean;
      push?: boolean;
    }
  ): void {
    this.stats.totalSent++;

    if (deliveryStatus.websocket) {
      this.stats.totalDelivered++;
    } else {
      this.stats.totalFailed++;
    }

    this.stats.deliveryRate =
      this.stats.totalSent > 0
        ? (this.stats.totalDelivered / this.stats.totalSent) * 100
        : 0;
    this.stats.averageDeliveryTime =
      this.stats.totalSent > 1
        ? (this.stats.averageDeliveryTime * (this.stats.totalSent - 1) +
            deliveryTime) /
          this.stats.totalSent
        : deliveryTime;

    this.stats.byType[notification.type] =
      (this.stats.byType[notification.type] || 0) + 1;
    const priority = notification.priority || NotificationPriority.NORMAL;
    this.stats.byPriority[priority] =
      (this.stats.byPriority[priority] || 0) + 1;
  }

  getHistory(limit: number = 50): INotificationHistory[] {
    return this.notificationHistory.slice(0, limit);
  }

  getConnectedClients(): { total: number; byType: Record<string, number> } {
    const clients = Array.from(this.clients.values());
    const byType = clients.reduce((acc, client) => {
      acc[client.type] = (acc[client.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total: clients.length,
      byType,
    };
  }

  async notifyNewSuggestion(
    restaurantId: string,
    suggestionData: any
  ): Promise<void> {
    this.emit("suggestion:created", { restaurantId, ...suggestionData });
  }

  async notifyVote(restaurantId: string, voteData: any): Promise<void> {
    this.emit("vote:cast", { restaurantId, ...voteData });
  }

  async notifyBadgeEarned(sessionId: string, badgeData: any): Promise<void> {
    this.emit("badge:earned", { clientSessionId: sessionId, ...badgeData });
  }

  async notifyPlaybackChange(
    restaurantId: string,
    playbackData: any
  ): Promise<void> {
    this.emit("playback:changed", { restaurantId, ...playbackData });
  }

  async notifySystemEvent(eventData: any): Promise<void> {
    this.emit("system:maintenance", eventData);
  }

  getStats(): INotificationStats {
    return { ...this.stats };
  }

  clearHistory(): void {
    this.notificationHistory = [];
    logger.info("Histórico de notificações limpo");
  }
}

export const notificationService = NotificationService.getInstance();
export { NotificationService };
export default NotificationService;
