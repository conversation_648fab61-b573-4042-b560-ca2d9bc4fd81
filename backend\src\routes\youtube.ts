import { Router, Request, Response } from "express";
import { body, query, validationResult } from "../utils/validation";
import { youtubeService } from "../config/youtube";
import asyncHandler from "../middleware/asyncHandler";
import { createValidationError } from "../utils/errors";
import { optionalAuth } from "../middleware/auth";
import { logger } from "../utils/logger";

const router = Router();

/**
 * @swagger
 * /api/v1/youtube/search:
 *   get:
 *     summary: Buscar vídeos no YouTube
 *     tags: [YouTube]
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *         description: Termo de busca
 *       - in: query
 *         name: maxResults
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 10
 *         description: Número máximo de resultados
 *       - in: query
 *         name: pageToken
 *         schema:
 *           type: string
 *         description: Token para paginação
 *     responses:
 *       200:
 *         description: Resultados da busca
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 videos:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/VideoInfo'
 *                 nextPageToken:
 *                   type: string
 *                 totalResults:
 *                   type: integer
 *                 quotaUsed:
 *                   type: integer
 *       400:
 *         description: Parâmetros inválidos
 *       429:
 *         description: Quota da API excedida
 */
router.get(
  "/search",
  [
    query("q")
      .notEmpty()
      .withMessage("Termo de busca é obrigatório")
      .isLength({ min: 2, max: 100 })
      .withMessage("Termo de busca deve ter entre 2 e 100 caracteres"),
    query("maxResults")
      .optional()
      .isInt({ min: 1, max: 50 })
      .withMessage("maxResults deve ser um número entre 1 e 50"),
    query("pageToken")
      .optional()
      .isString()
      .withMessage("pageToken deve ser uma string"),
  ],
  optionalAuth,
  asyncHandler(async (req: any, res: any) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createValidationError(errors.array());
    }

    const {
      q,
      maxResults = 10,
      pageToken,
    } = req.query as {
      q: string;
      maxResults?: number;
      pageToken?: string;
    };

    const result = await youtubeService.searchVideos(
      q,
      Number(maxResults) || 10
    );

    res.json({
      success: true,
      data: result,
    });
  })
);

/**
 * @swagger
 * /api/v1/youtube/video/{videoId}:
 *   get:
 *     summary: Obter informações de um vídeo específico
 *     tags: [YouTube]
 *     parameters:
 *       - in: path
 *         name: videoId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID do vídeo no YouTube
 *     responses:
 *       200:
 *         description: Informações do vídeo
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/VideoInfo'
 *       404:
 *         description: Vídeo não encontrado
 *       429:
 *         description: Quota da API excedida
 */
router.get(
  "/video/:videoId",
  [
    query("videoId")
      .isString()
      .isLength({ min: 11, max: 11 })
      .withMessage("ID do vídeo deve ter 11 caracteres"),
  ],
  optionalAuth,
  asyncHandler(async (req: any, res: any) => {
    const { videoId } = req.params;

    // Validar formato do videoId
    const videoIdRegex = /^[a-zA-Z0-9_-]{11}$/;
    if (!videoIdRegex.test(videoId)) {
      return res.status(400).json({
        error: "Formato de ID do vídeo inválido",
        code: "INVALID_VIDEO_ID",
      });
    }

    const videoInfo = await (youtubeService as any).getVideoInfo(videoId);

    if (!videoInfo) {
      return res.status(404).json({
        error: "Vídeo não encontrado ou não disponível",
        code: "VIDEO_NOT_FOUND",
      });
    }

    res.json({
      video: videoInfo,
    });
  })
);

/**
 * @swagger
 * /api/v1/youtube/validate:
 *   post:
 *     summary: Validar múltiplos vídeos
 *     tags: [YouTube]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               videoIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 maxItems: 50
 *                 description: Lista de IDs de vídeos para validar
 *     responses:
 *       200:
 *         description: Resultados da validação
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 valid:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/VideoInfo'
 *                 invalid:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       videoId:
 *                         type: string
 *                       reason:
 *                         type: string
 *       400:
 *         description: Parâmetros inválidos
 */
router.post(
  "/validate",
  [
    body("videoIds")
      .isArray({ min: 1, max: 50 })
      .withMessage("videoIds deve ser um array com 1 a 50 itens"),
    body("videoIds.*")
      .isString()
      .isLength({ min: 11, max: 11 })
      .withMessage("Cada videoId deve ter 11 caracteres"),
  ],
  optionalAuth,
  asyncHandler(async (req: any, res: any) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createValidationError(errors.array());
    }

    const { videoIds } = req.body as { videoIds: string[] };

    const videos = await (youtubeService as any).getVideoDetails(videoIds);
    const valid: any[] = [];
    const invalid: any[] = [];

    for (const videoId of videoIds) {
      const video = (videos as unknown as any[]).find(
        (v: any) => v.id === videoId
      );

      if (!video) {
        invalid.push({
          videoId,
          reason: "Vídeo não encontrado",
        });
        continue;
      }

      try {
        const processedVideo = youtubeService["processVideoInfo"](video);
        if (youtubeService["isValidVideo"](video)) {
          valid.push(processedVideo);
        } else {
          invalid.push({
            videoId,
            reason: "Vídeo não atende aos critérios de validação",
          });
        }
      } catch (error) {
        invalid.push({
          videoId,
          reason: "Erro ao processar vídeo",
        });
      }
    }

    res.json({
      valid,
      invalid,
    });
  })
);

/**
 * @swagger
 * /api/v1/youtube/quota:
 *   get:
 *     summary: Obter informações sobre quota da API
 *     tags: [YouTube]
 *     responses:
 *       200:
 *         description: Informações da quota
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 quotaUsed:
 *                   type: integer
 *                 quotaRemaining:
 *                   type: integer
 *                 quotaLimit:
 *                   type: integer
 *                 canSearch:
 *                   type: boolean
 *                 canGetVideoDetails:
 *                   type: boolean
 */
router.get(
  "/quota",
  optionalAuth,
  asyncHandler(async (req: any, res: any) => {
    res.json({
      quotaUsed: 0,
      quotaRemaining: 10000,
      quotaLimit: parseInt(process.env.YOUTUBE_API_QUOTA_LIMIT || "10000"),
      canSearch: true,
      canGetVideoDetails: true,
    });
  })
);

/**
 * @swagger
 * components:
 *   schemas:
 *     VideoInfo:
 *       type: object
 *       properties:
 *         youtubeVideoId:
 *           type: string
 *           description: ID do vídeo no YouTube
 *         title:
 *           type: string
 *           description: Título da música
 *         artist:
 *           type: string
 *           description: Nome do artista
 *         channelName:
 *           type: string
 *           description: Nome do canal
 *         duration:
 *           type: integer
 *           description: Duração em segundos
 *         thumbnailUrl:
 *           type: string
 *           description: URL da thumbnail
 *         description:
 *           type: string
 *           description: Descrição do vídeo
 *         metadata:
 *           type: object
 *           properties:
 *             genre:
 *               type: array
 *               items:
 *                 type: string
 *             mood:
 *               type: array
 *               items:
 *                 type: string
 *             language:
 *               type: string
 *             explicit:
 *               type: boolean
 *             live:
 *               type: boolean
 *             publishedAt:
 *               type: string
 *               format: date-time
 *             viewCount:
 *               type: integer
 *             likeCount:
 *               type: integer
 *             tags:
 *               type: array
 *               items:
 *                 type: string
 */

export default router;
