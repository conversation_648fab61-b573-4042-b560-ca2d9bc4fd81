# ============================================
# SCRIPT DE RECONSTRUÇÃO COMPLETA COM VERIFICAÇÃO
# Sistema de Playlist Interativa para Restaurantes
# ============================================

Write-Host "🚀 Iniciando reconstrução completa do sistema..." -ForegroundColor Cyan

# Parar todos os containers
Write-Host "`n🛑 Parando containers existentes..." -ForegroundColor Yellow
docker-compose down -v

# Remover imagens antigas (opcional)
Write-Host "`n🗑️  Removendo imagens antigas..." -ForegroundColor Yellow
docker image prune -f

# Verificar se os arquivos SQL existem
Write-Host "`n📁 Verificando arquivos de inicialização..." -ForegroundColor Yellow
$sqlFiles = @(
    "database/init.sql",
    "database/create-genres-table.sql", 
    "database/create-missing-tables.sql",
    "database/seed-data.sql",
    "database/verify-tables.sql"
)

foreach ($file in $sqlFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file encontrado" -ForegroundColor Green
    } else {
        Write-Host "❌ $file NÃO encontrado!" -ForegroundColor Red
        exit 1
    }
}

# Construir e iniciar containers
Write-Host "`n🔨 Construindo e iniciando containers..." -ForegroundColor Yellow
docker-compose up --build -d

# Aguardar containers ficarem prontos
Write-Host "`n⏳ Aguardando containers ficarem prontos..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Verificar se todos os containers estão rodando
Write-Host "`n📦 Verificando status dos containers..." -ForegroundColor Yellow
$containers = @("restaurant-playlist-db", "restaurant-playlist-redis", "restaurant-playlist-api", "restaurant-playlist-frontend")
$allRunning = $true

foreach ($container in $containers) {
    $status = docker ps --filter "name=$container" --format "{{.Status}}"
    if ($status -match "Up") {
        Write-Host "✅ $container está rodando" -ForegroundColor Green
    } else {
        Write-Host "❌ $container NÃO está rodando!" -ForegroundColor Red
        $allRunning = $false
    }
}

if (-not $allRunning) {
    Write-Host "`n❌ Alguns containers não estão rodando. Verificando logs..." -ForegroundColor Red
    foreach ($container in $containers) {
        Write-Host "`n--- Logs do $container ---" -ForegroundColor Gray
        docker logs $container --tail=10
    }
    exit 1
}

# Aguardar mais um pouco para o banco inicializar completamente
Write-Host "`n⏳ Aguardando inicialização completa do banco..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

# Verificar health checks
Write-Host "`n🏥 Verificando health checks..." -ForegroundColor Yellow
$healthyContainers = docker ps --filter "health=healthy" --format "{{.Names}}"
Write-Host "Containers saudáveis: $healthyContainers" -ForegroundColor Green

# Executar verificação do banco de dados
Write-Host "`n🔍 Executando verificação completa do banco..." -ForegroundColor Yellow
& "scripts/verify-database.ps1"

# Verificar se há erros nos logs
Write-Host "`n📝 Verificando logs para erros..." -ForegroundColor Yellow
$backendLogs = docker logs restaurant-playlist-api --tail=20 2>&1
if ($backendLogs -match "ERROR" -or $backendLogs -match "Error") {
    Write-Host "⚠️  Possíveis erros encontrados nos logs do backend:" -ForegroundColor Yellow
    $backendLogs | Where-Object { $_ -match "ERROR|Error" } | ForEach-Object { Write-Host $_ -ForegroundColor Red }
}

$dbLogs = docker logs restaurant-playlist-db --tail=20 2>&1
if ($dbLogs -match "ERROR" -or $dbLogs -match "Error") {
    Write-Host "⚠️  Possíveis erros encontrados nos logs do banco:" -ForegroundColor Yellow
    $dbLogs | Where-Object { $_ -match "ERROR|Error" } | ForEach-Object { Write-Host $_ -ForegroundColor Red }
}

# Teste final das funcionalidades
Write-Host "`n🧪 Executando testes finais..." -ForegroundColor Yellow

# Testar criação de restaurante
try {
    $testRestaurant = @{
        name = "Restaurante Teste"
        description = "Teste de criação"
        phone = "11999999999"
        address = "Rua Teste, 123"
    } | ConvertTo-Json

    $headers = @{"Content-Type" = "application/json"}
    $response = Invoke-RestMethod -Uri "http://localhost:8001/api/v1/admin/restaurants" -Method POST -Headers $headers -Body $testRestaurant -TimeoutSec 10
    Write-Host "✅ Teste de criação de restaurante passou!" -ForegroundColor Green
    
    # Deletar o restaurante de teste
    Invoke-RestMethod -Uri "http://localhost:8001/api/v1/admin/restaurants/$($response.restaurant.id)" -Method DELETE -TimeoutSec 10
    Write-Host "✅ Teste de deleção de restaurante passou!" -ForegroundColor Green
} catch {
    Write-Host "❌ Teste de CRUD de restaurante falhou!" -ForegroundColor Red
    Write-Host "Erro: $($_.Exception.Message)" -ForegroundColor Gray
}

Write-Host "`n🎉 Reconstrução e verificação concluídas!" -ForegroundColor Cyan
Write-Host "`n📊 Resumo do sistema:" -ForegroundColor White
Write-Host "  🌐 Frontend: http://localhost:8000" -ForegroundColor Gray
Write-Host "  🔧 Admin: http://localhost:8000/admin" -ForegroundColor Gray  
Write-Host "  📡 API: http://localhost:8001" -ForegroundColor Gray
Write-Host "  🗄️  Banco: localhost:8002" -ForegroundColor Gray
Write-Host "  🔴 Redis: localhost:8003" -ForegroundColor Gray

Write-Host "`n✨ Sistema pronto para uso!" -ForegroundColor Green
