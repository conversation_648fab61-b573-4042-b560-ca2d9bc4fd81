import { google, youtube_v3 } from "googleapis";
import { v4 as uuidv4, validate as uuidValidate } from "uuid";
import { OAuth2Client } from "google-auth-library";
import { AppDataSource } from "../config/database";
import { Restaurant } from "../models/Restaurant";
import { Playlist, PlaylistType } from "../models/Playlist";
import { Suggestion } from "../models/Suggestion";
import { Payment } from "../models/Payment";
import { SuggestionStatus } from "../models/Suggestion";
import { PlaylistStatus } from "../models/Playlist";
import { Repository, Like } from "typeorm";
import { YouTubeService } from "./YouTubeService";
import { WebSocketService } from "./WebSocketService";

// Nota: em testes, AppDataSource é mockado para retornar TestDataSource

/**
 * 🎵 SERVIÇO DE PLAYLIST COLABORATIVA CENTRAL
 *
 * Usa uma única conta YouTube Premium para gerenciar todas as playlists
 * dos restaurantes, eliminando a necessidade de OAuth individual.
 */
export class CollaborativePlaylistService {
  private oauth2Client: OAuth2Client;
  private youtube: youtube_v3.Youtube;
  private _restaurantRepository: Repository<Restaurant> | null = null;
  private _playlistRepository: Repository<Playlist> | null = null;
  private _suggestionRepository: Repository<Suggestion> | null = null;
  private youtubeService: YouTubeService;
  private webSocketService: WebSocketService;

  // Lazy loading getters para repositórios
  private get restaurantRepository(): Repository<Restaurant> {
    if (!this._restaurantRepository) {
  this._restaurantRepository = AppDataSource.getRepository(Restaurant);
    }
    return this._restaurantRepository;
  }

  private get playlistRepository(): Repository<Playlist> {
    if (!this._playlistRepository) {
  this._playlistRepository = AppDataSource.getRepository(Playlist);
    }
    return this._playlistRepository;
  }

  private get suggestionRepository(): Repository<Suggestion> {
    if (!this._suggestionRepository) {
  this._suggestionRepository = AppDataSource.getRepository(Suggestion);
    }
    return this._suggestionRepository;
  }

  constructor() {
    // Configurar OAuth com conta central
    this.oauth2Client = new google.auth.OAuth2(
      process.env.CENTRAL_GOOGLE_CLIENT_ID,
      process.env.CENTRAL_GOOGLE_CLIENT_SECRET,
      process.env.CENTRAL_GOOGLE_REDIRECT_URI
    );

    // Configurar credenciais da conta central
    this.oauth2Client.setCredentials({
      refresh_token: process.env.CENTRAL_GOOGLE_REFRESH_TOKEN,
      access_token: process.env.CENTRAL_GOOGLE_ACCESS_TOKEN,
    });

    // Inicializar YouTube API
    this.youtube = google.youtube({ version: "v3", auth: this.oauth2Client });

    // Inicializar serviços
    this.youtubeService = new YouTubeService();
    this.webSocketService = WebSocketService.getInstance();
  }

  /**
   * 🏪 CRIAR PLAYLIST COLABORATIVA PARA NOVO RESTAURANTE
   */
  async createRestaurantPlaylist(
    restaurantId: string,
    playlistName: string,
    description?: string
  ): Promise<{
    success: boolean;
    playlistId?: string;
    youtubePlaylistId?: string;
    message: string;
  }> {
    try {
      console.log(
        `🎵 Criando playlist colaborativa para restaurante: ${restaurantId}`
      );

      // 1. Verificar se restaurante existe
      const restaurant = await this.restaurantRepository.findOne({
        where: { id: restaurantId },
      });

      if (!restaurant) {
        return {
          success: false,
          message: "Restaurante não encontrado",
        };
      }

      // 2. Criar playlist no YouTube usando conta central
      const youtubePlaylist = await this.youtube.playlists.insert({
        part: ["snippet", "status"],
        requestBody: {
          snippet: {
            title: `${restaurant.name} - ${playlistName}`,
            description:
              description ||
              `Playlist colaborativa do ${restaurant.name}. Clientes podem sugerir músicas via QR Code.`,
            tags: [
              "restaurante",
              "colaborativa",
              "interativa",
              restaurant.name.toLowerCase(),
            ],
          },
          status: {
            privacyStatus: "public", // Playlist pública para colaboração
          },
        },
      });

      const youtubePlaylistId = youtubePlaylist.data.id!;
      console.log(`✅ Playlist criada no YouTube: ${youtubePlaylistId}`);

      // 3. Salvar playlist no banco de dados
      const playlist = this.playlistRepository.create({
        name: playlistName,
        description:
          description || `Playlist colaborativa do ${restaurant.name}`,
  type: PlaylistType.SUGGESTIONS,
        status: PlaylistStatus.ACTIVE,
        youtubePlaylistId,
        restaurant,
        isPublic: true,
        isDefault: true, // Primeira playlist é padrão
        trackCount: 0,
        totalDuration: 0,
        tracks: [],
        settings: {
          allowSuggestions: true,
          maxSuggestionsPerUser: 10,
          autoReorderByVotes: false,
          volume: 80,
          autoPlay: true,
        },
      });

      const savedPlaylist = await this.playlistRepository.save(playlist);

      console.log(`✅ Playlist salva no banco: ${savedPlaylist.id}`);

      return {
        success: true,
        playlistId: savedPlaylist.id,
        youtubePlaylistId,
        message: `Playlist colaborativa "${playlistName}" criada com sucesso!`,
      };
    } catch (error) {
      console.error("❌ Erro ao criar playlist colaborativa:", error);
      return {
        success: false,
        message: `Erro ao criar playlist: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
      };
    }
  }

  /**
   * 🎵 ADICIONAR MÚSICAS INICIAIS À PLAYLIST
   */
  async addInitialTracks(
    playlistId: string,
    videoIds: string[]
  ): Promise<{ success: boolean; message: string; addedCount?: number }> {
    try {
      console.log(
        `🎵 Adicionando ${videoIds.length} músicas iniciais à playlist ${playlistId}`
      );

      // 1. Buscar playlist
      const playlist = await this.playlistRepository.findOne({
        where: { id: playlistId },
        relations: ["restaurant"],
      });

      if (!playlist || !playlist.youtubePlaylistId) {
        return {
          success: false,
          message: "Playlist não encontrada",
        };
      }

      let addedCount = 0;

      // Preparar array de tracks no DB
      if (!playlist.tracks) {
        playlist.tracks = [] as any;
      }

      // 2. Adicionar cada vídeo à playlist do YouTube e salvar track no DB
      for (const videoId of videoIds) {
        try {
          await this.youtube.playlistItems.insert({
            part: ["snippet"],
            requestBody: {
              snippet: {
                playlistId: playlist.youtubePlaylistId,
                resourceId: {
                  kind: "youtube#video",
                  videoId,
                },
              },
            },
          });

          // Buscar metadados e persistir
          try {
            const info = await this.youtubeService.getVideoInfo(videoId);
            const newTrack = {
              youtubeVideoId: videoId,
              title: info?.title || `Vídeo ${videoId}`,
              artist: info?.artist || info?.channelName || "",
              duration: info?.duration || 0,
              thumbnailUrl:
                info?.thumbnailUrl || `https://i.ytimg.com/vi/${videoId}/mqdefault.jpg`,
              addedAt: new Date().toISOString(),
              position: playlist.tracks.length,
            } as any;
            playlist.tracks.push(newTrack);
            playlist.trackCount = playlist.tracks.length;
            if (newTrack.duration) {
              playlist.totalDuration = (playlist.totalDuration || 0) + newTrack.duration;
            }
          } catch (metaErr) {
            console.warn(`⚠️ Falha ao obter metadados de ${videoId}:`, metaErr);
          }

          addedCount++;
          console.log(`✅ Vídeo adicionado: ${videoId}`);
        } catch (error) {
          console.warn(`⚠️ Erro ao adicionar vídeo ${videoId}:`, error);
        }
      }

      // 3. Persistir atualização de tracks no DB
      try {
        await this.playlistRepository.save(playlist);
      } catch (saveErr) {
        console.warn("⚠️ Falha ao salvar tracks da playlist no DB:", saveErr);
      }

      return {
        success: true,
        message: `Adicionadas ${addedCount} músicas à playlist`,
        addedCount,
      };
    } catch (error) {
      console.error("❌ Erro ao adicionar músicas iniciais:", error);
      return {
        success: false,
        message: `Erro ao adicionar músicas: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
      };
    }
  }

  /**
   * 🚀 PROCESSAR SUGESTÃO PAGA (FILA PRIORITÁRIA)
   */
  async processPaidSuggestion(
    restaurantId: string,
    suggestionId: string
  ): Promise<{ success: boolean; message: string; newPosition?: number }> {
    try {
      console.log(`💰 Processando sugestão paga: ${suggestionId}`);

      // 1. Buscar sugestão e playlist
      const suggestion = await this.suggestionRepository.findOne({
        where: { id: suggestionId },
        relations: ["restaurant"],
      });

      if (!suggestion || suggestion.restaurant.id !== restaurantId) {
        return {
          success: false,
          message: "Sugestão não encontrada",
        };
      }

      const playlist = await this.playlistRepository.findOne({
        where: {
          restaurant: { id: restaurantId },
          status: PlaylistStatus.ACTIVE,
        },
        order: {
          isDefault: "DESC",
          createdAt: "DESC",
        },
      });

      if (!playlist || !playlist.youtubePlaylistId) {
        return {
          success: false,
          message: "Playlist ativa não encontrada",
        };
      }

      // 2. Obter itens atuais da playlist
      const playlistItems = await this.youtube.playlistItems.list({
        part: ["snippet"],
        playlistId: playlist.youtubePlaylistId,
        maxResults: 50,
      });

      const items = playlistItems.data.items || [];

      // 3. Encontrar a música na playlist
      const targetItem = items.find(
        (item) =>
          item.snippet?.resourceId?.videoId === suggestion.youtubeVideoId
      );

      if (!targetItem) {
        return {
          success: false,
          message:
            "Música não encontrada na playlist. Apenas músicas da playlist podem ser priorizadas.",
        };
      }

      // 4. Mover para a próxima posição (posição 1)
      await this.youtube.playlistItems.update({
        part: ["snippet"],
        requestBody: {
          id: targetItem.id!,
          snippet: {
            ...targetItem.snippet,
            position: 1, // Próxima a tocar
          },
        },
      });

      // 5. Marcar sugestão como processada
      suggestion.status = SuggestionStatus.APPROVED;
      await this.suggestionRepository.save(suggestion);

      console.log(`✅ Música paga movida para posição 1: ${suggestion.title}`);

      return {
        success: true,
        newPosition: 1,
        message: `Música "${suggestion.title}" movida para a próxima posição!`,
      };
    } catch (error) {
      console.error("❌ Erro ao processar sugestão paga:", error);
      return {
        success: false,
        message: `Erro ao processar sugestão: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
      };
    }
  }

  /**
   * 🔒 Verifica se um vídeo pertence à playlist ativa do restaurante e retorna o track
   */
  private async getActivePlaylistTrack(
    restaurantId: string,
    youtubeVideoId: string
  ): Promise<{
    playlist?: Playlist;
    track?: {
      youtubeVideoId: string;
      title: string;
      artist: string;
      duration: number;
      thumbnailUrl: string;
    };
  }> {
    const playlist = await this.playlistRepository.findOne({
      where: {
        restaurant: { id: restaurantId },
        status: PlaylistStatus.ACTIVE,
      },
      order: { isDefault: "DESC", createdAt: "DESC" },
    });
    const track = playlist?.tracks?.find(
      (t) => t.youtubeVideoId === youtubeVideoId
    );
    return {
      playlist: playlist || undefined,
      track: track
        ? {
            youtubeVideoId: track.youtubeVideoId,
            title: track.title,
            artist: track.artist,
            duration: track.duration,
            thumbnailUrl: track.thumbnailUrl,
          }
        : undefined,
    };
  }

  /**
   * 🗳️ PROCESSAR VOTO NORMAL (GRATUITO)
   */
  async processNormalVote(
    restaurantId: string,
    youtubeVideoId: string,
    tableNumber?: number,
    clientSessionId?: string
  ): Promise<{ success: boolean; message: string; voteWeight: number }> {
    try {
      console.log(
        `🗳️ Processando voto normal: ${youtubeVideoId} - Mesa ${tableNumber}`
      );
      console.log(
        `📍 Parâmetros: restaurantId=${restaurantId}, youtubeVideoId=${youtubeVideoId}`
      );

      // 1. Verificar se já existe sugestão para este vídeo (filtro direto por FK para evitar LEFT JOIN)
      let suggestion = await this.suggestionRepository
        .createQueryBuilder("suggestion")
        .where("suggestion.restaurant_id = :restaurantId", { restaurantId })
        .andWhere("suggestion.youtubeVideoId = :youtubeVideoId", {
          youtubeVideoId,
        })
        .andWhere("suggestion.status = :status", {
          status: SuggestionStatus.APPROVED,
        })
        .getOne();

      if (suggestion) {
        // Incrementar contagem de votos (peso 1)
        suggestion.voteCount = (suggestion.voteCount || 0) + 1;
        suggestion.upvotes = (suggestion.upvotes || 0) + 1;
      } else {
        // Criar nova sugestão
        const restaurant = await this.restaurantRepository.findOne({
          where: { id: restaurantId },
        });

        if (!restaurant) {
          return {
            success: false,
            message: "Restaurante não encontrado",
            voteWeight: 0,
          };
        }

        // Playlist protection: só permitir se a música estiver na playlist ativa
        const guard = await this.getActivePlaylistTrack(
          restaurantId,
          youtubeVideoId
        );
        if (!guard.track) {
          return {
            success: false,
            message: "Esta música não faz parte da playlist do restaurante",
            voteWeight: 0,
          };
        }

        // Preferir metadados do track da playlist
        const base = guard.track;

        suggestion = this.suggestionRepository.create({
          youtubeVideoId,
          title: base.title,
          artist: base.artist,
          channelName: "",
          duration: base.duration || 180,
          thumbnailUrl:
            base.thumbnailUrl ||
            `https://i.ytimg.com/vi/${youtubeVideoId}/mqdefault.jpg`,
          restaurant,
          status: SuggestionStatus.APPROVED,
          isPaid: false,
          voteCount: 1,
          upvotes: 1,
          downvotes: 0,
          paymentAmount: 0,
          clientSessionId: clientSessionId || `session_${Date.now()}`,
          tableNumber,
        });
      }

      await this.suggestionRepository.save(suggestion);

      console.log(
        `✅ Voto normal registrado: ${suggestion.voteCount} votos totais`
      );

      return {
        success: true,
        message: "Voto registrado com sucesso!",
        voteWeight: 1,
      };
    } catch (error) {
      console.error("❌ Erro ao processar voto normal:", error);
      return {
        success: false,
        message: `Erro ao processar voto: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
        voteWeight: 0,
      };
    }
  }

  /**
   * 💰 PROCESSAR SUPERVOTO (PAGO)
   */
  async processSuperVote(
    restaurantId: string,
    youtubeVideoId: string,
    paymentAmount: number,
    paymentId: string,
    tableNumber?: number,
  clientSessionId?: string,
  clientMessage?: string,
  clientName?: string
  ): Promise<{ success: boolean; message: string; voteWeight: number }> {
    try {
      console.log(
        `💰 Processando supervoto: R$ ${paymentAmount} - ${youtubeVideoId}`
      );

      // 1. Calcular peso do supervoto baseado no valor
      const voteWeight = this.calculateSuperVoteWeight(paymentAmount);

      // 2. Verificar se já existe sugestão para este vídeo (filtro direto por FK para evitar LEFT JOIN)
      let suggestion = await this.suggestionRepository
        .createQueryBuilder("suggestion")
        .where("suggestion.restaurant_id = :restaurantId", { restaurantId })
        .andWhere("suggestion.youtubeVideoId = :youtubeVideoId", {
          youtubeVideoId,
        })
        .andWhere("suggestion.status = :status", {
          status: SuggestionStatus.APPROVED,
        })
        .getOne();

      if (suggestion) {
        // Incrementar contagem com peso do supervoto
        suggestion.voteCount = (suggestion.voteCount || 0) + voteWeight;
        suggestion.upvotes = (suggestion.upvotes || 0) + voteWeight;

        // Atualizar informações de pagamento (marcar como pago)
        // Mantemos paymentAmount como fallback legado (maior valor visto)
        if (paymentAmount > (suggestion.paymentAmount || 0)) {
          suggestion.paymentAmount = paymentAmount;
        }
        suggestion.isPaid = true;
        suggestion.paymentId = paymentId;
        suggestion.paymentStatus = "paid";
      } else {
        // Criar nova sugestão
        const restaurant = await this.restaurantRepository.findOne({
          where: { id: restaurantId },
        });

        if (!restaurant) {
          return {
            success: false,
            message: "Restaurante não encontrado",
            voteWeight: 0,
          };
        }

        // Playlist protection: só permitir se a música estiver na playlist ativa
        const guard = await this.getActivePlaylistTrack(
          restaurantId,
          youtubeVideoId
        );
        if (!guard.track) {
          return {
            success: false,
            message: "Esta música não faz parte da playlist do restaurante",
            voteWeight: 0,
          };
        }

        // Preferir metadados do track da playlist
        const base = guard.track;

        suggestion = this.suggestionRepository.create({
          youtubeVideoId,
          title: base.title,
          artist: base.artist,
          channelName: "",
          duration: base.duration || 180,
          thumbnailUrl:
            base.thumbnailUrl ||
            `https://i.ytimg.com/vi/${youtubeVideoId}/mqdefault.jpg`,
          restaurant,
          status: SuggestionStatus.APPROVED,
          isPaid: true,
          voteCount: voteWeight,
          upvotes: voteWeight,
          downvotes: 0,
          paymentAmount,
          paymentId,
          paymentStatus: "paid",
          clientSessionId: clientSessionId || `session_${Date.now()}`,
          tableNumber,
        });
      }

      await this.suggestionRepository.save(suggestion);

      // 3. Registrar pagamento aprovado para fins de analytics/receita
      try {
        const paymentRepo = AppDataSource.getRepository(Payment);
        // Criar pagamento (em centavos)
        const amountInCents = Math.round((paymentAmount || 0) * 100);
        const sessionCandidate =
          clientSessionId || suggestion.clientSessionId || `session_${Date.now()}`;
        const sessionId = uuidValidate(sessionCandidate)
          ? sessionCandidate
          : uuidv4();

        // Se já existir um pagamento com o mesmo ID, não duplicar
        const existingPayment = await paymentRepo.findOne({
          where: { id: paymentId },
        });
    if (!existingPayment) {
          const payment = Payment.createPayment({
            id: paymentId,
            suggestionId: suggestion.id,
            sessionId,
            amount: amountInCents,
            metadata: {
              songTitle: suggestion.title,
              artist: suggestion.artist,
              paymentSource: "supervote",
      tableNumber,
      clientMessage,
      clientName,
            },
          });
          // Marcar como aprovado
          payment.updateStatus("approved", undefined, new Date());
          await paymentRepo.save(payment);
        }
      } catch (paymentErr) {
        console.warn(
          "⚠️ Falha ao registrar pagamento do supervoto:",
          paymentErr
        );
        // Não falhar o fluxo de voto por erro de registro de pagamento
      }

      console.log(
        `✅ Supervoto registrado: +${voteWeight} votos (R$ ${paymentAmount})`
      );

      // 🔔 Notificar supervoto em tempo real
      await this.notifySuperVote(
        restaurantId,
        suggestion,
        paymentAmount,
        voteWeight,
        tableNumber,
        clientMessage,
        clientName
      );

      return {
        success: true,
        message: `Supervoto de R$ ${paymentAmount.toFixed(
          2
        )} registrado! (+${voteWeight} votos)`,
        voteWeight,
      };
    } catch (error) {
      console.error("❌ Erro ao processar supervoto:", error);
      return {
        success: false,
        message: `Erro ao processar supervoto: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
        voteWeight: 0,
      };
    }
  }

  /**
   * 🧮 CALCULAR PESO DO SUPERVOTO
   */
  private calculateSuperVoteWeight(paymentAmount: number): number {
    // Pesos definidos: R$5=3 votos, R$20=8 votos, R$50=20 votos
    if (paymentAmount >= 50) return 20; // R$ 50+ = 20 votos
    if (paymentAmount >= 20) return 8; // R$ 20+ = 8 votos
    if (paymentAmount >= 5) return 3; // R$ 5+  = 3 votos
    return 1; // Voto normal
  }

  /**
   * 🔄 ATUALIZAR METADADOS DE SUGESTÕES COM FALLBACK
   * Tenta recuperar metadados de vídeos que foram criados com dados mínimos
   */
  async updateFallbackMetadata(restaurantId: string): Promise<{
    success: boolean;
    updated: number;
    message: string;
  }> {
    try {
      console.log(`🔄 Atualizando metadados de fallback para ${restaurantId}`);

      const fallbackSuggestions = await this.suggestionRepository
        .createQueryBuilder("suggestion")
        .where("suggestion.restaurant_id = :restaurantId", { restaurantId })
        .andWhere("suggestion.status = :status", {
          status: SuggestionStatus.APPROVED,
        })
        .andWhere("suggestion.description ILIKE :desc", {
          desc: "%dados mínimos%",
        })
        .take(10)
        .getMany();

      if (fallbackSuggestions.length === 0) {
        return {
          success: true,
          updated: 0,
          message: "Nenhuma sugestão com fallback encontrada",
        };
      }

      let updatedCount = 0;
      for (const suggestion of fallbackSuggestions) {
        try {
          const videoData = await this.youtubeService.getVideoInfo(
            suggestion.youtubeVideoId
          );
          if (videoData) {
            suggestion.title = videoData.title;
            suggestion.artist = videoData.artist;
            suggestion.channelName = videoData.channelName;
            suggestion.duration = videoData.duration;
            suggestion.thumbnailUrl = videoData.thumbnailUrl;
            suggestion.description = videoData.description || "";
            await this.suggestionRepository.save(suggestion);

            await this.notifyMetadataUpdate(restaurantId, suggestion);
            console.log(`✅ Metadados atualizados: ${suggestion.youtubeVideoId}`);
            updatedCount++;
          }
        } catch (error) {
          console.warn(`⚠️ Falha ao atualizar ${suggestion.youtubeVideoId}:`, error);
        }
      }

      return {
        success: true,
        updated: updatedCount,
        message: `${updatedCount} sugestões atualizadas com metadados completos`,
      };
    } catch (error) {
      console.error("❌ Erro ao atualizar metadados de fallback:", error);
      return {
        success: false,
        updated: 0,
        message: `Erro: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
      };
    }
  }

  /**
   * 🔎 OBTER PLAYLIST ATIVA DO RESTAURANTE
   */
  async getActivePlaylistForRestaurant(restaurantId: string): Promise<Playlist | null> {
    const playlist = await this.playlistRepository.findOne({
      where: {
        restaurant: { id: restaurantId },
        status: PlaylistStatus.ACTIVE,
      },
      order: { isDefault: "DESC", createdAt: "DESC" },
    });
    return playlist || null;
  }

  /**
   * 🔔 NOTIFICAR SUPERVOTO EM TEMPO REAL
   */
  private async notifySuperVote(
    restaurantId: string,
    suggestion: Suggestion,
    paymentAmount: number,
    voteWeight: number,
    tableNumber?: number,
    clientMessage?: string,
    clientName?: string
  ): Promise<void> {
    try {
      const notificationData = {
        type: "supervote",
        suggestion: {
          id: suggestion.id,
          title: suggestion.title,
          artist: suggestion.artist,
          youtubeVideoId: suggestion.youtubeVideoId,
          thumbnailUrl: suggestion.thumbnailUrl,
          duration: suggestion.duration,
        },
        payment: {
          amount: paymentAmount,
          voteWeight,
          tableNumber,
          message: clientMessage,
          clientName,
        },
        timestamp: new Date().toISOString(),
      };

      // Notificar todos os clientes do restaurante
      await this.webSocketService.emitToRestaurant(
        restaurantId,
        "superVoteReceived",
        notificationData
      );

      // Notificar administradores com dados detalhados
      const adminData = {
        ...notificationData,
        adminDetails: {
          paymentId: suggestion.paymentId,
          clientSessionId: suggestion.clientSessionId,
          paymentStatus: suggestion.paymentStatus,
        },
      };

      await this.webSocketService.emitToAdmins(
        restaurantId,
        "superVoteAdmin",
        adminData
      );

      console.log(
        `🔔 Notificação de supervoto enviada: R$ ${paymentAmount} - ${suggestion.title}`
      );
    } catch (error) {
      console.error("❌ Erro ao notificar supervoto:", error);
      // Não falhar o processo principal por erro de notificação
    }
  }

  /**
   * 🔔 NOTIFICAR FALHA DA YOUTUBE API
   */
  private async notifyYouTubeAPIFailure(
    restaurantId: string,
    youtubeVideoId: string
  ): Promise<void> {
    try {
      const notificationData = {
        type: "youtube_api_failure",
        youtubeVideoId,
        message: "YouTube API indisponível - usando dados de fallback",
        timestamp: new Date().toISOString(),
        severity: "warning",
      };

      // Notificar apenas administradores
      await this.webSocketService.emitToAdmins(
        restaurantId,
        "youtubeAPIFailure",
        notificationData
      );

      console.log(`🔔 Notificação de falha da API enviada: ${youtubeVideoId}`);
    } catch (error) {
      console.error("❌ Erro ao notificar falha da API:", error);
      // Não falhar o processo principal por erro de notificação
    }
  }

  /**
   * 🔔 NOTIFICAR ATUALIZAÇÃO DE METADADOS
   */
  private async notifyMetadataUpdate(
    restaurantId: string,
    suggestion: Suggestion
  ): Promise<void> {
    try {
      const notificationData = {
        type: "metadata_updated",
        suggestion: {
          id: suggestion.id,
          title: suggestion.title,
          artist: suggestion.artist,
          youtubeVideoId: suggestion.youtubeVideoId,
          thumbnailUrl: suggestion.thumbnailUrl,
        },
        message: "Metadados atualizados com sucesso",
        timestamp: new Date().toISOString(),
      };

      // Notificar todos os clientes
      await this.webSocketService.emitToRestaurant(
        restaurantId,
        "metadataUpdated",
        notificationData
      );

      console.log(`🔔 Notificação de atualização enviada: ${suggestion.title}`);
    } catch (error) {
      console.error("❌ Erro ao notificar atualização:", error);
      // Não falhar o processo principal por erro de notificação
    }
  }

  /**
   * 🔄 MARCAR MÚSICA EM COOLDOWN (10 MINUTOS)
   */
  async markSongInCooldown(
    restaurantId: string,
    youtubeVideoId: string,
    cooldownMinutes: number = 10
  ): Promise<void> {
    try {
      const { redisClient } = await import('../config/redis');
      const key = `song:cooldown:${restaurantId}:${youtubeVideoId}`;
      const client = redisClient.getClient();

      // Set cooldown com TTL em segundos
      const ttlSeconds = cooldownMinutes * 60;
      await client.setEx(key, ttlSeconds, '1');

      if (process.env.NODE_ENV !== 'test') {
        console.log(`⏰ Música ${youtubeVideoId} em cooldown por ${cooldownMinutes} minutos`);
      }
    } catch (error) {
      console.error('❌ Erro ao marcar música em cooldown:', error);
      // Não bloquear funcionalidade se Redis falhar
    }
  }

  /**
   * 🔍 VERIFICAR SE MÚSICA ESTÁ EM COOLDOWN
   */
  async isSongInCooldown(
    restaurantId: string,
    youtubeVideoId: string
  ): Promise<boolean> {
    try {
      const { redisClient } = await import('../config/redis');
      const key = `song:cooldown:${restaurantId}:${youtubeVideoId}`;
      const client = redisClient.getClient();

      const exists = await client.exists(key);
      return exists === 1;
    } catch (error) {
      console.error('❌ Erro ao verificar cooldown:', error);
      // Se Redis falhar, assumir que não está em cooldown
      return false;
    }
  }

  /**
   * 📊 OBTER RANKING DE VOTAÇÃO (COM FILTRO DE COOLDOWN)
   */
  async getVotingRanking(
    restaurantId: string,
    limit: number = 20
  ): Promise<{
    success: boolean;
    data?: Array<{
      youtubeVideoId: string;
      title?: string;
      artist?: string;
      voteCount: number;
      superVoteCount: number;
      normalVoteCount: number;
      totalRevenue: number;
      isPaid: boolean;
      paymentAmount: number;
      tableNumber?: number;
    }>;
    message: string;
  }> {
    try {
      // Evitar consultas/logs após teardown de testes
  const ds: any = AppDataSource as any;
  if (!ds || !ds.isInitialized || typeof ds.getRepository !== 'function') {
        return { success: true, data: [], message: 'DataSource não disponível' };
      }

      if (process.env.NODE_ENV !== 'test') {
        console.log(`📊 Obtendo ranking de votação para ${restaurantId}`);
      }

      const suggestions = await this.suggestionRepository
        .createQueryBuilder("suggestion")
        .where("suggestion.restaurant_id = :restaurantId", { restaurantId })
        .andWhere("suggestion.status = :status", {
          status: SuggestionStatus.APPROVED,
        })
  .orderBy("suggestion.is_paid", "DESC") // Supervotos primeiro
  .addOrderBy("suggestion.payment_amount", "DESC") // Maior valor primeiro
  .addOrderBy("suggestion.voteCount", "DESC") // TypeORM mapeia para vote_count
        .limit(limit)
        .getMany();

      // Pré-agregar receita por sugestão a partir da tabela de pagamentos aprovados
      const suggestionIds = suggestions.map((s) => s.id);
  const paymentRepo = AppDataSource.getRepository(Payment);

      let sumsBySuggestion: Record<string, number> = {};
      if (suggestionIds.length > 0) {
        const rawSums = await paymentRepo
          .createQueryBuilder("payment")
          .select("payment.suggestionId", "suggestionId")
          .addSelect("SUM(payment.amount)", "totalAmount")
          .where("payment.status = :status", { status: "approved" })
          .andWhere("payment.suggestionId IN (:...ids)", { ids: suggestionIds })
          .groupBy("payment.suggestionId")
          .getRawMany();

        sumsBySuggestion = rawSums.reduce((acc, row: any) => {
          acc[row.suggestionId] = Number(row.totalAmount || 0) / 100; // para reais
          return acc;
        }, {} as Record<string, number>);
      }

      const ranking = suggestions.map((suggestion) => {
        const superVoteWeight = suggestion.isPaid
          ? this.calculateSuperVoteWeight(suggestion.paymentAmount || 0)
          : 0;

        const normalVoteCount = Math.max(
          0,
          (suggestion.voteCount || 0) - superVoteWeight
        );

        const legacyAmount = suggestion.paymentAmount || 0;
        const paymentsSum =
          typeof sumsBySuggestion[suggestion.id] === "number"
            ? sumsBySuggestion[suggestion.id]
            : 0;

        return {
          youtubeVideoId: suggestion.youtubeVideoId,
          title: suggestion.title,
          artist: suggestion.artist,
          voteCount: suggestion.voteCount || 0,
          superVoteCount: suggestion.isPaid ? 1 : 0,
          normalVoteCount,
          // totalRevenue: somar pagamentos aprovados + valor legado (transição)
          totalRevenue: paymentsSum + legacyAmount,
          isPaid: suggestion.isPaid || false,
          paymentAmount: suggestion.paymentAmount || 0,
          tableNumber: suggestion.tableNumber,
        };
      });

      // 🔄 FILTRAR MÚSICAS EM COOLDOWN
      const filteredRanking = [];
      for (const item of ranking) {
        const isInCooldown = await this.isSongInCooldown(restaurantId, item.youtubeVideoId);
        if (!isInCooldown) {
          filteredRanking.push(item);
        } else if (process.env.NODE_ENV !== 'test') {
          console.log(`⏰ Música ${item.title} (${item.youtubeVideoId}) filtrada por cooldown`);
        }
      }

      // ✅ Evitar reaplicar o mesmo snapshot de ranking em loop
      try {
        const sig = this.computeRankingSignature(filteredRanking);
        const { redisClient } = await import('../config/redis');
        const client = redisClient.getClient();
        const key = `ranking:consumed:${restaurantId}:${sig}`;
        const consumed = await client.exists(key);
        if (consumed === 1) {
          if (process.env.NODE_ENV !== 'test') {
            console.log(`⏭️  Ranking consumido detectado (${restaurantId}) sig=${sig} — retornando lista vazia`);
          }
          return {
            success: true,
            data: [],
            message: 'Ranking consumido recentemente — aguardando novos votos',
          };
        }
      } catch {}

      return {
        success: true,
        data: filteredRanking,
        message: `${filteredRanking.length} músicas no ranking (${ranking.length - filteredRanking.length} em cooldown)`,
      };
    } catch (error) {
      console.error("❌ Erro ao obter ranking:", error);
      return {
        success: false,
        message: `Erro ao obter ranking: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
      };
    }
  }

  /**
   * 🔄 REORDENAR PLAYLIST BASEADA EM VOTOS
   */
  async reorderPlaylistByVotes(
    restaurantId: string
  ): Promise<{ success: boolean; message: string; reorderedCount?: number }> {
    try {
      console.log(`🔄 Reordenando playlist por votos: ${restaurantId}`);

      // 1. Obter ranking atual
      const ranking = await this.getVotingRanking(restaurantId, 50);

      if (!ranking.success || !ranking.data) {
        return { success: false, message: "Erro ao obter ranking de votos" };
      }

      // 2. Buscar playlist ativa
      const playlist = await this.playlistRepository.findOne({
        where: {
          restaurant: { id: restaurantId },
          status: PlaylistStatus.ACTIVE,
        },
        order: {
          isDefault: "DESC",
          createdAt: "DESC",
        },
      });

      if (!playlist || !playlist.youtubePlaylistId) {
        return { success: false, message: "Playlist ativa não encontrada" };
      }

      // 3. Obter itens atuais da playlist do YouTube
      const playlistItems = await this.youtube.playlistItems.list({
        part: ["snippet"],
        playlistId: playlist.youtubePlaylistId,
        maxResults: 50,
      });

      const items = playlistItems.data.items || [];
      let reorderedCount = 0;

      // 4. Reordenar baseado no ranking de votos
      for (let i = 0; i < ranking.data.length && i < items.length; i++) {
        const rankedVideo = ranking.data[i];

        // Encontrar item correspondente na playlist
        const playlistItem = items.find(
          (item) =>
            item.snippet?.resourceId?.videoId === rankedVideo.youtubeVideoId
        );

        if (playlistItem && playlistItem.id) {
          try {
            await this.youtube.playlistItems.update({
              part: ["snippet"],
              requestBody: {
                id: playlistItem.id,
                snippet: {
                  ...playlistItem.snippet,
                  position: i, // Nova posição baseada no ranking
                },
              },
            });

            reorderedCount++;
            console.log(
              `✅ Movido para posição ${i}: ${rankedVideo.title} (${rankedVideo.voteCount} votos)`
            );
          } catch (error) {
            console.warn(
              `⚠️ Erro ao mover ${rankedVideo.youtubeVideoId}:`,
              error
            );
          }
        }
      }

      // ✅ Marcar ranking aplicado como consumido por alguns minutos para evitar reaplicação
      try {
        await this.markRankingAsConsumed(
          restaurantId,
          ranking.data.map((d) => ({
            youtubeVideoId: d.youtubeVideoId,
            voteCount: d.voteCount,
            paymentAmount: d.paymentAmount,
            isPaid: d.isPaid,
          })),
          5 * 60
        );
      } catch (e) {
        console.warn('Falha ao marcar ranking como consumido (manual reorder)', e);
      }

      return {
        success: true,
        message: `Playlist reordenada com sucesso! ${reorderedCount} músicas movidas.`,
        reorderedCount,
      };
    } catch (error) {
      console.error("❌ Erro ao reordenar playlist:", error);
      return {
        success: false,
        message: `Erro ao reordenar playlist: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
      };
    }
  }

  /**
   * Gera uma assinatura estável do ranking atual para detectar reaplicações.
   * Usa ordem + videoId + votos e pagamento para construir um hash simples.
   */
  private computeRankingSignature(items: Array<{
    youtubeVideoId: string;
    voteCount?: number;
    paymentAmount?: number;
    isPaid?: boolean;
  }>): string {
    try {
      const base = items
        .map((it) => {
          const vid = String(it.youtubeVideoId || "");
          const vc = Number(it.voteCount ?? 0);
          const pa = Number(it.paymentAmount ?? 0);
          const ip = it.isPaid ? 1 : 0;
          return `${vid}#${vc}#${pa}#${ip}`;
        })
        .join("|");
      // Hash simples (djb2) para reduzir tamanho da chave
      let hash = 5381;
      for (let i = 0; i < base.length; i++) {
        hash = ((hash << 5) + hash) + base.charCodeAt(i);
        hash = hash >>> 0;
      }
      return `v1:${hash.toString(16)}`;
    } catch {
      // fallback: JSON string (pode ser longo, mas evita quebra)
      return `v1:${Buffer.from(JSON.stringify(items)).toString('base64')}`;
    }
  }

  /**
   * Marca o ranking atual (snapshot) como consumido por um período (TTL),
   * evitando que seja reaplicado até que mudanças ocorram (nova assinatura).
   */
  async markRankingAsConsumed(
    restaurantId: string,
    items: Array<{ youtubeVideoId: string; voteCount?: number; paymentAmount?: number; isPaid?: boolean }>,
    ttlSeconds: number = 300 // 5 minutos por padrão
  ): Promise<void> {
    try {
      const { redisClient } = await import('../config/redis');
      const client = redisClient.getClient();
      const sig = this.computeRankingSignature(items);
      const key = `ranking:consumed:${restaurantId}:${sig}`;
      await client.setEx(key, ttlSeconds, '1');
      if (process.env.NODE_ENV !== 'test') {
        console.log(`✅ Ranking marcado como consumido (${restaurantId}) sig=${sig} ttl=${ttlSeconds}s`);
      }
    } catch (err) {
      console.warn('Não foi possível marcar ranking como consumido (Redis indisponível?)', err);
    }
  }

  /**
   * 📊 OBTER ESTATÍSTICAS DA PLAYLIST COLABORATIVA
   */
  async getPlaylistStats(restaurantId: string): Promise<{
    success: boolean;
    data?: {
      playlistName: string;
      youtubePlaylistId: string;
      totalTracks: number;
      paidSuggestions: number;
      freeSuggestions: number;
      totalRevenue: number;
      totalVotes: number;
      activeTables: number;
    };
    message: string;
  }> {
    try {
      const playlist = await this.playlistRepository.findOne({
        where: {
          restaurant: { id: restaurantId },
          status: PlaylistStatus.ACTIVE,
        },
        order: {
          isDefault: "DESC",
          createdAt: "DESC",
        },
      });

      if (!playlist) {
        return {
          success: false,
          message: "Playlist não encontrada",
        };
      }

      // Buscar todas as sugestões aprovadas
      const suggestions = await this.suggestionRepository
        .createQueryBuilder("suggestion")
        .where("suggestion.restaurant_id = :restaurantId", { restaurantId })
        .andWhere("suggestion.status = :status", {
          status: SuggestionStatus.APPROVED,
        })
        .getMany();

      // Calcular estatísticas
      const paidSuggestions = suggestions.filter((s) => s.isPaid);
      const freeSuggestions = suggestions.filter((s) => !s.isPaid);
      const totalVotes = suggestions.reduce(
        (sum, s) => sum + (s.voteCount || 0),
        0
      );
      const totalRevenue = suggestions.reduce(
        (sum, s) => sum + Number(s.paymentAmount || 0),
        0
      );

      const activeTables = new Set(
        suggestions.map((s) => s.tableNumber).filter(Boolean)
      ).size;

      return {
        success: true,
        data: {
          playlistName: playlist.name,
          youtubePlaylistId: playlist.youtubePlaylistId || "",
          totalTracks: playlist.trackCount || 0,
          paidSuggestions: paidSuggestions.length,
          freeSuggestions: freeSuggestions.length,
          totalRevenue,
          totalVotes,
          activeTables,
        },
        message: "Estatísticas obtidas com sucesso",
      };
    } catch (error) {
      console.error("❌ Erro ao obter estatísticas:", error);
      return {
        success: false,
        message: `Erro ao obter estatísticas: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
      };
    }
  }
}

// Exportar instância singleton
export const collaborativePlaylistService = new CollaborativePlaylistService();
