#!/usr/bin/env ts-node

/**
 * 🚀 Script de Validação de Configuração de Produção
 * 
 * Este script verifica se todas as configurações necessárias
 * estão presentes para executar o sistema em produção.
 */

import dotenv from 'dotenv';
import { logger } from '../utils/logger';

// Carregar variáveis de ambiente
dotenv.config();

interface ConfigValidation {
  key: string;
  required: boolean;
  description: string;
  validator?: (value: string) => boolean;
  defaultValue?: string;
}

const REQUIRED_CONFIGS: ConfigValidation[] = [
  // Ambiente
  {
    key: 'NODE_ENV',
    required: true,
    description: 'Ambiente de execução',
    validator: (value) => value === 'production',
  },
  {
    key: 'PORT',
    required: true,
    description: 'Porta do servidor',
    validator: (value) => !isNaN(Number(value)) && Number(value) > 0,
    defaultValue: '8001',
  },
  {
    key: 'BACKEND_URL',
    required: true,
    description: 'URL do backend',
    validator: (value) => value.startsWith('https://'),
  },

  // Banco de Dados
  {
    key: 'DATABASE_HOST',
    required: true,
    description: 'Host do PostgreSQL',
  },
  {
    key: 'DATABASE_PORT',
    required: true,
    description: 'Porta do PostgreSQL',
    validator: (value) => !isNaN(Number(value)),
    defaultValue: '5432',
  },
  {
    key: 'DATABASE_USERNAME',
    required: true,
    description: 'Usuário do PostgreSQL',
  },
  {
    key: 'DATABASE_PASSWORD',
    required: true,
    description: 'Senha do PostgreSQL',
    validator: (value) => value.length >= 8,
  },
  {
    key: 'DATABASE_NAME',
    required: true,
    description: 'Nome do banco de dados',
  },

  // Redis
  {
    key: 'REDIS_HOST',
    required: true,
    description: 'Host do Redis',
  },
  {
    key: 'REDIS_PORT',
    required: true,
    description: 'Porta do Redis',
    validator: (value) => !isNaN(Number(value)),
    defaultValue: '6379',
  },

  // Mercado Pago - CRÍTICO
  {
    key: 'MERCADO_PAGO_ACCESS_TOKEN',
    required: true,
    description: 'Token de acesso do Mercado Pago',
    validator: (value) => value.startsWith('APP_USR-') && value !== 'TEST-YOUR-ACCESS-TOKEN',
  },
  {
    key: 'MERCADO_PAGO_PUBLIC_KEY',
    required: true,
    description: 'Chave pública do Mercado Pago',
    validator: (value) => value.startsWith('APP_USR-') && value !== 'TEST-YOUR-PUBLIC-KEY',
  },
  {
    key: 'MERCADO_PAGO_WEBHOOK_SECRET',
    required: true,
    description: 'Secret do webhook do Mercado Pago',
    validator: (value) => value.length >= 16 && value !== 'webhook_secret',
  },

  // APIs Externas
  {
    key: 'YOUTUBE_API_KEY',
    required: true,
    description: 'Chave da API do YouTube',
    validator: (value) => value.length >= 30,
  },

  // Segurança
  {
    key: 'JWT_SECRET',
    required: true,
    description: 'Chave secreta JWT',
    validator: (value) => value.length >= 32,
  },
  {
    key: 'CORS_ORIGIN',
    required: true,
    description: 'Origem permitida para CORS',
    validator: (value) => value.startsWith('https://'),
  },
];

const OPTIONAL_CONFIGS: ConfigValidation[] = [
  {
    key: 'MUSIXMATCH_API_KEY',
    required: false,
    description: 'Chave da API Musixmatch (letras)',
  },
  {
    key: 'LIRIKS_API_KEY',
    required: false,
    description: 'Chave da API Liriks (letras)',
  },
  {
    key: 'SENTRY_DSN',
    required: false,
    description: 'DSN do Sentry (monitoramento)',
  },
  {
    key: 'SMTP_HOST',
    required: false,
    description: 'Host SMTP para emails',
  },
];

class ProductionConfigValidator {
  private errors: string[] = [];
  private warnings: string[] = [];
  private validConfigs: string[] = [];

  async validate(): Promise<boolean> {
    logger.info('🚀 Iniciando validação de configuração de produção...');

    // Validar configurações obrigatórias
    for (const config of REQUIRED_CONFIGS) {
      this.validateConfig(config, true);
    }

    // Validar configurações opcionais
    for (const config of OPTIONAL_CONFIGS) {
      this.validateConfig(config, false);
    }

    // Validações especiais
    this.validateSpecialCases();

    // Exibir resultados
    this.displayResults();

    return this.errors.length === 0;
  }

  private validateConfig(config: ConfigValidation, isRequired: boolean): void {
    const value = process.env[config.key];

    if (!value) {
      if (isRequired) {
        if (config.defaultValue) {
          this.warnings.push(
            `⚠️  ${config.key}: Usando valor padrão '${config.defaultValue}' - ${config.description}`
          );
          process.env[config.key] = config.defaultValue;
          return;
        }
        this.errors.push(
          `❌ ${config.key}: OBRIGATÓRIO - ${config.description}`
        );
      } else {
        this.warnings.push(
          `⚠️  ${config.key}: Opcional não configurado - ${config.description}`
        );
      }
      return;
    }

    // Validar valor se houver validator
    if (config.validator && !config.validator(value)) {
      this.errors.push(
        `❌ ${config.key}: Valor inválido - ${config.description}`
      );
      return;
    }

    this.validConfigs.push(
      `✅ ${config.key}: Configurado corretamente - ${config.description}`
    );
  }

  private validateSpecialCases(): void {
    // Verificar se pelo menos uma API de letras está configurada
    const hasMusixmatch = !!process.env.MUSIXMATCH_API_KEY;
    const hasLiriks = !!process.env.LIRIKS_API_KEY;

    if (!hasMusixmatch && !hasLiriks) {
      this.warnings.push(
        '⚠️  Nenhuma API de letras configurada - Karaokê usará apenas letras de teste'
      );
    }

    // Verificar configurações de email
    const hasSmtp = !!(
      process.env.SMTP_HOST &&
      process.env.SMTP_USER &&
      process.env.SMTP_PASS
    );

    if (!hasSmtp) {
      this.warnings.push(
        '⚠️  SMTP não configurado - Notificações por email desabilitadas'
      );
    }

    // Verificar configurações de monitoramento
    if (!process.env.SENTRY_DSN) {
      this.warnings.push(
        '⚠️  Sentry não configurado - Monitoramento de erros limitado'
      );
    }

    // Verificar configurações de backup
    if (!process.env.BACKUP_S3_BUCKET) {
      this.warnings.push(
        '⚠️  Backup S3 não configurado - Backups automáticos desabilitados'
      );
    }
  }

  private displayResults(): void {
    console.log('\n' + '='.repeat(80));
    console.log('🚀 RELATÓRIO DE VALIDAÇÃO DE CONFIGURAÇÃO DE PRODUÇÃO');
    console.log('='.repeat(80));

    if (this.validConfigs.length > 0) {
      console.log('\n✅ CONFIGURAÇÕES VÁLIDAS:');
      this.validConfigs.forEach(config => console.log(`  ${config}`));
    }

    if (this.warnings.length > 0) {
      console.log('\n⚠️  AVISOS:');
      this.warnings.forEach(warning => console.log(`  ${warning}`));
    }

    if (this.errors.length > 0) {
      console.log('\n❌ ERROS CRÍTICOS:');
      this.errors.forEach(error => console.log(`  ${error}`));
      console.log('\n🚨 AÇÃO NECESSÁRIA: Corrija os erros acima antes de executar em produção!');
    } else {
      console.log('\n🎉 CONFIGURAÇÃO VÁLIDA PARA PRODUÇÃO!');
      console.log('✅ Todas as configurações obrigatórias estão presentes e válidas.');
    }

    console.log('\n📋 RESUMO:');
    console.log(`  ✅ Configurações válidas: ${this.validConfigs.length}`);
    console.log(`  ⚠️  Avisos: ${this.warnings.length}`);
    console.log(`  ❌ Erros: ${this.errors.length}`);
    console.log('='.repeat(80) + '\n');
  }
}

// Executar validação se chamado diretamente
if (require.main === module) {
  const validator = new ProductionConfigValidator();
  validator.validate().then(isValid => {
    process.exit(isValid ? 0 : 1);
  }).catch(error => {
    logger.error('Erro durante validação:', error);
    process.exit(1);
  });
}

export { ProductionConfigValidator };
