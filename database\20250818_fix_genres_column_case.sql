-- Ensure consistent casing for genre column references in suggestions (if exists)
-- Some environments may have "Genre" camel case; normalize to lowercase column 'genre'

-- Add genre column if missing
ALTER TABLE IF EXISTS suggestions
	ADD COLUMN IF NOT EXISTS genre VARCHAR NULL;

-- If there is a mis-cased column, attempt to rename safely
DO $$
BEGIN
	IF EXISTS (
		SELECT 1 FROM information_schema.columns
		WHERE table_name='suggestions' AND column_name='Genre'
	) THEN
		ALTER TABLE suggestions RENAME COLUMN "Genre" TO genre;
	END IF;
END $$;

