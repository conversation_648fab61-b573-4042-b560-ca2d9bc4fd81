import axios from "axios";
import {
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  IsEmail,
  validate,
} from "class-validator";
import { plainToClass } from "class-transformer";
import { AppDataSource } from "../config/database";
import { Payment } from "../models/Payment";
import { Suggestion, SuggestionStatus } from "../models/Suggestion";
import { ClientSession } from "../models/ClientSession";
import {
  notificationService,
  NotificationType,
  NotificationPriority,
} from "./NotificationService";
import { logger } from "../utils/logger";
import QRCode from "qrcode";
import { v4 as uuidv4, validate as uuidValidate } from "uuid";

/**
 * Interfaces tipadas para o PaymentService
 */
export interface IPixPaymentData {
  amount: number; // Valor em centavos (200 = R$ 2,00)
  description: string;
  external_reference: string; // ID da sugestão
  payer: {
    email?: string;
    first_name?: string;
    last_name?: string;
  };
}

export interface IPaymentResponse {
  paymentId: string;
  qrCode: string;
  qrCodeBase64: string;
  ticketUrl: string;
  amount: number;
}

export interface IMercadoPagoPayment {
  id: string;
  status: string;
  status_detail: string;
  transaction_amount: number;
  description: string;
  external_reference: string;
  point_of_interaction?: {
    transaction_data?: {
      qr_code?: string;
      qr_code_base64?: string;
      ticket_url?: string;
    };
  };
  payer?: {
    email?: string;
    first_name?: string;
    last_name?: string;
  };
}

export interface IPaymentStats {
  totalRevenue: number;
  totalPayments: number;
  successfulPayments: number;
  failedPayments: number;
  averageAmount: number;
  period: string;
  restaurantId?: string;
  dailyRevenue?: Array<{ date: string; revenue: number; count: number }>;
}

export interface IWebhookData {
  id: string;
  live_mode: boolean;
  type: string;
  date_created: string;
  application_id: string;
  user_id: string;
  version: string;
  api_version: string;
  action: string;
  data: {
    id: string;
  };
}

/**
 * Classes de validação para entrada de dados
 */
export class CreatePaymentDto {
  @IsString()
  suggestionId: string;

  @IsString()
  sessionId: string;
}

export class PaymentWebhookDto {
  @IsString()
  id: string;

  @IsString()
  type: string;

  @IsString()
  action: string;

  data: {
    id: string;
  };
}

export class PaymentStatsDto {
  @IsOptional()
  @IsString()
  restaurantId?: string;

  @IsOptional()
  @IsString()
  period?: string = "7d";
}

/**
 * Classe de erro personalizada para pagamentos
 */
export class PaymentError extends Error {
  public readonly code: string;
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly paymentId?: string;

  constructor(
    message: string,
    code: string = "PAYMENT_ERROR",
    statusCode: number = 500,
    isOperational: boolean = true,
    paymentId?: string
  ) {
    super(message);
    this.name = "PaymentError";
    this.code = code;
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.paymentId = paymentId;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Serviço de pagamento PIX integrado com Mercado Pago
 *
 * @class PaymentService
 * @description Gerencia pagamentos PIX para sugestões musicais prioritárias,
 * incluindo criação de pagamentos, processamento de webhooks e estatísticas.
 */
class PaymentService {
  private paymentRepository = AppDataSource.getRepository(Payment);
  private suggestionRepository = AppDataSource.getRepository(Suggestion);
  private sessionRepository = AppDataSource.getRepository(ClientSession);

  // Configurações do Mercado Pago
  private readonly mercadoPagoToken: string;
  private readonly mercadoPagoPublicKey: string;
  private readonly webhookSecret: string;
  private readonly environment: string;
  private readonly baseURL: string;

  constructor() {
    this.mercadoPagoToken =
      process.env.MERCADO_PAGO_ACCESS_TOKEN || "TEST-YOUR-ACCESS-TOKEN";
    this.mercadoPagoPublicKey =
      process.env.MERCADO_PAGO_PUBLIC_KEY || "TEST-YOUR-PUBLIC-KEY";
    this.webhookSecret =
      process.env.MERCADO_PAGO_WEBHOOK_SECRET || "webhook_secret";
    this.environment = process.env.NODE_ENV || "development";
    this.baseURL = "https://api.mercadopago.com/v1";

    this.validateConfiguration();
  }

  /**
   * Valida a configuração do serviço
   * @private
   */
  private validateConfiguration(): void {
    // Em produção, exigir configurações válidas
    if (this.environment === "production") {
      if (
        !this.mercadoPagoToken ||
        this.mercadoPagoToken === "TEST-YOUR-ACCESS-TOKEN"
      ) {
        throw new PaymentError(
          "Token do Mercado Pago é obrigatório em produção",
          "MISSING_MP_TOKEN",
          500
        );
      }

      if (
        !this.mercadoPagoPublicKey ||
        this.mercadoPagoPublicKey === "TEST-YOUR-PUBLIC-KEY"
      ) {
        throw new PaymentError(
          "Chave pública do Mercado Pago é obrigatória em produção",
          "MISSING_MP_PUBLIC_KEY",
          500
        );
      }

      if (!this.webhookSecret || this.webhookSecret === "webhook_secret") {
        throw new PaymentError(
          "Webhook secret do Mercado Pago é obrigatório em produção",
          "MISSING_WEBHOOK_SECRET",
          500
        );
      }

      logger.info("PaymentService configurado para PRODUÇÃO", {
        environment: this.environment,
        tokenConfigured: true,
        publicKeyConfigured: true,
        webhookConfigured: true,
      });
    } else {
      // Em desenvolvimento, apenas avisar
      if (
        !this.mercadoPagoToken ||
        this.mercadoPagoToken === "TEST-YOUR-ACCESS-TOKEN"
      ) {
        logger.warn(
          "Token do Mercado Pago não configurado - usando modo de teste"
        );
      }

      if (
        !this.mercadoPagoPublicKey ||
        this.mercadoPagoPublicKey === "TEST-YOUR-PUBLIC-KEY"
      ) {
        logger.warn("Chave pública do Mercado Pago não configurada");
      }

      logger.info("PaymentService inicializado em DESENVOLVIMENTO", {
        environment: this.environment,
        hasValidToken: this.mercadoPagoToken !== "TEST-YOUR-ACCESS-TOKEN",
        hasValidPublicKey: this.mercadoPagoPublicKey !== "TEST-YOUR-PUBLIC-KEY",
      });
    }
  }

  /**
   * Gera QR Code PIX com configurações otimizadas
   * @private
   * @param pixCode - Código PIX para gerar QR Code
   * @returns Promise<string> - QR Code em formato Data URL
   */
  private async generatePixQRCode(pixCode: string): Promise<string> {
    try {
      if (!pixCode) {
        throw new PaymentError("Código PIX inválido", "INVALID_PIX_CODE", 400);
      }

      const qrCodeDataURL = await QRCode.toDataURL(pixCode, {
        errorCorrectionLevel: "M",
        type: "image/png",
        margin: 2,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
        width: 256,
      });

      logger.info("QR Code PIX gerado com sucesso");
      return qrCodeDataURL;
    } catch (error) {
      logger.error("Erro ao gerar QR Code PIX:", error);

      // Fallback para um QR code simples
      const fallbackQR =
        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";

      throw new PaymentError(
        "Falha ao gerar QR Code PIX",
        "QR_CODE_GENERATION_FAILED",
        500
      );
    }
  }

  /**
   * Valida dados de entrada usando class-validator
   * @private
   */
  private async validateInput<T extends object>(
    dto: new () => T,
    data: any
  ): Promise<T> {
    const instance = plainToClass(dto, data);
    const errors = await validate(instance);

    if (errors.length > 0) {
      const errorMessages = errors
        .map((error) => Object.values(error.constraints || {}).join(", "))
        .join("; ");

      throw new PaymentError(
        `Dados inválidos: ${errorMessages}`,
        "VALIDATION_ERROR",
        400
      );
    }

    return instance;
  }

  /**
   * Cria um pagamento PIX para uma sugestão musical
   * @param suggestionId - ID da sugestão musical
   * @param sessionId - ID da sessão do cliente
   * @returns Promise<IPaymentResponse> - Dados do pagamento criado
   * @throws PaymentError - Quando há erro na criação do pagamento
   */
  async createPixPayment(
    suggestionId: string,
    sessionId: string,
    amountCents?: number
  ): Promise<IPaymentResponse> {
    try {
      // Validar entrada
      await this.validateInput(CreatePaymentDto, { suggestionId, sessionId });

      logger.info("Iniciando criação de pagamento PIX", {
        suggestionId,
        sessionId,
      });

      // Buscar sugestão com validação
      const suggestion = await this.findSuggestionById(suggestionId);

      // Normalizar sessionId para UUID (payments.session_id é uuid no schema)
      const normalizedSessionId = uuidValidate(sessionId)
        ? sessionId
        : uuidv4();

      // Verificar se já não foi paga
      await this.checkExistingPayment(suggestionId);

      // Buscar sessão do cliente (opcional)
      const session = await this.sessionRepository.findOne({
        where: { id: sessionId },
      });

      // Determinar valor do pagamento (padrão R$ 5,00 se não especificado)
      const finalAmountCents = amountCents || 500; // R$ 5,00 como padrão

      // Validar valores permitidos (R$ 5,00, R$ 20,00, R$ 50,00)
      const allowedAmounts = [500, 2000, 5000]; // R$ 5,00, R$ 20,00, R$ 50,00
      if (!allowedAmounts.includes(finalAmountCents)) {
        throw new PaymentError(
          `Valor inválido. Valores permitidos: R$ 5,00, R$ 20,00 ou R$ 50,00`,
          "INVALID_AMOUNT",
          400
        );
      }

      // Preparar dados do pagamento
      const paymentData: IPixPaymentData = {
        amount: finalAmountCents,
        description: `SuperVoto: ${suggestion.title} - ${suggestion.artist}`,
        external_reference: suggestionId,
        payer: {
          email: "<EMAIL>", // ClientSession doesn't have email field
          first_name: session?.clientName || "Cliente",
          last_name: "Jukebox",
        },
      };

      logger.info("Dados do pagamento preparados", {
        amount: paymentData.amount,
        description: paymentData.description,
      });

      // Tentar criar pagamento no Mercado Pago
      try {
        const mpPayment = await this.createMercadoPagoPayment(paymentData);
        const paymentResponse = await this.savePaymentToDatabase(
          mpPayment,
          suggestionId,
          normalizedSessionId,
          suggestion
        );

        logger.info("Pagamento PIX criado com sucesso", {
          paymentId: paymentResponse.paymentId,
        });
        return paymentResponse;
      } catch (mpError) {
        logger.error("Falha no Mercado Pago", {
          error: mpError,
          environment: this.environment,
        });

        // Em ambiente de produção, sempre propagar o erro
        if (this.environment === "production") {
          throw mpError;
        }

        // Em desenvolvimento, usar pagamento mock apenas como fallback
        logger.warn("Usando pagamento mock em desenvolvimento", {
          suggestionId,
          error: (mpError as Error).message,
        });

        return await this.createMockPayment(
          suggestionId,
          normalizedSessionId,
          suggestion,
          mpError as Error,
          finalAmountCents
        );
      }
    } catch (error) {
      logger.error("Erro ao criar pagamento PIX:", error);

      if (error instanceof PaymentError) {
        throw error;
      }

      throw new PaymentError(
        `Falha ao criar pagamento: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
        "PAYMENT_CREATION_FAILED",
        500
      );
    }
  }

  /**
   * Busca uma sugestão por ID com validação
   * @private
   */
  private async findSuggestionById(suggestionId: string): Promise<Suggestion> {
    const suggestion = await this.suggestionRepository.findOne({
      where: { id: suggestionId },
      relations: ["restaurant"],
    });

    if (!suggestion) {
      throw new PaymentError(
        "Sugestão não encontrada",
        "SUGGESTION_NOT_FOUND",
        404
      );
    }

    logger.info("Sugestão encontrada", {
      title: suggestion.title,
      artist: suggestion.artist,
      restaurantId: suggestion.restaurant?.id,
    });

    return suggestion;
  }

  /**
   * Verifica se já existe pagamento aprovado para a sugestão
   * @private
   */
  private async checkExistingPayment(suggestionId: string): Promise<void> {
    const existingPayment = await this.paymentRepository.findOne({
      where: {
        suggestionId,
        status: "approved",
      },
    });

    if (existingPayment) {
      throw new PaymentError(
        "Esta música já foi paga",
        "ALREADY_PAID",
        409,
        true,
        existingPayment.id
      );
    }
  }

  /**
   * Cria pagamento no Mercado Pago
   * @private
   */
  private async createMercadoPagoPayment(
    paymentData: IPixPaymentData
  ): Promise<IMercadoPagoPayment> {
    try {
      // Validar token antes de fazer a requisição
      if (!this.mercadoPagoToken || this.mercadoPagoToken === "TEST-YOUR-ACCESS-TOKEN") {
        throw new PaymentError(
          "Token do Mercado Pago não configurado",
          "MISSING_MP_TOKEN",
          500
        );
      }

      logger.info("Criando pagamento no Mercado Pago", {
        amount: paymentData.amount / 100,
        description: paymentData.description,
        environment: this.environment,
      });

      const paymentPayload = {
        transaction_amount: paymentData.amount / 100, // Converter centavos para reais
        description: paymentData.description,
        payment_method_id: "pix",
        external_reference: paymentData.external_reference,
        payer: {
          email: paymentData.payer.email,
          first_name: paymentData.payer.first_name,
          last_name: paymentData.payer.last_name,
        },
        notification_url: `${
          process.env.BACKEND_URL || "http://localhost:8001"
        }/api/v1/payments/webhook`,
        // Configurações adicionais para PIX
        date_of_expiration: new Date(Date.now() + 30 * 60 * 1000).toISOString(), // 30 minutos
        metadata: {
          restaurant_playlist: true,
          suggestion_id: paymentData.external_reference,
          created_at: new Date().toISOString(),
        },
      };

      logger.debug("Payload do pagamento", { paymentPayload });

      const response = await axios.post(
        `${this.baseURL}/payments`,
        paymentPayload,
        {
          headers: {
            Authorization: `Bearer ${this.mercadoPagoToken}`,
            "Content-Type": "application/json",
            "X-Idempotency-Key": `${paymentData.external_reference}-${Date.now()}`,
          },
          timeout: 10000, // 10 segundos de timeout
        }
      );

      logger.info("Pagamento criado no Mercado Pago com sucesso", {
        paymentId: response.data.id,
        status: response.data.status,
        amount: response.data.transaction_amount,
      });

      return response.data;
    } catch (error: any) {
      logger.error("Erro ao criar pagamento no Mercado Pago:", {
        error: error.message,
        response: error.response?.data,
        status: error.response?.status,
      });

      // Melhorar mensagens de erro específicas
      let errorMessage = "Erro desconhecido";
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.message) {
        errorMessage = error.message;
      }

      // Mapear erros comuns do Mercado Pago
      if (errorMessage.includes("user not found") || errorMessage.includes("invalid user")) {
        errorMessage = "Erro ao gerar pagamento Pix: user not found";
      } else if (errorMessage.includes("invalid access token") || errorMessage.includes("unauthorized")) {
        errorMessage = "Erro ao gerar pagamento Pix: invalid access token";
      }

      throw new PaymentError(
        errorMessage,
        "MERCADO_PAGO_ERROR",
        error.response?.status || 500
      );
    }
  }

  /**
   * Salva pagamento no banco de dados
   * @private
   */
  private async savePaymentToDatabase(
    mpPayment: IMercadoPagoPayment,
    suggestionId: string,
    sessionId: string,
    suggestion: Suggestion
  ): Promise<IPaymentResponse> {
    try {
      // Garantir que o sessionId é um UUID válido
      const sessionUUID = uuidValidate(sessionId) ? sessionId : uuidv4();
      const payment = this.paymentRepository.create({
        suggestionId,
        sessionId: sessionUUID,
        amount: mpPayment.transaction_amount * 100, // Converter para centavos
        status: mpPayment.status as any,
        statusDetail: mpPayment.status_detail,
        paymentMethod: "pix",
        externalReference: suggestionId,
        qrCode: mpPayment.point_of_interaction?.transaction_data?.qr_code || "",
        qrCodeBase64:
          mpPayment.point_of_interaction?.transaction_data?.qr_code_base64 ||
          "",
        ticketUrl:
          mpPayment.point_of_interaction?.transaction_data?.ticket_url || "",
        payerEmail: mpPayment.payer?.email || "<EMAIL>",
        payerName: `${mpPayment.payer?.first_name || "Cliente"} ${
          mpPayment.payer?.last_name || ""
        }`,
        platformFee: mpPayment.transaction_amount * 100 * 0.3, // 30% de taxa da plataforma
        restaurantAmount: mpPayment.transaction_amount * 100 * 0.7, // 70% para o restaurante
        metadata: {
          songTitle: suggestion.title,
          artist: suggestion.artist,
          restaurantName: suggestion.restaurant?.name || "Demo Restaurant",
          paymentSource: "web",
        },
      });

  // Definir o ID após criar (garantir string)
  payment.id = String(mpPayment.id);

      await this.paymentRepository.save(payment);
      logger.info("Pagamento salvo no banco", { paymentId: payment.id });

      // Gerar QR Code real se temos o código PIX
      const pixCode =
        mpPayment.point_of_interaction?.transaction_data?.qr_code || "";
      const qrCodeBase64 =
        mpPayment.point_of_interaction?.transaction_data?.qr_code_base64 ||
        (pixCode ? await this.generatePixQRCode(pixCode) : "");

      return {
  paymentId: String(mpPayment.id),
        qrCode: pixCode,
        qrCodeBase64,
        ticketUrl:
          mpPayment.point_of_interaction?.transaction_data?.ticket_url || "",
        amount: mpPayment.transaction_amount * 100,
      };
    } catch (error) {
      logger.error("Erro ao salvar pagamento no banco:", error);
      throw new PaymentError(
        "Falha ao salvar pagamento no banco de dados",
        "DATABASE_SAVE_ERROR",
        500
      );
    }
  }

  /**
   * Cria pagamento mock para testes/fallback
   * @private
   */
  private async createMockPayment(
    suggestionId: string,
    sessionId: string,
    suggestion: Suggestion,
    originalError: Error,
    amountCents: number = 500
  ): Promise<IPaymentResponse> {
    try {
  // Garantir que o sessionId é um UUID válido no mock também
  const sessionUUID = uuidValidate(sessionId) ? sessionId : uuidv4();
      const mockPaymentId = `mock-${Date.now()}-${Math.random()
        .toString(36)
        .substr(2, 9)}`;
      const amountReais = amountCents / 100;
      const mockQrCode = `00020126580014BR.GOV.BCB.PIX0136${mockPaymentId}520400005303986540${amountReais}5802BR5925Restaurant Playlist6009SAO PAULO62070503***6304`;

      const payment = this.paymentRepository.create({
        suggestionId,
  sessionId: sessionUUID,
        amount: amountCents,
        status: "pending" as any,
        statusDetail: "pending_payment",
        externalReference: suggestionId,
        paymentMethod: "pix",
        qrCode: mockQrCode,
        qrCodeBase64: "",
        ticketUrl: `https://mock-payment.com/ticket/${mockPaymentId}`,
        payerEmail: "<EMAIL>",
        payerName: "Cliente Mock",
        platformFee: Math.round(amountCents * 0.3), // 30% do valor
        restaurantAmount: Math.round(amountCents * 0.7), // 70% do valor
        metadata: {
          songTitle: suggestion.title,
          artist: suggestion.artist,
          restaurantName: suggestion.restaurant?.name || "Demo Restaurant",
          paymentSource: "web",
        },
      });

      // Definir o ID após criar
      payment.id = mockPaymentId;

      await this.paymentRepository.save(payment);
      logger.info("Pagamento mock salvo com sucesso", {
        paymentId: mockPaymentId,
      });

      // Gerar QR Code real para o código PIX mock
      const qrCodeBase64 = await this.generatePixQRCode(mockQrCode);

      return {
        paymentId: mockPaymentId,
        qrCode: mockQrCode,
        qrCodeBase64,
        ticketUrl: `https://mock-payment.com/ticket/${mockPaymentId}`,
        amount: amountCents,
      };
    } catch (error) {
      logger.error("Erro ao criar pagamento mock:", error);
      throw new PaymentError(
        "Falha ao criar pagamento mock",
        "MOCK_PAYMENT_ERROR",
        500
      );
    }
  }

  /**
   * Verifica status do pagamento
   * @param paymentId - ID do pagamento
   * @returns Status e detalhes do pagamento
   */
  async checkPaymentStatus(paymentId: string): Promise<any> {
    try {
      logger.info("Verificando status do pagamento", { paymentId });

      // Primeiro verificamos no banco local
      const payment = await this.paymentRepository.findOne({
        where: { id: paymentId },
      });

      if (!payment) {
        // Se não encontrar localmente, consulta na API do Mercado Pago
        try {
          const response = await axios.get(
            `${this.baseURL}/payments/${paymentId}`,
            {
              headers: {
                Authorization: `Bearer ${this.mercadoPagoToken}`,
              },
            }
          );

          logger.info("Status do pagamento obtido da API", {
            paymentId,
            status: response.data.status,
          });

          return response.data;
        } catch (apiError) {
          logger.error("Erro ao consultar API do Mercado Pago:", apiError);
          throw new PaymentError(
            "Pagamento não encontrado",
            "PAYMENT_NOT_FOUND",
            404
          );
        }
      }

      logger.info("Status do pagamento obtido do banco", {
        paymentId,
        status: payment.status,
      });

      return {
        id: payment.id,
  status: payment.status,
  status_detail: payment.statusDetail,
  statusDetail: payment.statusDetail,
  external_reference: payment.externalReference,
  externalReference: payment.externalReference,
  transaction_amount: payment.amount / 100,
  transactionAmount: payment.amount / 100,
        date_created: payment.createdAt,
        date_approved: payment.approvedAt,
      };
    } catch (error) {
      logger.error("Erro ao verificar status do pagamento:", error);

      if (error instanceof PaymentError) {
        throw error;
      }

      throw new PaymentError(
        "Erro ao verificar status do pagamento",
        "PAYMENT_STATUS_ERROR",
        500
      );
    }
  }

  /**
   * Processa webhook de pagamento do Mercado Pago
   * @param data - Dados do webhook recebido
   */
  async processWebhook(data: IWebhookData): Promise<void> {
    try {
      logger.info("Processando webhook de pagamento", {
        type: data.type,
        action: data.action,
        dataId: data.data.id,
      });

      // Validar dados do webhook
      await this.validateInput(PaymentWebhookDto, data);

      if (data.type === "payment" && data.action === "payment.updated") {
        const paymentId = data.data.id;
        const paymentStatus = await this.checkPaymentStatus(paymentId);

        // Atualizar pagamento no banco
        const payment = await this.paymentRepository.findOne({
          where: { id: paymentId },
        });

        if (payment) {
          payment.status = paymentStatus.status;
          payment.statusDetail = paymentStatus.status_detail;
          payment.updatedAt = new Date();

          if (paymentStatus.status === "approved") {
            payment.approvedAt = new Date(
              paymentStatus.date_approved || new Date()
            );
            await this.paymentRepository.save(payment);
            await this.approvePayment(payment);
          } else {
            await this.paymentRepository.save(payment);
          }

          logger.info("Pagamento atualizado via webhook", {
            paymentId,
            status: payment.status,
          });
        } else {
          logger.warn("Pagamento não encontrado para webhook", { paymentId });
        }
      }
    } catch (error) {
      logger.error("Erro ao processar webhook:", error);
      throw new PaymentError(
        "Erro ao processar webhook de pagamento",
        "WEBHOOK_PROCESSING_ERROR",
        500
      );
    }
  }

  /**
   * Aprovar pagamento e processar sugestão
   * @private
   */
  private async approvePayment(payment: Payment): Promise<void> {
    try {
      // Buscar sugestão
      const suggestion = await this.suggestionRepository.findOne({
        where: { id: payment.suggestionId },
        relations: ["restaurant", "session"],
      });

      if (!suggestion) {
        logger.error("Sugestão não encontrada para pagamento aprovado", {
          paymentId: payment.id,
          suggestionId: payment.suggestionId,
        });
        return;
      }

      // Marcar sugestão como paga e aprovada
      suggestion.isPaid = true;
      suggestion.status = SuggestionStatus.APPROVED;
      suggestion.paidAt = new Date();

      // Adicionar à fila de reprodução com prioridade
      suggestion.queuePosition = await this.getNextQueuePosition(
        suggestion.restaurant.id,
        true
      );

      await this.suggestionRepository.save(suggestion);
      logger.info("Sugestão atualizada como paga", {
        suggestionId: suggestion.id,
        queuePosition: suggestion.queuePosition,
      });

      // Notificar aprovação do pagamento
      await notificationService.sendNotification({
        type: NotificationType.SUCCESS,
        title: "Pagamento Aprovado!",
        message: `Sua música "${suggestion.title}" foi adicionada à fila prioritária`,
        priority: NotificationPriority.HIGH,
        category: "payment",
        targetUsers: [payment.sessionId],
        data: {
          suggestionId: suggestion.id,
          paymentId: payment.id,
          queuePosition: suggestion.queuePosition,
        },
      });

      // Notificar restaurante
      await notificationService.sendToRestaurant(suggestion.restaurant.id, {
        type: NotificationType.MUSIC,
        title: "Nova Música Paga",
        message: `"${suggestion.title}" foi paga e adicionada à fila`,
        priority: NotificationPriority.NORMAL,
        category: "payment",
        data: {
          suggestionId: suggestion.id,
          amount: payment.amount / 100,
          clientName:
            suggestion.clientSession?.clientName ||
            suggestion.clientName ||
            "Cliente",
        },
      });

      logger.info("Notificações enviadas para pagamento aprovado", {
        paymentId: payment.id,
        suggestionId: suggestion.id,
      });
    } catch (error) {
      logger.error("Erro ao aprovar pagamento:", error);
    }
  }

  /**
   * Obter próxima posição na fila (prioridade para pagos)
   * @private
   */
  private async getNextQueuePosition(
    restaurantId: string,
    isPaid: boolean = false
  ): Promise<number> {
    const query = this.suggestionRepository
      .createQueryBuilder("suggestion")
      .where("suggestion.restaurant_id = :restaurantId", { restaurantId })
      .andWhere("suggestion.status = :status", { status: "approved" })
      .andWhere("suggestion.queuePosition IS NOT NULL");

    if (isPaid) {
      // Músicas pagas têm prioridade - inserir no início das pagas
      query.andWhere("suggestion.isPaid = true");
    }

    const maxPosition = await query
      .select("MAX(suggestion.queuePosition)", "maxPosition")
      .getRawOne();

    return (maxPosition?.maxPosition || 0) + 1;
  }

  /**
   * Obtém estatísticas de pagamento
   * @param params - Parâmetros para filtrar estatísticas
   */
  async getPaymentStats(params: PaymentStatsDto): Promise<IPaymentStats> {
    try {
      // Validar parâmetros
      await this.validateInput(PaymentStatsDto, params);

      const { restaurantId, period = "7d" } = params;

      const endDate = new Date();
      const startDate = new Date();

      // Definir período de análise
      switch (period) {
        case "1d":
          startDate.setDate(endDate.getDate() - 1);
          break;
        case "7d":
          startDate.setDate(endDate.getDate() - 7);
          break;
        case "30d":
          startDate.setDate(endDate.getDate() - 30);
          break;
        case "90d":
          startDate.setDate(endDate.getDate() - 90);
          break;
        default:
          startDate.setDate(endDate.getDate() - 7);
      }

      logger.info("Obtendo estatísticas de pagamento", {
        restaurantId,
        period,
        startDate,
        endDate,
      });

      // Construir query base
      let queryBuilder = this.paymentRepository
        .createQueryBuilder("payment")
        .leftJoin("payment.suggestion", "suggestion")
        .leftJoin("suggestion.restaurant", "restaurant")
        .where("payment.createdAt BETWEEN :startDate AND :endDate", {
          startDate,
          endDate,
        });

      // Filtrar por restaurante se fornecido
      if (restaurantId) {
        queryBuilder = queryBuilder.andWhere("restaurant.id = :restaurantId", {
          restaurantId,
        });
      }

      // Executar query
      const payments = await queryBuilder.getMany();

      // Calcular estatísticas
      const totalPayments = payments.length;
      const successfulPayments = payments.filter(
        (p) => p.status === "approved"
      ).length;
      const failedPayments = payments.filter(
        (p) => p.status === "rejected"
      ).length;

      const totalRevenue =
        payments
          .filter((p) => p.status === "approved")
          .reduce((sum, p) => sum + p.amount, 0) / 100; // Converter para reais

      const averageAmount =
        successfulPayments > 0 ? totalRevenue / successfulPayments : 0;

      // Calcular receita diária
      const dailyRevenue = [];
      for (
        let d = new Date(startDate);
        d <= endDate;
        d.setDate(d.getDate() + 1)
      ) {
        const dateStr = d.toISOString().split("T")[0];
        const dayPayments = payments.filter(
          (p) =>
            p.createdAt.toISOString().split("T")[0] === dateStr &&
            p.status === "approved"
        );

        dailyRevenue.push({
          date: dateStr,
          revenue: dayPayments.reduce((sum, p) => sum + p.amount, 0) / 100,
          count: dayPayments.length,
        });
      }

      const stats: IPaymentStats = {
        totalRevenue,
        totalPayments,
        successfulPayments,
        failedPayments,
        averageAmount,
        period,
        restaurantId,
        dailyRevenue,
      };

      logger.info("Estatísticas de pagamento obtidas", {
        totalPayments,
        successfulPayments,
        totalRevenue,
      });

      return stats;
    } catch (error) {
      logger.error("Erro ao obter estatísticas de pagamento:", error);

      if (error instanceof PaymentError) {
        throw error;
      }

      throw new PaymentError(
        "Erro ao obter estatísticas de pagamento",
        "PAYMENT_STATS_ERROR",
        500
      );
    }
  }

  /**
   * Cancelar pagamento
   * @param paymentId - ID do pagamento a ser cancelado
   */
  async cancelPayment(paymentId: string): Promise<void> {
    try {
      logger.info("Iniciando cancelamento de pagamento", { paymentId });

      // Verificar se o pagamento existe
      const payment = await this.paymentRepository.findOne({
        where: { id: paymentId },
      });

      if (!payment) {
        throw new PaymentError(
          "Pagamento não encontrado",
          "PAYMENT_NOT_FOUND",
          404
        );
      }

      // Verificar se já está cancelado ou aprovado
      if (payment.status === "cancelled") {
        logger.info("Pagamento já está cancelado", { paymentId });
        return;
      }

      if (payment.status === "approved") {
        throw new PaymentError(
          "Não é possível cancelar um pagamento já aprovado",
          "CANNOT_CANCEL_APPROVED",
          400
        );
      }

      // Cancelar no Mercado Pago (se não for mock)
      if (!payment.id.startsWith("mock")) {
        try {
          await axios.put(
            `${this.baseURL}/payments/${paymentId}`,
            { status: "cancelled" },
            {
              headers: {
                Authorization: `Bearer ${this.mercadoPagoToken}`,
                "Content-Type": "application/json",
              },
            }
          );
          logger.info("Pagamento cancelado no Mercado Pago", { paymentId });
        } catch (apiError: any) {
          logger.error("Erro ao cancelar pagamento no Mercado Pago:", {
            paymentId,
            error: apiError.message,
            response: apiError.response?.data,
          });

          // Se for erro 404, continuamos com o cancelamento local
          if (apiError.response?.status !== 404) {
            throw new PaymentError(
              `Erro ao cancelar pagamento no Mercado Pago: ${
                apiError.response?.data?.message || apiError.message
              }`,
              "MERCADO_PAGO_CANCEL_ERROR",
              500
            );
          }
        }
      }

      // Atualizar no banco
      payment.status = "cancelled";
      payment.statusDetail = "cancelled_by_user";
      payment.updatedAt = new Date();
      await this.paymentRepository.save(payment);

      logger.info("Pagamento cancelado com sucesso", { paymentId });

      // Notificar usuário do cancelamento
      if (payment.sessionId) {
        await notificationService.sendNotification({
          type: NotificationType.INFO,
          title: "Pagamento Cancelado",
          message: "Seu pagamento foi cancelado com sucesso",
          priority: NotificationPriority.NORMAL,
          category: "payment",
          targetUsers: [payment.sessionId],
          data: {
            paymentId: payment.id,
          },
        });
      }
    } catch (error) {
      logger.error("Erro ao cancelar pagamento:", error);

      if (error instanceof PaymentError) {
        throw error;
      }

      throw new PaymentError(
        `Erro ao cancelar pagamento: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
        "PAYMENT_CANCEL_ERROR",
        500
      );
    }
  }

  /**
   * Obtém histórico de pagamentos
   * @param restaurantId - ID do restaurante (opcional)
   * @param sessionId - ID da sessão (opcional)
   */
  async getPaymentHistory(
    restaurantId?: string,
    sessionId?: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<{ payments: Payment[]; total: number }> {
    try {
      logger.info("Obtendo histórico de pagamentos", {
        restaurantId,
        sessionId,
        limit,
        offset,
      });

      const queryBuilder = this.paymentRepository
        .createQueryBuilder("payment")
        .leftJoinAndSelect("payment.suggestion", "suggestion")
        .leftJoinAndSelect("suggestion.restaurant", "restaurant")
        .orderBy("payment.createdAt", "DESC")
        .take(limit)
        .skip(offset);

      if (restaurantId) {
        queryBuilder.andWhere("restaurant.id = :restaurantId", {
          restaurantId,
        });
      }

      if (sessionId) {
        queryBuilder.andWhere("payment.sessionId = :sessionId", { sessionId });
      }

      const [payments, total] = await queryBuilder.getManyAndCount();

      logger.info("Histórico de pagamentos obtido", {
        count: payments.length,
        total,
      });

      return { payments, total };
    } catch (error) {
      logger.error("Erro ao obter histórico de pagamentos:", error);
      throw new PaymentError(
        "Erro ao obter histórico de pagamentos",
        "PAYMENT_HISTORY_ERROR",
        500
      );
    }
  }
}

export { PaymentService };
export const paymentService = new PaymentService();
export default PaymentService;
