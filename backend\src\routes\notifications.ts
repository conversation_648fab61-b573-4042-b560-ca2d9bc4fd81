import { Router } from "express";
import { body, param, query, validationResult } from "../utils/validation";
import {
  notificationService,
  NotificationType,
  NotificationPriority,
} from "../services/NotificationService";
import asyncHandler from "../middleware/asyncHandler";
import { optionalAuth } from "../middleware/auth";
import { ValidationError } from "../utils/errors";

const router = Router();

/**
 * @swagger
 * /api/v1/notifications:
 *   get:
 *     summary: Obter histórico de notificações
 *     tags: [Notificações]
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Número máximo de notificações
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Filtrar por categoria
 *     responses:
 *       200:
 *         description: Lista de notificações
 */
router.get(
  "/",
  [
    query("limit").optional().isInt({ min: 1, max: 100 }),
    query("category").optional().isString(),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { limit = 50, category } = req.query;
    let history = notificationService.getHistory(Number(limit));

    // Filtrar por categoria se especificado
    if (category) {
      history = history.filter((n) => n.category === category);
    }

    res.json({
      success: true,
      notifications: history,
      total: history.length,
    });
  })
);

/**
 * @swagger
 * /api/v1/notifications/send:
 *   post:
 *     summary: Enviar notificação
 *     tags: [Notificações]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - type
 *               - title
 *               - message
 *             properties:
 *               type:
 *                 type: string
 *                 enum: [success, error, warning, info, music, vote, badge, system]
 *               title:
 *                 type: string
 *               message:
 *                 type: string
 *               priority:
 *                 type: string
 *                 enum: [low, normal, high, urgent]
 *               category:
 *                 type: string
 *               targetUsers:
 *                 type: array
 *                 items:
 *                   type: string
 *               targetRestaurants:
 *                 type: array
 *                 items:
 *                   type: string
 *               data:
 *                 type: object
 *     responses:
 *       200:
 *         description: Notificação enviada
 *       400:
 *         description: Dados inválidos
 */
router.post(
  "/send",
  [
    body("type")
      .isIn([
        "success",
        "error",
        "warning",
        "info",
        "music",
        "vote",
        "badge",
        "system",
      ])
      .withMessage("Tipo de notificação inválido"),
    body("title").notEmpty().withMessage("Título é obrigatório"),
    body("message").notEmpty().withMessage("Mensagem é obrigatória"),
    body("priority")
      .optional()
      .isIn(["low", "normal", "high", "urgent"])
      .withMessage("Prioridade inválida"),
    body("category").optional().isString(),
    body("targetUsers").optional().isArray(),
    body("targetRestaurants").optional().isArray(),
    body("data").optional().isObject(),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const notificationData = req.body;
    const notification = await notificationService.sendNotification(
      notificationData
    );

    res.json({
      success: true,
      message: "Notificação enviada com sucesso",
      notification,
    });
  })
);

/**
 * @swagger
 * /api/v1/notifications/broadcast:
 *   post:
 *     summary: Enviar notificação para todos os usuários
 *     tags: [Notificações]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - type
 *               - title
 *               - message
 *             properties:
 *               type:
 *                 type: string
 *                 enum: [success, error, warning, info, music, vote, badge, system]
 *               title:
 *                 type: string
 *               message:
 *                 type: string
 *               priority:
 *                 type: string
 *                 enum: [low, normal, high, urgent]
 *               category:
 *                 type: string
 *               data:
 *                 type: object
 *     responses:
 *       200:
 *         description: Broadcast enviado
 */
router.post(
  "/broadcast",
  [
    body("type")
      .isIn([
        "success",
        "error",
        "warning",
        "info",
        "music",
        "vote",
        "badge",
        "system",
      ])
      .withMessage("Tipo de notificação inválido"),
    body("title").notEmpty().withMessage("Título é obrigatório"),
    body("message").notEmpty().withMessage("Mensagem é obrigatória"),
    body("priority")
      .optional()
      .isIn(["low", "normal", "high", "urgent"])
      .withMessage("Prioridade inválida"),
    body("category").optional().isString(),
    body("data").optional().isObject(),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const notificationData = req.body;
    const notification = await notificationService.broadcast(notificationData);

    res.json({
      success: true,
      message: "Broadcast enviado com sucesso",
      notification,
    });
  })
);

/**
 * @swagger
 * /api/v1/notifications/restaurant/{restaurantId}:
 *   post:
 *     summary: Enviar notificação para restaurante específico
 *     tags: [Notificações]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - type
 *               - title
 *               - message
 *             properties:
 *               type:
 *                 type: string
 *                 enum: [success, error, warning, info, music, vote, badge, system]
 *               title:
 *                 type: string
 *               message:
 *                 type: string
 *               priority:
 *                 type: string
 *                 enum: [low, normal, high, urgent]
 *               category:
 *                 type: string
 *               data:
 *                 type: object
 *     responses:
 *       200:
 *         description: Notificação enviada para o restaurante
 */
router.post(
  "/restaurant/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("type")
      .isIn([
        "success",
        "error",
        "warning",
        "info",
        "music",
        "vote",
        "badge",
        "system",
      ])
      .withMessage("Tipo de notificação inválido"),
    body("title").notEmpty().withMessage("Título é obrigatório"),
    body("message").notEmpty().withMessage("Mensagem é obrigatória"),
    body("priority")
      .optional()
      .isIn(["low", "normal", "high", "urgent"])
      .withMessage("Prioridade inválida"),
    body("category").optional().isString(),
    body("data").optional().isObject(),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const notificationData = req.body;

    const notification = await notificationService.sendToRestaurant(
      restaurantId,
      notificationData
    );

    res.json({
      success: true,
      message: "Notificação enviada para o restaurante",
      notification,
    });
  })
);

/**
 * @swagger
 * /api/v1/notifications/user/{userId}:
 *   post:
 *     summary: Enviar notificação para usuário específico
 *     tags: [Notificações]
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - type
 *               - title
 *               - message
 *             properties:
 *               type:
 *                 type: string
 *                 enum: [success, error, warning, info, music, vote, badge, system]
 *               title:
 *                 type: string
 *               message:
 *                 type: string
 *               priority:
 *                 type: string
 *                 enum: [low, normal, high, urgent]
 *               category:
 *                 type: string
 *               data:
 *                 type: object
 *     responses:
 *       200:
 *         description: Notificação enviada para o usuário
 */
router.post(
  "/user/:userId",
  [
    param("userId").notEmpty().withMessage("ID do usuário é obrigatório"),
    body("type")
      .isIn([
        "success",
        "error",
        "warning",
        "info",
        "music",
        "vote",
        "badge",
        "system",
      ])
      .withMessage("Tipo de notificação inválido"),
    body("title").notEmpty().withMessage("Título é obrigatório"),
    body("message").notEmpty().withMessage("Mensagem é obrigatória"),
    body("priority")
      .optional()
      .isIn(["low", "normal", "high", "urgent"])
      .withMessage("Prioridade inválida"),
    body("category").optional().isString(),
    body("data").optional().isObject(),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { userId } = req.params;
    const notificationData = req.body;

    const notification = await notificationService.sendToUser(
      userId,
      notificationData
    );

    res.json({
      success: true,
      message: "Notificação enviada para o usuário",
      notification,
    });
  })
);

/**
 * @swagger
 * /api/v1/notifications/stats:
 *   get:
 *     summary: Obter estatísticas das notificações
 *     tags: [Notificações]
 *     responses:
 *       200:
 *         description: Estatísticas das notificações
 */
router.get(
  "/stats",
  optionalAuth,
  asyncHandler(async (req, res) => {
    const connectedClients = notificationService.getConnectedClients();
    const history = notificationService.getHistory(100);

    // Estatísticas por tipo
    const byType = history.reduce((acc, notification) => {
      acc[notification.type] = (acc[notification.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Estatísticas por categoria
    const byCategory = history.reduce((acc, notification) => {
      const category = notification.category || "uncategorized";
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Estatísticas por prioridade
    const byPriority = history.reduce((acc, notification) => {
      const priority = notification.priority || "normal";
      acc[priority] = (acc[priority] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    res.json({
      success: true,
      stats: {
        connectedClients,
        totalNotifications: history.length,
        byType,
        byCategory,
        byPriority,
        recentActivity: history.slice(0, 10),
      },
    });
  })
);

/**
 * @swagger
 * /api/v1/notifications/test:
 *   post:
 *     summary: Enviar notificação de teste
 *     tags: [Notificações]
 *     responses:
 *       200:
 *         description: Notificação de teste enviada
 */
router.post(
  "/test",
  optionalAuth,
  asyncHandler(async (req, res) => {
    const testNotification = await notificationService.broadcast({
      type: NotificationType.INFO,
      title: "Teste de Notificação",
      message: `Notificação de teste enviada em ${new Date().toLocaleTimeString(
        "pt-BR"
      )}`,
      priority: NotificationPriority.NORMAL,
      category: "test",
      data: {
        timestamp: new Date().toISOString(),
        source: "api-test",
      },
    });

    res.json({
      success: true,
      message: "Notificação de teste enviada",
      notification: testNotification,
    });
  })
);

export default router;
