-- <PERSON><PERSON><PERSON> colunas ausentes/compatibilidade na client_sessions
-- pontos (points) e logs/contadores já existem no modelo; este script é idempotente

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'client_sessions' AND column_name = 'points'
  ) THEN
    ALTER TABLE client_sessions ADD COLUMN points INTEGER NOT NULL DEFAULT 0;
  END IF;

  -- <PERSON><PERSON><PERSON><PERSON> colunas exigidas pelos logs/analytics
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'client_sessions' AND column_name = 'pageviews'
  ) THEN
    ALTER TABLE client_sessions ADD COLUMN pageviews INTEGER NOT NULL DEFAULT 0;
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'client_sessions' AND column_name = 'sessionduration'
  ) THEN
    ALTER TABLE client_sessions ADD COLUMN sessionduration INTEGER NOT NULL DEFAULT 0;
  END IF;
END $$;
