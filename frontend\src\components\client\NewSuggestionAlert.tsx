import React, { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Music, ThumbsUp, ThumbsDown, Users, Clock, CreditCard, X } from "lucide-react";
import { toast } from "react-hot-toast";
import { useWebSocket } from "@/services/websocket";
import { buildApiUrl } from "@/config/api";

interface NewSuggestion {
  id: string;
  title: string;
  artist: string;
  thumbnailUrl: string;
  duration: number;
  formattedDuration: string;
  isPaid: boolean;
  clientName?: string;
  tableNumber?: string;
  upvotes: number;
  downvotes: number;
  score: number;
  createdAt: string;
  youtubeVideoId: string;
  clientSessionId?: string;
}

interface NewSuggestionAlertProps {
  restaurantId: string;
  sessionId: string;
  onVote?: (suggestionId: string, voteType: "up" | "down") => void;
}

export const NewSuggestionAlert: React.FC<NewSuggestionAlertProps> = ({
  restaurantId,
  sessionId,
  onVote,
}) => {
  const [newSuggestions, setNewSuggestions] = useState<NewSuggestion[]>([]);
  const [votingStates, setVotingStates] = useState<Record<string, boolean>>({});
  const [userVotes, setUserVotes] = useState<Record<string, "up" | "down">>({});

  const { isConnected, joinRestaurant, on, off } = useWebSocket();

  const formatDuration = useCallback((seconds: number): string => {
    if (!seconds) return "0:00";
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  }, []);

  const handleNewSuggestionData = useCallback((suggestion: any) => {
    const newSuggestion: NewSuggestion = {
      id: suggestion.id,
      title: suggestion.title || "Título Desconhecido",
      artist: suggestion.artist || "Artista Desconhecido",
      thumbnailUrl:
        suggestion.thumbnailUrl ||
        `https://img.youtube.com/vi/${suggestion.youtubeVideoId || "default"}/mqdefault.jpg`,
      duration: suggestion.duration || 0,
      formattedDuration: suggestion.formattedDuration || formatDuration(suggestion.duration || 0),
      isPaid: suggestion.isPaid || false,
      clientName: suggestion.clientName,
      tableNumber: suggestion.tableNumber,
      upvotes: suggestion.upvotes || 0,
      downvotes: suggestion.downvotes || 0,
      score: suggestion.score || 0,
      createdAt: suggestion.createdAt || new Date().toISOString(),
      youtubeVideoId: suggestion.youtubeVideoId || suggestion.id,
      clientSessionId: suggestion.clientSessionId,
    };

    setNewSuggestions((prev) => {
      if (prev.find((s) => s.id === newSuggestion.id)) return prev;
      return [newSuggestion, ...prev].slice(0, 5); // Limita a 5 sugestões
    });

    const tableInfo = suggestion.tableNumber ? `Mesa ${suggestion.tableNumber}` : suggestion.clientName || "Anônimo";
    const paymentInfo = suggestion.isPaid ? " (PAGO)" : "";
    toast.success(
      `🎵 ${tableInfo} sugeriu "${suggestion.title}"${paymentInfo}! Vote agora!`,
      { duration: 5000, icon: "🗳️" }
    );

    setTimeout(() => {
      setNewSuggestions((prev) => prev.filter((s) => s.id !== newSuggestion.id));
    }, 120000);
  }, [formatDuration]);

  const updateSuggestionVotes = useCallback((suggestionId: string, voteData: any) => {
    setNewSuggestions((prev) =>
      prev.map((suggestion) =>
        suggestion.id === suggestionId
          ? {
              ...suggestion,
              upvotes: voteData.upvotes ?? suggestion.upvotes,
              downvotes: voteData.downvotes ?? suggestion.downvotes,
              score: voteData.score ?? suggestion.score,
            }
          : suggestion
      )
    );
  }, []);

  const handleVote = useCallback(
    async (suggestionId: string, voteType: "up" | "down") => {
      if (votingStates[suggestionId] || userVotes[suggestionId]) return;

      setVotingStates((prev) => ({ ...prev, [suggestionId]: true }));

      try {
        const response = await fetch(buildApiUrl(`/suggestions/${suggestionId}/vote`), {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-Session-ID": sessionId,
          },
          body: JSON.stringify({ voteType }),
        });

        if (response.ok) {
          setUserVotes((prev) => ({ ...prev, [suggestionId]: voteType }));
          toast.success(
            `Voto ${voteType === "up" ? "positivo" : "negativo"} registrado! +10 pontos!`,
            { icon: voteType === "up" ? "👍" : "👎" }
          );
          onVote?.(suggestionId, voteType);

          setTimeout(() => {
            setNewSuggestions((prev) => prev.filter((s) => s.id !== suggestionId));
          }, 2000);
        } else {
          const errorData = await response.json();
          toast.error(errorData.message || "Erro ao votar");
        }
      } catch (error) {
        console.error("Erro ao votar:", error);
        toast.error("Erro ao votar");
      } finally {
        setVotingStates((prev) => ({ ...prev, [suggestionId]: false }));
      }
    },
    [sessionId, onVote, votingStates, userVotes]
  );

  const dismissSuggestion = useCallback((suggestionId: string) => {
    setNewSuggestions((prev) => prev.filter((s) => s.id !== suggestionId));
  }, []);

  useEffect(() => {
    if (!restaurantId || !sessionId) return;

    joinRestaurant(restaurantId);

    const handleNewSuggestion = (data: any) => {
      if (data.suggestion && data.suggestion.clientSessionId !== sessionId) {
        handleNewSuggestionData(data.suggestion);
      }
    };

    const handleVoteUpdate = (data: any) => {
      if (data.suggestionId) {
        updateSuggestionVotes(data.suggestionId, data);
      }
    };

    on("new-suggestion", handleNewSuggestion);
    on("vote-update", handleVoteUpdate);

    return () => {
      off("new-suggestion");
      off("vote-update");
    };
  }, [restaurantId, sessionId, joinRestaurant, on, off, handleNewSuggestionData, updateSuggestionVotes]);

  if (newSuggestions.length === 0) return null;

  return (
    <div className="fixed top-4 right-4 z-50 space-y-3 max-w-sm w-full">
      <AnimatePresence>
        {newSuggestions.slice(0, 3).map((suggestion) => (
          <motion.div
            key={suggestion.id}
            initial={{ opacity: 0, x: 300, scale: 0.8 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            exit={{ opacity: 0, x: 300, scale: 0.8 }}
            transition={{ duration: 0.4, type: "spring", bounce: 0.3 }}
            className="bg-gradient-to-r from-purple-700 to-pink-700 rounded-lg shadow-xl p-4 text-white border border-white/20"
            role="alert"
            aria-labelledby={`suggestion-alert-${suggestion.id}`}
          >
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <Music className="w-5 h-5 text-yellow-300" aria-hidden="true" />
                <span id={`suggestion-alert-${suggestion.id}`} className="font-bold text-sm">
                  Nova Sugestão
                </span>
                {suggestion.isPaid && (
                  <div className="flex items-center gap-1 bg-yellow-500 text-black px-2 py-1 rounded-full">
                    <CreditCard className="w-3 h-3" aria-hidden="true" />
                    <span className="text-xs font-bold">PAGO</span>
                  </div>
                )}
              </div>
              <button
                onClick={() => dismissSuggestion(suggestion.id)}
                className="text-white/70 hover:text-white transition-colors"
                aria-label="Fechar sugestão"
              >
                <X className="w-4 h-4" aria-hidden="true" />
              </button>
            </div>

            <div className="flex gap-3">
              <img
                src={suggestion.thumbnailUrl}
                alt={`Capa de ${suggestion.title}`}
                className="w-16 h-12 rounded-lg object-cover"
              />
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-sm truncate">{suggestion.title}</h3>
                <p className="text-white/80 text-xs truncate">{suggestion.artist}</p>
                <div className="flex items-center gap-3 mt-1 text-xs">
                  <div className="flex items-center gap-1">
                    <Users className="w-3 h-3" aria-hidden="true" />
                    <span>{suggestion.tableNumber ? `Mesa ${suggestion.tableNumber}` : suggestion.clientName || "Anônimo"}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="w-3 h-3" aria-hidden="true" />
                    <span>{suggestion.formattedDuration}</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between mt-3 text-xs">
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-1 text-green-300">
                  <ThumbsUp className="w-3 h-3" aria-hidden="true" />
                  <span>{suggestion.upvotes}</span>
                </div>
                <div className="flex items-center gap-1 text-red-300">
                  <ThumbsDown className="w-3 h-3" aria-hidden="true" />
                  <span>{suggestion.downvotes}</span>
                </div>
                <div className="flex items-center gap-1 text-yellow-300">
                  <Star className="w-3 h-3" aria-hidden="true" />
                  <span>Score: {suggestion.score}</span>
                </div>
              </div>
            </div>

            <div className="flex gap-2 mt-4">
              <button
                onClick={() => handleVote(suggestion.id, "up")}
                disabled={votingStates[suggestion.id] || userVotes[suggestion.id] === "up"}
                className={`flex-1 flex items-center justify-center gap-2 py-2 px-3 rounded-lg transition-colors ${
                  userVotes[suggestion.id] === "up"
                    ? "bg-green-600 text-white"
                    : "bg-white/20 hover:bg-green-600 hover:text-white"
                } disabled:opacity-50 disabled:cursor-not-allowed`}
                aria-label={`Votar positivamente em ${suggestion.title}`}
              >
                <ThumbsUp className="w-4 h-4" aria-hidden="true" />
                <span className="text-sm font-medium">Curtir</span>
              </button>
              <button
                onClick={() => handleVote(suggestion.id, "down")}
                disabled={votingStates[suggestion.id] || userVotes[suggestion.id] === "down"}
                className={`flex-1 flex items-center justify-center gap-2 py-2 px-3 rounded-lg transition-colors ${
                  userVotes[suggestion.id] === "down"
                    ? "bg-red-600 text-white"
                    : "bg-white/20 hover:bg-red-600 hover:text-white"
                } disabled:opacity-50 disabled:cursor-not-allowed`}
                aria-label={`Votar negativamente em ${suggestion.title}`}
              >
                <ThumbsDown className="w-4 h-4" aria-hidden="true" />
                <span className="text-sm font-medium">Não Curtir</span>
              </button>
            </div>

            {votingStates[suggestion.id] && (
              <div className="flex items-center justify-center mt-2 text-xs">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" aria-hidden="true"></div>
                <span className="ml-2">Enviando voto...</span>
              </div>
            )}
          </motion.div>
        ))}
      </AnimatePresence>

      {newSuggestions.length > 3 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-black/50 backdrop-blur-sm rounded-lg p-2 text-center text-white text-xs"
        >
          +{newSuggestions.length - 3} mais sugestões aguardando seu voto
        </motion.div>
      )}
    </div>
  );
};
