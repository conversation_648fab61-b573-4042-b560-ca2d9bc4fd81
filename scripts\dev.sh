#!/bin/bash

# Script para desenvolvimento local do Sistema de Playlist Interativa
# Este script facilita o desenvolvimento local com hot reload

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para imprimir mensagens coloridas
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Verificar se Node.js está instalado
check_node() {
    if ! command -v node &> /dev/null; then
        print_error "Node.js não está instalado!"
        echo "Instale Node.js 18+ para desenvolvimento local"
        exit 1
    fi
    
    NODE_VERSION=$(node --version)
    print_message "Node.js encontrado: $NODE_VERSION ✓"
}

# Verificar se npm está instalado
check_npm() {
    if ! command -v npm &> /dev/null; then
        print_error "npm não está instalado!"
        exit 1
    fi
    
    NPM_VERSION=$(npm --version)
    print_message "npm encontrado: $NPM_VERSION ✓"
}

# Instalar dependências do backend
install_backend_deps() {
    print_message "Instalando dependências do backend..."
    
    cd backend
    npm install
    cd ..
    
    print_message "Dependências do backend instaladas ✓"
}

# Instalar dependências do frontend
install_frontend_deps() {
    print_message "Instalando dependências do frontend..."
    
    cd frontend
    npm install
    cd ..
    
    print_message "Dependências do frontend instaladas ✓"
}

# Configurar ambiente de desenvolvimento
setup_dev_env() {
    if [ ! -f .env ]; then
        print_message "Criando arquivo .env para desenvolvimento..."
        cp .env.example .env
        
        # Configurar para desenvolvimento
        sed -i 's/NODE_ENV=production/NODE_ENV=development/' .env
        sed -i 's/LOG_LEVEL=info/LOG_LEVEL=debug/' .env
        
        print_warning "Configure YOUTUBE_API_KEY no arquivo .env"
    else
        print_message "Arquivo .env já existe ✓"
    fi
}

# Iniciar serviços de infraestrutura (PostgreSQL e Redis)
start_infrastructure() {
    print_message "Iniciando serviços de infraestrutura..."
    
    # Iniciar apenas PostgreSQL e Redis
    docker-compose up -d postgres redis
    
    # Aguardar serviços estarem prontos
    print_message "Aguardando serviços ficarem prontos..."
    sleep 10
    
    # Verificar se PostgreSQL está pronto
    until docker-compose exec postgres pg_isready -U restaurant_user -d restaurant_playlist; do
        print_message "Aguardando PostgreSQL..."
        sleep 2
    done
    
    print_message "Infraestrutura iniciada ✓"
}

# Executar migrações do banco
run_migrations() {
    print_message "Executando migrações do banco..."
    
    cd backend
    npm run migrate 2>/dev/null || print_warning "Comando migrate não encontrado (normal se não configurado)"
    cd ..
    
    print_message "Migrações executadas ✓"
}

# Iniciar backend em modo desenvolvimento
start_backend() {
    print_message "Iniciando backend em modo desenvolvimento..."
    
    cd backend
    npm run dev &
    BACKEND_PID=$!
    cd ..
    
    echo $BACKEND_PID > .backend.pid
    print_message "Backend iniciado (PID: $BACKEND_PID) ✓"
}

# Iniciar frontend em modo desenvolvimento
start_frontend() {
    print_message "Iniciando frontend em modo desenvolvimento..."
    
    cd frontend
    npm run dev &
    FRONTEND_PID=$!
    cd ..
    
    echo $FRONTEND_PID > .frontend.pid
    print_message "Frontend iniciado (PID: $FRONTEND_PID) ✓"
}

# Parar processos de desenvolvimento
stop_dev_processes() {
    print_message "Parando processos de desenvolvimento..."
    
    # Parar backend
    if [ -f .backend.pid ]; then
        BACKEND_PID=$(cat .backend.pid)
        if kill -0 $BACKEND_PID 2>/dev/null; then
            kill $BACKEND_PID
            print_message "Backend parado ✓"
        fi
        rm .backend.pid
    fi
    
    # Parar frontend
    if [ -f .frontend.pid ]; then
        FRONTEND_PID=$(cat .frontend.pid)
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            kill $FRONTEND_PID
            print_message "Frontend parado ✓"
        fi
        rm .frontend.pid
    fi
    
    # Parar infraestrutura
    docker-compose down
    print_message "Infraestrutura parada ✓"
}

# Mostrar logs em tempo real
show_logs() {
    print_message "Mostrando logs em tempo real..."
    print_message "Pressione Ctrl+C para parar"
    
    # Mostrar logs do Docker e dos processos Node.js
    docker-compose logs -f postgres redis &
    
    # Aguardar Ctrl+C
    trap 'stop_dev_processes; exit 0' INT
    wait
}

# Executar testes
run_tests() {
    print_message "Executando testes..."
    
    # Testes do backend
    if [ -d backend ]; then
        print_message "Executando testes do backend..."
        cd backend
        npm test 2>/dev/null || print_warning "Testes do backend não configurados"
        cd ..
    fi
    
    # Testes do frontend
    if [ -d frontend ]; then
        print_message "Executando testes do frontend..."
        cd frontend
        npm test 2>/dev/null || print_warning "Testes do frontend não configurados"
        cd ..
    fi
    
    print_message "Testes concluídos ✓"
}

# Executar linting
run_lint() {
    print_message "Executando linting..."
    
    # Lint do backend
    if [ -d backend ]; then
        print_message "Executando lint do backend..."
        cd backend
        npm run lint 2>/dev/null || print_warning "Lint do backend não configurado"
        cd ..
    fi
    
    # Lint do frontend
    if [ -d frontend ]; then
        print_message "Executando lint do frontend..."
        cd frontend
        npm run lint 2>/dev/null || print_warning "Lint do frontend não configurado"
        cd ..
    fi
    
    print_message "Linting concluído ✓"
}

# Reset do ambiente de desenvolvimento
reset_dev() {
    print_warning "ATENÇÃO: Isso irá remover todos os dados do banco de desenvolvimento!"
    echo "Deseja continuar? (y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        print_message "Reset cancelado"
        return 0
    fi
    
    print_message "Resetando ambiente de desenvolvimento..."
    
    # Parar tudo
    stop_dev_processes
    
    # Remover volumes do Docker
    docker-compose down -v
    
    # Remover node_modules
    rm -rf backend/node_modules frontend/node_modules
    
    # Reinstalar dependências
    install_backend_deps
    install_frontend_deps
    
    print_message "Reset concluído ✓"
}

# Mostrar status dos serviços
show_status() {
    print_header "STATUS DOS SERVIÇOS"
    
    # Verificar infraestrutura
    if docker-compose ps postgres | grep -q "Up"; then
        print_message "PostgreSQL: Rodando ✓"
    else
        print_warning "PostgreSQL: Parado"
    fi
    
    if docker-compose ps redis | grep -q "Up"; then
        print_message "Redis: Rodando ✓"
    else
        print_warning "Redis: Parado"
    fi
    
    # Verificar processos Node.js
    if [ -f .backend.pid ] && kill -0 $(cat .backend.pid) 2>/dev/null; then
        print_message "Backend: Rodando ✓"
    else
        print_warning "Backend: Parado"
    fi
    
    if [ -f .frontend.pid ] && kill -0 $(cat .frontend.pid) 2>/dev/null; then
        print_message "Frontend: Rodando ✓"
    else
        print_warning "Frontend: Parado"
    fi
    
    echo ""
    echo "URLs de desenvolvimento:"
    echo "  Frontend: http://localhost:3000"
    echo "  Backend: http://localhost:5000"
    echo "  Adminer: http://localhost:8080"
    echo "  Redis Commander: http://localhost:8081"
}

# Mostrar ajuda
show_help() {
    echo "Script de desenvolvimento do Sistema de Playlist Interativa"
    echo ""
    echo "Uso: $0 [comando]"
    echo ""
    echo "Comandos disponíveis:"
    echo "  start     - Iniciar ambiente de desenvolvimento completo"
    echo "  stop      - Parar todos os serviços"
    echo "  restart   - Reiniciar todos os serviços"
    echo "  logs      - Mostrar logs em tempo real"
    echo "  test      - Executar testes"
    echo "  lint      - Executar linting"
    echo "  reset     - Reset completo do ambiente"
    echo "  status    - Mostrar status dos serviços"
    echo "  help      - Mostrar esta ajuda"
    echo ""
    echo "Exemplos:"
    echo "  $0 start    # Iniciar desenvolvimento"
    echo "  $0 logs     # Ver logs"
    echo "  $0 stop     # Parar tudo"
}

# Função principal
main() {
    case "${1:-start}" in
        "start")
            print_header "INICIANDO AMBIENTE DE DESENVOLVIMENTO"
            check_node
            check_npm
            setup_dev_env
            install_backend_deps
            install_frontend_deps
            start_infrastructure
            run_migrations
            start_backend
            sleep 5
            start_frontend
            echo ""
            print_message "Ambiente de desenvolvimento iniciado!"
            show_status
            echo ""
            print_message "Execute '$0 logs' para ver os logs em tempo real"
            ;;
        "stop")
            print_header "PARANDO AMBIENTE DE DESENVOLVIMENTO"
            stop_dev_processes
            ;;
        "restart")
            print_header "REINICIANDO AMBIENTE DE DESENVOLVIMENTO"
            stop_dev_processes
            sleep 2
            main start
            ;;
        "logs")
            show_logs
            ;;
        "test")
            run_tests
            ;;
        "lint")
            run_lint
            ;;
        "reset")
            reset_dev
            ;;
        "status")
            show_status
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "Comando desconhecido: $1"
            show_help
            exit 1
            ;;
    esac
}

# Trap para limpeza em caso de interrupção
trap 'stop_dev_processes; exit 0' INT TERM

# Executar função principal
main "$@"
