import React, { useState, useEffect, useCallback, ChangeEvent } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, User, Music, Save } from "lucide-react";
import { toast } from "react-hot-toast";
import { buildApiUrl } from "@/config/api";

interface UserStats {
  totalSuggestions: number;
  totalVotes: number;
  approvedSuggestions: number;
  consecutiveDays: number;
  topVotedSongs: number;
  averageVotesPerSuggestion: number;
  favoriteGenres: string[];
  sessionDuration: number;
  pageViews: number;
  engagementLevel: string;
}

interface UserProfile {
  name: string;
  avatar?: string;
  joinedAt: string;
  level: number;
  experience: number;
  nextLevelExp: number;
  title: string;
  preferences: {
    favoriteGenres: string[];
    notifications: boolean;
    autoShare: boolean;
  };
}

interface ClientProfileProps {
  isOpen: boolean;
  onClose: () => void;
  restaurantId: string;
  sessionId: string;
}

const ClientProfile: React.FC<ClientProfileProps> = ({ isOpen, onClose, restaurantId, sessionId }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [profile, setProfile] = useState<UserProfile>({
    name: "",
    avatar: "",
    joinedAt: new Date().toISOString(),
    level: 1,
    experience: 0,
    nextLevelExp: 100,
    title: "Novo Ouvinte",
    preferences: { favoriteGenres: [], notifications: true, autoShare: false },
  });
  const [ranking, setRanking] = useState<Array<{ title: string; artist: string; points: number; voteCount: number; superVote5: number; superVote20: number; superVote40: number }>>([]);

  const loadProfile = useCallback(() => {
    setLoading(true);
    try {
      const savedProfile = localStorage.getItem(`clientProfile_${sessionId}`);
      if (savedProfile) {
        setProfile(JSON.parse(savedProfile));
      } else {
        const defaultProfile: UserProfile = {
          name: `Cliente ${sessionId.slice(0, 8)}`,
          avatar: "",
          joinedAt: new Date().toISOString(),
          level: 1,
          experience: 0,
          nextLevelExp: 100,
          title: "Novo Ouvinte",
          preferences: { favoriteGenres: [], notifications: true, autoShare: false },
        };
        setProfile(defaultProfile);
        localStorage.setItem(`clientProfile_${sessionId}`, JSON.stringify(defaultProfile));
      }
    } catch (error) {
      console.error("Error loading profile:", error);
    } finally {
      setLoading(false);
    }
  }, [sessionId]);


  // Carrega ranking das músicas mais votadas
  const loadRanking = useCallback(async () => {
    setLoading(true);
    try {
      // Tenta endpoint colaborativo, se não existir, usa dados mock
      const response = await fetch(buildApiUrl(`/collaborative-playlist/${restaurantId}/ranking`));
      if (response.ok) {
        const { ranking } = await response.json();
        setRanking(ranking);
      } else {
        throw new Error("Falha ao buscar ranking");
      }
    } catch (error) {
      console.error("Erro ao carregar ranking:", error);
      setRanking([
        { title: "Música Exemplo", artist: "Artista", points: 61, voteCount: 12, superVote5: 3, superVote20: 1, superVote40: 1 },
        { title: "Outra Música", artist: "Outro", points: 40, voteCount: 5, superVote5: 2, superVote20: 1, superVote40: 0 },
      ]);
      toast.error("Erro ao carregar ranking, usando dados de exemplo");
    } finally {
      setLoading(false);
    }
  }, [restaurantId]);

  const saveProfile = useCallback(() => {
    try {
      localStorage.setItem(`clientProfile_${sessionId}`, JSON.stringify(profile));
      setIsEditing(false);
      toast.success("Perfil salvo com sucesso!");
    } catch (error) {
      console.error("Error saving profile:", error);
      toast.error("Erro ao salvar perfil");
    }
  }, [profile, sessionId]);

  const handleAvatarUpload = useCallback((e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type.startsWith("image/")) {
      const reader = new FileReader();
      reader.onload = () => {
        setProfile((prev) => ({ ...prev, avatar: reader.result as string }));
      };
      reader.readAsDataURL(file);
    } else {
      toast.error("Por favor, selecione uma imagem válida");
    }
  }, []);


  const getLevelProgress = useCallback(() => {
    return Math.min((profile.experience / profile.nextLevelExp) * 100, 100);
  }, [profile.experience, profile.nextLevelExp]);

  const getEngagementColor = useCallback((level: string) => {
    const colorMap: { [key: string]: string } = {
      Iniciante: "text-gray-600 bg-gray-100 dark:bg-gray-700",
      Ativo: "text-blue-600 bg-blue-100 dark:bg-blue-900/30",
      Engajado: "text-green-600 bg-green-100 dark:bg-green-900/30",
      Expert: "text-purple-600 bg-purple-100 dark:bg-purple-900/30",
    };
    return colorMap[level] || colorMap.Iniciante;
  }, []);

  useEffect(() => {
    if (isOpen && sessionId) {
      loadProfile();
      loadRanking();
    }
  }, [isOpen, sessionId, loadProfile, loadRanking]);

  if (!isOpen) return null;


  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
        onClick={onClose}
        role="dialog"
        aria-modal="true"
        aria-labelledby="profile-modal-title"
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
          className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
        >
          <header className="bg-gradient-to-r from-purple-600 to-blue-600 p-4 sm:p-6 text-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                  {profile.avatar ? (
                    <img src={profile.avatar} alt={`Avatar de ${profile.name}`} className="w-full h-full rounded-full object-cover" />
                  ) : (
                    <User className="w-8 h-8" aria-hidden="true" />
                  )}
                </div>
                <div>
                  <h2 id="profile-modal-title" className="text-xl sm:text-2xl font-bold">{profile.name}</h2>
                  <p className="text-blue-100 text-sm">{profile.title}</p>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-sm">Nível {profile.level}</span>
                    <div className="w-24 h-2 bg-white/20 rounded-full">
                      <div
                        className="h-full bg-white rounded-full transition-all duration-300"
                        style={{ width: `${getLevelProgress()}%` }}
                        role="progressbar"
                        aria-valuenow={Math.round(getLevelProgress())}
                        aria-valuemin={0}
                        aria-valuemax={100}
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={onClose}
                  className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors"
                  aria-label="Fechar perfil"
                >
                  <X className="w-5 h-5" aria-hidden="true" />
                </button>
              </div>
            </div>
          </header>
          <div className="p-4 sm:p-6 max-h-[60vh] overflow-y-auto">
            {loading ? (
              <div className="text-center py-8">
                <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4" aria-hidden="true" />
                <p className="text-gray-500 dark:text-gray-400">Carregando perfil...</p>
              </div>
            ) : (
              <>
                <div className="space-y-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Nome</label>
                      {isEditing ? (
                        <input
                          type="text"
                          value={profile.name}
                          onChange={(e) => setProfile({ ...profile, name: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                          aria-label="Editar nome do perfil"
                        />
                      ) : (
                        <p className="text-gray-900 dark:text-white">{profile.name}</p>
                      )}
                    </div>
                    {isEditing && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Avatar</label>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleAvatarUpload}
                          className="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:bg-blue-100 file:text-blue-700 hover:file:bg-blue-200"
                          aria-label="Fazer upload de avatar"
                        />
                      </div>
                    )}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Membro desde</label>
                      <p className="text-gray-900 dark:text-white">
                        {new Date(profile.joinedAt).toLocaleDateString("pt-BR", {
                          day: "2-digit",
                          month: "long",
                          year: "numeric",
                        })}
                      </p>
                    </div>
                  </div>
                  <div className="flex justify-end items-center">
                    {isEditing ? (
                      <button
                        onClick={saveProfile}
                        className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                        aria-label="Salvar alterações do perfil"
                      >
                        <Save className="w-4 h-4" aria-hidden="true" />
                        <span>Salvar</span>
                      </button>
                    ) : (
                      <button
                        onClick={() => setIsEditing(true)}
                        className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        aria-label="Editar perfil"
                      >
                        <User className="w-4 h-4" aria-hidden="true" />
                        <span>Editar</span>
                      </button>
                    )}
                  </div>
                </div>
              </>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default ClientProfile;
