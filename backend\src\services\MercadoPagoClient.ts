import axios, { AxiosInstance } from "axios";
import { v4 as uuidv4 } from "uuid";

export interface CreatePixPaymentParams {
  amount: number; // em reais
  description: string;
  notificationUrl?: string;
  payer?: {
    email?: string;
    first_name?: string;
    last_name?: string;
  };
  metadata?: Record<string, any>;
}

export interface PixPaymentResponse {
  id: string; // payment id
  status: string; // pending, approved, rejected, cancelled
  transaction_amount: number;
  date_created?: string;
  date_approved?: string | null;
  point_of_interaction?: {
    transaction_data?: {
      qr_code?: string;
      qr_code_base64?: string;
      ticket_url?: string;
    };
  };
}

export class MercadoPagoClient {
  private http: AxiosInstance;

  constructor(private accessToken: string, private integratorId?: string) {
    this.http = axios.create({
      baseURL: "https://api.mercadopago.com",
      timeout: 10000,
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
        "Content-Type": "application/json",
        ...(integratorId ? { "x-integrator-id": integratorId } : {}),
      },
    });
  }

  async createPixPayment(params: CreatePixPaymentParams) {
    const body: any = {
      transaction_amount: Number(params.amount.toFixed(2)),
      description: params.description,
      payment_method_id: "pix",
      metadata: params.metadata || {},
    };

    // Só adicionar payer se tiver dados válidos
    if (params.payer && (params.payer.email || params.payer.first_name)) {
      body.payer = params.payer;
    }

    if (params.notificationUrl) {
      body.notification_url = params.notificationUrl;
    }

    // Gerar chave de idempotência (recomendada/necessária pela API)
    const idempotencyKey = `${params.metadata?.suggestionId || params.metadata?.youtubeId || "pix"}-${uuidv4()}`;

    console.log("🔥 MercadoPago Request:", JSON.stringify({
      ...body,
      // não logar cabeçalhos sensíveis; apenas indicar que a idempotência está presente
      _meta: { hasIdempotencyKey: true }
    }, null, 2));

    try {
      const { data } = await this.http.post("/v1/payments", body, {
        headers: {
          "X-Idempotency-Key": idempotencyKey,
        },
      });
      console.log("✅ MercadoPago Response:", JSON.stringify(data, null, 2));
      return data as PixPaymentResponse;
    } catch (error: any) {
      console.error("❌ MercadoPago Error:", {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
      });
      throw error;
    }
  }

  async getPayment(paymentId: string) {
    const { data } = await this.http.get(`/v1/payments/${paymentId}`);
    return data as PixPaymentResponse;
  }
}
