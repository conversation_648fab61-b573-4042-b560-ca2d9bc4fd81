-- Criação idempotente da tabela genres com colunas principais e seeds básicos


CREATE TABLE IF NOT EXISTS genres (
	id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
	name VARCHAR(100) UNIQUE NOT NULL,
	display_name VARCHAR(100),
	description VARCHAR(200),
	category VARCHAR(50) DEFAULT 'music',
	color VARCHAR(7) DEFAULT '#3B82F6',
	icon VARCHAR(50),
	metadata JSONB,
	priority INTEGER DEFAULT 0,
	is_active BOOLEAN DEFAULT true,
	is_default BOOLEAN DEFAULT false,
	usage_count INTEGER DEFAULT 0,
	created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
	updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- Índices
CREATE INDEX IF NOT EXISTS idx_genres_name ON genres(name);
CREATE INDEX IF NOT EXISTS idx_genres_category ON genres(category);
CREATE INDEX IF NOT EXISTS idx_genres_is_active ON genres(is_active);

-- Seeds básicos (não-duplicar)
INSERT INTO genres (name, display_name, description, category, is_default)
VALUES
	('Pop', 'Pop', 'Música popular', 'music', true),
	('Rock', 'Rock', 'Rock e suas variações', 'music', true),
	('Sertanejo', 'Sertanejo', 'Música sertaneja brasileira', 'music', true),
	('Funk', 'Funk', 'Funk brasileiro', 'music', true),
	('MPB', 'MPB', 'Música Popular Brasileira', 'music', true),
	('Eletrônica', 'Eletrônica', 'Música eletrônica', 'music', true),
	('Hip Hop', 'Hip Hop', 'Hip hop e rap', 'music', true),
	('Reggae', 'Reggae', 'Reggae e variações', 'music', true),
	('Jazz', 'Jazz', 'Jazz e blues', 'music', true),
	('Clássica', 'Clássica', 'Música clássica', 'music', true)
ON CONFLICT (name) DO NOTHING;

