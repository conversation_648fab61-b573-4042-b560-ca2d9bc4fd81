-- C<PERSON>r tabelas faltantes para o sistema de playlist

-- Tabela de agendamento de playlists (horários de funcionamento)
CREATE TABLE IF NOT EXISTS playlist_schedules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    restaurant_id VARCHAR(255) NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    "timeSlots" JSON NOT NULL,
    "isActive" BOOLEAN DEFAULT true,
    mode VARCHAR(50) DEFAULT 'normal' CHECK (mode IN ('normal', 'shuffle', 'repeat', 'smart')),
    settings JSON,
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- Tabela de faixas/músicas das playlists
CREATE TABLE IF NOT EXISTS playlist_tracks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    playlist_id UUID NOT NULL REFERENCES playlists(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    artist VARCHAR(200) NOT NULL,
    "youtubeVideoId" VARCHAR(50) NOT NULL,
    "thumbnailUrl" VARCHAR(500),
    duration INTEGER DEFAULT 0,
    position INTEGER DEFAULT 0,
    "isActive" BOOLEAN DEFAULT true,
    "isExplicit" BOOLEAN DEFAULT false,
    "isLive" BOOLEAN DEFAULT false,
    genre VARCHAR(100),
    mood VARCHAR(100),
    energy INTEGER DEFAULT 5 CHECK (energy >= 1 AND energy <= 10),
    "bpmRange" VARCHAR(20),
    language VARCHAR(10),
    "releaseYear" INTEGER,
    "addedBy" UUID REFERENCES users(id) ON DELETE SET NULL,
    "addedAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    "lastPlayedAt" TIMESTAMP WITHOUT TIME ZONE,
    "playCount" INTEGER DEFAULT 0,
    "skipCount" INTEGER DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.00,
    "ratingCount" INTEGER DEFAULT 0,
    tags JSON,
    metadata JSON,
    "moderationStatus" VARCHAR(20) DEFAULT 'approved' CHECK ("moderationStatus" IN ('pending', 'approved', 'rejected')),
    "moderatedBy" UUID REFERENCES users(id) ON DELETE SET NULL,
    "moderatedAt" TIMESTAMP WITHOUT TIME ZONE,
    "moderationReason" TEXT,
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_playlist_schedules_restaurant ON playlist_schedules(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_playlist_schedules_active ON playlist_schedules("isActive");

CREATE INDEX IF NOT EXISTS idx_playlist_tracks_playlist ON playlist_tracks(playlist_id);
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_position ON playlist_tracks(playlist_id, position);
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_youtube ON playlist_tracks("youtubeVideoId");
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_active ON playlist_tracks("isActive");
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_genre ON playlist_tracks(genre);
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_mood ON playlist_tracks(mood);

-- Dados de exemplo serão inseridos pelo script seed-data.sql após a criação dos restaurantes

SELECT 'Tabelas criadas e dados inseridos com sucesso!' as status;
