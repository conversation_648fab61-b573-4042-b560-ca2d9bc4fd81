-- ============================================
-- FULL BOOTSTRAP (idempotente) - Banco completo + ajustes + índices + seeds opcionais
-- Consolida init + migrações essenciais + correções de colunas + entidades novas
-- Seguro para reexecução (IF NOT EXISTS / DO $$ ... $$)
-- ============================================

-- Extensões e utilitários
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

CREATE OR REPLACE FUNCTION gen_random_uuid() RETURNS UUID AS $$
BEGIN
  RETURN uuid_generate_v4();
END;$$ LANGUAGE plpgsql;

-- ENUNS
DO $$ BEGIN
  CREATE TYPE user_role AS ENUM ('super_admin','admin','moderator','staff');
EXCEPTION WHEN duplicate_object THEN NULL; END $$;
DO $$ BEGIN
  CREATE TYPE restaurant_status AS ENUM ('active','inactive','suspended','trial');
EXCEPTION WHEN duplicate_object THEN NULL; END $$;
DO $$ BEGIN
  CREATE TYPE playlist_type AS ENUM ('custom','youtube_import','auto_generated','suggestions');
EXCEPTION WHEN duplicate_object THEN NULL; END $$;
-- Garantir que o enum playlist_type tenha o valor 'youtube' usado nas seeds
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_type t
    JOIN pg_enum e ON t.oid = e.enumtypid
    WHERE t.typname = 'playlist_type' AND e.enumlabel = 'youtube'
  ) THEN
    ALTER TYPE playlist_type ADD VALUE IF NOT EXISTS 'youtube';
  END IF;
EXCEPTION WHEN duplicate_object THEN
  -- ignorar corrida
END $$;
DO $$ BEGIN
  CREATE TYPE playlist_status AS ENUM ('active','inactive','archived');
EXCEPTION WHEN duplicate_object THEN NULL; END $$;
DO $$ BEGIN
  CREATE TYPE suggestion_status AS ENUM ('pending','approved','rejected','playing','played','skipped');
EXCEPTION WHEN duplicate_object THEN NULL; END $$;
DO $$ BEGIN
  CREATE TYPE suggestion_source AS ENUM ('client','admin','auto','import');
EXCEPTION WHEN duplicate_object THEN NULL; END $$;
DO $$ BEGIN
  CREATE TYPE rule_type AS ENUM ('blacklist_word','blacklist_artist','blacklist_channel','genre_restriction','duration_limit','content_rating','language_filter','time_restriction');
EXCEPTION WHEN duplicate_object THEN NULL; END $$;
DO $$ BEGIN
  CREATE TYPE rule_action AS ENUM ('auto_reject','flag_for_review','require_approval','auto_approve');
EXCEPTION WHEN duplicate_object THEN NULL; END $$;
DO $$ BEGIN
  CREATE TYPE play_status AS ENUM ('completed','skipped','interrupted','error');
EXCEPTION WHEN duplicate_object THEN NULL; END $$;

-- Adição de 'completed' ao enum suggestion_status (de fix_suggestions_columns.sql)
DO $$
BEGIN
	IF NOT EXISTS (
		SELECT 1 FROM pg_type t
		JOIN pg_enum e ON t.oid = e.enumtypid
		WHERE t.typname = 'suggestion_status' AND e.enumlabel = 'completed'
	) THEN
		ALTER TYPE suggestion_status ADD VALUE IF NOT EXISTS 'completed';
	END IF;
EXCEPTION WHEN duplicate_object THEN
	-- ignore concurrent add
END $$;

-- FKs de queue_items (idempotente)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name='queue_items') THEN
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.table_constraints WHERE table_name='queue_items' AND constraint_name='fk_queue_items_restaurant'
    ) THEN
      ALTER TABLE queue_items ADD CONSTRAINT fk_queue_items_restaurant FOREIGN KEY (restaurant_id) REFERENCES restaurants(id) ON DELETE CASCADE;
    END IF;
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.table_constraints WHERE table_name='queue_items' AND constraint_name='fk_queue_items_suggestion'
    ) THEN
      ALTER TABLE queue_items ADD CONSTRAINT fk_queue_items_suggestion FOREIGN KEY (suggestion_id) REFERENCES suggestions(id) ON DELETE CASCADE;
    END IF;
    IF EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='queue_items' AND column_name='session_id'
    ) AND NOT EXISTS (
      SELECT 1 FROM information_schema.table_constraints WHERE table_name='queue_items' AND constraint_name='fk_queue_items_session'
    ) THEN
      ALTER TABLE queue_items ADD CONSTRAINT fk_queue_items_session FOREIGN KEY (session_id) REFERENCES client_sessions(id) ON DELETE SET NULL;
    END IF;
  END IF;
END $$;

-- Função de trigger genérica
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW."updatedAt" = NOW();
  RETURN NEW;
END;$$ LANGUAGE plpgsql;

-- Tabelas principais (baseado em database/init.sql)
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  role user_role DEFAULT 'staff',
  "isActive" BOOLEAN DEFAULT TRUE,
  "emailVerified" BOOLEAN DEFAULT FALSE,
  "emailVerifiedAt" TIMESTAMP,
  "lastLoginAt" TIMESTAMP,
  "loginCount" INTEGER DEFAULT 0,
  "resetPasswordToken" VARCHAR(255),
  "resetPasswordExpires" TIMESTAMP,
  "twoFactorSecret" VARCHAR(255),
  "twoFactorEnabled" BOOLEAN DEFAULT FALSE,
  preferences JSON DEFAULT '{}',
  metadata JSON DEFAULT '{}',
  "createdAt" TIMESTAMP DEFAULT NOW(),
  "updatedAt" TIMESTAMP DEFAULT NOW()
);

-- Users: colunas opcionais esperadas pelo backend
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name='users') THEN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='users' AND column_name='phone') THEN
      ALTER TABLE users ADD COLUMN phone VARCHAR;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='users' AND column_name='avatar') THEN
      ALTER TABLE users ADD COLUMN avatar TEXT;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='users' AND column_name='lastLoginIp') THEN
      ALTER TABLE users ADD COLUMN "lastLoginIp" INET;
    END IF;
  END IF;
END $$;

CREATE TABLE IF NOT EXISTS restaurants (
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(200) NOT NULL,
  description TEXT,
  phone VARCHAR(20),
  address JSON DEFAULT '{}'::json,
  status restaurant_status DEFAULT 'trial',
  settings JSON DEFAULT '{}',
  language VARCHAR(10) DEFAULT 'pt-BR',
  timezone VARCHAR(50) DEFAULT 'America/Sao_Paulo',
  "isActive" BOOLEAN DEFAULT TRUE,
  "subscriptionPlan" VARCHAR(50) DEFAULT 'free',
  "subscriptionExpires" TIMESTAMP,
  "maxUsers" INTEGER DEFAULT 5,
  "maxPlaylists" INTEGER DEFAULT 3,
  "maxSuggestions" INTEGER DEFAULT 100,
  "storageUsed" BIGINT DEFAULT 0,
  "storageLimit" BIGINT DEFAULT 1073741824,
  "apiUsage" JSON DEFAULT '{}',
  "lastActivity" TIMESTAMP,
  metadata JSON DEFAULT '{}',
  "createdAt" TIMESTAMP DEFAULT NOW(),
  "updatedAt" TIMESTAMP DEFAULT NOW()
);

-- Restaurants: colunas opcionais esperadas pelo backend/seed
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name='restaurants') THEN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='restaurants' AND column_name='logo') THEN
      ALTER TABLE restaurants ADD COLUMN logo VARCHAR NULL;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='restaurants' AND column_name='email') THEN
      ALTER TABLE restaurants ADD COLUMN email VARCHAR NULL;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='restaurants' AND column_name='website') THEN
      ALTER TABLE restaurants ADD COLUMN website VARCHAR NULL;
    END IF;
    -- address: garantir tipo JSON (se já existir como TEXT em bancos antigos)
    IF EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='restaurants' AND column_name='address' AND data_type='text'
    ) THEN
      ALTER TABLE restaurants ALTER COLUMN address TYPE JSON USING '{}'::json;
    END IF;
    -- Campos de YouTube e sociais
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='restaurants' AND column_name='youtubeChannelId') THEN
      ALTER TABLE restaurants ADD COLUMN "youtubeChannelId" VARCHAR NULL;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='restaurants' AND column_name='youtubePremiumToken') THEN
      ALTER TABLE restaurants ADD COLUMN "youtubePremiumToken" TEXT NULL;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='restaurants' AND column_name='youtubeCredentials') THEN
      ALTER TABLE restaurants ADD COLUMN "youtubeCredentials" JSON NULL;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='restaurants' AND column_name='socialMedia') THEN
      ALTER TABLE restaurants ADD COLUMN "socialMedia" JSON NULL;
    END IF;
    -- Campos de timeline e horários
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='restaurants' AND column_name='trialExpiresAt') THEN
      ALTER TABLE restaurants ADD COLUMN "trialExpiresAt" TIMESTAMP NULL;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='restaurants' AND column_name='lastActivityAt') THEN
      ALTER TABLE restaurants ADD COLUMN "lastActivityAt" TIMESTAMP NULL;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='restaurants' AND column_name='businessHours') THEN
      ALTER TABLE restaurants ADD COLUMN "businessHours" JSON NULL;
    END IF;
  END IF;
END $$;

CREATE TABLE IF NOT EXISTS playlists (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(200) NOT NULL,
  description TEXT,
  restaurant_id VARCHAR(255) NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
  type playlist_type DEFAULT 'custom',
  status playlist_status DEFAULT 'active',
  "isPublic" BOOLEAN DEFAULT FALSE,
  "isDefault" BOOLEAN DEFAULT FALSE,
  "trackCount" INTEGER DEFAULT 0,
  "totalDuration" INTEGER DEFAULT 0,
  "playCount" INTEGER DEFAULT 0,
  "lastPlayedAt" TIMESTAMP,
  execution_order INTEGER DEFAULT NULL,
  settings JSON DEFAULT '{}',
  metadata JSON DEFAULT '{}',
  "createdBy" UUID REFERENCES users(id) ON DELETE SET NULL,
  "createdAt" TIMESTAMP DEFAULT NOW(),
  "updatedAt" TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS suggestions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  -- IDs e metadados principais
  "youtubeVideoId" VARCHAR(50) NOT NULL, -- normalizado para youtube_video_id abaixo
  youtube_video_id VARCHAR(100),
  title VARCHAR(300) NOT NULL,
  artist VARCHAR(200),
  -- Canal/thumbnail (camelCase mantido para legados; snake criado abaixo)
  "channelName" VARCHAR(200),
  channel_name VARCHAR(200),
  duration INTEGER DEFAULT 0,
  "thumbnailUrl" VARCHAR(500),
  thumbnail_url VARCHAR(500),
  description TEXT,
  genre VARCHAR,
  -- Status e origem
  status suggestion_status DEFAULT 'pending',
  source suggestion_source DEFAULT 'client',
  -- Votos
  "voteCount" INTEGER DEFAULT 0,
  vote_count INTEGER DEFAULT 0,
  votes_count INTEGER DEFAULT 0,
  upvotes INTEGER DEFAULT 0,
  downvotes INTEGER DEFAULT 0,
  -- Fila/execução
  "queuePosition" INTEGER,
  queue_position INTEGER,
  "playedAt" TIMESTAMP,
  played_at TIMESTAMP,
  play_duration INTEGER,
  "skipReason" VARCHAR(100),
  skip_reason TEXT,
  -- Relacionamentos
  restaurant_id VARCHAR(255) NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
  playlist_id UUID NULL,
  -- Sessões/cliente
  "sessionId" UUID,
  session_id UUID NULL,
  "clientIp" INET,
  client_ip INET,
  "userAgent" TEXT,
  client_user_agent TEXT,
  client_name VARCHAR(100),
  table_number INTEGER,
  client_session_id VARCHAR NULL,
  -- Autoria/moderação
  "suggestedBy" VARCHAR(100),
  suggested_by UUID NULL,
  "moderatedBy" UUID REFERENCES users(id) ON DELETE SET NULL,
  moderated_by UUID NULL REFERENCES users(id) ON DELETE SET NULL,
  "moderatedAt" TIMESTAMP,
  moderated_at TIMESTAMP,
  "moderationReason" TEXT,
  moderation_reason TEXT,
  moderation_flags JSON,
  rejection_reason TEXT,
  -- Pagamentos PIX
  is_priority BOOLEAN DEFAULT FALSE,
  is_paid BOOLEAN DEFAULT FALSE,
  paid_at TIMESTAMP,
  payment_amount DECIMAL(10,2),
  payment_id VARCHAR,
  payment_status VARCHAR(20) DEFAULT 'pending',
  pix_code TEXT,
  -- Metadados
  metadata JSON DEFAULT '{}',
  -- Datas
  "createdAt" TIMESTAMP DEFAULT NOW(),
  "updatedAt" TIMESTAMP DEFAULT NOW(),
  created_at TIMESTAMP,
  updated_at TIMESTAMP,
  completed_at TIMESTAMP
);

-- Normalizações/ajustes idempotentes para suggestions (consolidando migrações 010–019)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name='suggestions') THEN
    -- Preencher snake_case a partir de camelCase se necessário
    IF EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='youtubeVideoId'
    ) AND EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='youtube_video_id'
    ) THEN
      UPDATE suggestions SET youtube_video_id = COALESCE(youtube_video_id, "youtubeVideoId");
    END IF;
    IF EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='channelName'
    ) AND EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='channel_name'
    ) THEN
      UPDATE suggestions SET channel_name = COALESCE(channel_name, "channelName");
    END IF;
    IF EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='thumbnailUrl'
    ) AND EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='thumbnail_url'
    ) THEN
      UPDATE suggestions SET thumbnail_url = COALESCE(thumbnail_url, "thumbnailUrl");
    END IF;
    IF EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='clientIp'
    ) AND EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='client_ip'
    ) THEN
      UPDATE suggestions SET client_ip = COALESCE(client_ip, "clientIp");
    END IF;
    IF EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='userAgent'
    ) AND EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='client_user_agent'
    ) THEN
      UPDATE suggestions SET client_user_agent = COALESCE(client_user_agent, "userAgent");
    END IF;
    IF EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='sessionId'
    ) AND EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='session_id'
    ) THEN
      UPDATE suggestions SET session_id = COALESCE(session_id, "sessionId");
    END IF;
    IF EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='playedAt'
    ) AND EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='played_at'
    ) THEN
      UPDATE suggestions SET played_at = COALESCE(played_at, "playedAt");
    END IF;
    IF EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='skipReason'
    ) AND EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='skip_reason'
    ) THEN
      UPDATE suggestions SET skip_reason = COALESCE(skip_reason, "skipReason");
    END IF;
    IF EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='moderatedAt'
    ) AND EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='moderated_at'
    ) THEN
      UPDATE suggestions SET moderated_at = COALESCE(moderated_at, "moderatedAt");
    END IF;
    IF EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='moderationReason'
    ) AND EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='moderation_reason'
    ) THEN
      UPDATE suggestions SET moderation_reason = COALESCE(moderation_reason, "moderationReason");
    END IF;

    -- Índices úteis
    CREATE INDEX IF NOT EXISTS idx_suggestions_youtube_video_id ON suggestions(youtube_video_id);
    CREATE INDEX IF NOT EXISTS idx_suggestions_created_at ON suggestions(created_at);
    CREATE INDEX IF NOT EXISTS idx_suggestions_channel_name ON suggestions(channel_name);
    CREATE INDEX IF NOT EXISTS idx_suggestions_thumbnail_url ON suggestions(thumbnail_url);
    CREATE INDEX IF NOT EXISTS idx_suggestions_vote_count ON suggestions(vote_count);

    -- FK playlist
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.table_constraints WHERE table_name='suggestions' AND constraint_name='fk_suggestions_playlist'
    ) THEN
      ALTER TABLE suggestions ADD CONSTRAINT fk_suggestions_playlist FOREIGN KEY (playlist_id) REFERENCES playlists(id) ON DELETE SET NULL;
    END IF;

    -- FK session_id -> client_sessions (somente se a tabela já existir)
    IF EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='session_id'
    ) AND EXISTS (
      SELECT 1 FROM information_schema.tables WHERE table_name='client_sessions'
    ) AND NOT EXISTS (
      SELECT 1 FROM information_schema.table_constraints WHERE table_name='suggestions' AND constraint_name='fk_suggestions_session'
    ) THEN
      ALTER TABLE suggestions ADD CONSTRAINT fk_suggestions_session FOREIGN KEY (session_id) REFERENCES client_sessions(id) ON DELETE SET NULL;
    END IF;

    -- FKs moderated_by/suggested_by -> users
    IF EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='moderated_by'
    ) AND NOT EXISTS (
      SELECT 1 FROM information_schema.table_constraints WHERE table_name='suggestions' AND constraint_name='fk_suggestions_moderated_by'
    ) THEN
      ALTER TABLE suggestions ADD CONSTRAINT fk_suggestions_moderated_by FOREIGN KEY (moderated_by) REFERENCES users(id) ON DELETE SET NULL;
    END IF;
    IF EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='suggested_by'
    ) AND NOT EXISTS (
      SELECT 1 FROM information_schema.table_constraints WHERE table_name='suggestions' AND constraint_name='fk_suggestions_suggested_by'
    ) THEN
      ALTER TABLE suggestions ADD CONSTRAINT fk_suggestions_suggested_by FOREIGN KEY (suggested_by) REFERENCES users(id) ON DELETE SET NULL;
    END IF;
  END IF;
END $$;

CREATE TABLE IF NOT EXISTS client_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "sessionToken" VARCHAR(255) UNIQUE NOT NULL,
  "ipAddress" INET,
  "userAgent" TEXT,
  "deviceInfo" JSON,
  location JSON,
  "lastActivity" TIMESTAMP DEFAULT NOW(),
  "suggestionsCount" INTEGER DEFAULT 0,
  "votesCount" INTEGER DEFAULT 0,
  "pageViews" INTEGER DEFAULT 0,
  metadata JSON DEFAULT '{}',
  "createdAt" TIMESTAMP DEFAULT NOW(),
  "updatedAt" TIMESTAMP DEFAULT NOW()
);

-- Tabelas adicionais
CREATE TABLE IF NOT EXISTS genres (
	id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
	name VARCHAR(100) UNIQUE NOT NULL,
	display_name VARCHAR(100),
	description VARCHAR(200),
	category VARCHAR(50) DEFAULT 'music',
	color VARCHAR(7) DEFAULT '#3B82F6',
	icon VARCHAR(50),
	metadata JSONB,
	priority INTEGER DEFAULT 0,
	is_active BOOLEAN DEFAULT true,
	is_default BOOLEAN DEFAULT false,
	usage_count INTEGER DEFAULT 0,
	created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
	updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- Versão simples de genres se preferir (mas usamos a detalhada; este é para compat)
-- Ignorar create_genres_table_simple.sql pois detalhada já existe

CREATE TABLE IF NOT EXISTS suggestion_genres (
	suggestion_id UUID NOT NULL REFERENCES suggestions(id) ON DELETE CASCADE,
	genre_id UUID NOT NULL REFERENCES genres(id) ON DELETE CASCADE,
	PRIMARY KEY (suggestion_id, genre_id)
);

-- Atualizar payments para versão mais completa (de create_payments_table.sql)
DROP TABLE IF EXISTS payments; -- Remover versão antiga se conflitar; mas como idempotente, melhor recriar
CREATE TABLE IF NOT EXISTS payments (
  id VARCHAR PRIMARY KEY,
  suggestion_id UUID NOT NULL,
  session_id UUID NOT NULL,
  amount INTEGER NOT NULL,
  status VARCHAR NOT NULL DEFAULT 'pending',
  status_detail VARCHAR NULL,
  payment_method VARCHAR NOT NULL DEFAULT 'pix',
  external_reference VARCHAR NULL,
  qr_code TEXT NULL,
  qr_code_base64 TEXT NULL,
  ticket_url VARCHAR NULL,
  created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
  approved_at TIMESTAMP WITHOUT TIME ZONE NULL,
  expires_at TIMESTAMP WITHOUT TIME ZONE NULL,
  payer_email VARCHAR NULL,
  payer_name VARCHAR NULL,
  platform_fee INTEGER NOT NULL DEFAULT 60,
  restaurant_amount INTEGER NOT NULL DEFAULT 140,
  metadata JSON NULL
);

CREATE TABLE IF NOT EXISTS queue_items (
	id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
	restaurant_id VARCHAR(255) NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
	suggestion_id UUID REFERENCES suggestions(id) ON DELETE SET NULL,
  session_id UUID NULL REFERENCES client_sessions(id) ON DELETE SET NULL,
	position INTEGER NOT NULL,
	status play_status,
  priority INTEGER DEFAULT 2,
  is_playing BOOLEAN DEFAULT FALSE,
	play_start TIMESTAMP,
	play_end TIMESTAMP,
	metadata JSON DEFAULT '{}',
	created_at TIMESTAMP DEFAULT NOW(),
	updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS qr_codes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  restaurant_id VARCHAR(255) NOT NULL,
  type VARCHAR NOT NULL,
  name VARCHAR NOT NULL,
  table_number VARCHAR NULL,
  url VARCHAR NOT NULL,
  qr_code_data TEXT NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);


-- Fixes adicionais: suggestions.playlist_id + FK (20250818_fix_playlists_and_suggestions_fk.sql)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name='suggestions') THEN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='playlist_id') THEN
      ALTER TABLE suggestions ADD COLUMN playlist_id UUID;
    END IF;
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.table_constraints WHERE table_name='suggestions' AND constraint_name='fk_suggestions_playlist'
    ) THEN
      ALTER TABLE suggestions ADD CONSTRAINT fk_suggestions_playlist FOREIGN KEY (playlist_id) REFERENCES playlists(id) ON DELETE SET NULL;
    END IF;
  END IF;
END $$;

-- Align qr_codes + suggestions.genre (20250818_fix_columns_qrcodes_suggestions.sql)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'qr_codes') THEN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='qr_codes' AND column_name='type') THEN
      ALTER TABLE qr_codes ADD COLUMN type VARCHAR;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='qr_codes' AND column_name='name') THEN
      ALTER TABLE qr_codes ADD COLUMN name VARCHAR;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='qr_codes' AND column_name='table_number') THEN
      ALTER TABLE qr_codes ADD COLUMN table_number VARCHAR;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='qr_codes' AND column_name='url') THEN
      ALTER TABLE qr_codes ADD COLUMN url VARCHAR;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='qr_codes' AND column_name='qr_code_data') THEN
      ALTER TABLE qr_codes ADD COLUMN qr_code_data TEXT;
    END IF;
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='qr_codes' AND column_name='code') THEN
      UPDATE qr_codes SET qr_code_data = COALESCE(qr_code_data, code) WHERE qr_code_data IS NULL AND code IS NOT NULL;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='qr_codes' AND column_name='is_active') THEN
      ALTER TABLE qr_codes ADD COLUMN is_active BOOLEAN DEFAULT TRUE;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='qr_codes' AND column_name='updated_at') THEN
      ALTER TABLE qr_codes ADD COLUMN updated_at TIMESTAMP;
    END IF;
  END IF;
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'suggestions') THEN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='genre') THEN
      ALTER TABLE suggestions ADD COLUMN genre VARCHAR;
    END IF;
  END IF;
END $$;
CREATE INDEX IF NOT EXISTS idx_qr_codes_active ON qr_codes(is_active);
CREATE INDEX IF NOT EXISTS idx_qr_codes_restaurant ON qr_codes(restaurant_id);

-- YouTube IDs normalization (20250818_fix_playlists_youtube_id.sql)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name='suggestions') THEN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='youtube_video_id')
       AND EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='youtubeVideoId') THEN
      ALTER TABLE suggestions ADD COLUMN youtube_video_id VARCHAR(100);
      UPDATE suggestions SET youtube_video_id = "youtubeVideoId" WHERE youtube_video_id IS NULL;
    END IF;
    CREATE INDEX IF NOT EXISTS idx_suggestions_youtube_snake ON suggestions(youtube_video_id);
  END IF;
END $$;
DO $$ BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name='play_history') THEN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='play_history' AND column_name='youtubeVideoId') THEN
      ALTER TABLE play_history ADD COLUMN "youtubeVideoId" VARCHAR(50);
    END IF;
    CREATE INDEX IF NOT EXISTS idx_play_history_youtube ON play_history("youtubeVideoId");
  END IF;
END $$;

-- Client sessions missing columns (20250818_fix_clientsession_columns.sql)
DO $$
BEGIN
  -- points
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_name = 'client_sessions' AND column_name = 'points'
  ) THEN
    ALTER TABLE client_sessions ADD COLUMN points INTEGER NOT NULL DEFAULT 0;
  END IF;

  -- pageViews: prefer camelCase "pageViews"; rename/drop lowercase if exists
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_name = 'client_sessions' AND column_name = 'pageViews'
  ) THEN
    IF EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name = 'client_sessions' AND column_name = 'pageviews'
    ) THEN
      ALTER TABLE client_sessions RENAME COLUMN pageviews TO "pageViews";
    ELSE
      ALTER TABLE client_sessions ADD COLUMN "pageViews" INTEGER NOT NULL DEFAULT 0;
    END IF;
  END IF;

  -- sessionDuration: prefer camelCase "sessionDuration"; migrate from lowercase if present
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_name = 'client_sessions' AND column_name = 'sessionDuration'
  ) THEN
    IF EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name = 'client_sessions' AND column_name = 'sessionduration'
    ) THEN
      ALTER TABLE client_sessions RENAME COLUMN sessionduration TO "sessionDuration";
    ELSE
      ALTER TABLE client_sessions ADD COLUMN "sessionDuration" INTEGER NOT NULL DEFAULT 0;
    END IF;
  END IF;

  -- isActive
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_name = 'client_sessions' AND column_name = 'isActive'
  ) THEN
    ALTER TABLE client_sessions ADD COLUMN "isActive" BOOLEAN NOT NULL DEFAULT TRUE;
  END IF;

  -- tableNumber/clientName (armazenados em minúsculo conforme entidade)
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_name = 'client_sessions' AND column_name = 'tablenumber'
  ) THEN
    ALTER TABLE client_sessions ADD COLUMN tablenumber VARCHAR NULL;
  END IF;
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_name = 'client_sessions' AND column_name = 'clientname'
  ) THEN
    ALTER TABLE client_sessions ADD COLUMN clientname VARCHAR NULL;
  END IF;

  -- preferences/activityLog JSON
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_name = 'client_sessions' AND column_name = 'preferences'
  ) THEN
    ALTER TABLE client_sessions ADD COLUMN preferences JSON NULL;
  END IF;
  -- activityLog deve ser camelCase (com aspas). Se existir activitylog minúsculo, renomear
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_name = 'client_sessions' AND column_name = 'activityLog'
  ) THEN
    IF EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name = 'client_sessions' AND column_name = 'activitylog'
    ) THEN
      ALTER TABLE client_sessions RENAME COLUMN activitylog TO "activityLog";
    ELSE
      ALTER TABLE client_sessions ADD COLUMN "activityLog" JSON NULL;
    END IF;
  END IF;

  -- restaurant_id + FK
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_name = 'client_sessions' AND column_name = 'restaurant_id'
  ) THEN
    ALTER TABLE client_sessions ADD COLUMN restaurant_id VARCHAR(255) NULL;
  END IF;
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints WHERE table_name='client_sessions' AND constraint_name='fk_client_sessions_restaurant'
  ) THEN
    ALTER TABLE client_sessions
      ADD CONSTRAINT fk_client_sessions_restaurant
      FOREIGN KEY (restaurant_id) REFERENCES restaurants(id) ON DELETE CASCADE;
  END IF;
END $$;

-- Playlist tracks indexes (serão criados mais abaixo, após a criação da tabela)

-- Votes (tabela mínima para contadores)
CREATE TABLE IF NOT EXISTS votes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  suggestion_id UUID NOT NULL REFERENCES suggestions(id) ON DELETE CASCADE,
  -- identificadores de sessão/mesa/cliente
  client_session_id VARCHAR NULL,
  session_id UUID NULL REFERENCES client_sessions(id) ON DELETE SET NULL,
  table_number INTEGER NULL,
  client_ip INET NULL,
  client_user_agent TEXT NULL,
  user_id UUID NULL REFERENCES users(id) ON DELETE SET NULL,
  vote_type VARCHAR(10) NOT NULL CHECK (vote_type IN ('up','down')),
  weight INTEGER DEFAULT 1,
  created_at TIMESTAMP DEFAULT NOW()
);
CREATE INDEX IF NOT EXISTS idx_votes_suggestion ON votes(suggestion_id);
CREATE INDEX IF NOT EXISTS idx_votes_client_session_id ON votes(client_session_id);
CREATE INDEX IF NOT EXISTS idx_votes_created_at ON votes(created_at);
DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint WHERE conname='uq_vote_suggestion_client_session'
  ) THEN
    ALTER TABLE votes
      ADD CONSTRAINT uq_vote_suggestion_client_session
      UNIQUE (suggestion_id, client_session_id);
  END IF;
END $$;

-- Votes counters trigger (20250818_votes_counters.sql)
CREATE OR REPLACE FUNCTION update_suggestion_vote_counts()
RETURNS TRIGGER AS $fn$
DECLARE tgt UUID;
BEGIN
  IF (TG_OP = 'INSERT') THEN
    tgt := NEW.suggestion_id;
  ELSIF (TG_OP = 'UPDATE') THEN
    IF NEW.suggestion_id IS DISTINCT FROM OLD.suggestion_id THEN
      IF OLD.suggestion_id IS NOT NULL THEN
        UPDATE suggestions s SET
          upvotes = (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = OLD.suggestion_id AND v.vote_type = 'up'),
          downvotes = (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = OLD.suggestion_id AND v.vote_type = 'down'),
          vote_count = (
            (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = OLD.suggestion_id AND v.vote_type = 'up') -
            (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = OLD.suggestion_id AND v.vote_type = 'down')
          ),
          "voteCount" = (
            (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = OLD.suggestion_id AND v.vote_type = 'up') -
            (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = OLD.suggestion_id AND v.vote_type = 'down')
          )
        WHERE s.id = OLD.suggestion_id;
      END IF;
      tgt := NEW.suggestion_id;
    ELSE
      tgt := NEW.suggestion_id;
    END IF;
  ELSIF (TG_OP = 'DELETE') THEN
    tgt := OLD.suggestion_id;
  END IF;

  IF tgt IS NOT NULL THEN
    UPDATE suggestions s SET
      upvotes = (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = tgt AND v.vote_type = 'up'),
      downvotes = (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = tgt AND v.vote_type = 'down'),
      vote_count = (
        (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = tgt AND v.vote_type = 'up') -
        (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = tgt AND v.vote_type = 'down')
      ),
      "voteCount" = (
        (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = tgt AND v.vote_type = 'up') -
        (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = tgt AND v.vote_type = 'down')
      )
    WHERE s.id = tgt;
  END IF;
  RETURN NULL;
END;
$fn$ LANGUAGE plpgsql;
DO $$
DECLARE r_exists boolean;
BEGIN
  SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema='public' AND table_name='votes') INTO r_exists;
  IF r_exists THEN
    IF EXISTS (
      SELECT 1 FROM information_schema.triggers WHERE event_object_table='votes' AND trigger_name='trg_update_vote_counts'
    ) THEN
      DROP TRIGGER trg_update_vote_counts ON votes;
    END IF;
    CREATE TRIGGER trg_update_vote_counts AFTER INSERT OR UPDATE OR DELETE ON votes FOR EACH ROW EXECUTE FUNCTION update_suggestion_vote_counts();
  END IF;
END $$;

CREATE TABLE IF NOT EXISTS playlist_schedules (
	id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
	restaurant_id VARCHAR NOT NULL,
	playlist_id UUID NOT NULL,
	is_active BOOLEAN DEFAULT TRUE,
	schedule JSONB NULL,
	created_at TIMESTAMP DEFAULT now(),
	updated_at TIMESTAMP DEFAULT now()
);

-- Tabela playlist_tracks conforme modelo do backend
CREATE TABLE IF NOT EXISTS playlist_tracks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  playlist_id UUID NOT NULL REFERENCES playlists(id) ON DELETE CASCADE,
  title VARCHAR NOT NULL,
  artist VARCHAR NOT NULL,
  youtube_video_id VARCHAR NOT NULL,
  thumbnail_url VARCHAR NULL,
  duration INT DEFAULT 0,
  position INT DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  is_explicit BOOLEAN DEFAULT FALSE,
  genre VARCHAR NULL,
  mood VARCHAR NULL,
  bpm INT NULL,
  key VARCHAR NULL,
  energy FLOAT DEFAULT 0,
  valence FLOAT DEFAULT 0.5,
  danceability FLOAT DEFAULT 0.5,
  play_count INT DEFAULT 0,
  skip_count INT DEFAULT 0,
  upvotes INT DEFAULT 0,
  downvotes INT DEFAULT 0,
  average_play_duration FLOAT DEFAULT 0,
  completion_rate FLOAT DEFAULT 0,
  last_played_at TIMESTAMP NULL,
  analytics JSON NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Índices de playlist_tracks (após criar a tabela)
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_playlist ON playlist_tracks(playlist_id);
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_position ON playlist_tracks(playlist_id, position);
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_active ON playlist_tracks(is_active);
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_genre ON playlist_tracks(genre);
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_mood ON playlist_tracks(mood);
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_youtube ON playlist_tracks(youtube_video_id);

-- Tabelas legadas (de create_legacy_support_tables.sql)
CREATE TABLE IF NOT EXISTS songs (
  id VARCHAR(255) PRIMARY KEY,
  youtube_video_id VARCHAR(255) UNIQUE NOT NULL,
  title VARCHAR(500) NOT NULL,
  artist VARCHAR(255),
  duration INTEGER,
  thumbnail_url VARCHAR(500),
  channel_name VARCHAR(255),
  view_count BIGINT DEFAULT 0,
  published_at DATE,
  genre VARCHAR(100),
  language VARCHAR(10),
  created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS playback_queue (
  id VARCHAR(255) PRIMARY KEY,
  restaurant_id VARCHAR(255) NOT NULL,
  suggestion_id UUID NOT NULL,
  position INTEGER NOT NULL,
  is_playing BOOLEAN DEFAULT FALSE,
  played_at TIMESTAMP WITHOUT TIME ZONE,
  added_to_queue_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS analytics_events (
  id VARCHAR(255) PRIMARY KEY,
  restaurant_id VARCHAR(255) NOT NULL,
  client_session_id UUID,
  event_type VARCHAR(50) NOT NULL,
  event_data JSON,
  ip_address VARCHAR(45),
  user_agent TEXT,
  created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS daily_stats (
  id VARCHAR(255) PRIMARY KEY,
  restaurant_id VARCHAR(255) NOT NULL,
  date DATE NOT NULL,
  suggestions_count INTEGER DEFAULT 0,
  votes_count INTEGER DEFAULT 0,
  plays_count INTEGER DEFAULT 0,
  unique_users INTEGER DEFAULT 0,
  total_session_time INTEGER DEFAULT 0,
  created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
  UNIQUE (restaurant_id, date)
);

-- Tabelas missing core (de create_missing_core_tables.sql)
CREATE TABLE IF NOT EXISTS analytics_daily (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    date DATE NOT NULL,
    "totalSuggestions" INTEGER DEFAULT 0,
    "approvedSuggestions" INTEGER DEFAULT 0,
    "rejectedSuggestions" INTEGER DEFAULT 0,
    "pendingSuggestions" INTEGER DEFAULT 0,
    "totalVotes" INTEGER DEFAULT 0,
    upvotes INTEGER DEFAULT 0,
    downvotes INTEGER DEFAULT 0,
    "uniqueSessions" INTEGER DEFAULT 0,
    "totalPageViews" INTEGER DEFAULT 0,
    "totalPlayTime" INTEGER DEFAULT 0,
    "songsPlayed" INTEGER DEFAULT 0,
    "songsSkipped" INTEGER DEFAULT 0,
    "averageSessionDuration" DOUBLE PRECISION DEFAULT 0,
    "averageSongRating" DOUBLE PRECISION DEFAULT 0,
    "topGenres" JSON,
    "topArtists" JSON,
    "hourlyActivity" JSON,
    devicestats JSON,
    locationstats JSON,
    engagementstats JSON,
    moderationstats JSON,
    restaurant_id VARCHAR(255) NOT NULL,
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS competitive_votes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    voting_session_id VARCHAR(255) NOT NULL,
    suggestion_id UUID NOT NULL,
    voter_session_id UUID NOT NULL,
    voter_table_name VARCHAR(100),
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    voter_ip VARCHAR(100),
    voter_user_agent TEXT,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- Assuming the truncated parts; including what's provided
-- For example, voting_sessions, etc. - since truncated, include visible ones

CREATE TABLE IF NOT EXISTS moderation_rules (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    "ruleType" rule_type NOT NULL,
    action rule_action DEFAULT 'flag_for_review',
    "ruleValue" JSON NOT NULL,
    "isActive" BOOLEAN DEFAULT TRUE,
    priority INTEGER DEFAULT 0,
    "matchCount" INTEGER DEFAULT 0,
    "lastMatchedAt" TIMESTAMP WITHOUT TIME ZONE,
    restaurant_id VARCHAR(255) NOT NULL,
    created_by UUID,
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS play_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    "youtubeVideoId" VARCHAR(100) NOT NULL,
    title VARCHAR(300) NOT NULL,
    artist VARCHAR(200),
    duration INTEGER NOT NULL,
    "playDuration" INTEGER NOT NULL,
    status play_status NOT NULL,
    "skipReason" TEXT,
    "playedAt" TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    "endedAt" TIMESTAMP WITHOUT TIME ZONE,
    "queuePosition" INTEGER,
    "voteCount" INTEGER DEFAULT 0,
    metadata JSON,
    restaurant_id VARCHAR(255) NOT NULL,
    suggestion_id UUID,
    played_by UUID,
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS rewards (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    restaurant_id VARCHAR(255) NOT NULL,
    session_id UUID NOT NULL,
    client_name VARCHAR(255),
    table_name VARCHAR(100),
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    reward_data JSON NOT NULL,
    awarded_for VARCHAR(50) NOT NULL,
    awarded_date TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    expires_at TIMESTAMP WITHOUT TIME ZONE,
    status VARCHAR(50) DEFAULT 'active',
    used_at TIMESTAMP WITHOUT TIME ZONE,
    usage_count INTEGER DEFAULT 0,
    performance_data JSON,
    social_sharing JSON,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- FKs adicionais
DO $$
BEGIN
	IF EXISTS (
		SELECT 1 FROM information_schema.columns
		WHERE table_name='qr_codes' AND column_name='restaurant_id'
	) AND NOT EXISTS (
		SELECT 1 FROM information_schema.table_constraints
		WHERE table_name='qr_codes' AND constraint_name='fk_qr_codes_restaurant'
	) THEN
		ALTER TABLE qr_codes
		ADD CONSTRAINT fk_qr_codes_restaurant
			FOREIGN KEY (restaurant_id) REFERENCES restaurants(id) ON DELETE CASCADE;
	END IF;
END $$;

DO $$
BEGIN
	IF EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='session_id'
	) AND EXISTS (
		SELECT 1 FROM information_schema.tables WHERE table_name='client_sessions'
	) AND NOT EXISTS (
		SELECT 1 FROM information_schema.table_constraints
		WHERE table_name='suggestions' AND constraint_name='fk_suggestions_session'
	) THEN
		ALTER TABLE suggestions
		ADD CONSTRAINT fk_suggestions_session
			FOREIGN KEY (session_id) REFERENCES client_sessions(id) ON DELETE SET NULL;
	END IF;
END $$;

DO $$
BEGIN
	IF EXISTS (
		SELECT 1 FROM information_schema.tables WHERE table_name='restaurants'
	) AND NOT EXISTS (
		SELECT 1 FROM information_schema.table_constraints
		WHERE table_name='playlist_schedules' AND constraint_name='fk_ps_restaurant'
	) THEN
		ALTER TABLE playlist_schedules
		ADD CONSTRAINT fk_ps_restaurant
			FOREIGN KEY (restaurant_id) REFERENCES restaurants(id) ON DELETE CASCADE;
	END IF;
	IF EXISTS (
		SELECT 1 FROM information_schema.tables WHERE table_name='playlists'
	) AND NOT EXISTS (
		SELECT 1 FROM information_schema.table_constraints
		WHERE table_name='playlist_schedules' AND constraint_name='fk_ps_playlist'
	) THEN
		ALTER TABLE playlist_schedules
		ADD CONSTRAINT fk_ps_playlist
			FOREIGN KEY (playlist_id) REFERENCES playlists(id) ON DELETE CASCADE;
	END IF;
END $$;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE constraint_type = 'FOREIGN KEY' AND constraint_name = 'fk_payments_suggestion'
  ) THEN
    ALTER TABLE payments
      ADD CONSTRAINT fk_payments_suggestion FOREIGN KEY (suggestion_id)
      REFERENCES suggestions(id) ON DELETE CASCADE;
  END IF;
END $$;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE constraint_type='FOREIGN KEY' AND constraint_name='fk_analytics_restaurant'
  ) THEN
    ALTER TABLE analytics_daily
      ADD CONSTRAINT fk_analytics_restaurant FOREIGN KEY (restaurant_id)
      REFERENCES restaurants(id) ON DELETE CASCADE;
  END IF;
END $$;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE constraint_type='FOREIGN KEY' AND constraint_name='fk_comp_votes_suggestion'
  ) THEN
    ALTER TABLE competitive_votes
      ADD CONSTRAINT fk_comp_votes_suggestion FOREIGN KEY (suggestion_id)
      REFERENCES suggestions(id) ON DELETE CASCADE;
  END IF;
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE constraint_type='FOREIGN KEY' AND constraint_name='fk_comp_votes_voter_session'
  ) THEN
    ALTER TABLE competitive_votes
      ADD CONSTRAINT fk_comp_votes_voter_session FOREIGN KEY (voter_session_id)
      REFERENCES client_sessions(id) ON DELETE CASCADE;
  END IF;
END $$;

-- Índices essenciais
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_restaurants_status ON restaurants(status);
CREATE INDEX IF NOT EXISTS idx_restaurants_active ON restaurants("isActive");
CREATE INDEX IF NOT EXISTS idx_playlists_restaurant ON playlists(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_playlists_execution_order ON playlists(restaurant_id, execution_order);
CREATE INDEX IF NOT EXISTS idx_suggestions_restaurant ON suggestions(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_suggestions_youtube ON suggestions("youtubeVideoId");
CREATE INDEX IF NOT EXISTS idx_suggestions_status ON suggestions(status);
CREATE INDEX IF NOT EXISTS idx_suggestions_queue ON suggestions("queuePosition");
CREATE INDEX IF NOT EXISTS idx_client_sessions_token ON client_sessions("sessionToken");
CREATE UNIQUE INDEX IF NOT EXISTS idx_queue_items_restaurant_position ON queue_items(restaurant_id, position);
CREATE INDEX IF NOT EXISTS idx_queue_items_status ON queue_items(status);
CREATE INDEX IF NOT EXISTS idx_queue_items_restaurant_playing ON queue_items(restaurant_id, is_playing);
CREATE INDEX IF NOT EXISTS idx_queue_items_session ON queue_items(session_id);

CREATE INDEX IF NOT EXISTS idx_genres_name ON genres(name);
CREATE INDEX IF NOT EXISTS idx_genres_category ON genres(category);
CREATE INDEX IF NOT EXISTS idx_genres_is_active ON genres(is_active);

CREATE INDEX IF NOT EXISTS idx_payments_suggestion ON payments(suggestion_id);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);

CREATE INDEX IF NOT EXISTS idx_playback_queue_restaurant ON playback_queue(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_playback_queue_position ON playback_queue(position);

CREATE INDEX IF NOT EXISTS idx_analytics_restaurant ON analytics_events(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_analytics_type ON analytics_events(event_type);
CREATE INDEX IF NOT EXISTS idx_analytics_date ON analytics_events(created_at);

CREATE INDEX IF NOT EXISTS idx_daily_stats_restaurant ON daily_stats(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_daily_stats_date ON daily_stats(date);

CREATE INDEX IF NOT EXISTS idx_mod_rules_restaurant ON moderation_rules(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_mod_rules_active ON moderation_rules("isActive");

CREATE INDEX IF NOT EXISTS idx_play_history_restaurant ON play_history(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_play_history_suggestion ON play_history(suggestion_id);

CREATE INDEX IF NOT EXISTS idx_rewards_restaurant ON rewards(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_rewards_session ON rewards(session_id);

-- (índices de qr_codes já criados acima)
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_playlist ON playlist_tracks(playlist_id);
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_position ON playlist_tracks(playlist_id, position);
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_youtube ON playlist_tracks(youtube_video_id);

-- Triggers updatedAt
DROP TRIGGER IF EXISTS trigger_users_updated_at ON users;
CREATE TRIGGER trigger_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
DROP TRIGGER IF EXISTS trigger_restaurants_updated_at ON restaurants;
CREATE TRIGGER trigger_restaurants_updated_at BEFORE UPDATE ON restaurants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
DROP TRIGGER IF EXISTS trigger_playlists_updated_at ON playlists;
CREATE TRIGGER trigger_playlists_updated_at BEFORE UPDATE ON playlists FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
DROP TRIGGER IF EXISTS trigger_suggestions_updated_at ON suggestions;
CREATE TRIGGER trigger_suggestions_updated_at BEFORE UPDATE ON suggestions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
DROP TRIGGER IF EXISTS trigger_client_sessions_updated_at ON client_sessions;
CREATE TRIGGER trigger_client_sessions_updated_at BEFORE UPDATE ON client_sessions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Correções idempotentes de colunas
DO $$ BEGIN
  -- users.restaurant_id deve ser VARCHAR(255) e ter FK
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='users' AND column_name='restaurant_id') THEN
    ALTER TABLE users ADD COLUMN restaurant_id VARCHAR(255) NULL;
  ELSE
    IF EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='users' AND column_name='restaurant_id' AND data_type <> 'character varying'
    ) THEN
      ALTER TABLE users ALTER COLUMN restaurant_id TYPE VARCHAR(255) USING restaurant_id::text;
    END IF;
  END IF;
  IF EXISTS (
    SELECT 1 FROM information_schema.table_constraints WHERE constraint_name='fk_users_restaurant' AND constraint_type='FOREIGN KEY'
  ) THEN
    ALTER TABLE users DROP CONSTRAINT fk_users_restaurant;
  END IF;
  ALTER TABLE users
    ADD CONSTRAINT fk_users_restaurant FOREIGN KEY (restaurant_id) REFERENCES restaurants(id) ON DELETE SET NULL;

  -- suggestions: campos de pagamento e cliente
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='is_paid') THEN
    ALTER TABLE suggestions ADD COLUMN is_paid BOOLEAN NOT NULL DEFAULT FALSE;
  END IF;
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='payment_amount') THEN
    ALTER TABLE suggestions ADD COLUMN payment_amount DECIMAL(10,2) NULL;
  END IF;
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='client_name') THEN
    ALTER TABLE suggestions ADD COLUMN client_name VARCHAR(100);
  END IF;
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='table_number') THEN
    ALTER TABLE suggestions ADD COLUMN table_number INTEGER;
  END IF;

  -- client_sessions: pontos para gamificação e mais (de fix_clientsession_columns.sql)
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='client_sessions' AND column_name='points') THEN
    ALTER TABLE client_sessions ADD COLUMN points INTEGER DEFAULT 0;
  END IF;
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'client_sessions' AND column_name = 'pageviews'
  ) THEN
    ALTER TABLE client_sessions ADD COLUMN pageviews INTEGER NOT NULL DEFAULT 0;
  END IF;
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'client_sessions' AND column_name = 'sessionduration'
  ) THEN
    ALTER TABLE client_sessions ADD COLUMN sessionduration INTEGER NOT NULL DEFAULT 0;
  END IF;

  -- restaurants: colunas utilitárias
  IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='restaurants' AND column_name='code'
	) THEN
		ALTER TABLE restaurants ADD COLUMN code VARCHAR(50);
	END IF;
	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='restaurants' AND column_name='slug'
	) THEN
		ALTER TABLE restaurants ADD COLUMN slug VARCHAR(80);
	END IF;
	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='restaurants' AND column_name='timezone'
	) THEN
		ALTER TABLE restaurants ADD COLUMN timezone VARCHAR(60) NOT NULL DEFAULT 'America/Sao_Paulo';
	END IF;
	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='restaurants' AND column_name='isActive'
	) THEN
		ALTER TABLE restaurants ADD COLUMN "isActive" BOOLEAN NOT NULL DEFAULT true;
	END IF;
	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='restaurants' AND column_name='created_at'
	) THEN
		ALTER TABLE restaurants ADD COLUMN created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW();
	END IF;
	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='restaurants' AND column_name='updated_at'
	) THEN
		ALTER TABLE restaurants ADD COLUMN updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW();
	END IF;

  -- playlists: sinalizadores comuns
	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='is_public'
	) THEN
		ALTER TABLE playlists ADD COLUMN is_public BOOLEAN NOT NULL DEFAULT true;
	END IF;
	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='is_default'
	) THEN
		ALTER TABLE playlists ADD COLUMN is_default BOOLEAN NOT NULL DEFAULT false;
	END IF;
  ALTER TABLE IF EXISTS playlists
	ADD COLUMN IF NOT EXISTS "youtubePlaylistId" VARCHAR NULL,
	ADD COLUMN IF NOT EXISTS tracks JSON NULL,
	ADD COLUMN IF NOT EXISTS "isDefault" BOOLEAN DEFAULT FALSE,
	ADD COLUMN IF NOT EXISTS "isPublic" BOOLEAN DEFAULT TRUE,
	ADD COLUMN IF NOT EXISTS execution_order INTEGER NULL;

  -- suggestions: adições
  ALTER TABLE IF EXISTS suggestions
	ADD COLUMN IF NOT EXISTS queue_position INTEGER NULL;
  ALTER TABLE IF EXISTS suggestions
	ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP NULL;
  ALTER TABLE IF EXISTS suggestions
	ADD COLUMN IF NOT EXISTS is_paid BOOLEAN DEFAULT FALSE,
	ADD COLUMN IF NOT EXISTS payment_amount DECIMAL(10,2) NULL;
  ALTER TABLE IF EXISTS suggestions
	ADD COLUMN IF NOT EXISTS client_session_id VARCHAR NULL,
	ADD COLUMN IF NOT EXISTS session_id UUID NULL;

  -- suggestions.genre (de fix_columns_qrcodes_suggestions.sql e fix_genres_column_case.sql)
  ALTER TABLE IF EXISTS suggestions
	ADD COLUMN IF NOT EXISTS genre VARCHAR NULL;
  IF EXISTS (
		SELECT 1 FROM information_schema.columns
		WHERE table_name='suggestions' AND column_name='Genre'
	) THEN
		ALTER TABLE suggestions RENAME COLUMN "Genre" TO genre;
	END IF;

  -- qr_codes colunas adicionais (de fix_columns_qrcodes_suggestions.sql)
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'qr_codes') THEN
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='qr_codes' AND column_name='type'
    ) THEN
      ALTER TABLE qr_codes ADD COLUMN type VARCHAR;
    END IF;
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='qr_codes' AND column_name='name'
    ) THEN
      ALTER TABLE qr_codes ADD COLUMN name VARCHAR;
    END IF;
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='qr_codes' AND column_name='table_number'
    ) THEN
      ALTER TABLE qr_codes ADD COLUMN table_number VARCHAR;
    END IF;
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='qr_codes' AND column_name='url'
    ) THEN
      ALTER TABLE qr_codes ADD COLUMN url VARCHAR;
    END IF;
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='qr_codes' AND column_name='qr_code_data'
    ) THEN
      ALTER TABLE qr_codes ADD COLUMN qr_code_data TEXT;
    END IF;
    IF EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='qr_codes' AND column_name='code'
    ) THEN
      EXECUTE 'UPDATE qr_codes SET qr_code_data = COALESCE(qr_code_data, code) WHERE qr_code_data IS NULL AND code IS NOT NULL';
    END IF;
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='qr_codes' AND column_name='is_active'
    ) THEN
      ALTER TABLE qr_codes ADD COLUMN is_active BOOLEAN DEFAULT true;
    END IF;
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='qr_codes' AND column_name='updated_at'
    ) THEN
      ALTER TABLE qr_codes ADD COLUMN updated_at TIMESTAMP;
    END IF;
  END IF;
END $$;

-- Backfill e ajustes específicos
-- Backfill execution_order para playlists
DO $$
BEGIN
	IF EXISTS (
		SELECT 1 FROM information_schema.columns
		WHERE table_name='playlists' AND column_name='created_at'
	) THEN
		UPDATE playlists
		SET execution_order = sub.rn
		FROM (
			SELECT id, ROW_NUMBER() OVER (ORDER BY COALESCE(created_at, now())) AS rn
			FROM playlists
		) sub
		WHERE playlists.id = sub.id AND playlists.execution_order IS NULL;
	ELSE
		UPDATE playlists
		SET execution_order = sub.rn
		FROM (
			SELECT id, ROW_NUMBER() OVER (ORDER BY COALESCE("createdAt", now())) AS rn
			FROM playlists
		) sub
		WHERE playlists.id = sub.id AND playlists.execution_order IS NULL;
	END IF;
END $$;

-- Ajustes de youtube_video_id
DO $$
BEGIN
	IF EXISTS (
		SELECT 1 FROM information_schema.tables WHERE table_name='suggestions'
	) THEN
		IF NOT EXISTS (
			SELECT 1 FROM information_schema.columns
			WHERE table_name='suggestions' AND column_name='youtube_video_id'
		) AND EXISTS (
			SELECT 1 FROM information_schema.columns
			WHERE table_name='suggestions' AND column_name='youtubeVideoId'
		) THEN
			EXECUTE 'ALTER TABLE suggestions ADD COLUMN youtube_video_id VARCHAR(100)';
			EXECUTE 'UPDATE suggestions SET youtube_video_id = "youtubeVideoId" WHERE youtube_video_id IS NULL';
		END IF;

		IF EXISTS (
			SELECT 1 FROM information_schema.columns
			WHERE table_name='suggestions' AND column_name='youtube_video_id'
		) THEN
			CREATE INDEX IF NOT EXISTS idx_suggestions_youtube_snake ON suggestions(youtube_video_id);
		END IF;

		IF EXISTS (
			SELECT 1 FROM information_schema.columns
			WHERE table_name='suggestions' AND column_name='youtube_video_id'
		) THEN
			EXECUTE 'ALTER TABLE suggestions ALTER COLUMN youtube_video_id SET NOT NULL';
		ELSIF EXISTS (
			SELECT 1 FROM information_schema.columns
			WHERE table_name='suggestions' AND column_name='youtubeVideoId'
		) THEN
			EXECUTE 'ALTER TABLE suggestions ALTER COLUMN "youtubeVideoId" SET NOT NULL';
		END IF;
	END IF;
END $$;

-- Ajustes de case
DO $$
BEGIN
	IF EXISTS (
		SELECT 1 FROM information_schema.columns
		WHERE table_name='playlists' AND column_name='executionOrder'
	) THEN
		ALTER TABLE playlists RENAME COLUMN "executionOrder" TO execution_order;
	END IF;
END $$;

DO $$
BEGIN
	IF EXISTS (
		SELECT 1 FROM information_schema.columns
		WHERE table_name='restaurants' AND column_name='isactive'
	) THEN
		ALTER TABLE restaurants RENAME COLUMN isactive TO "isActive";
	END IF;
END $$;

DO $$
BEGIN
	IF EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='createdAt'
	) THEN
    -- manter camelCase para compatibilidade; se necessário, criar coluna snake separada em outra migração
	END IF;
	IF EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='updatedAt'
	) THEN
    -- manter camelCase para compatibilidade; se necessário, criar coluna snake separada em outra migração
	END IF;
END $$;

-- ============================================
-- ALIGN: playlist_schedules ao modelo (20250818_fix_playlist_schedules_align_entity.sql)
-- ============================================
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name='playlist_schedules') THEN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_schedules' AND column_name='name') THEN
      ALTER TABLE playlist_schedules ADD COLUMN name VARCHAR NULL;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_schedules' AND column_name='description') THEN
      ALTER TABLE playlist_schedules ADD COLUMN description TEXT NULL;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_schedules' AND column_name='timeSlots') THEN
      ALTER TABLE playlist_schedules ADD COLUMN "timeSlots" JSON NULL;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_schedules' AND column_name='isActive') THEN
      ALTER TABLE playlist_schedules ADD COLUMN "isActive" BOOLEAN DEFAULT TRUE;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_schedules' AND column_name='mode') THEN
      ALTER TABLE playlist_schedules ADD COLUMN mode VARCHAR DEFAULT 'normal';
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_schedules' AND column_name='settings') THEN
      ALTER TABLE playlist_schedules ADD COLUMN settings JSON NULL;
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_schedules' AND column_name='createdAt') THEN
      ALTER TABLE playlist_schedules ADD COLUMN "createdAt" TIMESTAMP DEFAULT NOW();
    END IF;
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_schedules' AND column_name='updatedAt') THEN
      ALTER TABLE playlist_schedules ADD COLUMN "updatedAt" TIMESTAMP DEFAULT NOW();
    END IF;
    -- Tornar playlist_id opcional caso exista e seja NOT NULL
    IF EXISTS (
      SELECT 1 FROM information_schema.columns
      WHERE table_name='playlist_schedules' AND column_name='playlist_id' AND is_nullable='NO'
    ) THEN
      ALTER TABLE playlist_schedules ALTER COLUMN playlist_id DROP NOT NULL;
    END IF;
  END IF;
END $$;

-- Backfill de dados legados
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name='playlist_schedules') THEN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_schedules' AND column_name='schedule') THEN
      UPDATE playlist_schedules
      SET "timeSlots" = CASE
        WHEN jsonb_typeof(schedule) = 'object' AND (schedule ? 'timeSlots') THEN (schedule -> 'timeSlots')::json
        WHEN jsonb_typeof(schedule) = 'array' THEN schedule::json
        ELSE COALESCE("timeSlots", '[]'::json)
      END
      WHERE "timeSlots" IS NULL;
    END IF;
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_schedules' AND column_name='is_active') THEN
      UPDATE playlist_schedules SET "isActive" = COALESCE("isActive", is_active, TRUE) WHERE "isActive" IS NULL;
    END IF;
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_schedules' AND column_name='created_at') THEN
      UPDATE playlist_schedules SET "createdAt" = COALESCE("createdAt", created_at) WHERE created_at IS NOT NULL AND "createdAt" IS NULL;
    END IF;
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_schedules' AND column_name='updated_at') THEN
      UPDATE playlist_schedules SET "updatedAt" = COALESCE("updatedAt", updated_at) WHERE updated_at IS NOT NULL AND "updatedAt" IS NULL;
    END IF;
  END IF;
END $$;

-- Índices e trigger de updatedAt
CREATE INDEX IF NOT EXISTS idx_playlist_schedules_restaurant ON playlist_schedules(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_playlist_schedules_createdat ON playlist_schedules("createdAt");
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trigger_playlist_schedules_updated_at') THEN
    CREATE TRIGGER trigger_playlist_schedules_updated_at
      BEFORE UPDATE ON playlist_schedules
      FOR EACH ROW
      EXECUTE FUNCTION update_updated_at_column();
  END IF;
END $$;

-- Seeds (mais detalhado de seed_demo_restaurant.sql)
-- Seed demo restaurant if missing (idempotent)
INSERT INTO restaurants (
    id, name, description, phone, email, website, address, status,
    settings, language, timezone, "isActive", "subscriptionPlan",
    "maxUsers", "maxPlaylists", "maxSuggestions", "storageLimit",
    "apiUsage", metadata, "createdAt", "updatedAt", logo,
    "youtubeChannelId", "socialMedia", "businessHours"
)
SELECT
    'demo-restaurant',
    'Restaurante Demo',
    'Restaurante de demonstração para testes do sistema de playlist colaborativa',
    '+55 11 99999-9999',
    '<EMAIL>',
    'https://demo-restaurante.com',
    '{"street": "Rua Demo, 123", "city": "São Paulo", "state": "SP", "zipCode": "01234-567", "country": "Brasil"}'::json,
    'active',
    '{"allowSuggestions": true, "moderationEnabled": false, "maxSuggestionsPerUser": 5, "votingEnabled": true, "pixPayments": true, "autoPlay": true, "playlist": {"defaultVolume": 70}}'::json,
    'pt-BR',
    'America/Sao_Paulo',
    true,
    'premium',
    50,
    10,
    1000,
    ***********,
    '{"requests": 0, "lastReset": "2024-01-01T00:00:00Z"}'::json,
    '{"demo": true, "setupComplete": true, "paymentEnabled": true}'::json,
    NOW(),
    NOW(),
    'https://placehold.co/200x200/6366f1/ffffff?text=DEMO',
    'UCdemoChannelId123',
    '{"instagram": "@demo_restaurant", "facebook": "DemoRestaurant"}'::json,
    '{"monday": {"open": "11:00", "close": "23:00"}, "tuesday": {"open": "11:00", "close": "23:00"}, "wednesday": {"open": "11:00", "close": "23:00"}, "thursday": {"open": "11:00", "close": "23:00"}, "friday": {"open": "11:00", "close": "00:00"}, "saturday": {"open": "11:00", "close": "00:00"}, "sunday": {"open": "11:00", "close": "22:00"}}'::json
WHERE NOT EXISTS (
    SELECT 1 FROM restaurants WHERE id = 'demo-restaurant'
);

-- Criar playlist demo se não existir
DO $$
DECLARE
  demo_playlist_id UUID;
BEGIN
  IF NOT EXISTS (SELECT 1 FROM playlists WHERE restaurant_id = 'demo-restaurant' AND name = 'Playlist Demo Principal') THEN
    demo_playlist_id := gen_random_uuid();
    INSERT INTO playlists (
      id, name, description, type, status, "youtubePlaylistId",
      "isDefault", "isPublic", restaurant_id, execution_order,
      "createdAt", "updatedAt"
    ) VALUES (
      demo_playlist_id,
      'Playlist Demo Principal',
      'Playlist principal do restaurante demo com músicas populares',
  'youtube_import',
      'active',
      'PLdemoPlaylistId123',
      true,
      true,
      'demo-restaurant',
      1,
      NOW(),
      NOW()
    );
  END IF;
END $$;

DO $$
DECLARE
  demo_admin_email CONSTANT VARCHAR := '<EMAIL>';
  demo_admin_hash  CONSTANT VARCHAR := '$2b$12$LQv3c1yqBwEHFl5ePEjNNONciJ0MGhppMn5rjJ9TndsqKJbvHEubS'; -- admin123
  demo_admin_id UUID;
BEGIN
  IF NOT EXISTS (SELECT 1 FROM users WHERE email = demo_admin_email) THEN
    demo_admin_id := gen_random_uuid();
    INSERT INTO users (id, email, name, password, role, "isActive", restaurant_id, "createdAt", "updatedAt")
    VALUES (demo_admin_id, demo_admin_email, 'Admin Demo', demo_admin_hash, 'admin', TRUE, 'demo-restaurant', NOW(), NOW());
  END IF;
END $$;

-- Admin principal com senha conhecida (admin123) se ausente
DO $$
DECLARE
  admin_email CONSTANT VARCHAR := '<EMAIL>';
  admin_hash  CONSTANT VARCHAR := '$2b$12$LQv3c1yqBwEHFl5ePEjNNONciJ0MGhppMn5rjJ9TndsqKJbvHEubS';
  admin_id UUID;
BEGIN
  IF NOT EXISTS (SELECT 1 FROM users WHERE email = admin_email) THEN
    admin_id := gen_random_uuid();
    INSERT INTO users (id, name, email, password, role, "isActive", restaurant_id, "createdAt", "updatedAt")
    VALUES (admin_id, 'Administrador', admin_email, admin_hash, 'admin', TRUE, 'demo-restaurant', NOW(), NOW());
  ELSE
    UPDATE users SET password = admin_hash, "updatedAt"=NOW() WHERE email = admin_email;
  END IF;
END $$;

-- Seeds de genres
INSERT INTO genres (name, display_name, description, category, is_default)
VALUES
	('Pop', 'Pop', 'Música popular', 'music', true),
	('Rock', 'Rock', 'Rock e suas variações', 'music', true),
	('Sertanejo', 'Sertanejo', 'Música sertaneja brasileira', 'music', true),
	('Funk', 'Funk', 'Funk brasileiro', 'music', true),
	('MPB', 'MPB', 'Música Popular Brasileira', 'music', true),
	('Eletrônica', 'Eletrônica', 'Música eletrônica', 'music', true),
	('Hip Hop', 'Hip Hop', 'Hip hop e rap', 'music', true),
	('Reggae', 'Reggae', 'Reggae e variações', 'music', true),
	('Jazz', 'Jazz', 'Jazz e blues', 'music', true),
	('Clássica', 'Clássica', 'Música clássica', 'music', true)
ON CONFLICT (name) DO NOTHING;

-- Verificações finais
DO $$
DECLARE missing TEXT := '';
BEGIN
  FOR missing IN SELECT t FROM (
    SELECT 'users' t UNION ALL SELECT 'restaurants' UNION ALL SELECT 'playlists' UNION ALL SELECT 'suggestions' UNION ALL
    SELECT 'client_sessions' UNION ALL SELECT 'genres' UNION ALL SELECT 'suggestion_genres' UNION ALL SELECT 'payments' UNION ALL SELECT 'queue_items'
    UNION ALL SELECT 'qr_codes' UNION ALL SELECT 'playlist_schedules' UNION ALL SELECT 'songs' UNION ALL SELECT 'playback_queue'
    UNION ALL SELECT 'analytics_events' UNION ALL SELECT 'daily_stats' UNION ALL SELECT 'analytics_daily' UNION ALL SELECT 'competitive_votes'
    UNION ALL SELECT 'moderation_rules' UNION ALL SELECT 'play_history' UNION ALL SELECT 'rewards'
  ) s WHERE NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema='public' AND table_name=s.t)
  LOOP
    RAISE NOTICE 'Tabela faltando: %', missing;
  END LOOP;
  RAISE NOTICE 'Full bootstrap executado com sucesso';
END $$;