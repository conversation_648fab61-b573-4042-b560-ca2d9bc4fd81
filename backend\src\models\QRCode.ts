import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
} from "typeorm";
import { Restaurant } from "./Restaurant";

export enum QRCodeType {
  TABLE = "table",
  RESTAURANT = "restaurant",
}

@Entity("qr_codes")
export class QRCode {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @ManyToOne(() => Restaurant, { eager: true })
  @JoinColumn({ name: "restaurant_id" })
  restaurant: Restaurant;

  @Column({ type: "varchar" })
  type: QRCodeType;

  @Column({ type: "varchar" })
  name: string;

  @Column({ name: "table_number", type: "varchar", nullable: true })
  tableNumber?: string;

  @Column({ type: "varchar" })
  url: string;

  @Column({ name: "qr_code_data", type: "text" })
  qrCodeData: string;

  @Column({ name: "is_active", type: "boolean", default: true })
  isActive: boolean;

  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at" })
  updatedAt: Date;

  // Método para converter para JSON público
  toPublicJSON() {
    return {
      id: this.id,
      type: this.type,
      name: this.name,
      tableNumber: this.tableNumber,
      url: this.url,
      qrCodeDataURL: this.qrCodeData,
      isActive: this.isActive,
      createdAt: this.createdAt.toISOString(),
      restaurantId: this.restaurant.id,
      restaurant: {
        id: this.restaurant.id,
        name: this.restaurant.name,
      },
    };
  }
}
