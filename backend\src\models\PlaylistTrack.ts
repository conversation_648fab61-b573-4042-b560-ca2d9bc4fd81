import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyToOne,
  <PERSON>in<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { Playlist } from './Playlist';

@Entity('playlist_tracks')

export class PlaylistTrack {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Playlist, playlist => playlist.tracks, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'playlist_id' })
  playlist: Playlist;

  @Column({ name: 'playlist_id' })
  playlistId: string;

  @Column({ type: 'varchar' })
  title: string;

  @Column({ type: 'varchar' })
  artist: string;

  @Column({ name: 'youtube_video_id', type: 'varchar', unique: false })
  youtubeVideoId: string;

  @Column({ name: 'thumbnail_url', type: 'varchar', nullable: true })
  thumbnailUrl: string | null;

  @Column({ type: 'int', default: 0 })
  duration: number; // Duração em segundos

  @Column({ type: 'int', default: 0 })
  position: number; // Posição na playlist (para ordenação)

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'boolean', default: false })
  isExplicit: boolean;

  @Column({ type: 'varchar', nullable: true })
  genre: string | null;

  @Column({ type: 'varchar', nullable: true })
  mood: string | null;

  @Column({ type: 'int', nullable: true })
  bpm: number | null; // Batidas por minuto (para transições suaves)

  @Column({ type: 'varchar', nullable: true })
  key: string | null; // Tonalidade musical (para harmonia)

  @Column({ type: 'float', default: 0 })
  energy: number; // Nível de energia (0-1)

  @Column({ type: 'float', default: 0.5 })
  valence: number; // Positividade (0-1)

  @Column({ type: 'float', default: 0.5 })
  danceability: number; // Dançabilidade (0-1)

  // Estatísticas de performance
  @Column({ name: 'play_count', type: 'int', default: 0 })
  playCount: number;

  @Column({ name: 'skip_count', type: 'int', default: 0 })
  skipCount: number;

  @Column({ type: 'int', default: 0 })
  upvotes: number;

  @Column({ type: 'int', default: 0 })
  downvotes: number;

  @Column({ name: 'average_play_duration', type: 'float', default: 0 })
  averagePlayDuration: number; // Duração média de reprodução

  @Column({ name: 'completion_rate', type: 'float', default: 0 })
  completionRate: number; // Taxa de conclusão (0-1)

  @Column({ name: 'last_played_at', type: 'timestamp', nullable: true })
  lastPlayedAt: Date | null;

  @Column({ type: 'json', nullable: true })
  analytics: {
    hourlyPlayCounts: { [hour: string]: number };
    dayOfWeekPlayCounts: { [day: string]: number };
    recentVotes: Array<{ type: 'up' | 'down'; timestamp: Date }>;
    skipReasons: string[];
    performanceScore: number; // Score calculado (0-100)
  } | null;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Métodos calculados
  get score(): number {
    return this.upvotes - this.downvotes;
  }

  get negativeVoteRatio(): number {
    const totalVotes = this.upvotes + this.downvotes;
    return totalVotes > 0 ? this.downvotes / totalVotes : 0;
  }

  get positiveVoteRatio(): number {
    const totalVotes = this.upvotes + this.downvotes;
    return totalVotes > 0 ? this.upvotes / totalVotes : 0;
  }

  get skipRate(): number {
    const totalPlays = this.playCount + this.skipCount;
    return totalPlays > 0 ? this.skipCount / totalPlays : 0;
  }

  // Calcular performance geral da música
  calculatePerformanceScore(): number {
    let score = 50; // Base score

    // Fator de votos (peso: 30%)
    const voteScore = this.positiveVoteRatio * 100;
    score += (voteScore - 50) * 0.3;

    // Fator de conclusão (peso: 25%)
    const completionScore = this.completionRate * 100;
    score += (completionScore - 50) * 0.25;

    // Fator de pulos (peso: 20%)
    const skipScore = (1 - this.skipRate) * 100;
    score += (skipScore - 50) * 0.2;

    // Fator de popularidade (peso: 15%)
    const popularityScore = Math.min(this.playCount / 10, 1) * 100;
    score += (popularityScore - 50) * 0.15;

    // Fator de recência (peso: 10%)
    const daysSinceLastPlay = this.lastPlayedAt 
      ? (Date.now() - this.lastPlayedAt.getTime()) / (1000 * 60 * 60 * 24)
      : 30;
    const recencyScore = Math.max(0, 100 - daysSinceLastPlay * 2);
    score += (recencyScore - 50) * 0.1;

    return Math.max(0, Math.min(100, score));
  }

  // Determinar se a música é problemática
  isProblematic(): boolean {
    return (
      this.negativeVoteRatio > 0.7 ||
      this.skipRate > 0.5 ||
      this.completionRate < 0.3 ||
      this.calculatePerformanceScore() < 30
    );
  }

  // Obter recomendação para a música
  getRecommendation(): 'keep' | 'monitor' | 'remove' | 'blacklist' {
    const score = this.calculatePerformanceScore();
    const totalVotes = this.upvotes + this.downvotes;

    if (score < 20 && totalVotes > 10) return 'blacklist';
    if (score < 30 || this.isProblematic()) return 'remove';
    if (score < 50) return 'monitor';
    return 'keep';
  }

  // Verificar compatibilidade para transição suave
  isCompatibleWith(nextTrack: PlaylistTrack): {
    compatible: boolean;
    score: number;
    reasons: string[];
  } {
    const reasons: string[] = [];
    let compatibilityScore = 50;

    // Verificar BPM (se disponível)
    if (this.bpm && nextTrack.bpm) {
      const bpmDiff = Math.abs(this.bpm - nextTrack.bpm);
      if (bpmDiff <= 10) {
        compatibilityScore += 20;
        reasons.push('BPM compatível');
      } else if (bpmDiff <= 30) {
        compatibilityScore += 10;
        reasons.push('BPM moderadamente compatível');
      } else {
        compatibilityScore -= 15;
        reasons.push('BPM muito diferente');
      }
    }

    // Verificar energia
    const energyDiff = Math.abs(this.energy - nextTrack.energy);
    if (energyDiff <= 0.2) {
      compatibilityScore += 15;
      reasons.push('Energia similar');
    } else if (energyDiff <= 0.4) {
      compatibilityScore += 5;
    } else {
      compatibilityScore -= 10;
      reasons.push('Energia muito diferente');
    }

    // Verificar gênero
    if (this.genre && nextTrack.genre) {
      if (this.genre === nextTrack.genre) {
        compatibilityScore += 15;
        reasons.push('Mesmo gênero');
      } else {
        compatibilityScore -= 5;
        reasons.push('Gêneros diferentes');
      }
    }

    // Verificar mood
    if (this.mood && nextTrack.mood) {
      if (this.mood === nextTrack.mood) {
        compatibilityScore += 10;
        reasons.push('Mesmo mood');
      }
    }

    return {
      compatible: compatibilityScore >= 60,
      score: Math.max(0, Math.min(100, compatibilityScore)),
      reasons
    };
  }

  // Atualizar analytics após reprodução
  updateAnalytics(playData: {
    completed: boolean;
    playDuration: number;
    skipped: boolean;
    skipReason?: string;
  }): void {
    const now = new Date();
    const hour = now.getHours().toString();
    const dayOfWeek = now.getDay().toString();

    if (!this.analytics) {
      this.analytics = {
        hourlyPlayCounts: {},
        dayOfWeekPlayCounts: {},
        recentVotes: [],
        skipReasons: [],
        performanceScore: 0
      };
    }

    // Atualizar contadores por horário
    this.analytics.hourlyPlayCounts[hour] = (this.analytics.hourlyPlayCounts[hour] || 0) + 1;
    this.analytics.dayOfWeekPlayCounts[dayOfWeek] = (this.analytics.dayOfWeekPlayCounts[dayOfWeek] || 0) + 1;

    // Atualizar estatísticas gerais
    if (playData.completed) {
      this.playCount++;
      this.averagePlayDuration = (this.averagePlayDuration * (this.playCount - 1) + playData.playDuration) / this.playCount;
    }

    if (playData.skipped) {
      this.skipCount++;
      if (playData.skipReason) {
        this.analytics.skipReasons.push(playData.skipReason);
      }
    }

    // Recalcular taxa de conclusão
    const totalPlays = this.playCount + this.skipCount;
    this.completionRate = totalPlays > 0 ? this.playCount / totalPlays : 0;

    // Atualizar performance score
    this.analytics.performanceScore = this.calculatePerformanceScore();

    this.lastPlayedAt = now;
  }
}
