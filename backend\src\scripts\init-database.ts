import "reflect-metadata";
import { AppDataSource } from "../config/database";
import { logger } from "../utils/logger";

async function initializeDatabase() {
  try {
    logger.info("🔄 Inicializando banco de dados...");
    
    // Conectar ao banco
    await AppDataSource.initialize();
    logger.info("✅ Conexão com PostgreSQL estabelecida");

    // Forçar sincronização das tabelas
    logger.info("🔄 Sincronizando schema do banco de dados...");
    await AppDataSource.synchronize(true); // true = drop existing tables
    logger.info("✅ Schema sincronizado com sucesso");

    // Verificar tabelas criadas
    const queryRunner = AppDataSource.createQueryRunner();
    const tables = await queryRunner.getTables();
    
    logger.info("📋 Tabelas criadas:");
    tables.forEach(table => {
      logger.info(`  - ${table.name}`);
    });

    await queryRunner.release();
    
    logger.info("🎉 Banco de dados inicializado com sucesso!");
    
  } catch (error) {
    logger.error("❌ Erro ao inicializar banco de dados:", error);
    throw error;
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
    }
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  initializeDatabase()
    .then(() => {
      logger.info("✅ Inicialização concluída");
      process.exit(0);
    })
    .catch((error) => {
      logger.error("❌ Falha na inicialização:", error);
      process.exit(1);
    });
}

export { initializeDatabase };
