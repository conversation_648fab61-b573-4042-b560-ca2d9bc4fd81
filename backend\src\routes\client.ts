import { Router } from "express";
import { body, param, validationResult } from "../utils/validation";
import { AppDataSource } from "../config/database";
import { ClientSession } from "../models/ClientSession";
import { Restaurant } from "../models/Restaurant";
import { Suggestion } from "../models/Suggestion";
import { Vote } from "../models/Vote";
import asyncHandler from "../middleware/asyncHandler";
import { ValidationError } from "../utils/errors";

const router = Router();

/**
 * @swagger
 * /api/v1/client/session:
 *   post:
 *     summary: Create a new client session
 *     tags: [Client]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - sessionToken
 *               - restaurantId
 *             properties:
 *               sessionToken:
 *                 type: string
 *                 format: uuid
 *               restaurantId:
 *                 type: string
 *               tableNumber:
 *                 type: string
 *               clientName:
 *                 type: string
 *               deviceInfo:
 *                 type: object
 *     responses:
 *       201:
 *         description: Session created successfully
 *       400:
 *         description: Invalid input
 *       404:
 *         description: Restaurant not found
 */
router.post(
  "/session",
  [
    body("sessionToken")
      .isUUID()
      .withMessage("Session token must be a valid UUID"),
    body("restaurantId").notEmpty().withMessage("Restaurant ID is required"),
    body("tableNumber")
      .optional()
      .isString()
      .withMessage("Table number must be a string"),
    body("clientName")
      .optional()
      .isString()
      .withMessage("Client name must be a string"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message:
          "Dados de entrada inválidos. Campos obrigatórios: sessionToken (UUID), restaurantId. Exemplo: { sessionToken: 'uuid', restaurantId: 'demo-restaurant', tableNumber: '1', clientName: 'João' }",
        errors: errors.array(),
      });
    }

    const { sessionToken, restaurantId, tableNumber, clientName, deviceInfo } =
      req.body;

    const restaurantRepository = AppDataSource.getRepository(Restaurant);
    const sessionRepository = AppDataSource.getRepository(ClientSession);

    // Verify restaurant exists
    const restaurant = await restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      throw new ValidationError("Restaurant not found");
    }

    // Check if session already exists
    let session = await sessionRepository.findOne({
      where: { sessionToken },
      relations: ["restaurant"],
    });

    if (session) {
      // Update existing session
      session.lastActivity = new Date();
      session.pageViews = (session.pageViews || 0) + 1;
      if (tableNumber) session.tableNumber = tableNumber;
      if (clientName) session.clientName = clientName;
      if (deviceInfo) session.deviceInfo = deviceInfo;

      session.updateActivity();
      await sessionRepository.save(session);
    } else {
      // Create new session
      session = sessionRepository.create({
        sessionToken,
        restaurant,
        tableNumber,
        clientName,
        ipAddress: req.ip,
        userAgent: req.get("User-Agent"),
        deviceInfo,
        lastActivity: new Date(),
        pageViews: 1,
        suggestionsCount: 0,
        votesCount: 0,
        sessionDuration: 0,
        isActive: true,
      });

      await sessionRepository.save(session);
    }

    // Add gamification stats
    const sessionWithStats = {
      ...session.toJSON(),
      points: 0,
      level: 1,
      badges: [],
      streak: 0,
    };

    res.status(201).json(sessionWithStats);
  })
);

/**
 * @swagger
 * /api/v1/client/session/{sessionToken}:
 *   get:
 *     summary: Get client session info
 *     tags: [Client]
 *     parameters:
 *       - in: path
 *         name: sessionToken
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Session info retrieved
 *       404:
 *         description: Session not found
 */
router.get(
  "/session/:sessionToken",
  [
    param("sessionToken")
      .isUUID()
      .withMessage("Session token must be a valid UUID"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: "Dados de entrada inválidos",
        errors: errors.array(),
      });
    }

    const { sessionToken } = req.params;
    const sessionRepository = AppDataSource.getRepository(ClientSession);

    const session = await sessionRepository.findOne({
      where: { sessionToken },
      relations: ["restaurant"],
    });

    if (!session) {
      throw new ValidationError("Session not found");
    }

    // Update activity
    session.updateActivity();
    await sessionRepository.save(session);

    res.json(session.toJSON());
  })
);

/**
 * @swagger
 * /api/v1/client/session/{sessionToken}/stats:
 *   put:
 *     summary: Update client session stats
 *     tags: [Client]
 *     parameters:
 *       - in: path
 *         name: sessionToken
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               action:
 *                 type: string
 *                 enum: [suggestion, vote, page_view]
 *               points:
 *                 type: number
 *     responses:
 *       200:
 *         description: Stats updated
 *       404:
 *         description: Session not found
 */
router.put(
  "/session/:sessionToken/stats",
  [
    param("sessionToken")
      .isUUID()
      .withMessage("Session token must be a valid UUID"),
    body("action")
      .isIn(["suggestion", "vote", "page_view"])
      .withMessage("Action must be suggestion, vote, or page_view"),
    body("points")
      .optional()
      .isInt({ min: 0 })
      .withMessage("Points must be a non-negative integer"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: "Dados de entrada inválidos",
        errors: errors.array(),
      });
    }

    const { sessionToken } = req.params;
    const { action, points } = req.body;
    const sessionRepository = AppDataSource.getRepository(ClientSession);

    const session = await sessionRepository.findOne({
      where: { sessionToken },
    });

    if (!session) {
      throw new ValidationError("Session not found");
    }

    // Update stats based on action
    switch (action) {
      case "suggestion":
        session.incrementSuggestions();
        break;
      case "vote":
        session.incrementVotes();
        break;
      case "page_view":
        session.incrementPageViews();
        break;
    }

    if (points) {
      session.points = (session.points || 0) + points;
    }

    await sessionRepository.save(session);

    res.json({
      message: "Stats updated successfully",
      session: session.toJSON(),
    });
  })
);

/**
 * @swagger
 * /api/v1/client/{sessionToken}/stats:
 *   get:
 *     summary: Get client session statistics for badge system
 *     tags: [Client]
 *     parameters:
 *       - in: path
 *         name: sessionToken
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Client statistics
 *       404:
 *         description: Session not found
 */
router.get(
  "/:sessionToken/stats",
  [
    param("sessionToken")
      .isUUID()
      .withMessage("Session token must be a valid UUID"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: "Dados de entrada inválidos",
        errors: errors.array(),
      });
    }

    const { sessionToken } = req.params;
    const sessionRepository = AppDataSource.getRepository(ClientSession);
    const suggestionRepository = AppDataSource.getRepository(Suggestion);
    const voteRepository = AppDataSource.getRepository(Vote);

    const session = await sessionRepository.findOne({
      where: { sessionToken },
      relations: ["restaurant"],
    });

    if (!session) {
      throw new ValidationError("Session not found");
    }

    // Fetch suggestions and votes concurrently
    const [suggestions, votes] = await Promise.all([
      suggestionRepository.find({
        where: { clientSessionId: sessionToken },
        relations: ["votes"],
      }),
      voteRepository.find({
        where: { clientSessionId: sessionToken },
      }),
    ]);

    // Calculate statistics
    const approvedSuggestions = suggestions.filter(
      (s) =>
        s.status === "approved" ||
        s.status === "playing" ||
        s.status === "played"
    ).length;

    const topVotedSongs = suggestions.filter((s) => s.upvotes >= 5).length;

    // Calculate consecutive days
    const today = new Date();
    const sessionAge = Math.floor(
      (today.getTime() - session.createdAt.getTime()) / (1000 * 60 * 60 * 24)
    );
    const consecutiveDays = Math.min(sessionAge + 1, 7);

    // Calculate favorite genres based on suggestions
    const genreCounts = suggestions.reduce((acc, s) => {
      const genre = s.genre || "unknown";
      acc[genre] = (acc[genre] || 0) + 1;
      return acc;
    }, {});
    const favoriteGenres = Object.entries(genreCounts)
      .sort(([, a], [, b]) => (b as number) - (a as number))
      .slice(0, 3)
      .map(([genre]) => genre);

    const stats = {
      totalSuggestions: suggestions.length,
      totalVotes: votes.length,
      approvedSuggestions,
      consecutiveDays,
      topVotedSongs,
      averageVotesPerSuggestion:
        suggestions.length > 0
          ? suggestions.reduce((sum, s) => sum + s.upvotes + s.downvotes, 0) /
            suggestions.length
          : 0,
      favoriteGenres: favoriteGenres.length > 0 ? favoriteGenres : ["unknown"],
      lastActivity: session.lastActivity,
      sessionDuration: session.sessionDuration,
      pageViews: session.pageViews,
      engagementLevel: session.getEngagementLevel(),
    };

    res.json({
      success: true,
      stats,
      sessionId: sessionToken,
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /api/v1/client/{sessionToken}/badges:
 *   get:
 *     summary: Get client badges and achievements
 *     tags: [Client]
 *     parameters:
 *       - in: path
 *         name: sessionToken
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Client badges
 *       404:
 *         description: Session not found
 */
router.get(
  "/:sessionToken/badges",
  [
    param("sessionToken")
      .isUUID()
      .withMessage("Session token must be a valid UUID"),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: "Dados de entrada inválidos",
        errors: errors.array(),
      });
    }

    const { sessionToken } = req.params;
    const sessionRepository = AppDataSource.getRepository(ClientSession);

    const session = await sessionRepository.findOne({
      where: { sessionToken },
    });

    if (!session) {
      throw new ValidationError("Session not found");
    }

    // Fetch stats using the stats endpoint internally
    const statsResponse = await fetch(
      `http://localhost:8001/api/v1/client/${sessionToken}/stats`
    );
    if (!statsResponse.ok) {
      throw new ValidationError("Session stats not found");
    }

    const responseData = await statsResponse.json();
    const stats = (responseData as any).stats || {
      total: 0,
      approved: 0,
      pending: 0,
      rejected: 0,
      totalAmount: 0,
    };

    // Define available badges
    const availableBadges = [
      {
        id: "first_suggestion",
        name: "Primeira Sugestão",
        description: "Sugeriu sua primeira música",
        requirement: 1,
        category: "suggestions",
        icon: "Music",
        color: "blue",
      },
      {
        id: "music_lover",
        name: "Amante da Música",
        description: "Sugeriu 10 músicas",
        requirement: 10,
        category: "suggestions",
        icon: "Heart",
        color: "red",
      },
      {
        id: "dj_master",
        name: "DJ Master",
        description: "Sugeriu 50 músicas",
        requirement: 50,
        category: "suggestions",
        icon: "Crown",
        color: "purple",
      },
      {
        id: "first_vote",
        name: "Primeiro Voto",
        description: "Votou pela primeira vez",
        requirement: 1,
        category: "votes",
        icon: "ThumbsUp",
        color: "green",
      },
      {
        id: "active_voter",
        name: "Eleitor Ativo",
        description: "Fez 25 votos",
        requirement: 25,
        category: "votes",
        icon: "Target",
        color: "orange",
      },
      {
        id: "vote_champion",
        name: "Campeão dos Votos",
        description: "Fez 100 votos",
        requirement: 100,
        category: "votes",
        icon: "Trophy",
        color: "yellow",
      },
      {
        id: "hit_maker",
        name: "Criador de Hits",
        description: "Teve 5 sugestões aprovadas",
        requirement: 5,
        category: "engagement",
        icon: "Star",
        color: "indigo",
      },
      {
        id: "trending_master",
        name: "Mestre das Tendências",
        description: "Teve 3 músicas com mais de 5 votos",
        requirement: 3,
        category: "engagement",
        icon: "TrendingUp",
        color: "pink",
      },
    ];

    // Calculate badge progress
    const badges = availableBadges.map((badge) => {
      let currentProgress = 0;

      switch (badge.category) {
        case "suggestions":
          currentProgress = stats.totalSuggestions;
          break;
        case "votes":
          currentProgress = stats.totalVotes;
          break;
        case "engagement":
          if (badge.id === "hit_maker") {
            currentProgress = stats.approvedSuggestions;
          } else if (badge.id === "trending_master") {
            currentProgress = stats.topVotedSongs;
          }
          break;
      }

      const unlocked = currentProgress >= badge.requirement;

      return {
        ...badge,
        currentProgress,
        unlocked,
        unlockedAt: unlocked ? new Date().toISOString() : null,
        progressPercentage: Math.min(
          100,
          (currentProgress / badge.requirement) * 100
        ),
      };
    });

    const unlockedBadges = badges.filter((b) => b.unlocked);
    const nextBadge = badges.find((b) => !b.unlocked) || null;

    res.json({
      success: true,
      badges,
      summary: {
        totalBadges: badges.length,
        unlockedBadges: unlockedBadges.length,
        nextBadge,
        completionPercentage: (unlockedBadges.length / badges.length) * 100,
      },
      sessionId: sessionToken,
      timestamp: new Date().toISOString(),
    });
  })
);

export default router;
