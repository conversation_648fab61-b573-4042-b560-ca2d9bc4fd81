#!/usr/bin/env node

// Script para testar todos os endpoints usados pelo player
const http = require("http");

const testEndpoint = (path, description) => {
  return new Promise((resolve) => {
    console.log(`🔍 Testando ${description}...`);

    const options = {
      hostname: "localhost",
      port: 8001,
      path: `/api/v1${path}`,
      method: "GET",
    };

    const req = http.request(options, (res) => {
      console.log(
        `${
          res.statusCode >= 200 && res.statusCode < 300 ? "✅" : "❌"
        } ${description}: ${res.statusCode}`
      );

      let data = "";
      res.on("data", (chunk) => {
        data += chunk;
      });

      res.on("end", () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          try {
            const parsed = JSON.parse(data);
            console.log(
              `📊 ${description} - Items encontrados:`,
              parsed.suggestions?.length || parsed.queue?.length || "N/A"
            );
          } catch (e) {
            console.log(`📊 ${description} - Response OK`);
          }
        } else {
          console.log(`❌ ${description} - Erro:`, data);
        }
        resolve();
      });
    });

    req.on("error", (e) => {
      console.error(`❌ ${description} - Erro de conexão: ${e.message}`);
      resolve();
    });

    req.setTimeout(5000, () => {
      console.error(`⏰ ${description} - Timeout`);
      req.destroy();
      resolve();
    });

    req.end();
  });
};

const testPlayerEndpoint = (path, method, description, bodyObj) => {
  return new Promise((resolve) => {
    console.log(`🔍 Testando ${description}...`);

    const options = {
      hostname: "localhost",
      port: 8001,
      path: `/api/v1${path}`,
      method: method,
      headers: {
        "Content-Type": "application/json",
      },
    };

    const req = http.request(options, (res) => {
      console.log(
        `${
          res.statusCode >= 200 && res.statusCode < 400 ? "✅" : "❌"
        } ${description}: ${res.statusCode}`
      );
      resolve();
    });

    req.on("error", (e) => {
      console.error(`❌ ${description} - Erro: ${e.message}`);
      resolve();
    });

    req.setTimeout(3000, () => {
      console.error(`⏰ ${description} - Timeout`);
      req.destroy();
      resolve();
    });

    if (method === "POST") {
      const payload = bodyObj || { songId: "demo" };
      req.write(JSON.stringify(payload));
    }

    req.end();
  });
};

async function runTests() {
  console.log("🚀 Testando conectividade do Player de Música\n");

  // Testar endpoints de dados
  await testEndpoint("/suggestions/demo-restaurant", "Sugestões");
  await testEndpoint("/playback-queue/demo-restaurant", "Fila de Reprodução");

  // Testar endpoints do player
  await testPlayerEndpoint(
    "/playback/demo-restaurant/state",
    "GET",
    "Estado do Player"
  );
  // Esperamos 400 para songId inválido
  await testPlayerEndpoint(
    "/playback/demo-restaurant/play",
    "POST",
    "Play (songId inválido)",
    { songId: "demo" }
  );
  // Buscar suggestionId válido e testar Play 200
  let suggestionId = null;
  try {
    const options = { hostname: "localhost", port: 8001, path: "/api/v1/suggestions/demo-restaurant", method: "GET" };
    const suggestions = await new Promise((resolve) => {
      const req = http.request(options, (res) => {
        let data = "";
        res.on("data", (c) => (data += c));
        res.on("end", () => {
          try { resolve(JSON.parse(data)); } catch { resolve(null); }
        });
      });
      req.on("error", () => resolve(null));
      req.end();
    });
    suggestionId = suggestions?.suggestions?.[0]?.id || null;
  } catch {}
  if (suggestionId) {
    await testPlayerEndpoint(
      "/playback/demo-restaurant/play",
      "POST",
      "Play (songId válido)",
      { songId: suggestionId }
    );
  }
  await testPlayerEndpoint("/playback/demo-restaurant/pause", "POST", "Pause");
  await testPlayerEndpoint("/playback/demo-restaurant/stop", "POST", "Stop");

  console.log("\n📋 Resumo dos testes completado!");
  console.log(
    "🎵 Se os endpoints básicos funcionam, o player deve funcionar corretamente"
  );
  console.log(
    "💡 Endpoints com erro 404 são normais - nem todos estão implementados ainda"
  );
}

runTests();
