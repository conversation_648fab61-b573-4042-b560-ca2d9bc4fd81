import React, { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Play,
  Clock,
  Users,
  CreditCard,
  Music,
  ChevronDown,
  ChevronUp,
  Star,
  Volume2,
} from "lucide-react";
import { buildApiUrl } from "@/config/api";
import { useWebSocket } from "@/services/websocket";
// import { toast } from "react-hot-toast"; // removido: curtidas não são exibidas aqui

interface QueueItem {
  id: string;
  suggestionId: string;
  youtubeVideoId?: string;
  title: string;
  artist: string;
  duration: number;
  thumbnailUrl?: string;
  isPaid: boolean;
  paymentAmount?: number;
  clientName?: string;
  tableName?: string;
  position: number;
  addedAt?: string;
  estimatedPlayTime?: string;
}

interface QueueStats {
  totalItems: number;
  paidItems: number;
  freeItems: number;
  totalDuration: number;
  estimatedWaitTime: number;
  currentlyPlaying?: QueueItem;
  nextUp?: QueueItem;
}

interface PlaybackQueueProps {
  restaurantId: string;
  sessionId?: string;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

const PlaybackQueue: React.FC<PlaybackQueueProps> = ({
  restaurantId,
  sessionId,
  isCollapsed = false,
  onToggleCollapse,
}) => {
  const [queue, setQueue] = useState<QueueItem[]>([]);
  const [stats, setStats] = useState<QueueStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [currentlyPlaying, setCurrentlyPlaying] = useState<QueueItem | null>(
    null
  );
  const [ranking, setRanking] = useState<Array<{ youtubeVideoId: string; title?: string; voteCount: number; isPaid?: boolean; normalVoteCount?: number; superVoteCount?: number }>>([]);
  const { on, off, joinRestaurant } = useWebSocket();

  // Definir handlers antes dos efeitos para evitar TDZ em dependências
  const loadQueue = useCallback(async () => {
    try {
      setLoading(true);

      const [queueRes, rankRes] = await Promise.all([
        fetch(buildApiUrl(`/playback-queue/${restaurantId}`)),
        fetch(buildApiUrl(`/collaborative-playlist/${restaurantId}/ranking`)),
      ]);

      if (queueRes.ok) {
        const data = await queueRes.json();
        const sorted = Array.isArray(data.queue)
          ? [...data.queue].sort((a: any, b: any) => {
              const hasPosA = typeof a.position === "number" && a.position > 0;
              const hasPosB = typeof b.position === "number" && b.position > 0;
              if (hasPosA && hasPosB) return a.position - b.position;
              if (hasPosA) return -1;
              if (hasPosB) return 1;
              if (a.isPaid !== b.isPaid) return a.isPaid ? -1 : 1;
              const ta = a.addedAt ? new Date(a.addedAt).getTime() : 0;
              const tb = b.addedAt ? new Date(b.addedAt).getTime() : 0;
              return ta - tb;
            })
          : [];
        setQueue(sorted);
        setStats(data.stats);
        setCurrentlyPlaying(data.currentlyPlaying || null);
      }

      if (rankRes.ok) {
        const rjson = await rankRes.json();
        setRanking(Array.isArray(rjson?.data) ? rjson.data : []);
      }
    } catch (error) {
      console.error("Erro ao carregar fila/ranking:", error);
    } finally {
      setLoading(false);
    }
  }, [restaurantId]);

  const loadRanking = useCallback(async () => {
    try {
      const rankRes = await fetch(
        buildApiUrl(`/collaborative-playlist/${restaurantId}/ranking`)
      );
      if (rankRes.ok) {
        const rjson = await rankRes.json();
        setRanking(Array.isArray(rjson?.data) ? rjson.data : []);
      }
    } catch (e) {
      console.warn("Falha ao recarregar ranking:", e);
    }
  }, [restaurantId]);

  // Carregar fila e ranking inicialmente
  useEffect(() => {
    loadQueue();
    joinRestaurant(restaurantId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [restaurantId]);

  // Atualizações em tempo real via WebSocket
  useEffect(() => {
    const handleQueueUpdate = (data: any) => {
      try {
        if (data?.queue) {
          const sorted = Array.isArray(data.queue)
            ? [...data.queue].sort((a: any, b: any) => {
                const hasPosA = typeof a.position === "number" && a.position > 0;
                const hasPosB = typeof b.position === "number" && b.position > 0;
                if (hasPosA && hasPosB) return a.position - b.position;
                if (hasPosA) return -1;
                if (hasPosB) return 1;
                // Fallback: pagas primeiro
                if (a.isPaid !== b.isPaid) return a.isPaid ? -1 : 1;
                // Fallback: por data de adição crescente
                const ta = a.addedAt ? new Date(a.addedAt).getTime() : 0;
                const tb = b.addedAt ? new Date(b.addedAt).getTime() : 0;
                return ta - tb;
              })
            : [];
          setQueue(sorted);
        }
        if (data?.stats) setStats(data.stats);
        if (data?.currentlyPlaying) setCurrentlyPlaying(data.currentlyPlaying);
      } catch (e) {
        console.warn("Falha ao aplicar queue-update:", e);
      }
      // Atualizar ranking oportunisticamente
      loadRanking();
    };

    // Eventos que exigem recarga autoritativa da fila
    const triggerReload = () => {
      // Recarregar lista + stats + currentlyPlaying a partir da API
      loadQueue();
      // Ranking também pode ter mudado
      loadRanking();
    };

    on("queue-update" as any, handleQueueUpdate as any);
    on("reorderSelected" as any, triggerReload as any);
    on("playlistReordered" as any, triggerReload as any);
    // Alias legado
    on("reorder-selected" as any, triggerReload as any);
    return () => {
      off("queue-update" as any, handleQueueUpdate as any);
      off("reorderSelected" as any, triggerReload as any);
      off("playlistReordered" as any, triggerReload as any);
      off("reorder-selected" as any, triggerReload as any);
    };
  }, [on, off, loadQueue, loadRanking]);

  // Likes no box de filas foram removidos; sem refresh de likes aqui

  // sendLike removido

  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  const formatWaitTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    if (minutes > 60) {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return `${hours}h ${remainingMinutes}m`;
    }
    return `${minutes}m`;
  };

  const isMyMusic = (_item: QueueItem): boolean => {
    // Sem sessionId na tipagem atual; manter sempre false por ora
    return false;
  };

  if (loading && queue.length === 0) {
    return (
      <div className="bg-white/5 rounded-lg p-4 border border-white/10">
        <div className="flex items-center justify-center space-x-2">
          <div className="w-4 h-4 border-2 border-purple-400 border-t-transparent rounded-full animate-spin" />
          <span className="text-white">Carregando fila...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 overflow-hidden shadow-xl">
      {/* Header melhorado */}
      <div
        className="p-6 bg-gradient-to-r from-purple-500/20 to-indigo-500/20 cursor-pointer hover:from-purple-500/30 hover:to-indigo-500/30 transition-all"
        onClick={onToggleCollapse}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full flex items-center justify-center shadow-lg">
              <Music className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-white">Fila de Reprodução</h3>
              {stats && (
                <div className="flex items-center gap-4 mt-1 text-sm">
                  <span className="text-purple-200">
                    {stats.totalItems} música{stats.totalItems !== 1 ? 's' : ''}
                  </span>
                  {stats.paidItems > 0 && (
                    <span className="flex items-center gap-1 text-yellow-300">
                      <CreditCard className="w-3 h-3" />
                      {stats.paidItems} paga{stats.paidItems !== 1 ? 's' : ''}
                    </span>
                  )}
                  <span className="text-indigo-300">
                    {formatWaitTime(stats.estimatedWaitTime)} total
                  </span>
                </div>
              )}
              {ranking.length > 0 && (
                <div className="mt-2 p-3 bg-emerald-500/20 rounded-lg border border-emerald-400/30">
                  <p className="text-emerald-300 text-sm font-medium">
                    🎯 Próxima por votação: {ranking[0]?.title || ranking[0]?.youtubeVideoId}
                  </p>
                  <p className="text-emerald-200 text-xs">
                    {ranking[0]?.voteCount} votos • Será tocada automaticamente
                  </p>
                </div>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {stats && stats.paidItems > 0 && (
              <div className="flex items-center space-x-1 bg-yellow-500/20 px-3 py-1 rounded-full">
                <CreditCard className="w-4 h-4 text-yellow-400" />
                <span className="text-yellow-300 font-medium">{stats.paidItems}</span>
              </div>
            )}

            {onToggleCollapse && (
              <button className="p-2 bg-white/10 rounded-lg text-purple-300 hover:text-white hover:bg-white/20 transition-all">
                {isCollapsed ? (
                  <ChevronDown className="w-5 h-5" />
                ) : (
                  <ChevronUp className="w-5 h-5" />
                )}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Currently Playing melhorado */}
      {currentlyPlaying && !isCollapsed && (
        <div className="p-6 bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-y border-green-400/30">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <img
                src={
                  currentlyPlaying.thumbnailUrl ||
                  `https://img.youtube.com/vi/${currentlyPlaying.suggestionId}/mqdefault.jpg`
                }
                alt={currentlyPlaying.title}
                className="w-16 h-16 rounded-xl object-cover shadow-lg"
              />
              <div className="absolute inset-0 flex items-center justify-center bg-black/40 rounded-xl">
                <Volume2 className="w-5 h-5 text-green-400 animate-pulse" />
              </div>
            </div>

            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-2">
                <Play className="w-4 h-4 text-green-400" />
                <span className="text-green-400 text-sm font-bold uppercase tracking-wide">
                  Tocando Agora
                </span>
                {currentlyPlaying.isPaid && (
                  <div className="flex items-center gap-1 bg-yellow-500/20 px-2 py-1 rounded-full">
                    <CreditCard className="w-3 h-3 text-yellow-400" />
                    <span className="text-yellow-300 text-xs font-medium">SuperVoto</span>
                  </div>
                )}
              </div>
              <h4 className="text-white font-bold text-lg truncate">
                {currentlyPlaying.title}
              </h4>
              <p className="text-gray-300 text-sm truncate">
                {currentlyPlaying.artist}
              </p>
              {currentlyPlaying.tableName && (
                <p className="text-purple-300 text-xs">
                  Por {currentlyPlaying.tableName}
                </p>
              )}
            </div>

            <div className="text-right">
              <div className="text-white text-sm">
                {formatDuration(currentlyPlaying.duration)}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Ranking melhorado (Top 5 pelo voto ponderado) */}
      {!isCollapsed && ranking.length > 0 && (
        <div className="p-6 border-t border-white/20 bg-gradient-to-r from-indigo-500/10 to-purple-500/10">
          <div className="flex items-center gap-2 mb-4">
            <Star className="w-5 h-5 text-yellow-400" />
            <h4 className="text-lg font-bold text-white">Mais Votadas Agora</h4>
          </div>
          <div className="space-y-3">
            {ranking.slice(0, 5).map((r, i) => (
              <div key={r.youtubeVideoId} className={`p-3 rounded-lg border transition-all ${
                i === 0
                  ? 'bg-gradient-to-r from-emerald-500/20 to-green-500/20 border-emerald-400/30'
                  : 'bg-white/5 border-white/10 hover:bg-white/10'
              }`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold text-sm ${
                      i === 0 ? 'bg-emerald-500 text-white' :
                      i === 1 ? 'bg-gray-400 text-black' :
                      i === 2 ? 'bg-amber-600 text-white' :
                      'bg-indigo-500/30 text-indigo-300'
                    }`}>
                      #{i + 1}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-white truncate">
                        {r.title || r.youtubeVideoId}
                        {i === 0 && (
                          <span className="ml-2 px-2 py-1 bg-emerald-500/30 text-emerald-300 rounded-full text-xs font-bold">
                            🎯 Próxima
                          </span>
                        )}
                      </div>
                      <div className="flex items-center gap-2 mt-1 text-xs">
                        <span className="flex items-center gap-1 text-indigo-300">
                          <Users className="w-3 h-3" />
                          {r.voteCount} votos
                        </span>
                        {r.isPaid && (
                          <span className="flex items-center gap-1 text-yellow-300 bg-yellow-500/20 px-2 py-1 rounded">
                            <CreditCard className="w-3 h-3" />
                            SuperVoto
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Queue List */}
      <AnimatePresence>
        {!isCollapsed && (
          <motion.div
            initial={{ height: 0 }}
            animate={{ height: "auto" }}
            exit={{ height: 0 }}
            className="overflow-hidden"
          >
            {queue.length === 0 ? (
              <div className="p-8 text-center">
                <Music className="w-16 h-16 text-gray-500 mx-auto mb-4" />
                <p className="text-gray-400 text-lg font-medium">Fila vazia</p>
                <p className="text-gray-500 text-sm">
                  Seja o primeiro a sugerir uma música!
                </p>
              </div>
            ) : (
              <div className="p-4 space-y-3 max-h-96 overflow-y-auto">
                {queue.map((item, index) => (
                  <motion.div
                    key={item.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className={`p-4 rounded-lg border transition-all hover:shadow-lg ${
                      isMyMusic(item)
                        ? "bg-blue-500/20 border-blue-400/30 hover:bg-blue-500/30"
                        : "bg-white/5 border-white/10 hover:bg-white/10"
                    }`}
                  >
                    <div className="flex items-center space-x-4">
                      {/* Position */}
                      <div className="flex-shrink-0">
                        <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold ${
                          item.isPaid
                            ? "bg-yellow-500 text-black"
                            : "bg-purple-500/30 text-purple-300"
                        }`}>
                          #{item.position}
                        </div>
                      </div>

                      {/* Thumbnail */}
                      <div className="relative">
                        <img
                          src={
                            item.thumbnailUrl ||
                            `https://img.youtube.com/vi/${item.suggestionId}/mqdefault.jpg`
                          }
                          alt={item.title}
                          className="w-14 h-14 rounded-lg object-cover shadow-md"
                        />
                        {item.isPaid && (
                          <div className="absolute -top-1 -right-1 w-5 h-5 bg-yellow-500 rounded-full flex items-center justify-center shadow-lg">
                            <CreditCard className="w-3 h-3 text-black" />
                          </div>
                        )}
                      </div>

                      {/* Info melhorada */}
                      <div className="flex-1 min-w-0">
                        <h4 className={`font-bold text-lg truncate ${
                          isMyMusic(item) ? "text-blue-300" : "text-white"
                        }`}>
                          {item.title}
                        </h4>
                        <p className="text-purple-200 text-sm truncate mb-2">
                          {item.artist}
                        </p>

                        <div className="flex flex-wrap items-center gap-2">
                          {item.tableName && (
                            <div className="flex items-center gap-1 bg-purple-500/20 px-2 py-1 rounded-full">
                              <Users className="w-3 h-3 text-purple-400" />
                              <span className="text-purple-300 text-xs font-medium">
                                {item.tableName}
                              </span>
                            </div>
                          )}

                          {item.isPaid && item.paymentAmount && (
                            <div className="flex items-center gap-1 bg-yellow-500/20 px-2 py-1 rounded-full">
                              <CreditCard className="w-3 h-3 text-yellow-400" />
                              <span className="text-yellow-300 text-xs font-bold">
                                R$ {Number(item.paymentAmount || 0).toFixed(2)}
                              </span>
                            </div>
                          )}

                          {typeof (item as any).voteCount === "number" && (
                            <div className="flex items-center gap-1 bg-indigo-500/20 px-2 py-1 rounded-full">
                              <Star className="w-3 h-3 text-indigo-400" />
                              <span className="text-indigo-300 text-xs font-medium">
                                {(item as any).voteCount} votos
                              </span>
                            </div>
                          )}

                          {isMyMusic(item) && (
                            <div className="flex items-center gap-1 bg-blue-500/20 px-2 py-1 rounded-full">
                              <Star className="w-3 h-3 text-blue-400" />
                              <span className="text-blue-300 text-xs font-bold">
                                Sua música
                              </span>
                            </div>
                          )}
                        </div>

                        {/* Likes removidos no box de filas (curtidas ficam no card principal) */}
                      </div>

                      {/* Duration & Wait Time */}
                      <div className="text-right flex-shrink-0">
                        <div className="text-white text-sm">
                          {formatDuration(item.duration)}
                        </div>
                        {item.estimatedPlayTime && (
                          <div className="flex items-center space-x-1 text-gray-400 text-xs">
                            <Clock className="w-3 h-3" />
                            <span>
                              {new Date(
                                item.estimatedPlayTime
                              ).toLocaleTimeString("pt-BR", {
                                hour: "2-digit",
                                minute: "2-digit",
                              })}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Footer Stats */}
      {!isCollapsed && stats && stats.totalItems > 0 && (
        <div className="p-3 bg-white/5 border-t border-white/10">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1 text-purple-300">
                <Music className="w-3 h-3" />
                <span>{stats.totalItems} total</span>
              </div>

              {stats.paidItems > 0 && (
                <div className="flex items-center space-x-1 text-yellow-400">
                  <CreditCard className="w-3 h-3" />
                  <span>{stats.paidItems} pagas</span>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-1 text-gray-400">
              <Clock className="w-3 h-3" />
              <span>{formatWaitTime(stats.totalDuration)} total</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PlaybackQueue;
