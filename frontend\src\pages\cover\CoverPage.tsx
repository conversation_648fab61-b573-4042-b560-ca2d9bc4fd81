import React, { useEffect, useMemo, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { useWebSocket } from "@/services/websocket";
import { cooldownService } from "@/services/cooldownService";
import { buildApiUrl, getAuthHeaders } from "@/config/api";
import {
  Star,
  Clock,
  CreditCard,
  MessageSquare,
  Timer,
  Music,
  Play,
  Pause,
  Users,
  TrendingUp,
  RefreshCw,
  Volume2,
  Heart,
  Zap,
  Award,
  Eye
} from "lucide-react";

type RankingItem = {
  youtubeVideoId: string;
  title?: string;
  artist?: string;
  voteCount: number;
  isPaid?: boolean;
  normalVoteCount?: number;
  superVoteCount?: number;
  thumbnailUrl?: string;
  isInCooldown?: boolean;
  cooldownTimeLeft?: number; // em segundos
};

type QueueItem = {
  id: string;
  suggestionId: string;
  youtubeVideoId?: string;
  title: string;
  artist: string;
  duration: number;
  thumbnailUrl?: string;
  isPaid: boolean;
  paymentAmount?: number;
  position: number;
  isInCooldown?: boolean;
  cooldownTimeLeft?: number; // em segundos
};

type NowPlaying = QueueItem | null;

type SuperNote = {
  id: string;
  at: string; // ISO
  amount: number;
  voteWeight: number;
  title?: string;
  artist?: string;
  youtubeVideoId?: string;
  clientName?: string;
  tableNumber?: number;
  message?: string;
};

const fmtTime = (seconds: number) => {
  const m = Math.floor(seconds / 60);
  const s = seconds % 60;
  return `${m}:${String(s).padStart(2, "0")}`;
};

type PlaylistTrack = {
  id?: string;
  title: string;
  artist: string;
  duration: number;
  thumbnailUrl?: string;
  youtubeVideoId: string;
  formattedDuration?: string;
  position?: number;
  isAvailable?: boolean;
};

const CoverPage: React.FC = () => {
  const { restaurantId } = useParams<{ restaurantId: string }>();
  const { on, off, joinRestaurant } = useWebSocket();
  const [nowPlaying, setNowPlaying] = useState<NowPlaying>(null);
  const [queue, setQueue] = useState<QueueItem[]>([]);
  const [ranking, setRanking] = useState<RankingItem[]>([]);
  const [notes, setNotes] = useState<SuperNote[]>([]);
  const [nextReorderAt, setNextReorderAt] = useState<number | null>(null);
  const [nowTs, setNowTs] = useState<number>(Date.now());
  const [fullPlaylist, setFullPlaylist] = useState<PlaylistTrack[]>([]);
  const [playlistLoading, setPlaylistLoading] = useState<boolean>(false);
  const [playlistError, setPlaylistError] = useState<string | null>(null);
  // Cooldown timer global para atualizar contagem regressiva
  useEffect(() => {
    if (restaurantId) cooldownService.startCooldownTimer();
  }, [restaurantId]);


  // Guardas para evitar resets/jitter do countdown
  const lastVoteTsRef = useRef<number>(0);
  const lastNextSetRef = useRef<number>(0);

  // Buscar playlist completa com Auth e retry leve (transiente quando backend sobe)
  const refetchFullPlaylist = async () => {
    if (!restaurantId) return;
    setPlaylistLoading(true);
    setPlaylistError(null);
    const headers = getAuthHeaders();
    let attempts = 0;
    const maxAttempts = 3;
    while (attempts < maxAttempts) {
      try {
        const res = await fetch(
          buildApiUrl(`/restaurants/${restaurantId}/playlist`),
          { headers }
        );
        if (!res.ok) {
          // 502/500 podem ocorrer se o upstream ainda está iniciando
          if ((res.status === 502 || res.status === 500) && attempts < maxAttempts - 1) {
            attempts++;
            await new Promise((r) => setTimeout(r, 500 * attempts));
            continue;
          }
          throw new Error(`HTTP ${res.status}`);
        }
        const json = await res.json();
        const items: PlaylistTrack[] = Array.isArray(json?.results) ? json.results : [];
        // Ordenar por position quando disponível
        items.sort((a, b) => (a.position || 0) - (b.position || 0));
        setFullPlaylist(items);
        setPlaylistLoading(false);
        return;
      } catch (err: any) {
        if (attempts < maxAttempts - 1) {
          attempts++;
          await new Promise((r) => setTimeout(r, 500 * attempts));
          continue;
        }
        setPlaylistError(err?.message || "Falha ao carregar playlist");
        setPlaylistLoading(false);
        return;
      }
    }
  };


  // inicialização
  useEffect(() => {
    if (!restaurantId) return;
    joinRestaurant(restaurantId);
    // carregar estado inicial
    const loadInitial = async () => {
      try {
        const auth = getAuthHeaders();
        const [queueRes, rankRes] = await Promise.all([
          fetch(buildApiUrl(`/playback-queue/${restaurantId}`), { headers: auth }),
          fetch(buildApiUrl(`/collaborative-playlist/${restaurantId}/ranking`), { headers: auth }),
        ]);
        if (queueRes.ok) {
          const q = await queueRes.json();
          setQueue(q.queue || []);
          setNowPlaying(q.currentlyPlaying || null);
        }
        if (rankRes.ok) {
          const r = await rankRes.json();

        // Enriquecer ranking com status de cooldown (para UI e lógica)
        try {
          const withCooldown = await Promise.all(
            (Array.isArray(r?.data) ? r.data : []).map(async (x: any) => {
              const id = x.youtubeVideoId;
              try {
                const status = await cooldownService.checkSongCooldown(restaurantId!, id);
                return { ...x, isInCooldown: status.isInCooldown, cooldownTimeLeft: status.cooldownTimeLeft };
              } catch {
                return x;
              }
            })
          );
          setRanking(withCooldown);
        } catch {
          // mantém ranking básico caso falhe
          setRanking(Array.isArray(r?.data) ? r.data : []);
        }
        }

        // Fallback: obter próxima execução global do serviço (se ativo)
        try {
          const statusRes = await fetch(buildApiUrl(`/playlist-reorder/status`), { headers: auth });
          if (statusRes.ok) {
            const json = await statusRes.json();
            const next = json?.status?.nextExecution;
            if (next && !nextReorderAt) {
              setNextReorderAt(new Date(next).getTime());
            }
          }
        } catch {}

  // Carregar playlist completa (pode falhar enquanto backend inicia; possui retry)
  await refetchFullPlaylist();
    // Join sala WS com o restaurantId antes de eventos
    if (restaurantId) {
      try { localStorage.setItem("currentRestaurantId", restaurantId); } catch {}
      joinRestaurant(restaurantId);
    }

      } catch (e) {
        // silencioso
      }
    };
    loadInitial();
  }, [restaurantId, joinRestaurant]);

  // realtime
  useEffect(() => {
    const handleQueue = (data: any) => {
      if (Array.isArray(data?.queue)) setQueue(data.queue);
      if (data?.currentlyPlaying) setNowPlaying(data.currentlyPlaying);
    };
    const handleRankingSnapshot = (snap: any) => {

      // Regras de 5 minutos: usar timestamp do snapshot como base para próxima execução programada
      // Evita retrocessos e jitter: só avança o ponteiro quando o snapshot trouxer timestamp mais recente
      try {
        const ts = snap?.timestamp ? new Date(snap.timestamp).getTime() : null;
        if (ts) {
          const candidate = ts + 5 * 60 * 1000;
          if (!nextReorderAt || candidate > nextReorderAt + 1000) {
            setNextReorderAt(candidate);
            lastNextSetRef.current = candidate;
          }
        }
      } catch {}

      try {
        // Atualiza ranking a partir do snapshot (paid/free já ordenados pelo backend)
        const paid = Array.isArray(snap?.paidQueue) ? snap.paidQueue : [];
        const free = Array.isArray(snap?.freeQueue) ? snap.freeQueue : [];
        const combined = [...paid, ...free].map((x: any) => ({
          youtubeVideoId: x.youtubeVideoId,
          title: x.title,
          artist: x.artist,
          voteCount: Number(x.voteCount || x.normalVoteCount || 0),
          isPaid: Boolean(x.isPaid),
          normalVoteCount: Number(x.normalVoteCount || 0),
          superVoteCount: Number(x.superVoteCount || 0),
          thumbnailUrl: x.thumbnailUrl,
        }));
        if (combined.length > 0) setRanking(combined);

        // Usar timestamp do snapshot como base para o próximo ciclo de 5min (forward-only)
        const ts = snap?.timestamp ? new Date(snap.timestamp).getTime() : null;
        if (ts) {
          const candidate = ts + 5 * 60 * 1000;
          if (!nextReorderAt || candidate > nextReorderAt + 1000) {
            setNextReorderAt(candidate);
            lastNextSetRef.current = candidate;
          }
        }
      } catch {}
    };
    const handleNowPlaying = (data: any) => {
      if (data?.suggestion) {
        const s = data.suggestion;
        setNowPlaying({
          id: s.id || s.youtubeVideoId,
          suggestionId: s.youtubeVideoId,
          youtubeVideoId: s.youtubeVideoId,
          title: s.title,
          artist: s.artist,
          duration: s.duration || 0,
          thumbnailUrl: s.thumbnailUrl,
          isPaid: !!(s as any).isPaid,
          paymentAmount: (s as any).paymentAmount,
          position: 0,
        });
      }
    };
    const handleSuperVote = (data: any) => {
      const n: SuperNote = {
        id: `${Date.now()}_${Math.random()}`,
        at: data?.timestamp || new Date().toISOString(),
        amount: Number(data?.payment?.amount) || 0,
        voteWeight: Number(data?.payment?.voteWeight) || 0,
        title: data?.suggestion?.title,
        artist: data?.suggestion?.artist,
        youtubeVideoId: data?.suggestion?.youtubeVideoId,
        clientName: data?.payment?.clientName,
        tableNumber: data?.payment?.tableNumber,
        message: data?.payment?.message,
      };
      setNotes((prev) => [n, ...prev].slice(0, 50));
    };
    // Debounce para evitar requisições em excesso
    const rankFetchTimer = { id: 0 as any };
    const lastFetchRef = { t: 0 };
    const fetchRanking = async () => {
      try {
        const res = await fetch(
          buildApiUrl(`/collaborative-playlist/${restaurantId}/ranking`),
          { headers: getAuthHeaders() }
        );
        if (res.ok) {
          const r = await res.json();
          setRanking(Array.isArray(r?.data) ? r.data : []);
        }
      } catch {}
    };
    const handleVoteUpdate = (payload?: any) => {
      const now = Date.now();
      if (now - lastFetchRef.t < 1000) {
        // já buscou há < 1s, ignora
        return;
      }
      lastFetchRef.t = now;
      // agendar busca em ~300ms para agrupar múltiplos eventos próximos
      if (rankFetchTimer.id) {
        clearTimeout(rankFetchTimer.id);
      }
      rankFetchTimer.id = setTimeout(() => {
        fetchRanking();
        rankFetchTimer.id = 0;
      }, 300) as any;

      // Atualiza previsão de próxima reordenação: 5 minutos após o timestamp do voto
      // Regras: requer timestamp; só AVANÇA o ponteiro; evita reset por eventos duplicados
      try {
        const ts = payload?.timestamp ? new Date(payload.timestamp).getTime() : null;
        if (!ts) return; // sem timestamp, não alteramos o relógio
        if (ts <= lastVoteTsRef.current) return; // voto mais antigo/duplicado
        lastVoteTsRef.current = ts;
        const candidate = ts + 5 * 60 * 1000;
        if (!nextReorderAt || candidate > nextReorderAt) {
          setNextReorderAt(candidate);
          lastNextSetRef.current = candidate;
        }
      } catch {}
    };

    const handlePlaylistReordered = (data: any) => {
      // Preferir horário autoritativo do admin; caso contrário, só usar timestamp se existir.
      try {
        let next: number | null = null;
        const adminNext = data?.adminDetails?.nextReorderTime;
        if (adminNext) {
          next = new Date(adminNext).getTime();
        } else if (data?.timestamp) {
          const base = new Date(data.timestamp).getTime();
          next = base + 5 * 60 * 1000;
        }

        if (next) {
          // Só atualiza se realmente avança e evita jitter pequeno (<= 2s)
          if (!nextReorderAt || next > nextReorderAt + 2000) {
            setNextReorderAt(next);
            lastNextSetRef.current = next;
          }
        }
      } catch {
        // não altera em caso de erro
      }

  // Atualizar playlist completa após reordenação
  refetchFullPlaylist();
    };

    on("queue-update" as any, handleQueue as any);
    on("now-playing" as any, handleNowPlaying as any);
    on("superVoteReceived" as any, handleSuperVote as any);
    on("vote-update" as any, handleVoteUpdate as any);
    on("playlistReordered" as any, handlePlaylistReordered as any);
    on("ranking-snapshot" as any, handleRankingSnapshot as any);
    return () => {
      if (rankFetchTimer.id) clearTimeout(rankFetchTimer.id);
      off("queue-update" as any, handleQueue as any);
      off("now-playing" as any, handleNowPlaying as any);
      off("superVoteReceived" as any, handleSuperVote as any);
      off("vote-update" as any, handleVoteUpdate as any);
      off("playlistReordered" as any, handlePlaylistReordered as any);
      off("ranking-snapshot" as any, handleRankingSnapshot as any);
    };
    // Desabilitar re-subscribe por mudanças de identidade de on/off
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [restaurantId]);

  // Tick de 1s para atualizar o contador de reordenação
  useEffect(() => {
    const id = setInterval(() => setNowTs(Date.now()), 1000);
    return () => clearInterval(id);
  }, []);

  const reorderCountdown = useMemo(() => {
    if (!nextReorderAt) return null;
    const diff = nextReorderAt - nowTs;
    if (diff <= 0) return "00:00";
    const m = Math.floor(diff / 60000);
    const s = Math.floor((diff % 60000) / 1000);
    return `${String(m).padStart(2, "0")}:${String(s).padStart(2, "0")}`;
  }, [nextReorderAt, nowTs]);

  const top5 = useMemo(() => ranking.slice(0, 5), [ranking]);

  // sem catálogo: Couvert foca na fila (sequência)

  return (
    <div className="min-h-screen px-4 py-6 md:px-8 md:py-10 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white">
      <div className="mx-auto max-w-7xl">
        {/* Header melhorado */}
        <motion.header
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between flex-wrap gap-4">
            <div>
              <div className="flex items-center gap-3 mb-2">
                <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-violet-500 rounded-full flex items-center justify-center">
                  <Music className="w-6 h-6" />
                </div>
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-pink-400 to-violet-400 bg-clip-text text-transparent">
                    Painel do Músico
                  </h1>
                  <p className="text-purple-200 text-sm">Couvert Musical Interativo</p>
                </div>
              </div>
              <p className="text-purple-300 text-sm max-w-2xl">
                Acompanhe a fila, ranking por votos e recados dos fãs em tempo real.
                Sistema de votação colaborativa com SuperVotos.
              </p>
            </div>

            {/* Stats rápidas */}
            <div className="flex gap-4 text-sm">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg px-3 py-2 text-center">
                <div className="text-lg font-bold text-green-400">{queue.length}</div>
                <div className="text-purple-200 text-xs">Na fila</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-lg px-3 py-2 text-center">
                <div className="text-lg font-bold text-blue-400">{ranking.length}</div>
                <div className="text-purple-200 text-xs">Com votos</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-lg px-3 py-2 text-center">
                <div className="text-lg font-bold text-yellow-400">{notes.length}</div>
                <div className="text-purple-200 text-xs">SuperVotos</div>
              </div>
            </div>
          </div>

          {/* Countdown melhorado */}
          {reorderCountdown && (
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              className="mt-4 inline-flex items-center gap-3 text-sm bg-gradient-to-r from-indigo-500/20 to-purple-500/20 backdrop-blur-sm border border-indigo-400/30 rounded-lg px-4 py-2"
            >
              <Timer className="w-5 h-5 text-indigo-300 animate-pulse"/>
              <span className="text-indigo-200">Próxima reordenação automática em</span>
              <span className="font-mono font-bold text-xl text-indigo-100 bg-indigo-500/30 px-2 py-1 rounded">
                {reorderCountdown}
              </span>
            </motion.div>
          )}
        </motion.header>

        {/* Tocando agora (se houver) */}
        <AnimatePresence>
          {nowPlaying && (
            <motion.section
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="mb-6 bg-gradient-to-r from-green-500/20 to-emerald-500/20 backdrop-blur-sm border border-green-400/30 rounded-xl p-6"
            >
              <div className="flex items-center gap-2 mb-3">
                <Volume2 className="w-5 h-5 text-green-400 animate-pulse" />
                <h2 className="text-lg font-semibold text-green-300">Tocando Agora</h2>
              </div>
              <div className="flex items-center gap-4">
                <img
                  src={nowPlaying.thumbnailUrl || `https://img.youtube.com/vi/${nowPlaying.youtubeVideoId}/mqdefault.jpg`}
                  alt={nowPlaying.title}
                  className="w-20 h-16 rounded-lg object-cover shadow-lg"
                />
                <div className="flex-1 min-w-0">
                  <h3 className="text-xl font-bold text-white truncate">{nowPlaying.title}</h3>
                  <p className="text-green-200 truncate">{nowPlaying.artist}</p>
                  <div className="flex items-center gap-4 mt-2 text-sm">
                    <span className="flex items-center gap-1 text-green-300">
                      <Clock className="w-4 h-4" />
                      {fmtTime(nowPlaying.duration)}
                    </span>
                    {nowPlaying.isPaid && nowPlaying.paymentAmount && (
                      <span className="flex items-center gap-1 text-yellow-300 bg-yellow-500/20 px-2 py-1 rounded">
                        <CreditCard className="w-4 h-4" />
                        SuperVoto R$ {Number(nowPlaying.paymentAmount).toFixed(2)}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </motion.section>
          )}
        </AnimatePresence>

        {/* Sequência (Fila) + Top ranking */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          <motion.section
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="lg:col-span-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold flex items-center gap-2">
                <Play className="w-5 h-5 text-purple-400" />
                Sequência (Fila)
              </h2>
              <div className="text-sm text-purple-300">
                {queue.length} música{queue.length !== 1 ? 's' : ''}
              </div>
            </div>
            <div className="max-h-[500px] overflow-y-auto space-y-2">
              {queue.length === 0 ? (
                <div className="p-8 text-center">
                  <Music className="w-12 h-12 text-gray-500 mx-auto mb-3" />
                  <p className="text-gray-400">Fila vazia</p>
                  <p className="text-gray-500 text-sm">Aguardando sugestões dos clientes</p>
                </div>
              ) : (
                queue.map((item, index) => (
                  <motion.div
                    key={item.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="p-4 bg-white/5 hover:bg-white/10 rounded-lg transition-all duration-200 border border-white/10"
                  >
                    <div className="flex items-center gap-4">
                      <div className="w-10 h-10 bg-purple-500/20 rounded-full flex items-center justify-center">
                        <span className="text-purple-300 font-bold">#{item.position}</span>
                      </div>
                      <img
                        src={item.thumbnailUrl || `https://img.youtube.com/vi/${item.youtubeVideoId}/mqdefault.jpg`}
                        alt={item.title}
                        className="w-16 h-12 rounded-lg object-cover shadow-md"
                      />
                      <div className="min-w-0 flex-1">
                        <div className="text-white font-semibold truncate">{item.title}</div>
                        <div className="text-purple-200 text-sm truncate">{item.artist}</div>
                        <div className="flex items-center gap-3 mt-2 text-xs">
                          <span className="flex items-center gap-1 text-purple-300">
                            <Clock className="w-3 h-3" />
                            {fmtTime(item.duration)}
                          </span>
                          {item.isPaid && item.paymentAmount && (
                            <span className="flex items-center gap-1 text-yellow-300 bg-yellow-500/20 px-2 py-1 rounded">
                              <Zap className="w-3 h-3" />
                              R$ {Number(item.paymentAmount).toFixed(2)}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))
              )}
            </div>
          </motion.section>

          <motion.section
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6"
          >
            <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
              <Award className="w-5 h-5 text-yellow-400" />
              Ranking de Votos
            </h2>
            {top5.length === 0 ? (
              <div className="text-center py-8">
                <TrendingUp className="w-12 h-12 text-gray-500 mx-auto mb-3" />
                <p className="text-gray-400">Sem votos ainda</p>
                <p className="text-gray-500 text-sm">Aguardando votação dos clientes</p>
              </div>
            ) : (
              <div className="space-y-3">
                {top5.map((r, i) => (
                  <motion.div
                    key={r.youtubeVideoId}
                    initial={{ opacity: 0, x: 10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: i * 0.1 }}
                    className={`p-3 rounded-lg border transition-all duration-200 ${
                      r.isInCooldown
                        ? 'bg-orange-500/10 border-orange-400/30 opacity-70'
                        : i === 0
                        ? 'bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border-yellow-400/30'
                        : 'bg-white/5 border-white/10 hover:bg-white/10'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold ${
                        r.isInCooldown
                          ? 'bg-orange-500/70 text-white'
                          : i === 0 ? 'bg-yellow-500 text-black' :
                          i === 1 ? 'bg-gray-400 text-black' :
                          i === 2 ? 'bg-amber-600 text-white' :
                          'bg-indigo-500/30 text-indigo-300'
                      }`}>
                        {r.isInCooldown ? '⏰' : `#${i + 1}`}
                      </div>
                      <img
                        src={r.thumbnailUrl || `https://img.youtube.com/vi/${r.youtubeVideoId}/default.jpg`}
                        className={`w-12 h-9 rounded object-cover shadow-md ${
                          r.isInCooldown ? 'grayscale' : ''
                        }`}
                        alt={r.title || r.youtubeVideoId}
                      />
                      <div className="flex-1 min-w-0">
                        <div className={`font-medium truncate ${
                          r.isInCooldown ? 'text-orange-300' : 'text-white'
                        }`}>
                          {r.title || r.youtubeVideoId}
                          {i === 0 && !r.isInCooldown && <span className="ml-2 text-xs bg-emerald-500/30 text-emerald-300 px-2 py-1 rounded">Próxima</span>}
                          {r.isInCooldown && <span className="ml-2 text-xs bg-orange-500/30 text-orange-300 px-2 py-1 rounded">Cooldown</span>}
                        </div>
                        {r.artist && <div className={`text-sm truncate ${
                          r.isInCooldown ? 'text-orange-400' : 'text-purple-200'
                        }`}>{r.artist}</div>}
                        <div className="flex items-center gap-2 mt-1 text-xs">
                          {r.isInCooldown && r.cooldownTimeLeft ? (
                            <span className="flex items-center gap-1 text-orange-400">
                              <Timer className="w-3 h-3" />
                              {Math.ceil(r.cooldownTimeLeft / 60)}min restantes
                            </span>
                          ) : (
                            <>
                              <span className="flex items-center gap-1 text-indigo-300">
                                <Heart className="w-3 h-3" />
                                {r.voteCount} votos
                              </span>
                              {r.isPaid && (
                                <span className="flex items-center gap-1 text-yellow-300">
                                  <CreditCard className="w-3 h-3" />
                                  Pago
                                </span>
                              )}
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </motion.section>
        </div>

  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">

          {/* Playlist completa */}
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6 lg:col-span-2"
          >
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold flex items-center gap-2">
                <Music className="w-5 h-5 text-blue-400" />
                Catálogo Completo
              </h2>
              <div className="flex items-center gap-3">
                <span className="text-sm text-purple-300">
                  {fullPlaylist.length} música{fullPlaylist.length !== 1 ? 's' : ''}
                </span>
                <button
                  onClick={refetchFullPlaylist}
                  disabled={playlistLoading}
                  className="flex items-center gap-1 text-xs text-blue-300 hover:text-blue-200 transition-colors disabled:opacity-50"
                >
                  <RefreshCw className={`w-3 h-3 ${playlistLoading ? 'animate-spin' : ''}`} />
                  {playlistLoading ? 'Carregando...' : 'Atualizar'}
                </button>
              </div>
            </div>

            {playlistError && (
              <div className="mb-4 p-3 bg-red-500/20 border border-red-400/30 rounded-lg text-red-300 text-sm">
                {playlistError}
              </div>
            )}

            <div className="max-h-[500px] overflow-y-auto space-y-2">
              {fullPlaylist.length === 0 && !playlistLoading ? (
                <div className="text-center py-8">
                  <Music className="w-12 h-12 text-gray-500 mx-auto mb-3" />
                  <p className="text-gray-400">Nenhuma música cadastrada</p>
                  <p className="text-gray-500 text-sm">Configure a playlist no painel administrativo</p>
                </div>
              ) : (
                fullPlaylist.map((t, idx) => (
                  <motion.div
                    key={t.id || t.youtubeVideoId || idx}
                    initial={{ opacity: 0, y: 5 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: idx * 0.02 }}
                    className="p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-all duration-200 border border-white/10"
                  >
                    <div className="flex items-center gap-4">
                      <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
                        <span className="text-blue-300 text-sm font-medium">
                          #{(t.position && t.position > 0) ? t.position : (idx + 1)}
                        </span>
                      </div>
                      <img
                        src={t.thumbnailUrl || `https://img.youtube.com/vi/${t.youtubeVideoId}/mqdefault.jpg`}
                        alt={t.title}
                        className="w-14 h-10 rounded object-cover shadow-md"
                      />
                      <div className="min-w-0 flex-1">
                        <div className="text-white font-medium truncate">{t.title}</div>
                        <div className="text-purple-200 text-sm truncate">{t.artist}</div>
                        <div className="flex items-center gap-3 mt-1 text-xs">
                          <span className="flex items-center gap-1 text-purple-300">
                            <Clock className="w-3 h-3" />
                            {t.formattedDuration || fmtTime(Math.max(0, Math.floor(t.duration || 0)))}
                          </span>
                          {t.isAvailable === false && (
                            <span className="flex items-center gap-1 text-amber-300 bg-amber-500/20 px-2 py-1 rounded">
                              <Eye className="w-3 h-3" />
                              Indisponível
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))
              )}
            </div>
          </motion.section>

          {/* Mensagens de SuperVoto */}
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl overflow-hidden"
          >
            <div className="p-6 border-b border-white/20 bg-gradient-to-r from-emerald-500/10 to-green-500/10">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold flex items-center gap-2">
                  <MessageSquare className="w-5 h-5 text-emerald-400" />
                  SuperVotos & Recados
                </h2>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
                  <span className="text-xs text-emerald-300">Tempo real</span>
                </div>
              </div>
            </div>

            <div className="max-h-[500px] overflow-y-auto">
              {notes.length === 0 ? (
                <div className="p-8 text-center">
                  <MessageSquare className="w-12 h-12 text-gray-500 mx-auto mb-3" />
                  <p className="text-gray-400">Sem SuperVotos ainda</p>
                  <p className="text-gray-500 text-sm">
                    Mensagens e pagamentos aparecem aqui quando clientes fazem SuperVotos
                  </p>
                </div>
              ) : (
                <div className="p-4 space-y-4">
                  <AnimatePresence>
                    {notes.map((n, index) => (
                      <motion.div
                        key={n.id}
                        initial={{ opacity: 0, x: -20, scale: 0.95 }}
                        animate={{ opacity: 1, x: 0, scale: 1 }}
                        exit={{ opacity: 0, x: 20, scale: 0.95 }}
                        transition={{ delay: index * 0.05 }}
                        className="p-4 bg-gradient-to-r from-emerald-500/10 to-green-500/10 border border-emerald-400/20 rounded-lg"
                      >
                        <div className="flex items-start gap-4">
                          <div className="w-12 h-12 rounded-full bg-gradient-to-r from-emerald-500 to-green-500 flex items-center justify-center text-white font-bold shadow-lg">
                            <Zap className="w-5 h-5" />
                          </div>
                          <div className="min-w-0 flex-1">
                            <div className="flex items-center justify-between mb-2">
                              <div className="font-semibold text-white truncate">
                                {n.title || n.youtubeVideoId || "Música"}
                              </div>
                              <div className="text-xs text-emerald-300 bg-emerald-500/20 px-2 py-1 rounded">
                                {new Date(n.at).toLocaleTimeString("pt-BR", {
                                  hour: "2-digit",
                                  minute: "2-digit"
                                })}
                              </div>
                            </div>

                            {n.artist && (
                              <div className="text-sm text-emerald-200 mb-2 truncate">{n.artist}</div>
                            )}

                            {n.message && (
                              <div className="text-sm text-gray-200 mb-3 p-2 bg-white/5 rounded border-l-2 border-emerald-400">
                                "{n.message}"
                              </div>
                            )}

                            <div className="flex items-center gap-4 text-xs">
                              <span className="flex items-center gap-1 text-yellow-300 bg-yellow-500/20 px-2 py-1 rounded">
                                <CreditCard className="w-3 h-3" />
                                R$ {n.amount.toFixed(2)}
                              </span>
                              <span className="flex items-center gap-1 text-indigo-300 bg-indigo-500/20 px-2 py-1 rounded">
                                <Star className="w-3 h-3" />
                                +{n.voteWeight} votos
                              </span>
                              {n.clientName && (
                                <span className="flex items-center gap-1 text-purple-300">
                                  <Users className="w-3 h-3" />
                                  {n.clientName}
                                </span>
                              )}
                              {n.tableNumber && (
                                <span className="text-purple-300">Mesa {n.tableNumber}</span>
                              )}
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>
              )}
            </div>
          </motion.section>
        </div>
      </div>
    </div>
  );
};

export default CoverPage;
