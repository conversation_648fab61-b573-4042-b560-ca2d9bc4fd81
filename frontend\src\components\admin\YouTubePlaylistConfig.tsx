import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Youtube, 
  Plus, 
  Trash2, 
  RefreshCw, 
  ExternalLink, 
  Music, 
  Clock,
  AlertCircle,
  CheckCircle,
  Settings
} from 'lucide-react';
import { toast } from 'react-hot-toast';

interface YouTubePlaylist {
  id: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  videoCount: number;
  url: string;
  isActive: boolean;
  lastSync: string;
}

interface PlaylistVideo {
  id: string;
  title: string;
  artist: string;
  duration: number;
  formattedDuration: string;
  thumbnailUrl: string;
  isEnabled: boolean;
}

const YouTubePlaylistConfig: React.FC = () => {
  const [playlists, setPlaylists] = useState<YouTubePlaylist[]>([]);
  const [selectedPlaylist, setSelectedPlaylist] = useState<YouTubePlaylist | null>(null);
  const [playlistVideos, setPlaylistVideos] = useState<PlaylistVideo[]>([]);
  const [newPlaylistUrl, setNewPlaylistUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [syncing, setSyncing] = useState(false);

  // Mock data para demonstração
  useEffect(() => {
    const mockPlaylists: YouTubePlaylist[] = [
      {
        id: 'PLrAl6rYgs4IvGFBDEaVGFO6TLukOt3QkN',
        title: 'Playlist Principal do Restaurante',
        description: 'Músicas selecionadas para o ambiente do restaurante',
        thumbnailUrl: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/mqdefault.jpg',
        videoCount: 25,
        url: 'https://youtube.com/playlist?list=PLrAl6rYgs4IvGFBDEaVGFO6TLukOt3QkN',
        isActive: true,
        lastSync: '2024-01-15T10:30:00Z'
      },
      {
        id: 'PLrAl6rYgs4IvGFBDEaVGFO6TLukOt3QkM',
        title: 'Música Ambiente - Jantar',
        description: 'Playlist para horário de jantar',
        thumbnailUrl: 'https://i.ytimg.com/vi/fJ9rUzIMcZQ/mqdefault.jpg',
        videoCount: 18,
        url: 'https://youtube.com/playlist?list=PLrAl6rYgs4IvGFBDEaVGFO6TLukOt3QkM',
        isActive: false,
        lastSync: '2024-01-14T15:20:00Z'
      }
    ];
    setPlaylists(mockPlaylists);
  }, []);

  const addPlaylist = async () => {
    if (!newPlaylistUrl.trim()) {
      toast.error('Digite a URL da playlist do YouTube');
      return;
    }

    if (!newPlaylistUrl.includes('youtube.com/playlist') && !newPlaylistUrl.includes('youtu.be/playlist')) {
      toast.error('URL inválida. Use uma URL de playlist do YouTube');
      return;
    }

    setLoading(true);
    try {
      // Em uma implementação real, isso faria uma chamada para a API
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const newPlaylist: YouTubePlaylist = {
        id: `playlist-${Date.now()}`,
        title: 'Nova Playlist',
        description: 'Playlist importada do YouTube',
        thumbnailUrl: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/mqdefault.jpg',
        videoCount: 12,
        url: newPlaylistUrl,
        isActive: false,
        lastSync: new Date().toISOString()
      };

      setPlaylists([...playlists, newPlaylist]);
      setNewPlaylistUrl('');
      toast.success('Playlist adicionada com sucesso!');
    } catch (error) {
      toast.error('Erro ao adicionar playlist');
    } finally {
      setLoading(false);
    }
  };

  const removePlaylist = (playlistId: string) => {
    setPlaylists(playlists.filter(p => p.id !== playlistId));
    if (selectedPlaylist?.id === playlistId) {
      setSelectedPlaylist(null);
      setPlaylistVideos([]);
    }
    toast.success('Playlist removida');
  };

  const togglePlaylistActive = (playlistId: string) => {
    setPlaylists(playlists.map(p => 
      p.id === playlistId ? { ...p, isActive: !p.isActive } : p
    ));
  };

  const syncPlaylist = async (playlistId: string) => {
    setSyncing(true);
    try {
      // Em uma implementação real, isso sincronizaria com a API do YouTube
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      setPlaylists(playlists.map(p => 
        p.id === playlistId 
          ? { ...p, lastSync: new Date().toISOString(), videoCount: Math.floor(Math.random() * 30) + 10 }
          : p
      ));
      
      toast.success('Playlist sincronizada com sucesso!');
    } catch (error) {
      toast.error('Erro ao sincronizar playlist');
    } finally {
      setSyncing(false);
    }
  };

  const loadPlaylistVideos = async (playlist: YouTubePlaylist) => {
    setSelectedPlaylist(playlist);
    setLoading(true);
    
    try {
      // Mock de vídeos da playlist
      const mockVideos: PlaylistVideo[] = [
        {
          id: 'dQw4w9WgXcQ',
          title: 'Never Gonna Give You Up',
          artist: 'Rick Astley',
          duration: 213,
          formattedDuration: '3:33',
          thumbnailUrl: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/mqdefault.jpg',
          isEnabled: true
        },
        {
          id: 'fJ9rUzIMcZQ',
          title: 'Bohemian Rhapsody',
          artist: 'Queen',
          duration: 355,
          formattedDuration: '5:55',
          thumbnailUrl: 'https://i.ytimg.com/vi/fJ9rUzIMcZQ/mqdefault.jpg',
          isEnabled: true
        },
        {
          id: 'kJQP7kiw5Fk',
          title: 'Despacito',
          artist: 'Luis Fonsi ft. Daddy Yankee',
          duration: 281,
          formattedDuration: '4:41',
          thumbnailUrl: 'https://i.ytimg.com/vi/kJQP7kiw5Fk/mqdefault.jpg',
          isEnabled: false
        }
      ];
      
      setPlaylistVideos(mockVideos);
    } catch (error) {
      toast.error('Erro ao carregar vídeos da playlist');
    } finally {
      setLoading(false);
    }
  };

  const toggleVideoEnabled = (videoId: string) => {
    setPlaylistVideos(videos => 
      videos.map(v => 
        v.id === videoId ? { ...v, isEnabled: !v.isEnabled } : v
      )
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Configuração de Playlists do YouTube
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Gerencie as playlists do YouTube que serão usadas no restaurante
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Youtube className="w-6 h-6 text-red-600" />
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {playlists.filter(p => p.isActive).length} ativas
          </span>
        </div>
      </div>

      {/* Adicionar nova playlist */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold mb-4">Adicionar Nova Playlist</h3>
        <div className="flex space-x-4">
          <input
            type="url"
            placeholder="Cole a URL da playlist do YouTube aqui..."
            value={newPlaylistUrl}
            onChange={(e) => setNewPlaylistUrl(e.target.value)}
            className="flex-1 border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
          <button
            onClick={addPlaylist}
            disabled={loading}
            className="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 disabled:opacity-50 flex items-center space-x-2"
          >
            {loading ? (
              <RefreshCw className="w-4 h-4 animate-spin" />
            ) : (
              <Plus className="w-4 h-4" />
            )}
            <span>Adicionar</span>
          </button>
        </div>
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
          Exemplo: https://youtube.com/playlist?list=PLrAl6rYgs4IvGFBDEaVGFO6TLukOt3QkN
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Lista de playlists */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold">Playlists Configuradas</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {playlists.map((playlist) => (
                <motion.div
                  key={playlist.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      <img
                        src={playlist.thumbnailUrl}
                        alt={playlist.title}
                        className="w-16 h-12 object-cover rounded"
                      />
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-gray-900 dark:text-white truncate">
                          {playlist.title}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                          {playlist.description}
                        </p>
                        <div className="flex items-center space-x-4 mt-2">
                          <span className="text-xs text-gray-500 flex items-center space-x-1">
                            <Music className="w-3 h-3" />
                            <span>{playlist.videoCount} vídeos</span>
                          </span>
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            playlist.isActive 
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                              : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-400'
                          }`}>
                            {playlist.isActive ? 'Ativa' : 'Inativa'}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => loadPlaylistVideos(playlist)}
                        className="p-2 text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900/20 rounded-lg"
                        title="Ver vídeos"
                      >
                        <Settings className="w-4 h-4" />
                      </button>
                      
                      <button
                        onClick={() => syncPlaylist(playlist.id)}
                        disabled={syncing}
                        className="p-2 text-green-600 hover:bg-green-100 dark:hover:bg-green-900/20 rounded-lg"
                        title="Sincronizar"
                      >
                        <RefreshCw className={`w-4 h-4 ${syncing ? 'animate-spin' : ''}`} />
                      </button>
                      
                      <button
                        onClick={() => togglePlaylistActive(playlist.id)}
                        className={`p-2 rounded-lg ${
                          playlist.isActive
                            ? 'text-orange-600 hover:bg-orange-100 dark:hover:bg-orange-900/20'
                            : 'text-green-600 hover:bg-green-100 dark:hover:bg-green-900/20'
                        }`}
                        title={playlist.isActive ? 'Desativar' : 'Ativar'}
                      >
                        {playlist.isActive ? <AlertCircle className="w-4 h-4" /> : <CheckCircle className="w-4 h-4" />}
                      </button>
                      
                      <a
                        href={playlist.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg"
                        title="Abrir no YouTube"
                      >
                        <ExternalLink className="w-4 h-4" />
                      </a>
                      
                      <button
                        onClick={() => removePlaylist(playlist.id)}
                        className="p-2 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg"
                        title="Remover"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>

        {/* Vídeos da playlist selecionada */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold">
              {selectedPlaylist ? `Vídeos: ${selectedPlaylist.title}` : 'Selecione uma playlist'}
            </h3>
          </div>
          <div className="p-6">
            {selectedPlaylist ? (
              <div className="space-y-3">
                {playlistVideos.map((video) => (
                  <div
                    key={video.id}
                    className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      <img
                        src={video.thumbnailUrl}
                        alt={video.title}
                        className="w-12 h-9 object-cover rounded"
                      />
                      <div>
                        <h5 className="font-medium text-gray-900 dark:text-white text-sm">
                          {video.title}
                        </h5>
                        <p className="text-xs text-gray-600 dark:text-gray-400">
                          {video.artist} • {video.formattedDuration}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => toggleVideoEnabled(video.id)}
                        className={`px-3 py-1 rounded-full text-xs font-medium ${
                          video.isEnabled
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                            : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-400'
                        }`}
                      >
                        {video.isEnabled ? 'Habilitado' : 'Desabilitado'}
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Youtube className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-400">
                  Selecione uma playlist para ver seus vídeos
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default YouTubePlaylistConfig;
