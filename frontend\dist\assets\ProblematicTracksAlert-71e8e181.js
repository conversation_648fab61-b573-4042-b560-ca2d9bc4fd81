import{u as T,b as g,c as o,j as e}from"./index-3b37f57e.js";import{r as t}from"./vendor-66b0ef43.js";import{R as N,a9 as f,A as R,m as k,a0 as b,f as E,X as $,q as y,c as A,aB as P,b as D}from"./ui-1cb796d3.js";import"./router-f729e475.js";import"./utils-08f61814.js";const B=()=>{const{restaurantId:r}=T(),[a,m]=t.useState(null),[v,d]=t.useState(!0),[x,n]=t.useState(!1),[h,u]=t.useState(!1),c=t.useCallback(async()=>{if(!r){console.error("Restaurant ID não encontrado");return}try{d(!0);const s=await fetch(g(`/playback/${r}/problematic-report`));if(s.ok){const l=await s.json();m(l.report)}else throw new Error(`HTTP ${s.status}`)}catch(s){console.error("Erro ao carregar relatório de músicas problemáticas:",s),m({problematicTracks:[],recommendations:[],healthScore:85}),o.error("Falha ao carregar relatório")}finally{d(!1)}},[r]),w=t.useCallback(async s=>{if(r)try{const l=await fetch(g(`/restaurants/${r}/playlist/${s}`),{method:"DELETE"});if(l.ok)o.success("Música removida com sucesso"),await c();else throw new Error(`HTTP ${l.status}`)}catch(l){console.error("Erro ao remover música:",l),o.error("Erro ao remover música")}},[r,c]),j=t.useCallback(()=>{n(!1),u(!0),setTimeout(()=>u(!1),60*60*1e3)},[]);t.useEffect(()=>{if(!r)return;c();const s=setInterval(c,10*60*1e3);return()=>clearInterval(s)},[r]),t.useEffect(()=>{a!=null&&a.problematicTracks.length&&!h&&n(!0)},[a,h]);const i=s=>s>=80?"text-green-600":s>=60?"text-yellow-600":s>=40?"text-orange-600":"text-red-600",p=s=>s>=80?"bg-green-50 border-green-200":s>=60?"bg-yellow-50 border-yellow-200":s>=40?"bg-orange-50 border-orange-200":"bg-red-50 border-red-200",C=s=>{switch(s){case"keep":return"text-green-600 bg-green-100";case"monitor":return"text-yellow-600 bg-yellow-100";case"remove":return"text-red-600 bg-red-100";case"blacklist":return"text-red-800 bg-red-200";default:return"text-gray-600 bg-gray-100"}},S=s=>{switch(s){case"keep":return e.jsx(f,{className:"w-4 h-4"});case"monitor":return e.jsx(D,{className:"w-4 h-4"});case"remove":return e.jsx(P,{className:"w-4 h-4"});case"blacklist":return e.jsx(y,{className:"w-4 h-4"});default:return e.jsx(A,{className:"w-4 h-4"})}};return v?e.jsxs("div",{className:"flex items-center space-x-2 text-gray-600",children:[e.jsx(N,{className:"w-4 h-4 animate-spin"}),e.jsx("span",{className:"text-sm",children:"Verificando músicas problemáticas..."})]}):!a||!a.problematicTracks.length?e.jsxs("div",{className:"flex items-center space-x-2 text-green-600",children:[e.jsx(f,{className:"w-4 h-4"}),e.jsx("span",{className:"text-sm",children:"Playlist saudável - nenhuma música problemática"})]}):e.jsxs(e.Fragment,{children:[e.jsx(R,{children:x&&e.jsx(k.div,{initial:{opacity:0,y:-50,scale:.9},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.9},className:"fixed top-4 right-4 z-50 max-w-md",children:e.jsxs("div",{className:`rounded-lg border-2 p-4 shadow-lg ${p(a.healthScore)}`,children:[e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(b,{className:`w-6 h-6 mt-0.5 ${i(a.healthScore)}`}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-semibold text-gray-900",children:"🚨 Músicas Problemáticas Detectadas"}),e.jsxs("p",{className:"text-sm text-gray-700 mt-1",children:[a.problematicTracks.length," música(s) com alta taxa de rejeição"]}),e.jsx("div",{className:"mt-2",children:e.jsxs("div",{className:"flex items-center space-x-2 text-sm",children:[e.jsx(E,{className:"w-4 h-4"}),e.jsxs("span",{className:`font-medium ${i(a.healthScore)}`,children:["Score de Saúde: ",a.healthScore,"/100"]})]})})]})]}),e.jsx("button",{onClick:j,className:"text-gray-400 hover:text-gray-600 transition-colors","aria-label":"Fechar alerta",children:e.jsx($,{className:"w-5 h-5"})})]}),e.jsxs("div",{className:"mt-3 flex space-x-2",children:[e.jsx("button",{onClick:()=>n(!1),className:"text-sm px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:"Ver Detalhes"}),e.jsx("button",{onClick:j,className:"text-sm px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors",children:"Dispensar"})]})]})})}),!x&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:`rounded-lg border p-4 ${p(a.healthScore)}`,children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(b,{className:`w-6 h-6 ${i(a.healthScore)}`}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-gray-900",children:"Saúde da Playlist"}),e.jsxs("p",{className:"text-sm text-gray-700",children:["Score:"," ",e.jsxs("span",{className:`font-medium ${i(a.healthScore)}`,children:[a.healthScore,"/100"]})]})]})]}),e.jsx("button",{onClick:c,className:"p-2 text-gray-600 hover:text-gray-800 transition-colors","aria-label":"Atualizar relatório",children:e.jsx(N,{className:"w-4 h-4"})})]}),a.recommendations.length>0&&e.jsxs("div",{className:"mt-3",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Recomendações:"}),e.jsx("ul",{className:"space-y-1",children:a.recommendations.map((s,l)=>e.jsxs("li",{className:"text-sm text-gray-700 flex items-start space-x-2",children:[e.jsx("span",{className:"text-gray-400 mt-0.5",children:"•"}),e.jsx("span",{children:s})]},l))})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("h4",{className:"font-medium text-gray-900",children:["Músicas Problemáticas (",a.problematicTracks.length,")"]}),a.problematicTracks.map(s=>e.jsx("div",{className:"bg-white border border-gray-200 rounded-lg p-4 shadow-sm",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h5",{className:"font-medium text-gray-900",children:s.title}),e.jsx("p",{className:"text-sm text-gray-600",children:s.artist}),e.jsxs("div",{className:"mt-2 grid grid-cols-2 md:grid-cols-4 gap-3 text-sm",children:[e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-red-600 font-medium",children:[(s.negativeVoteRatio*100).toFixed(1),"%"]}),e.jsx("div",{className:"text-gray-500",children:"Rejeição"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-blue-600 font-medium",children:s.score}),e.jsx("div",{className:"text-gray-500",children:"Score"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-purple-600 font-medium",children:s.playCount}),e.jsx("div",{className:"text-gray-500",children:"Reproduções"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-green-600 font-medium",children:[(s.completionRate*100).toFixed(1),"%"]}),e.jsx("div",{className:"text-gray-500",children:"Conclusão"})]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2 ml-4",children:[e.jsxs("span",{className:`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${C(s.recommendation)}`,children:[S(s.recommendation),e.jsx("span",{className:"capitalize",children:s.recommendation})]}),(s.recommendation==="remove"||s.recommendation==="blacklist")&&e.jsx("button",{onClick:()=>w(s.id),className:"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors","aria-label":`Remover ${s.title} da playlist`,children:e.jsx(y,{className:"w-4 h-4"})})]})]})},s.id))]})]})]})};export{B as default};
//# sourceMappingURL=ProblematicTracksAlert-71e8e181.js.map
