-- ============================================
-- DADOS ADICIONAIS PARA TABELAS EXTRAS
-- Sistema de Playlist Interativa para Restaurantes
-- ============================================

-- Inserir dados de exemplo para o restaurante demo (após restaurante ser criado)
INSERT INTO playlist_schedules (restaurant_id, name, description, "timeSlots", "isActive", mode, settings) VALUES
('demo-restaurant', 'Horário Comercial', 'Playlist para horário de funcionamento normal', 
'[{"playlistId": "main", "playlistName": "Playlist Principal", "startTime": "08:00", "endTime": "22:00", "days": [1,2,3,4,5,6,0], "priority": 1, "isActive": true, "description": "Música ambiente para todo o dia"}]', 
true, 'normal', '{"volume": 70, "crossfade": 3, "autoNext": true}')
ON CONFLICT DO NOTHING;

-- Inserir algumas faixas de exemplo na playlist principal
INSERT INTO playlist_tracks (
    playlist_id, title, artist, "youtubeVideoId", "thumbnailUrl", duration, position, 
    genre, mood, energy, language, "releaseYear", "addedAt"
) VALUES
((SELECT id FROM playlists WHERE restaurant_id = 'demo-restaurant' LIMIT 1), 
'Never Gonna Give You Up', 'Rick Astley', 'dQw4w9WgXcQ', 
'https://i.ytimg.com/vi/dQw4w9WgXcQ/hqdefault.jpg', 214, 1, 
'Pop', 'Upbeat', 7, 'en', 1987, NOW()),

((SELECT id FROM playlists WHERE restaurant_id = 'demo-restaurant' LIMIT 1), 
'Gangnam Style', 'PSY', '9bZkp7q19f0', 
'https://i.ytimg.com/vi/9bZkp7q19f0/hqdefault.jpg', 253, 2, 
'K-Pop', 'Energetic', 9, 'ko', 2012, NOW()),

((SELECT id FROM playlists WHERE restaurant_id = 'demo-restaurant' LIMIT 1), 
'Despacito', 'Luis Fonsi ft. Daddy Yankee', 'kJQP7kiw5Fk', 
'https://i.ytimg.com/vi/kJQP7kiw5Fk/hqdefault.jpg', 282, 3, 
'Latin Pop', 'Romantic', 6, 'es', 2017, NOW()),

((SELECT id FROM playlists WHERE restaurant_id = 'demo-restaurant' LIMIT 1), 
'Shape of You', 'Ed Sheeran', 'JGwWNGJdvx8', 
'https://i.ytimg.com/vi/JGwWNGJdvx8/hqdefault.jpg', 263, 4, 
'Pop', 'Upbeat', 7, 'en', 2017, NOW()),

((SELECT id FROM playlists WHERE restaurant_id = 'demo-restaurant' LIMIT 1), 
'Blinding Lights', 'The Weeknd', '4NRXx6U8ABQ', 
'https://i.ytimg.com/vi/4NRXx6U8ABQ/hqdefault.jpg', 200, 5, 
'Synthpop', 'Energetic', 8, 'en', 2019, NOW()),

((SELECT id FROM playlists WHERE restaurant_id = 'demo-restaurant' LIMIT 1), 
'Bohemian Rhapsody', 'Queen', 'fJ9rUzIMcZQ', 
'https://i.ytimg.com/vi/fJ9rUzIMcZQ/hqdefault.jpg', 355, 6, 
'Rock', 'Epic', 9, 'en', 1975, NOW()),

((SELECT id FROM playlists WHERE restaurant_id = 'demo-restaurant' LIMIT 1), 
'Imagine', 'John Lennon', 'YkgkThdzX-8', 
'https://i.ytimg.com/vi/YkgkThdzX-8/hqdefault.jpg', 183, 7, 
'Rock', 'Peaceful', 4, 'en', 1971, NOW()),

((SELECT id FROM playlists WHERE restaurant_id = 'demo-restaurant' LIMIT 1), 
'Hotel California', 'Eagles', 'BciS5krYL80', 
'https://i.ytimg.com/vi/BciS5krYL80/hqdefault.jpg', 391, 8, 
'Rock', 'Mysterious', 6, 'en', 1976, NOW()),

((SELECT id FROM playlists WHERE restaurant_id = 'demo-restaurant' LIMIT 1), 
'Billie Jean', 'Michael Jackson', 'Zi_XLOBDo_Y', 
'https://i.ytimg.com/vi/Zi_XLOBDo_Y/hqdefault.jpg', 294, 9, 
'Pop', 'Groovy', 8, 'en', 1982, NOW()),

((SELECT id FROM playlists WHERE restaurant_id = 'demo-restaurant' LIMIT 1), 
'Sweet Child O Mine', 'Guns N Roses', '1w7OgIMMRc4', 
'https://i.ytimg.com/vi/1w7OgIMMRc4/hqdefault.jpg', 356, 10, 
'Rock', 'Energetic', 9, 'en', 1987, NOW());

-- Associar gêneros às sugestões existentes
INSERT INTO suggestion_genres (suggestion_id, genre_id) 
SELECT s.id, g.id 
FROM suggestions s, genres g 
WHERE s.restaurant_id = 'demo-restaurant' 
  AND s.title LIKE '%Never Gonna Give You Up%' 
  AND g.name = 'pop'
ON CONFLICT DO NOTHING;

INSERT INTO suggestion_genres (suggestion_id, genre_id) 
SELECT s.id, g.id 
FROM suggestions s, genres g 
WHERE s.restaurant_id = 'demo-restaurant' 
  AND s.title LIKE '%GANGNAM STYLE%' 
  AND g.name = 'energetic'
ON CONFLICT DO NOTHING;

INSERT INTO suggestion_genres (suggestion_id, genre_id) 
SELECT s.id, g.id 
FROM suggestions s, genres g 
WHERE s.restaurant_id = 'demo-restaurant' 
  AND s.title LIKE '%Despacito%' 
  AND g.name = 'romantic'
ON CONFLICT DO NOTHING;

-- Atualizar contadores de uso dos gêneros
UPDATE genres SET "usageCount" = "usageCount" + 1, "lastUsedAt" = NOW() 
WHERE name IN ('pop', 'energetic', 'romantic');

SELECT 'Dados adicionais inseridos com sucesso!' as status;
