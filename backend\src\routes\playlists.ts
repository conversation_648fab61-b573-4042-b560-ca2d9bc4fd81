import { Router, Request, Response } from "express";
import { param, body, query, validationResult } from "../utils/validation";
import { In } from "typeorm";
import { AppDataSource } from "../config/database";
import { Playlist, PlaylistStatus, PlaylistType } from "../models/Playlist";
import { Restaurant } from "../models/Restaurant";
import asyncHandler from "../middleware/asyncHandler";
import { NotFoundError, ValidationError } from "../utils/errors";
import { authMiddleware, optionalAuth } from "../middleware/auth";
import { youtubeService } from "../config/youtube";

const router = Router();

/**
 * @swagger
 * /api/v1/playlists/{restaurantId}:
 *   get:
 *     summary: Obter playlists de um restaurante
 *     tags: [Playlists]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Lista de playlists
 */
router.get(
  "/restaurant/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const playlistRepository = AppDataSource.getRepository(Playlist);

    const playlists = await playlistRepository.find({
      where: {
        restaurant: { id: restaurantId },
        status: In([PlaylistStatus.ACTIVE, PlaylistStatus.INACTIVE]),
      },
      relations: ["suggestions"],
      order: {
        executionOrder: "ASC",
        createdAt: "DESC",
      },
    });

    res.json({
      playlists: playlists.map((p) => ({
        id: p.id,
        name: p.name,
        description: p.description,
        youtubePlaylistId: p.youtubePlaylistId,
        youtubeUrl: p.youtubePlaylistId
          ? `https://www.youtube.com/playlist?list=${p.youtubePlaylistId}`
          : null,
        // Garantir thumbnail sempre que possível: coverImage ou primeira track
        thumbnail:
          p.coverImage || (Array.isArray(p.tracks) && p.tracks.length > 0
            ? (p.tracks[0] as any).thumbnailUrl
            : null),
        videoCount: p.trackCount,
        isActive: p.status === PlaylistStatus.ACTIVE,
        isDefault: p.isDefault,
        order: p.executionOrder,
        tags: [...(p.genreTags || []), ...(p.moodTags || [])],
        suggestionsCount: p.suggestions?.length || 0,
        createdAt: p.createdAt,
        lastSync: p.updatedAt,
        schedule: p.schedule,
      })),
    });
  })
);

/**
 * @swagger
 * /api/v1/playlists/{id}/sync:
 *   post:
 *     summary: Sincronizar playlist com YouTube
 *     tags: [Playlists]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Playlist sincronizada com sucesso
 *       404:
 *         description: Playlist não encontrada
 */
router.post(
  "/:id/sync",
  [param("id").notEmpty().withMessage("ID da playlist é obrigatório")],
  authMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { id } = req.params;
    const playlistRepository = AppDataSource.getRepository(Playlist);

    const playlist = await playlistRepository.findOne({
      where: { id },
      relations: ["restaurant"],
    });

    if (!playlist) {
      throw new NotFoundError("Playlist não encontrada");
    }

    if (!playlist.youtubePlaylistId) {
      throw new ValidationError(
        "Playlist não possui ID do YouTube para sincronização",
        []
      );
    }

    try {
      console.log(
        `🔄 Iniciando sincronização da playlist ${playlist.name} (${playlist.youtubePlaylistId})`
      );

      const youtubePlaylist = await youtubeService.getPlaylist(
        playlist.youtubePlaylistId
      );

      if (youtubePlaylist) {
        console.log(
          `✅ Dados obtidos do YouTube: ${youtubePlaylist.videos.length} vídeos`
        );

        playlist.trackCount = youtubePlaylist.videoCount;
        playlist.totalDuration = 0; // YouTubePlaylist não tem totalDuration
        playlist.tracks = youtubePlaylist.videos.map((video, index) => ({
          youtubeVideoId: video.id,
          title: video.title,
          artist: video.artist,
          duration: parseInt(video.duration) || 0, // Converter string para number
          formattedDuration: video.duration,
          thumbnailUrl: video.thumbnail, // Usar 'thumbnail' ao invés de 'thumbnailUrl'
          addedAt: new Date().toISOString(),
          position: index,
        }));

        await playlistRepository.save(playlist);

        console.log(
          `✅ Playlist sincronizada: ${playlist.tracks.length} tracks salvas`
        );

        res.json({
          success: true,
          message: "Playlist sincronizada com sucesso",
          playlist: {
            id: playlist.id,
            name: playlist.name,
            trackCount: playlist.trackCount,
            totalDuration: playlist.totalDuration,
            tracksCount: playlist.tracks.length,
            lastSync: playlist.updatedAt,
            tracks: playlist.tracks.slice(0, 5), // Primeiras 5 tracks para preview
          },
        });
      } else {
        console.error("❌ Não foi possível obter dados da playlist do YouTube");
        throw new Error("Não foi possível obter dados da playlist do YouTube");
      }
    } catch (error) {
      console.error("❌ Erro ao sincronizar playlist:", error);
      throw new Error(`Erro ao sincronizar com o YouTube: ${error.message}`);
    }
  })
);

/**
 * @swagger
 * /api/v1/playlists/{id}:
 *   get:
 *     summary: Buscar playlist por ID com suas tracks
 *     tags: [Playlists]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: ID da playlist
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Número máximo de tracks a retornar
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           default: 0
 *         description: Número de tracks a pular
 *     responses:
 *       200:
 *         description: Dados da playlist
 *       404:
 *         description: Playlist não encontrada
 */
router.get(
  "/:id",
  [
    param("id").notEmpty().withMessage("ID da playlist é obrigatório"),
    query("limit").optional().isInt({ min: 1, max: 100 }).toInt(),
    query("offset").optional().isInt({ min: 0 }).toInt(),
  ],
  optionalAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { id } = req.params;
    const { limit = 50, offset = 0 } = req.query;
    const playlistRepository = AppDataSource.getRepository(Playlist);

    const playlist = await playlistRepository.findOne({
      where: { id },
      relations: ["restaurant", "suggestions"],
    });

    if (!playlist) {
      throw new NotFoundError("Playlist não encontrada");
    }

    // Aplicar paginação nas tracks
    const tracks = playlist.tracks || [];
    const paginatedTracks = tracks.slice(
      offset as number,
      (offset as number) + (limit as number)
    );

    res.json({
      id: playlist.id,
      name: playlist.name,
      description: playlist.description,
      youtubePlaylistId: playlist.youtubePlaylistId,
      youtubeUrl: playlist.youtubePlaylistId
        ? `https://www.youtube.com/playlist?list=${playlist.youtubePlaylistId}`
        : null,
      // Garantir thumbnail sempre que possível: coverImage ou primeira track
      thumbnail:
        playlist.coverImage || (Array.isArray(playlist.tracks) && playlist.tracks.length > 0
          ? (playlist.tracks[0] as any).thumbnailUrl
          : null),
      videoCount: playlist.trackCount,
      isActive: playlist.status === PlaylistStatus.ACTIVE,
      isDefault: playlist.isDefault,
      tags: [...(playlist.genreTags || []), ...(playlist.moodTags || [])],
      suggestionsCount: playlist.suggestions?.length || 0,
      createdAt: playlist.createdAt,
      lastSync: playlist.updatedAt,
      schedule: playlist.schedule,
      tracks: paginatedTracks,
      totalTracks: tracks.length,
      totalDuration: playlist.totalDuration,
      playCount: playlist.playCount,
      averageRating: playlist.averageRating,
      settings: playlist.settings,
      pagination: {
        limit: limit as number,
        offset: offset as number,
        total: tracks.length,
        hasMore: (offset as number) + (limit as number) < tracks.length,
      },
    });
  })
);

/**
 * @swagger
 * /api/v1/playlists:
 *   post:
 *     summary: Criar nova playlist
 *     tags: [Playlists]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - restaurantId
 *               - name
 *               - youtubeUrl
 *             properties:
 *               restaurantId:
 *                 type: string
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               youtubeUrl:
 *                 type: string
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *               isActive:
 *                 type: boolean
 *     responses:
 *       201:
 *         description: Playlist criada com sucesso
 *       400:
 *         description: Dados inválidos
 */
router.post(
  "/",
  [
    body("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("name").notEmpty().withMessage("Nome da playlist é obrigatório"),
    body("youtubeUrl")
      .notEmpty()
      .withMessage("URL do YouTube é obrigatória")
      .matches(/^https:\/\/(www\.)?(youtube\.com|youtu\.be)/)
      .withMessage("URL deve ser do YouTube"),
    body("description").optional().isString(),
    body("tags").optional().isArray(),
    body("isActive").optional().isBoolean(),
  ],
  authMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    console.log(`🚀 Iniciando criação de playlist...`);
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId, name, description, youtubeUrl, tags, isActive } =
      req.body;

    // Verificar se o restaurante existe
    const restaurantRepository = AppDataSource.getRepository(Restaurant);
    const restaurant = await restaurantRepository.findOne({
      where: { id: restaurantId, isActive: true },
    });

    if (!restaurant) {
      throw new NotFoundError("Restaurante não encontrado");
    }

    // Extrair ID da playlist do YouTube da URL
    let youtubePlaylistId = "";
    try {
      const url = new URL(youtubeUrl);
      if (url.hostname.includes("youtube.com")) {
        youtubePlaylistId = url.searchParams.get("list") || "";
      }

      if (!youtubePlaylistId) {
        throw new ValidationError(
          "URL do YouTube inválida - não foi possível extrair ID da playlist",
          []
        );
      }
    } catch (error) {
      throw new ValidationError("URL do YouTube inválida", []);
    }

    const playlistRepository = AppDataSource.getRepository(Playlist);

    // Verificar se já existe uma playlist com o mesmo YouTube ID para este restaurante
    const existingPlaylist = await playlistRepository.findOne({
      where: {
        restaurant: { id: restaurantId },
        youtubePlaylistId,
        status: PlaylistStatus.ACTIVE,
      },
    });

    if (existingPlaylist) {
      throw new ValidationError(
        "Esta playlist do YouTube já foi adicionada",
        []
      );
    }

    // Criar nova playlist
    const playlist = playlistRepository.create({
      name,
      description: description || "",
      type: PlaylistType.YOUTUBE,
      status:
        isActive !== false ? PlaylistStatus.ACTIVE : PlaylistStatus.INACTIVE,
      youtubePlaylistId,
      genreTags: tags || [],
      isDefault: false,
      isPublic: true,
      trackCount: 0,
      totalDuration: 0,
      playCount: 0,
      averageRating: 0,
      tracks: [],
      settings: {
        shuffle: false,
        repeat: "none",
        autoPlay: true,
        crossfade: 3,
        volume: 80,
        allowVoting: true,
        allowSuggestions: true,
        maxSuggestionsPerUser: 5,
      },
      restaurant,
    });

    // Definir ordem de execução automaticamente
    const maxOrderResult = await playlistRepository
      .createQueryBuilder("playlist")
      .select("MAX(playlist.executionOrder)", "maxOrder")
      .where("playlist.restaurant = :restaurantId", { restaurantId })
      .getRawOne();

    playlist.executionOrder = (maxOrderResult?.maxOrder || 0) + 1;

    await playlistRepository.save(playlist);
    console.log(`✅ Playlist salva no banco de dados: ${playlist.id}`);

    // Sincronizar com o YouTube imediatamente
    try {
      console.log(`🔄 Sincronizando playlist ${playlist.name} com YouTube...`);
      console.log(`🔗 YouTube Playlist ID: ${youtubePlaylistId}`);

      const youtubePlaylist = await youtubeService.getPlaylist(
        youtubePlaylistId
      );

      console.log(
        `📊 Resultado da sincronização:`,
        youtubePlaylist ? "Sucesso" : "Falhou"
      );

      if (youtubePlaylist) {
        console.log(
          `✅ Obtidos ${youtubePlaylist.videos.length} vídeos do YouTube`
        );
        playlist.trackCount = youtubePlaylist.videoCount;
        playlist.totalDuration = 0; // YouTubePlaylist não tem totalDuration
        playlist.tracks = youtubePlaylist.videos.map((video, index) => ({
          youtubeVideoId: video.id,
          title: video.title,
          artist: video.artist, // Usar artist ao invés de channelTitle
          duration: parseInt(video.duration) || 0, // Converter string para number
          formattedDuration: video.duration,
          thumbnailUrl: video.thumbnail, // Usar thumbnail ao invés de thumbnailUrl
          addedAt: new Date().toISOString(),
          position: index,
        }));
        await playlistRepository.save(playlist);
        console.log(
          `✅ Playlist sincronizada com ${playlist.tracks.length} tracks`
        );
      } else {
        console.log(`❌ Não foi possível obter dados da playlist do YouTube`);
      }
    } catch (error) {
      console.error("⚠️ Erro ao sincronizar playlist com YouTube:", error);
      console.error("⚠️ Stack trace:", error.stack);
      // Não falhar a criação da playlist se a sincronização falhar
    }

    res.status(201).json({
      success: true,
      message: "Playlist criada com sucesso",
      playlist: {
        id: playlist.id,
        name: playlist.name,
        description: playlist.description,
        youtubePlaylistId: playlist.youtubePlaylistId,
        isDefault: playlist.isDefault,
        trackCount: playlist.trackCount,
        status: playlist.status,
        createdAt: playlist.createdAt,
      },
    });
  })
);

/**
 * @swagger
 * /api/v1/playlists/{id}:
 *   put:
 *     summary: Atualizar playlist
 *     tags: [Playlists]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               youtubeUrl:
 *                 type: string
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *               isActive:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Playlist atualizada com sucesso
 *       404:
 *         description: Playlist não encontrada
 */
router.put(
  "/:id",
  [
    param("id").notEmpty().withMessage("ID da playlist é obrigatório"),
    body("name").optional().notEmpty().withMessage("Nome não pode ser vazio"),
    body("youtubeUrl")
      .optional()
      .matches(/^https:\/\/(www\.)?(youtube\.com|youtu\.be)/)
      .withMessage("URL deve ser do YouTube"),
    body("description").optional().isString(),
    body("tags").optional().isArray(),
    body("isActive").optional().isBoolean(),
  ],
  authMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { id } = req.params;
    const { name, description, youtubeUrl, tags, isActive } = req.body;

    const playlistRepository = AppDataSource.getRepository(Playlist);

    const playlist = await playlistRepository.findOne({
      where: { id },
      relations: ["restaurant"],
    });

    if (!playlist) {
      throw new NotFoundError("Playlist não encontrada");
    }

    // Atualizar campos
    if (name !== undefined) playlist.name = name;
    if (description !== undefined) playlist.description = description;
    if (tags !== undefined) playlist.genreTags = tags;
    if (isActive !== undefined) {
      playlist.status = isActive
        ? PlaylistStatus.ACTIVE
        : PlaylistStatus.INACTIVE;
    }

    // Se a URL do YouTube foi alterada, extrair novo ID
    if (youtubeUrl && youtubeUrl !== playlist.youtubePlaylistId) {
      let youtubePlaylistId = "";
      try {
        const url = new URL(youtubeUrl);
        if (url.hostname.includes("youtube.com")) {
          youtubePlaylistId = url.searchParams.get("list") || "";
        }

        if (!youtubePlaylistId) {
          throw new ValidationError(
            "URL do YouTube inválida - não foi possível extrair ID da playlist",
            []
          );
        }

        playlist.youtubePlaylistId = youtubePlaylistId;

        // Resetar dados da playlist para nova sincronização
        playlist.trackCount = 0;
        playlist.totalDuration = 0;
        playlist.tracks = [];
      } catch (error) {
        if (error instanceof ValidationError) throw error;
        throw new ValidationError("URL do YouTube inválida", []);
      }
    }

    await playlistRepository.save(playlist);

    res.json({
      success: true,
      message: "Playlist atualizada com sucesso",
      playlist: {
        id: playlist.id,
        name: playlist.name,
        description: playlist.description,
        youtubePlaylistId: playlist.youtubePlaylistId,
        isDefault: playlist.isDefault,
        trackCount: playlist.trackCount,
        status: playlist.status,
        updatedAt: playlist.updatedAt,
      },
    });
  })
);

/**
 * @swagger
 * /api/v1/playlists/{id}:
 *   delete:
 *     summary: Deletar playlist
 *     tags: [Playlists]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Playlist deletada com sucesso
 *       404:
 *         description: Playlist não encontrada
 *       400:
 *         description: Não é possível deletar playlist padrão
 */
router.delete(
  "/:id",
  [param("id").notEmpty().withMessage("ID da playlist é obrigatório")],
  authMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { id } = req.params;
    console.log(`🗑️ Tentando deletar playlist: ${id}`);
    console.log(`👤 Usuário autenticado:`, req.user?.id || "Não autenticado");

    const playlistRepository = AppDataSource.getRepository(Playlist);

    const playlist = await playlistRepository.findOne({
      where: { id },
      relations: ["restaurant"],
    });

    if (!playlist) {
      console.log(`❌ Playlist não encontrada: ${id}`);
      throw new NotFoundError("Playlist não encontrada");
    }

    console.log(
      `📝 Playlist encontrada: ${playlist.name}, status atual: ${playlist.status}`
    );

    if (playlist.isDefault) {
      console.log(`⚠️ Tentativa de deletar playlist padrão bloqueada`);
      throw new ValidationError("Não é possível deletar a playlist padrão", []);
    }

    try {
      // Usar transação para garantir consistência
      await AppDataSource.transaction(async (manager) => {
        const oldStatus = playlist.status;
        playlist.status = PlaylistStatus.DELETED;
        console.log(
          `🔄 Mudando status de '${oldStatus}' para '${PlaylistStatus.DELETED}'`
        );

        await manager.save(playlist);
        console.log(`✅ Playlist marcada como deletada com sucesso`);
      });

      // Verificar se realmente foi salva
      const verificacao = await playlistRepository.findOne({ where: { id } });
      console.log(`🔍 Verificação pós-transação: ${verificacao?.status}`);

      res.json({
        success: true,
        message: "Playlist deletada com sucesso",
      });
    } catch (error) {
      console.error(`❌ Erro ao deletar playlist:`, error);
      throw error;
    }
  })
);

/**
 * @swagger
 * /api/v1/playlists/{playlistId}/schedule:
 *   patch:
 *     summary: Atualizar agendamento de uma playlist
 *     tags: [Playlists]
 *     parameters:
 *       - in: path
 *         name: playlistId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               schedule:
 *                 type: object
 *                 properties:
 *                   enabled:
 *                     type: boolean
 *                   startTime:
 *                     type: string
 *                   endTime:
 *                     type: string
 *                   days:
 *                     type: array
 *                     items:
 *                       type: string
 *     responses:
 *       200:
 *         description: Agendamento atualizado com sucesso
 *       404:
 *         description: Playlist não encontrada
 */
router.patch(
  "/:playlistId/schedule",
  [
    param("playlistId")
      .isUUID()
      .withMessage("ID da playlist deve ser um UUID válido"),
    body("schedule").isObject().withMessage("Schedule deve ser um objeto"),
    body("schedule.enabled")
      .isBoolean()
      .withMessage("schedule.enabled deve ser booleano"),
    body("schedule.startTime")
      .optional()
      .isString()
      .withMessage("schedule.startTime deve ser string"),
    body("schedule.endTime")
      .optional()
      .isString()
      .withMessage("schedule.endTime deve ser string"),
    body("schedule.days")
      .optional()
      .isArray()
      .withMessage("schedule.days deve ser array"),
  ],
  authMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { playlistId } = req.params;
    const { schedule } = req.body;

    const playlistRepository = AppDataSource.getRepository(Playlist);

    // Verificar se a playlist existe
    const playlist = await playlistRepository.findOne({
      where: { id: playlistId },
      relations: ["restaurant"],
    });

    if (!playlist) {
      throw new NotFoundError("Playlist não encontrada");
    }

    // Atualizar o agendamento da playlist
    playlist.schedule = schedule;
    await playlistRepository.save(playlist);

    console.log("✅ Agendamento da playlist atualizado:", {
      playlistId,
      playlistName: playlist.name,
      schedule: schedule,
    });

    res.json({
      success: true,
      message: "Agendamento atualizado com sucesso",
      playlist: {
        id: playlist.id,
        name: playlist.name,
        schedule: playlist.schedule,
      },
      timestamp: new Date().toISOString(),
    });
  })
);

/**
 * @swagger
 * /api/v1/playlists/{id}/default:
 *   patch:
 *     summary: Definir playlist como padrão
 *     tags: [Playlists]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               isDefault:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Playlist definida como padrão com sucesso
 */
router.patch(
  "/:id/default",
  [param("id").isUUID().withMessage("ID deve ser um UUID válido")],
  authMiddleware,
  asyncHandler(async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: "Dados inválidos",
          errors: errors.array(),
        });
      }

      const { id } = req.params;
      const { isDefault } = req.body;

      console.log(
        `🎯 [DEFAULT] Processando playlist: ${id}, isDefault: ${isDefault}`
      );

      const playlistRepository = AppDataSource.getRepository(Playlist);

      // Verificar se a playlist existe - SEM relações para evitar erro
      const playlist = await playlistRepository.findOne({
        where: { id },
      });

      if (!playlist) {
        console.log(`❌ [DEFAULT] Playlist não encontrada: ${id}`);
        return res.status(404).json({
          success: false,
          message: "Playlist não encontrada",
        });
      }

      console.log(`✅ [DEFAULT] Playlist encontrada: ${playlist.name}`);

      // Buscar o restaurante separadamente se necessário
      const restaurantRepository = AppDataSource.getRepository(Restaurant);
      const restaurant = await restaurantRepository.findOne({
        where: { id: playlist.restaurant.id },
      });

      if (!restaurant) {
        return res.status(404).json({
          success: false,
          message: "Restaurante não encontrado",
        });
      }

      console.log(`🏪 [DEFAULT] Restaurante encontrado: ${restaurant.name}`);

      // Verificar se o usuário tem permissão (deve ser do mesmo restaurante)
      if (req.user?.restaurant?.id !== restaurant.id) {
        console.log(
          `🚫 [DEFAULT] Sem permissão: usuário restaurante ${req.user?.restaurant?.id} vs playlist restaurante ${restaurant.id}`
        );
        return res.status(403).json({
          success: false,
          message: "Sem permissão para alterar esta playlist",
        });
      }

      if (isDefault) {
        // Remover flag de padrão de todas as outras playlists do mesmo restaurante
        console.log(
          `🔄 [DEFAULT] Removendo flag padrão de outras playlists do restaurante ${restaurant.id}`
        );
        await playlistRepository.update(
          { restaurant: { id: restaurant.id } },
          { isDefault: false }
        );

        // Definir esta playlist como padrão
        playlist.isDefault = true;
        await playlistRepository.save(playlist);
        console.log(
          `✅ [DEFAULT] Playlist ${playlist.name} definida como padrão`
        );
      } else {
        playlist.isDefault = false;
        await playlistRepository.save(playlist);
        console.log(
          `✅ [DEFAULT] Playlist ${playlist.name} removida como padrão`
        );
      }

      res.json({
        success: true,
        message: isDefault
          ? "Playlist definida como padrão com sucesso"
          : "Playlist removida como padrão com sucesso",
        playlist: {
          id: playlist.id,
          name: playlist.name,
          isDefault: playlist.isDefault,
        },
      });
    } catch (error) {
      console.error(`❌ [DEFAULT] Erro ao processar playlist:`, error);
      return res.status(500).json({
        success: false,
        message: "Erro interno do servidor",
        error: error.message,
      });
    }
  })
);

/**
 * @swagger
 * /api/v1/playlists/{id}/status:
 *   patch:
 *     summary: Alterar status de ativação da playlist
 *     tags: [Playlists]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               isActive:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Status da playlist atualizado com sucesso
 *       404:
 *         description: Playlist não encontrada
 */
router.patch(
  "/:id/status",
  [
    param("id").isUUID().withMessage("ID deve ser um UUID válido"),
    body("isActive").isBoolean().withMessage("isActive deve ser booleano"),
  ],
  authMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { id } = req.params;
    const { isActive } = req.body;

    const playlistRepository = AppDataSource.getRepository(Playlist);

    // Verificar se a playlist existe
    const playlist = await playlistRepository.findOne({
      where: { id },
      relations: ["restaurant"],
    });

    if (!playlist) {
      throw new NotFoundError("Playlist não encontrada");
    }

    // Verificar se o usuário tem permissão (deve ser do mesmo restaurante)
    if (req.user?.restaurant?.id !== playlist.restaurant.id) {
      return res.status(403).json({
        success: false,
        message: "Sem permissão para alterar esta playlist",
      });
    }

    // Atualizar o status
    playlist.status = isActive
      ? PlaylistStatus.ACTIVE
      : PlaylistStatus.INACTIVE;
    await playlistRepository.save(playlist);

    res.json({
      success: true,
      message: "Status da playlist atualizado com sucesso",
      playlist: {
        id: playlist.id,
        name: playlist.name,
        isActive: playlist.status === PlaylistStatus.ACTIVE,
      },
    });
  })
);

/**
 * @swagger
 * /api/v1/playlists/reorder:
 *   patch:
 *     summary: Reordenar playlists
 *     tags: [Playlists]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               restaurantId:
 *                 type: string
 *               playlistIds:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Playlists reordenadas com sucesso
 */
router.patch(
  "/reorder",
  [
    body("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("playlistIds")
      .isArray()
      .withMessage("Lista de IDs das playlists é obrigatória"),
  ],
  authMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId, playlistIds } = req.body;
    const playlistRepository = AppDataSource.getRepository(Playlist);

    // Verificar se todas as playlists pertencem ao restaurante
    const playlists = await playlistRepository.find({
      where: {
        id: In(playlistIds),
        restaurant: { id: restaurantId },
      },
    });

    if (playlists.length !== playlistIds.length) {
      throw new ValidationError(
        "Algumas playlists não foram encontradas ou não pertencem ao restaurante",
        []
      );
    }

    // Atualizar ordem de execução
    const updates = playlistIds.map((id: string, index: number) => {
      return playlistRepository.update(
        { id, restaurant: { id: restaurantId } },
        { executionOrder: index + 1 }
      );
    });

    await Promise.all(updates);

    res.json({
      success: true,
      message: "Ordem das playlists atualizada com sucesso",
    });
  })
);

// Função auxiliar para formatar duração
function formatDurationHelper(duration: string | number): string {
  // Se já é uma string no formato MM:SS, retorna como está
  if (typeof duration === "string" && duration.includes(":")) {
    return duration;
  }

  // Se é um número (segundos), converte para MM:SS
  const seconds = typeof duration === "string" ? parseInt(duration) : duration;
  if (isNaN(seconds)) {
    return "0:00";
  }

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
}

export default router;
