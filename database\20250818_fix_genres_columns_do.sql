-- Ensure genres table exists with minimal schema (idempotent)
CREATE TABLE IF NOT EXISTS genres (
	id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
	name VARCHAR NOT NULL UNIQUE,
	created_at TIMESTAMP DEFAULT now(),
	updated_at TIMESTAMP DEFAULT now()
);

-- Add missing columns safely
ALTER TABLE IF EXISTS genres
	ADD COLUMN IF NOT EXISTS description TEXT NULL;

-- Normalize name casing (optional example)
UPDATE genres SET name = INITCAP(name)
WHERE name IS NOT NULL AND name <> INITCAP(name);

