import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Settings,
  Save,
  Clock,
  Users,
  Music,
  Shield,
  Palette,
  Bell,
  Globe,
  Volume2,
  Eye,
  Lock,
  Youtube,
} from "lucide-react";
import { toast } from "react-hot-toast";
import { YouTubeAuthManager } from "@/components/restaurant/YouTubeAuthManager";
import { useSettings } from "@/store";
import { useRestaurantContext } from "@/components/restaurant/RestaurantDashboard";

interface RestaurantConfig {
  general: {
    name: string;
    description: string;
    timezone: string;
    language: string;
  };
  interface: {
    theme: "light" | "dark" | "auto";
    primaryColor: string;
    allowSuggestions: boolean;
    allowVoting: boolean;
    showQueue: boolean;
    showVoteCounts: boolean;
    maxSuggestionsPerUser: number;
  };
  moderation: {
    autoApprove: boolean;
    requireModeration: boolean;
    bannedWords: string[];
    maxVotesForAutoApproval: number;
    minVotesForAutoRejection: number;
  };
  schedule: {
    enabled: boolean;
    openTime: string;
    closeTime: string;
    timezone: string;
    closedMessage: string;
  };
  notifications: {
    emailNotifications: boolean;
    newSuggestionAlert: boolean;
    highVoteAlert: boolean;
    moderationAlert: boolean;
  };
  audio: {
    volume: number;
    fadeInDuration: number;
    fadeOutDuration: number;
    crossfade: boolean;
  };
}

const RestaurantSettings: React.FC = () => {
  const { settings, updateSettings } = useSettings();
  const [config, setConfig] = useState<RestaurantConfig>({
    general: {
      name: "Restaurante Demo",
      description: "Um restaurante incrível com playlist interativa",
      timezone: "America/Sao_Paulo",
      language: "pt-BR",
    },
    interface: {
      theme: "auto",
      primaryColor: "#3B82F6",
      allowSuggestions: true,
      allowVoting: true,
      showQueue: true,
      showVoteCounts: true,
      maxSuggestionsPerUser: 5,
    },
    moderation: {
      autoApprove: false,
      requireModeration: true,
      bannedWords: ["palavra1", "palavra2"],
      maxVotesForAutoApproval: 10,
      minVotesForAutoRejection: -5,
    },
    schedule: {
      enabled: true,
      openTime: "11:00",
      closeTime: "23:00",
      timezone: "America/Sao_Paulo",
      closedMessage:
        "Estamos fechados. Volte durante nosso horário de funcionamento!",
    },
    notifications: {
      emailNotifications: true,
      newSuggestionAlert: true,
      highVoteAlert: true,
      moderationAlert: true,
    },
    audio: {
      volume: 75,
      fadeInDuration: 3,
      fadeOutDuration: 3,
      crossfade: true,
    },
  });

  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("general");
  const { restaurantId } = useRestaurantContext();

  const saveSettings = async () => {
    setLoading(true);
    try {
      // Em uma implementação real, isso salvaria as configurações na API
      await new Promise((resolve) => setTimeout(resolve, 1500));
      toast.success("Configurações salvas com sucesso!");
    } catch (error) {
      toast.error("Erro ao salvar configurações");
    } finally {
      setLoading(false);
    }
  };

  const updateConfig = (
    section: keyof RestaurantConfig,
    field: string,
    value: any
  ) => {
    setConfig((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }));
  };

  const tabs = [
    { id: "general", name: "Geral", icon: Settings },
    { id: "interface", name: "Interface", icon: Palette },
    { id: "moderation", name: "Moderação", icon: Shield },
    { id: "schedule", name: "Horários", icon: Clock },
    { id: "notifications", name: "Notificações", icon: Bell },
    { id: "audio", name: "Áudio", icon: Volume2 },
    { id: "youtube", name: "YouTube", icon: Youtube },
  ];

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Nome do Restaurante
        </label>
        <input
          type="text"
          value={config.general.name}
          onChange={(e) => updateConfig("general", "name", e.target.value)}
          className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Descrição
        </label>
        <textarea
          value={config.general.description}
          onChange={(e) =>
            updateConfig("general", "description", e.target.value)
          }
          rows={3}
          className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Fuso Horário
          </label>
          <select
            value={config.general.timezone}
            onChange={(e) =>
              updateConfig("general", "timezone", e.target.value)
            }
            className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="America/Sao_Paulo">São Paulo (GMT-3)</option>
            <option value="America/New_York">Nova York (GMT-5)</option>
            <option value="Europe/London">Londres (GMT+0)</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Idioma
          </label>
          <select
            value={config.general.language}
            onChange={(e) =>
              updateConfig("general", "language", e.target.value)
            }
            className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="pt-BR">Português (Brasil)</option>
            <option value="en-US">English (US)</option>
            <option value="es-ES">Español</option>
          </select>
        </div>
      </div>
    </div>
  );

  const renderInterfaceSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Tema
          </label>
          <select
            value={config.interface.theme}
            onChange={(e) => updateConfig("interface", "theme", e.target.value)}
            className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="light">Claro</option>
            <option value="dark">Escuro</option>
            <option value="auto">Automático</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Cor Principal
          </label>
          <input
            type="color"
            value={config.interface.primaryColor}
            onChange={(e) =>
              updateConfig("interface", "primaryColor", e.target.value)
            }
            className="w-full h-10 border border-gray-300 dark:border-gray-600 rounded-lg"
          />
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Posição das Notificações
          </label>
          <select
            value={settings.notificationPosition || 'top-left'}
            onChange={(e) => updateSettings({ notificationPosition: e.target.value as any })}
            className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="top-right">Topo Direita</option>
            <option value="top-left">Topo Esquerda</option>
            <option value="bottom-right">Base Direita</option>
            <option value="bottom-left">Base Esquerda</option>
          </select>
          <p className="text-sm text-gray-500 mt-1">Ajusta onde os avisos aparecem na tela do restaurante</p>
        </div>
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white">
              Permitir Sugestões
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Clientes podem sugerir músicas
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={config.interface.allowSuggestions}
              onChange={(e) =>
                updateConfig("interface", "allowSuggestions", e.target.checked)
              }
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white">
              Permitir Votação
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Clientes podem votar em sugestões
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={config.interface.allowVoting}
              onChange={(e) =>
                updateConfig("interface", "allowVoting", e.target.checked)
              }
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white">
              Mostrar Fila
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Exibir fila de reprodução para clientes
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={config.interface.showQueue}
              onChange={(e) =>
                updateConfig("interface", "showQueue", e.target.checked)
              }
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Máximo de Sugestões por Cliente
        </label>
        <input
          type="number"
          min="1"
          max="20"
          value={config.interface.maxSuggestionsPerUser}
          onChange={(e) =>
            updateConfig(
              "interface",
              "maxSuggestionsPerUser",
              parseInt(e.target.value)
            )
          }
          className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        />
      </div>
    </div>
  );

  const renderModerationSettings = () => (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white">
              Aprovação Automática
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Aprovar sugestões automaticamente com base em votos
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={config.moderation.autoApprove}
              onChange={(e) =>
                updateConfig("moderation", "autoApprove", e.target.checked)
              }
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white">
              Requer Moderação
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Todas as sugestões precisam ser moderadas
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={config.moderation.requireModeration}
              onChange={(e) =>
                updateConfig(
                  "moderation",
                  "requireModeration",
                  e.target.checked
                )
              }
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
          </label>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Votos para Aprovação Automática
          </label>
          <input
            type="number"
            value={config.moderation.maxVotesForAutoApproval}
            onChange={(e) =>
              updateConfig(
                "moderation",
                "maxVotesForAutoApproval",
                parseInt(e.target.value)
              )
            }
            className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Votos para Rejeição Automática
          </label>
          <input
            type="number"
            value={config.moderation.minVotesForAutoRejection}
            onChange={(e) =>
              updateConfig(
                "moderation",
                "minVotesForAutoRejection",
                parseInt(e.target.value)
              )
            }
            className="w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>
      </div>
    </div>
  );

  const renderCurrentTab = () => {
    switch (activeTab) {
      case "general":
        return renderGeneralSettings();
      case "interface":
        return renderInterfaceSettings();
      case "moderation":
        return renderModerationSettings();
      case "youtube":
        return renderYouTubeSettings();
      default:
        return (
          <div className="text-center py-8 text-gray-500">
            Em desenvolvimento...
          </div>
        );
    }
  };

  const renderYouTubeSettings = () => {
    console.log(
      "🎵 Renderizando configurações do YouTube para restaurantId:",
      restaurantId
    );

    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Integração com YouTube
          </h3>
          <p className="text-gray-600 dark:text-gray-400 text-sm mb-6">
            Configure a autenticação com YouTube para controlar playlists em
            tempo real baseado nas votações dos clientes.
          </p>
        </div>

        <YouTubeAuthManager
          restaurantId={restaurantId}
          onAuthStatusChange={(isAuthenticated) => {
            console.log("YouTube Auth Status:", isAuthenticated);
          }}
        />

        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
            💡 Como funciona
          </h4>
          <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
            <li>• Conecte sua conta YouTube Premium</li>
            <li>• Crie playlists controláveis pelo sistema</li>
            <li>
              • As votações dos clientes reordenam automaticamente as músicas
            </li>
            <li>• Controle total sobre a ordem de reprodução</li>
          </ul>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Configurações do Restaurante
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Personalize a experiência do seu restaurante
          </p>
        </div>

        <button
          onClick={saveSettings}
          disabled={loading}
          className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2"
        >
          <Save className="w-4 h-4" />
          <span>{loading ? "Salvando..." : "Salvar"}</span>
        </button>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        {/* Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 py-4 px-1 border-b-2 text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? "border-blue-500 text-blue-600 dark:text-blue-400"
                    : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">{renderCurrentTab()}</div>
      </div>
    </div>
  );
};

export default RestaurantSettings;
