import React, { useState, useEffect, useCallback, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Award, Star, Music, ThumbsUp, Crown, Trophy, Target, Zap, Heart, TrendingUp, X } from "lucide-react";
import { toast } from "react-hot-toast";
import { buildApiUrl } from "@/config/api";
import sessionService, { ClientSession } from "@/services/sessionService";

interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  bgColor: string;
  requirement: number;
  currentProgress: number;
  unlocked: boolean;
  unlockedAt?: string;
  progressPercentage: number;
  category: "suggestions" | "votes" | "engagement" | "special";
}

interface UserStats {
  totalSuggestions: number;
  totalVotes: number;
  approvedSuggestions: number;
  consecutiveDays: number;
  topVotedSongs: number;
}

interface BadgeSystemProps {
  restaurantId: string;
  sessionId: string;
}

const BadgeSystem: React.FC<BadgeSystemProps> = ({ restaurantId, sessionId }) => {
  // sessionId efetivo, permite atualização após recriar sessão no backend
  const [effectiveSessionId, setEffectiveSessionId] = useState<string>(sessionId);
  const [badges, setBadges] = useState<Badge[]>([]);
  const [newlyUnlockedBadge, setNewlyUnlockedBadge] = useState<Badge | null>(null);
  const [userStats, setUserStats] = useState<UserStats>({
    totalSuggestions: 0,
    totalVotes: 0,
    approvedSuggestions: 0,
    consecutiveDays: 0,
    topVotedSongs: 0,
  });
  // Evita tentar recriar a sessão repetidamente
  const triedSessionRecovery = useRef(false);

  // Sincroniza quando prop mudar externamente
  useEffect(() => {
    setEffectiveSessionId(sessionId);
  }, [sessionId]);

  const availableBadges: Omit<Badge, "currentProgress" | "unlocked" | "unlockedAt" | "progressPercentage">[] = [
    {
      id: "first_suggestion",
      name: "Primeira Sugestão",
      description: "Sugeriu sua primeira música",
      icon: "Music",
      color: "text-blue-600 dark:text-blue-400",
      bgColor: "bg-blue-100 dark:bg-blue-900/30",
      requirement: 1,
      category: "suggestions",
    },
    {
      id: "music_lover",
      name: "Amante da Música",
      description: "Sugeriu 10 músicas",
      icon: "Heart",
      color: "text-red-600 dark:text-red-400",
      bgColor: "bg-red-100 dark:bg-red-900/30",
      requirement: 10,
      category: "suggestions",
    },
    {
      id: "dj_master",
      name: "DJ Master",
      description: "Sugeriu 50 músicas",
      icon: "Crown",
      color: "text-purple-600 dark:text-purple-400",
      bgColor: "bg-purple-100 dark:bg-purple-900/30",
      requirement: 50,
      category: "suggestions",
    },
    {
      id: "first_vote",
      name: "Primeiro Voto",
      description: "Votou pela primeira vez",
      icon: "ThumbsUp",
      color: "text-green-600 dark:text-green-400",
      bgColor: "bg-green-100 dark:bg-green-900/30",
      requirement: 1,
      category: "votes",
    },
    {
      id: "active_voter",
      name: "Eleitor Ativo",
      description: "Fez 25 votos",
      icon: "Target",
      color: "text-orange-600 dark:text-orange-400",
      bgColor: "bg-orange-100 dark:bg-orange-900/30",
      requirement: 25,
      category: "votes",
    },
    {
      id: "vote_champion",
      name: "Campeão dos Votos",
      description: "Fez 100 votos",
      icon: "Trophy",
      color: "text-yellow-600 dark:text-yellow-400",
      bgColor: "bg-yellow-100 dark:bg-yellow-900/30",
      requirement: 100,
      category: "votes",
    },
    {
      id: "hit_maker",
      name: "Criador de Hits",
      description: "Teve 5 sugestões aprovadas",
      icon: "Star",
      color: "text-indigo-600 dark:text-indigo-400",
      bgColor: "bg-indigo-100 dark:bg-indigo-900/30",
      requirement: 5,
      category: "engagement",
    },
    {
      id: "trending_master",
      name: "Mestre das Tendências",
      description: "Teve uma música no top 3 mais votadas",
      icon: "TrendingUp",
      color: "text-pink-600 dark:text-pink-400",
      bgColor: "bg-pink-100 dark:bg-pink-900/30",
      requirement: 1,
      category: "special",
    },
    {
      id: "speed_demon",
      name: "Demônio da Velocidade",
      description: "Sugeriu 5 músicas em menos de 1 minuto",
      icon: "Zap",
      color: "text-cyan-600 dark:text-cyan-400",
      bgColor: "bg-cyan-100 dark:bg-cyan-900/30",
      requirement: 5,
      category: "special",
    },
    {
      id: "legend",
      name: "Lenda",
      description: "Desbloqueou todos os outros badges",
      icon: "Award",
      color: "text-gradient-to-r from-yellow-600 to-orange-500 dark:from-yellow-400 dark:to-orange-400",
      bgColor: "bg-gradient-to-r from-yellow-400 to-orange-500 dark:from-yellow-500 dark:to-orange-600",
      requirement: 9,
      category: "special",
    },
  ];

  const getIconComponent = useCallback((iconName: string): React.ComponentType<{ className?: string }> => {
    const iconMap: { [key: string]: React.ComponentType<{ className?: string }> } = {
      Music,
      Heart,
      Crown,
      ThumbsUp,
      Target,
      Trophy,
      Star,
      TrendingUp,
      Award,
      Zap,
    };
    return iconMap[iconName] || Award;
  }, []);

  const loadBadges = useCallback(() => {
    const updatedBadges = availableBadges.map((badge) => {
      let currentProgress = 0;
      let unlocked = false;

      switch (badge.id) {
        case "first_suggestion":
        case "music_lover":
        case "dj_master":
          currentProgress = userStats.totalSuggestions;
          break;
        case "first_vote":
        case "active_voter":
        case "vote_champion":
          currentProgress = userStats.totalVotes;
          break;
        case "hit_maker":
          currentProgress = userStats.approvedSuggestions;
          break;
        case "trending_master":
          currentProgress = userStats.topVotedSongs;
          break;
        case "speed_demon":
          currentProgress = 0; // Implementar lógica no backend
          break;
        case "legend":
          const unlockedCount = availableBadges.filter((b) => {
            if (b.id === "legend") return false;
            let progress = 0;
            switch (b.category) {
              case "suggestions":
                progress = userStats.totalSuggestions;
                break;
              case "votes":
                progress = userStats.totalVotes;
                break;
              case "engagement":
                progress = userStats.approvedSuggestions;
                break;
              case "special":
                progress = b.id === "trending_master" ? userStats.topVotedSongs : 0;
                break;
            }
            return progress >= b.requirement;
          }).length;
          currentProgress = unlockedCount;
          break;
      }

      unlocked = currentProgress >= badge.requirement;
      const progressPercentage = Math.min((currentProgress / badge.requirement) * 100, 100);

      return {
        ...badge,
        currentProgress,
        unlocked,
        // no modelo de availableBadges não há unlockedAt; setamos somente quando desbloqueado
        unlockedAt: unlocked ? new Date().toISOString() : undefined,
        progressPercentage,
      } as Badge;
    });

    setBadges(updatedBadges);
    checkForNewlyUnlockedBadges(updatedBadges);
  }, [userStats]);

  const loadUserStats = useCallback(async () => {
    try {
      const response = await fetch(buildApiUrl(`/client/${effectiveSessionId}/stats`), {
        headers: { "X-Session-ID": effectiveSessionId },
      });
      if (response.ok) {
        const { stats } = await response.json();
        setUserStats(stats);
        loadBadgesFromAPI();
      } else {
        // Se a sessão não existir no backend (400/404), tentar criar e refazer uma vez
        if (!triedSessionRecovery.current && (response.status === 400 || response.status === 404)) {
          triedSessionRecovery.current = true;
          try {
            await sessionService.createSession(restaurantId);
            const newToken = sessionService.getSessionToken() || effectiveSessionId;
            setEffectiveSessionId(newToken);
            const retry = await fetch(buildApiUrl(`/client/${newToken}/stats`), {
              headers: { "X-Session-ID": newToken },
            });
            if (retry.ok) {
              const { stats } = await retry.json();
              setUserStats(stats);
              await loadBadgesFromAPI();
              return;
            }
          } catch (e) {
            // segue para fallback
          }
        }
        throw new Error("Erro ao carregar estatísticas");
      }
    } catch (error) {
      console.error("Erro ao carregar estatísticas:", error);
      setUserStats({
        totalSuggestions: 3,
        totalVotes: 12,
        approvedSuggestions: 1,
        consecutiveDays: 2,
        topVotedSongs: 0,
      });
      loadBadges();
      toast.error("Erro ao carregar estatísticas, usando dados de demonstração");
    }
  }, [effectiveSessionId, loadBadges, restaurantId, loadBadgesFromAPI]);

  const loadBadgesFromAPI = useCallback(async () => {
    try {
      const response = await fetch(buildApiUrl(`/client/${effectiveSessionId}/badges`), {
        headers: { "X-Session-ID": effectiveSessionId },
      });
      if (response.ok) {
        const { badges: apiBadges } = await response.json();
        const convertedBadges: Badge[] = apiBadges.map((badge: any) => ({
          ...badge,
          icon: badge.icon,
          color: `text-${badge.color}-600 dark:text-${badge.color}-400`,
          bgColor: `bg-${badge.color}-100 dark:bg-${badge.color}-900/30`,
          currentProgress: badge.currentProgress,
          unlocked: badge.unlocked,
          unlockedAt: badge.unlockedAt as string | undefined,
          progressPercentage: Math.min((badge.currentProgress / badge.requirement) * 100, 100),
        }));
        setBadges(convertedBadges);
        checkForNewlyUnlockedBadges(convertedBadges);
      } else {
        // Recuperação semelhante para badges
        if (!triedSessionRecovery.current && (response.status === 400 || response.status === 404)) {
          triedSessionRecovery.current = true;
          try {
            await sessionService.createSession(restaurantId);
            const newToken = sessionService.getSessionToken() || effectiveSessionId;
            setEffectiveSessionId(newToken);
            const retry = await fetch(buildApiUrl(`/client/${newToken}/badges`), {
              headers: { "X-Session-ID": newToken },
            });
            if (retry.ok) {
              const { badges: apiBadges } = await retry.json();
              const convertedBadges: Badge[] = apiBadges.map((badge: any) => ({
                ...badge,
                icon: badge.icon,
                color: `text-${badge.color}-600 dark:text-${badge.color}-400`,
                bgColor: `bg-${badge.color}-100 dark:bg-${badge.color}-900/30`,
                currentProgress: badge.currentProgress,
                unlocked: badge.unlocked,
                unlockedAt: badge.unlockedAt as string | undefined,
                progressPercentage: Math.min((badge.currentProgress / badge.requirement) * 100, 100),
              }));
              setBadges(convertedBadges);
              checkForNewlyUnlockedBadges(convertedBadges);
              return;
            }
          } catch (e) {
            // segue para fallback
          }
        }
        throw new Error("Erro ao carregar badges");
      }
    } catch (error) {
      console.error("Erro ao carregar badges:", error);
      loadBadges();
      toast.error("Erro ao carregar badges, usando dados locais");
    }
  }, [effectiveSessionId, loadBadges, restaurantId, checkForNewlyUnlockedBadges]);

  const checkForNewlyUnlockedBadges = useCallback(
    (updatedBadges: Badge[]) => {
      const newlyUnlocked = updatedBadges.find(
        (badge) => badge.unlocked && !badges.find((b) => b.id === badge.id && b.unlocked)
      );
      if (newlyUnlocked) {
        setNewlyUnlockedBadge(newlyUnlocked);
        toast.success(`🏆 Badge desbloqueado: ${newlyUnlocked.name}!`, { duration: 5000 });
      }
    },
    [badges]
  );

  const getBadgesByCategory = useCallback((category: Badge["category"]) => {
    return badges.filter((badge) => badge.category === category);
  }, [badges]);

  useEffect(() => {
    if (restaurantId && effectiveSessionId) {
      loadUserStats();
    }
  }, [restaurantId, effectiveSessionId, loadUserStats]);

  if (!badges.length) {
    return (
      <div className="text-center py-8">
        <Award className="w-12 h-12 text-gray-400 mx-auto mb-3" aria-hidden="true" />
        <p className="text-gray-500 dark:text-gray-400">Carregando conquistas...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-900 rounded-xl p-4 sm:p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-2">
            <Trophy className="w-5 h-5 text-yellow-500" aria-hidden="true" />
            Suas Conquistas
          </h3>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {badges.filter((b) => b.unlocked).length}/{badges.length} badges
          </div>
        </div>
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 mb-4 sm:mb-6">
          {[
            { label: "Sugestões", value: userStats.totalSuggestions, color: "text-blue-600" },
            { label: "Votos", value: userStats.totalVotes, color: "text-green-600" },
            { label: "Aprovadas", value: userStats.approvedSuggestions, color: "text-purple-600" },
            { label: "Badges", value: badges.filter((b) => b.unlocked).length, color: "text-orange-600" },
          ].map((stat) => (
            <div key={stat.label} className="text-center">
              <div className={`text-xl sm:text-2xl font-bold ${stat.color}`}>{stat.value}</div>
              <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">{stat.label}</div>
            </div>
          ))}
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(badges.filter((b) => b.unlocked).length / badges.length) * 100}%` }}
            role="progressbar"
            aria-valuenow={Math.round((badges.filter((b) => b.unlocked).length / badges.length) * 100)}
            aria-valuemin={0}
            aria-valuemax={100}
          />
        </div>
      </div>
      {["suggestions", "votes", "engagement", "special"].map((category) => {
        const categoryBadges = getBadgesByCategory(category as Badge["category"]);
        if (!categoryBadges.length) return null;
        return (
          <div
            key={category}
            className="bg-white dark:bg-gray-900 rounded-xl p-4 sm:p-6 shadow-sm border border-gray-200 dark:border-gray-700"
          >
            <h4 className="text-md sm:text-lg font-medium text-gray-900 dark:text-white mb-4 capitalize">
              {category === "suggestions" && "Sugestões"}
              {category === "votes" && "Votação"}
              {category === "engagement" && "Engajamento"}
              {category === "special" && "Especiais"}
            </h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
              {categoryBadges.map((badge) => {
                const Icon = getIconComponent(badge.icon);
                return (
                  <motion.div
                    key={badge.id}
                    className={`relative p-4 rounded-lg border transition-all duration-200 ${
                      badge.unlocked
                        ? "border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/20"
                        : "border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/20"
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    role="button"
                    tabIndex={0}
                    aria-label={`Ver detalhes do badge ${badge.name}`}
                    onClick={() => {
                      setNewlyUnlockedBadge(badge);
                      if (badge.unlocked) toast.success(`🏆 ${badge.name} desbloqueado!`);
                    }}
                    onKeyPress={(e) => e.key === "Enter" && setNewlyUnlockedBadge(badge)}
                  >
                    {badge.unlocked && (
                      <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <Award className="w-3 h-3 text-white" aria-hidden="true" />
                      </div>
                    )}
                    <div className="flex items-center gap-3 mb-3">
                      <div
                        className={`w-10 h-10 rounded-lg ${badge.bgColor} flex items-center justify-center ${
                          badge.unlocked ? "" : "opacity-50"
                        }`}
                      >
                        <Icon className={`w-5 h-5 ${badge.color}`} aria-hidden="true" />
                      </div>
                      <div className="flex-1">
                        <h5
                          className={`font-medium text-sm ${
                            badge.unlocked ? "text-gray-900 dark:text-white" : "text-gray-500 dark:text-gray-400"
                          }`}
                        >
                          {badge.name}
                        </h5>
                        <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2">{badge.description}</p>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-xs">
                        <span className="text-gray-600 dark:text-gray-400">Progresso</span>
                        <span className="text-gray-600 dark:text-gray-400">
                          {badge.currentProgress}/{badge.requirement}
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                        <div
                          className={`h-1.5 rounded-full transition-all duration-300 ${
                            badge.unlocked ? "bg-green-500" : "bg-blue-500"
                          }`}
                          style={{ width: `${badge.progressPercentage}%` }}
                          role="progressbar"
                          aria-valuenow={Math.round(badge.progressPercentage)}
                          aria-valuemin={0}
                          aria-valuemax={100}
                        />
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>
        );
      })}
      <AnimatePresence>
        {newlyUnlockedBadge && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setNewlyUnlockedBadge(null)}
            role="dialog"
            aria-modal="true"
            aria-labelledby={`badge-${newlyUnlockedBadge.id}`}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              className="bg-white dark:bg-gray-800 rounded-xl p-6 sm:p-8 max-w-md w-full text-center"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="mb-4">
                <div className={`w-20 h-20 mx-auto ${newlyUnlockedBadge.bgColor} rounded-full flex items-center justify-center mb-4`}>
                  {React.createElement(getIconComponent(newlyUnlockedBadge.icon), {
                    className: `w-10 h-10 ${newlyUnlockedBadge.color}`,
                  })}
                </div>
                <h3 id={`badge-${newlyUnlockedBadge.id}`} className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white mb-2">
                  {newlyUnlockedBadge.unlocked ? "Badge Desbloqueado!" : newlyUnlockedBadge.name}
                </h3>
                <h4 className="text-md sm:text-lg font-semibold text-gray-800 dark:text-gray-200">{newlyUnlockedBadge.name}</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-2 line-clamp-3">{newlyUnlockedBadge.description}</p>
                {newlyUnlockedBadge.unlocked && newlyUnlockedBadge.unlockedAt && (
                  <p className="text-xs text-green-600 dark:text-green-400 mt-2">
                    Desbloqueado em{" "}
                    {new Date(newlyUnlockedBadge.unlockedAt).toLocaleDateString("pt-BR", {
                      day: "2-digit",
                      month: "long",
                      year: "numeric",
                    })}
                  </p>
                )}
              </div>
              <button
                onClick={() => setNewlyUnlockedBadge(null)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                aria-label="Fechar detalhes do badge"
              >
                Continuar
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default BadgeSystem;
