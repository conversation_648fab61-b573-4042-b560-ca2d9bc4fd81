import { Router } from "express";
import { body, param, query, validationResult } from "../utils/validation";
import { rewardService } from "../services/RewardService";
import asyncHandler from "../middleware/asyncHandler";
import { optionalAuth } from "../middleware/auth";
import { ValidationError, NotFoundError } from "../utils/errors";

const router = Router();

/**
 * @swagger
 * /api/v1/rewards/client/{sessionId}:
 *   get:
 *     summary: Obter prêmios do cliente
 *     tags: [Rewards]
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Lista de prêmios do cliente
 */
router.get(
  "/client/:sessionId",
  [param("sessionId").notEmpty().withMessage("ID da sessão é obrigatório")],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { sessionId } = req.params;

    const rewards = await rewardService.getClientRewards(sessionId);

    res.json({
      success: true,
      rewards: rewards.map((reward) => reward.toPublicJSON()),
      total: rewards.length,
      active: rewards.filter((r) => r.isActive()).length,
    });
  })
);

/**
 * @swagger
 * /api/v1/rewards/restaurant/{restaurantId}:
 *   get:
 *     summary: Obter prêmios do restaurante
 *     tags: [Rewards]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *     responses:
 *       200:
 *         description: Lista de prêmios do restaurante
 */
router.get(
  "/restaurant/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    query("limit").optional().isInt({ min: 1, max: 100 }),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const { limit = 50 } = req.query;

    const rewards = await rewardService.getRestaurantRewards(
      restaurantId,
      parseInt(limit as string)
    );

    res.json({
      success: true,
      rewards: rewards.map((reward) => reward.toRestaurantJSON()),
      total: rewards.length,
    });
  })
);

/**
 * @swagger
 * /api/v1/rewards/use/{rewardId}:
 *   post:
 *     summary: Usar prêmio
 *     tags: [Rewards]
 *     parameters:
 *       - in: path
 *         name: rewardId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - sessionId
 *             properties:
 *               sessionId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Prêmio utilizado com sucesso
 */
router.post(
  "/use/:rewardId",
  [
    param("rewardId").notEmpty().withMessage("ID do prêmio é obrigatório"),
    body("sessionId").notEmpty().withMessage("ID da sessão é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { rewardId } = req.params;
    const { sessionId } = req.body;

    const result = await rewardService.useReward(rewardId, sessionId);

    if (!result.success) {
      throw new ValidationError(result.message);
    }

    res.json({
      success: true,
      message: result.message,
    });
  })
);

/**
 * @swagger
 * /api/v1/rewards/daily-winners/{restaurantId}:
 *   post:
 *     summary: Processar vencedores diários
 *     tags: [Rewards]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Vencedores diários processados
 */
router.post(
  "/daily-winners/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    const winners = await rewardService.processDailyRewards(restaurantId);

    res.json({
      success: true,
      message: "Vencedores diários processados",
      winners,
      totalWinners: winners.length,
      rewardsCreated: Math.min(3, winners.length),
    });
  })
);

/**
 * @swagger
 * /api/v1/rewards/create:
 *   post:
 *     summary: Criar prêmio personalizado
 *     tags: [Rewards]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - restaurantId
 *               - sessionId
 *               - type
 *               - title
 *               - description
 *               - rewardData
 *             properties:
 *               restaurantId:
 *                 type: string
 *               sessionId:
 *                 type: string
 *               clientName:
 *                 type: string
 *               tableName:
 *                 type: string
 *               type:
 *                 type: string
 *                 enum: [discount, free_song, priority_queue, badge, custom]
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               rewardData:
 *                 type: object
 *               expiresAt:
 *                 type: string
 *                 format: date-time
 *     responses:
 *       201:
 *         description: Prêmio criado com sucesso
 */
router.post(
  "/create",
  [
    body("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("sessionId").notEmpty().withMessage("ID da sessão é obrigatório"),
    body("type")
      .isIn(["discount", "free_song", "priority_queue", "badge", "custom"])
      .withMessage("Tipo de prêmio inválido"),
    body("title").notEmpty().withMessage("Título é obrigatório"),
    body("description").notEmpty().withMessage("Descrição é obrigatória"),
    body("rewardData")
      .isObject()
      .withMessage("Dados do prêmio devem ser um objeto"),
    body("expiresAt").optional().isISO8601(),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const {
      restaurantId,
      sessionId,
      clientName,
      tableName,
      type,
      title,
      description,
      rewardData,
      expiresAt,
    } = req.body;

    const reward = await rewardService.createCustomReward({
      restaurantId,
      sessionId,
      clientName,
      tableName,
      type,
      title,
      description,
      rewardData,
      expiresAt: expiresAt ? new Date(expiresAt) : undefined,
    });

    res.status(201).json({
      success: true,
      message: "Prêmio criado com sucesso",
      reward: reward.toPublicJSON(),
    });
  })
);

/**
 * @swagger
 * /api/v1/rewards/stats/{restaurantId}:
 *   get:
 *     summary: Obter estatísticas de prêmios
 *     tags: [Rewards]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Estatísticas de prêmios
 */
router.get(
  "/stats/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    const stats = await rewardService.getRewardStats(restaurantId);

    res.json({
      success: true,
      stats,
    });
  })
);

/**
 * @swagger
 * /api/v1/rewards/cleanup/{restaurantId}:
 *   post:
 *     summary: Limpar prêmios expirados
 *     tags: [Rewards]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Prêmios expirados limpos
 */
router.post(
  "/cleanup/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    const cleanedCount = await rewardService.cleanupExpiredRewards(
      restaurantId
    );

    res.json({
      success: true,
      message: `${cleanedCount} prêmios expirados foram limpos`,
      cleanedCount,
    });
  })
);

export default router;
