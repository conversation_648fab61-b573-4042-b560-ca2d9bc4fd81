#!/bin/bash

# Script de deploy para produção do Sistema de Playlist Interativa
# Este script automatiza o processo de deploy em ambiente de produção

set -e  # Parar em caso de erro

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configurações
BACKUP_DIR="./backups"
DEPLOY_LOG="./deploy.log"
MAX_BACKUPS=5

# Função para imprimir mensagens coloridas
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
    echo "$(date '+%Y-%m-%d %H:%M:%S') [INFO] $1" >> "$DEPLOY_LOG"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    echo "$(date '+%Y-%m-%d %H:%M:%S') [WARNING] $1" >> "$DEPLOY_LOG"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    echo "$(date '+%Y-%m-%d %H:%M:%S') [ERROR] $1" >> "$DEPLOY_LOG"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
    echo "$(date '+%Y-%m-%d %H:%M:%S') ================================" >> "$DEPLOY_LOG"
    echo "$(date '+%Y-%m-%d %H:%M:%S') $1" >> "$DEPLOY_LOG"
    echo "$(date '+%Y-%m-%d %H:%M:%S') ================================" >> "$DEPLOY_LOG"
}

# Verificar se está em ambiente de produção
check_production_env() {
    if [ ! -f .env ]; then
        print_error "Arquivo .env não encontrado!"
        exit 1
    fi
    
    if grep -q "NODE_ENV=development" .env; then
        print_error "NODE_ENV está configurado como development!"
        echo "Configure NODE_ENV=production no arquivo .env antes do deploy"
        exit 1
    fi
    
    print_message "Ambiente de produção verificado ✓"
}

# Verificar configurações críticas
check_critical_configs() {
    print_message "Verificando configurações críticas..."
    
    # Verificar JWT_SECRET
    if grep -q "JWT_SECRET=your-super-secret-jwt-key-change-in-production" .env; then
        print_error "JWT_SECRET não foi alterado!"
        echo "Configure uma chave JWT segura no arquivo .env"
        exit 1
    fi
    
    # Verificar YOUTUBE_API_KEY
    if ! grep -q "YOUTUBE_API_KEY=" .env || grep -q "YOUTUBE_API_KEY=your-youtube-api-key-here" .env; then
        print_error "YOUTUBE_API_KEY não configurado!"
        exit 1
    fi
    
    # Verificar URLs de produção
    if grep -q "localhost" .env; then
        print_warning "URLs com localhost detectadas. Verifique se estão corretas para produção."
    fi
    
    print_message "Configurações críticas verificadas ✓"
}

# Criar backup do banco de dados
backup_database() {
    print_message "Criando backup do banco de dados..."
    
    mkdir -p "$BACKUP_DIR"
    
    BACKUP_FILE="$BACKUP_DIR/backup_$(date +%Y%m%d_%H%M%S).sql"
    
    if docker-compose exec -T postgres pg_dump -U restaurant_user restaurant_playlist > "$BACKUP_FILE"; then
        print_message "Backup criado: $BACKUP_FILE ✓"
        
        # Comprimir backup
        gzip "$BACKUP_FILE"
        print_message "Backup comprimido: $BACKUP_FILE.gz ✓"
        
        # Limpar backups antigos
        cleanup_old_backups
    else
        print_error "Falha ao criar backup do banco de dados!"
        exit 1
    fi
}

# Limpar backups antigos
cleanup_old_backups() {
    print_message "Limpando backups antigos..."
    
    # Manter apenas os últimos MAX_BACKUPS backups
    cd "$BACKUP_DIR"
    ls -t backup_*.sql.gz | tail -n +$((MAX_BACKUPS + 1)) | xargs -r rm
    cd - > /dev/null
    
    print_message "Backups antigos removidos ✓"
}

# Verificar se há mudanças no Git
check_git_status() {
    if [ -d .git ]; then
        if [ -n "$(git status --porcelain)" ]; then
            print_warning "Há mudanças não commitadas no Git"
            echo "Deseja continuar? (y/N)"
            read -r response
            if [[ ! "$response" =~ ^[Yy]$ ]]; then
                print_message "Deploy cancelado pelo usuário"
                exit 0
            fi
        fi
        
        # Obter informações do commit atual
        CURRENT_COMMIT=$(git rev-parse HEAD)
        CURRENT_BRANCH=$(git branch --show-current)
        print_message "Branch atual: $CURRENT_BRANCH"
        print_message "Commit atual: $CURRENT_COMMIT"
    fi
}

# Parar serviços atuais
stop_services() {
    print_message "Parando serviços atuais..."
    
    docker-compose down
    
    print_message "Serviços parados ✓"
}

# Fazer pull das imagens mais recentes
pull_images() {
    print_message "Fazendo pull das imagens base..."
    
    docker-compose pull
    
    print_message "Pull das imagens concluído ✓"
}

# Build das novas imagens
build_images() {
    print_message "Fazendo build das novas imagens..."
    
    # Build sem cache para garantir atualizações
    docker-compose build --no-cache
    
    print_message "Build das imagens concluído ✓"
}

# Executar migrações do banco
run_migrations() {
    print_message "Executando migrações do banco de dados..."
    
    # Iniciar apenas o banco para executar migrações
    docker-compose up -d postgres redis
    
    # Aguardar banco estar pronto
    sleep 10
    
    # Executar migrações (se houver)
    # docker-compose exec backend npm run migrate
    
    print_message "Migrações executadas ✓"
}

# Iniciar serviços
start_services() {
    print_message "Iniciando serviços..."
    
    docker-compose up -d
    
    # Aguardar serviços estarem prontos
    print_message "Aguardando serviços ficarem prontos..."
    sleep 30
    
    print_message "Serviços iniciados ✓"
}

# Verificar saúde dos serviços
health_check() {
    print_message "Verificando saúde dos serviços..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:5000/health &> /dev/null; then
            print_message "Backend: OK ✓"
            break
        else
            if [ $attempt -eq $max_attempts ]; then
                print_error "Backend não respondeu após $max_attempts tentativas"
                return 1
            fi
            print_message "Tentativa $attempt/$max_attempts - Aguardando backend..."
            sleep 5
            ((attempt++))
        fi
    done
    
    # Verificar frontend
    if curl -f http://localhost:3000 &> /dev/null; then
        print_message "Frontend: OK ✓"
    else
        print_warning "Frontend: Não respondendo"
    fi
    
    return 0
}

# Executar testes de smoke
smoke_tests() {
    print_message "Executando testes de smoke..."
    
    # Teste básico da API
    if curl -f http://localhost:5000/api/v1/health &> /dev/null; then
        print_message "API health check: OK ✓"
    else
        print_error "API health check: FALHOU"
        return 1
    fi
    
    # Teste de conexão com banco
    if docker-compose exec postgres pg_isready -U restaurant_user -d restaurant_playlist &> /dev/null; then
        print_message "Conexão com banco: OK ✓"
    else
        print_error "Conexão com banco: FALHOU"
        return 1
    fi
    
    # Teste de conexão com Redis
    if docker-compose exec redis redis-cli ping &> /dev/null; then
        print_message "Conexão com Redis: OK ✓"
    else
        print_error "Conexão com Redis: FALHOU"
        return 1
    fi
    
    print_message "Testes de smoke concluídos ✓"
    return 0
}

# Rollback em caso de falha
rollback() {
    print_error "Deploy falhou! Iniciando rollback..."
    
    # Parar serviços atuais
    docker-compose down
    
    # Restaurar backup mais recente
    LATEST_BACKUP=$(ls -t "$BACKUP_DIR"/backup_*.sql.gz | head -n1)
    if [ -n "$LATEST_BACKUP" ]; then
        print_message "Restaurando backup: $LATEST_BACKUP"
        gunzip -c "$LATEST_BACKUP" | docker-compose exec -T postgres psql -U restaurant_user -d restaurant_playlist
    fi
    
    # Iniciar serviços
    docker-compose up -d
    
    print_error "Rollback concluído. Verifique os logs para identificar o problema."
}

# Limpeza pós-deploy
cleanup() {
    print_message "Executando limpeza pós-deploy..."
    
    # Remover imagens Docker não utilizadas
    docker image prune -f
    
    # Remover volumes não utilizados
    docker volume prune -f
    
    print_message "Limpeza concluída ✓"
}

# Mostrar informações finais
show_deploy_info() {
    print_header "DEPLOY CONCLUÍDO COM SUCESSO!"
    
    echo ""
    echo "🎉 Deploy realizado com sucesso!"
    echo ""
    echo "📊 Informações do deploy:"
    if [ -n "$CURRENT_BRANCH" ]; then
        echo "   Branch: $CURRENT_BRANCH"
    fi
    if [ -n "$CURRENT_COMMIT" ]; then
        echo "   Commit: $CURRENT_COMMIT"
    fi
    echo "   Data: $(date)"
    echo ""
    echo "🔗 URLs do sistema:"
    echo "   Frontend: $(grep FRONTEND_URL .env | cut -d'=' -f2)"
    echo "   API: $(grep API_URL .env | cut -d'=' -f2)"
    echo ""
    echo "📋 Próximos passos:"
    echo "   - Verificar logs: docker-compose logs -f"
    echo "   - Monitorar métricas de performance"
    echo "   - Testar funcionalidades críticas"
    echo ""
}

# Função principal
main() {
    print_header "DEPLOY PARA PRODUÇÃO"
    
    # Inicializar log
    echo "Deploy iniciado em $(date)" > "$DEPLOY_LOG"
    
    # Verificações pré-deploy
    check_production_env
    check_critical_configs
    check_git_status
    
    # Confirmação final
    echo ""
    print_warning "ATENÇÃO: Este script irá fazer deploy em PRODUÇÃO!"
    echo "Deseja continuar? (y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        print_message "Deploy cancelado pelo usuário"
        exit 0
    fi
    
    # Backup e deploy
    backup_database
    stop_services
    pull_images
    build_images
    run_migrations
    start_services
    
    # Verificações pós-deploy
    if health_check && smoke_tests; then
        cleanup
        show_deploy_info
        print_message "Deploy concluído com sucesso!"
    else
        rollback
        exit 1
    fi
}

# Trap para capturar erros e fazer rollback
trap 'rollback' ERR

# Executar função principal
main "$@"
