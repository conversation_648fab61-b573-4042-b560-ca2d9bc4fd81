import React from "react";
import { Navigate, useParams } from "react-router-dom";
import { useAuth } from "@/store";
import LoadingSpinner from "@/components/ui/LoadingSpinner";

interface ProtectedRestaurantRouteProps {
  children: React.ReactNode;
}

/**
 * Componente de rota protegida para dashboard de restaurante
 * Verifica se o usuário está autenticado e tem permissão para acessar o dashboard do restaurante
 */
const ProtectedRestaurantRoute: React.FC<ProtectedRestaurantRouteProps> = ({
  children,
}) => {
  const { isAuthenticated, user, authToken } = useAuth();
  const { restaurantId } = useParams();

  console.log("🔐 ProtectedRoute - Estado:", { isAuthenticated, user: !!user, authToken: !!authToken });

  // Se ainda está carregando o estado de autenticação
  if (authToken && !user) {
    console.log("🔐 Carregando autenticação...");
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600 dark:text-gray-400">
            Verificando autenticação...
          </p>
        </div>
      </div>
    );
  }

  // Se não está autenticado, redirecionar para login
  if (!isAuthenticated || !user || !authToken) {
    console.log("🔐 Não autenticado, redirecionando para login");
    return <Navigate to="/admin/login" replace />;
  }

  // Verificar se tem permissão para acessar dashboard de restaurante
  if (!["admin", "moderator", "staff"].includes(user.role)) {
    console.log("🔐 Sem permissão, redirecionando para home");
    return <Navigate to="/" replace />;
  }

  console.log("🔐 Acesso autorizado ao dashboard");
  return <>{children}</>;
};

export default ProtectedRestaurantRoute;
