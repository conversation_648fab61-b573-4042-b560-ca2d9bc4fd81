import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Building2,
  Users,
  Settings,
  Plus,
  Eye,
  Edit,
  Trash2,
  Crown,
  Shield,
  Zap,
  CheckCircle,
  XCircle,
  Calendar,
  Mail,
  Globe,
} from "lucide-react";
import Button from "@/components/ui/Button";
import { toast } from "react-hot-toast";

interface Tenant {
  id: string;
  name: string;
  subdomain?: string;
  plan: "starter" | "professional" | "enterprise";
  isActive: boolean;
  createdAt: string;
  settings: {
    maxTables: number;
    maxUsers: number;
    features: string[];
  };
  owner: {
    email: string;
    name: string;
    password?: string; // Apenas para criação
  };
  credentials?: {
    loginUrl: string;
    tempPassword?: string;
  };
}

const TenantManager: React.FC = () => {
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedTenant, setSelectedTenant] = useState<Tenant | null>(null);
  const [showCredentials, setShowCredentials] = useState(false);
  const [newTenantCredentials, setNewTenantCredentials] = useState<{
    email: string;
    password: string;
    loginUrl: string;
    tenantName: string;
  } | null>(null);

  const [newTenant, setNewTenant] = useState({
    name: "",
    subdomain: "",
    plan: "starter" as const,
    owner: {
      name: "",
      email: "",
      password: "",
    },
  });

  useEffect(() => {
    loadTenants();
  }, []);

  const loadTenants = async () => {
    try {
      const response = await fetch(
        "http://localhost:8001/api/v1/admin/tenants"
      );
      const data = await response.json();
      setTenants(data.tenants || []);
    } catch (error) {
      console.error("Erro ao carregar tenants:", error);
      toast.error("Erro ao carregar restaurantes");
    } finally {
      setLoading(false);
    }
  };

  const createTenant = async () => {
    try {
      const response = await fetch(
        "http://localhost:8001/api/v1/admin/tenants",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(newTenant),
        }
      );

      if (response.ok) {
        const data = await response.json();
        setTenants((prev) => [...prev, data.tenant]);
        setShowCreateForm(false);

        // Mostrar credenciais de acesso
        const loginUrl = data.tenant.subdomain
          ? `http://${data.tenant.subdomain}.localhost:3001/admin`
          : `http://localhost:3001/restaurant/${data.tenant.id}/admin`;

        setNewTenantCredentials({
          email: newTenant.owner.email,
          password: newTenant.owner.password!,
          loginUrl,
          tenantName: newTenant.name,
        });
        setShowCredentials(true);

        // Reset form
        setNewTenant({
          name: "",
          subdomain: "",
          plan: "starter",
          owner: { name: "", email: "", password: "" },
        });
      } else {
        const error = await response.json();
        toast.error(error.error || "Erro ao criar restaurante");
      }
    } catch (error) {
      console.error("Erro ao criar tenant:", error);
      toast.error("Erro ao criar restaurante");
    }
  };

  const getPlanIcon = (plan: string) => {
    switch (plan) {
      case "starter":
        return Shield;
      case "professional":
        return Zap;
      case "enterprise":
        return Crown;
      default:
        return Shield;
    }
  };

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case "starter":
        return "from-blue-500 to-blue-600";
      case "professional":
        return "from-purple-500 to-purple-600";
      case "enterprise":
        return "from-yellow-500 to-yellow-600";
      default:
        return "from-gray-500 to-gray-600";
    }
  };

  const getPlanPrice = (plan: string) => {
    switch (plan) {
      case "starter":
        return "R$ 97/mês";
      case "professional":
        return "R$ 197/mês";
      case "enterprise":
        return "R$ 397/mês";
      default:
        return "Grátis";
    }
  };

  const viewTenant = (tenant: Tenant) => {
    // Abrir em nova aba a interface do restaurante
    const url = tenant.subdomain
      ? `http://${tenant.subdomain}.localhost:3001`
      : `http://localhost:3001/restaurant/${tenant.id}`;

    window.open(url, "_blank");
    toast.success(`Abrindo interface do ${tenant.name}`);
  };

  const configureTenant = (tenant: Tenant) => {
    // Abrir em nova aba o painel admin do restaurante
    const url = tenant.subdomain
      ? `http://${tenant.subdomain}.localhost:3001/admin`
      : `http://localhost:3001/restaurant/${tenant.id}/admin`;

    window.open(url, "_blank");
    toast.success(`Abrindo configurações do ${tenant.name}`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Gerenciar Restaurantes
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Gerencie todos os restaurantes da plataforma
          </p>
        </div>

        <Button
          onClick={() => setShowCreateForm(true)}
          className="bg-gradient-to-r from-blue-500 to-purple-600"
        >
          <Plus className="w-4 h-4 mr-2" />
          Novo Restaurante
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <Building2 className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">Total</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {tenants.length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
              <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">Ativos</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {tenants.filter((t) => t.isActive).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
              <Crown className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Enterprise
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {tenants.filter((t) => t.plan === "enterprise").length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg">
              <Zap className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Professional
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {tenants.filter((t) => t.plan === "professional").length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Tenants Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {tenants.map((tenant, index) => {
          const PlanIcon = getPlanIcon(tenant.plan);

          return (
            <motion.div
              key={tenant.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow"
            >
              <div className="p-6">
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div
                      className={`p-2 bg-gradient-to-r ${getPlanColor(
                        tenant.plan
                      )} rounded-lg`}
                    >
                      <PlanIcon className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">
                        {tenant.name}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {tenant.plan.charAt(0).toUpperCase() +
                          tenant.plan.slice(1)}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-1">
                    {tenant.isActive ? (
                      <CheckCircle className="w-5 h-5 text-green-500" />
                    ) : (
                      <XCircle className="w-5 h-5 text-red-500" />
                    )}
                  </div>
                </div>

                {/* Info */}
                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                    <Mail className="w-4 h-4 mr-2 flex-shrink-0" />
                    <span className="truncate">{tenant.owner.email}</span>
                  </div>

                  {tenant.subdomain && (
                    <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                      <Globe className="w-4 h-4 mr-2 flex-shrink-0" />
                      <span className="truncate">
                        {tenant.subdomain}.playlistinterativa.com
                      </span>
                    </div>
                  )}

                  <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                    <Calendar className="w-4 h-4 mr-2 flex-shrink-0" />
                    <span>
                      {new Date(tenant.createdAt).toLocaleDateString("pt-BR")}
                    </span>
                  </div>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="text-center">
                    <p className="text-lg font-semibold text-gray-900 dark:text-white">
                      {tenant.settings.maxTables}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Mesas
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-lg font-semibold text-gray-900 dark:text-white">
                      {tenant.settings.features.length}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Recursos
                    </p>
                  </div>
                </div>

                {/* Price */}
                <div className="text-center mb-4">
                  <p className="text-lg font-bold text-blue-600 dark:text-blue-400">
                    {getPlanPrice(tenant.plan)}
                  </p>
                </div>

                {/* Actions */}
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={() => viewTenant(tenant)}
                  >
                    <Eye className="w-4 h-4 mr-1" />
                    Ver
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={() => configureTenant(tenant)}
                  >
                    <Settings className="w-4 h-4 mr-1" />
                    Config
                  </Button>
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* Create Tenant Modal */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
              Novo Restaurante
            </h2>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Nome do Restaurante
                </label>
                <input
                  type="text"
                  value={newTenant.name}
                  onChange={(e) =>
                    setNewTenant((prev) => ({ ...prev, name: e.target.value }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder="Ex: Restaurante Sabor & Arte"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Subdomínio (opcional)
                </label>
                <input
                  type="text"
                  value={newTenant.subdomain}
                  onChange={(e) =>
                    setNewTenant((prev) => ({
                      ...prev,
                      subdomain: e.target.value,
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder="sabor-arte"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Será acessível em: {newTenant.subdomain || "subdominio"}
                  .playlistinterativa.com
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Plano
                </label>
                <select
                  value={newTenant.plan}
                  onChange={(e) =>
                    setNewTenant((prev) => ({
                      ...prev,
                      plan: e.target.value as any,
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="starter">Starter - R$ 97/mês</option>
                  <option value="professional">
                    Professional - R$ 197/mês
                  </option>
                  <option value="enterprise">Enterprise - R$ 397/mês</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Nome do Proprietário
                </label>
                <input
                  type="text"
                  value={newTenant.owner.name}
                  onChange={(e) =>
                    setNewTenant((prev) => ({
                      ...prev,
                      owner: { ...prev.owner, name: e.target.value },
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder="Carlos Silva"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Email do Proprietário
                </label>
                <input
                  type="email"
                  value={newTenant.owner.email}
                  onChange={(e) =>
                    setNewTenant((prev) => ({
                      ...prev,
                      owner: { ...prev.owner, email: e.target.value },
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Senha Inicial
                </label>
                <input
                  type="password"
                  value={newTenant.owner.password}
                  onChange={(e) =>
                    setNewTenant((prev) => ({
                      ...prev,
                      owner: { ...prev.owner, password: e.target.value },
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder="Mínimo 8 caracteres"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  O proprietário poderá alterar esta senha no primeiro login
                </p>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowCreateForm(false)}
                className="flex-1"
              >
                Cancelar
              </Button>
              <Button
                onClick={createTenant}
                className="flex-1 bg-gradient-to-r from-blue-500 to-purple-600"
                disabled={
                  !newTenant.name ||
                  !newTenant.owner.name ||
                  !newTenant.owner.email ||
                  !newTenant.owner.password ||
                  newTenant.owner.password.length < 8
                }
              >
                Criar
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Credentials Modal */}
      {showCredentials && newTenantCredentials && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
              </div>
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                Restaurante Criado!
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                {newTenantCredentials.tenantName} foi criado com sucesso
              </p>
            </div>

            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-3">
                Credenciais de Acesso:
              </h3>

              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Email:
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={newTenantCredentials.email}
                      readOnly
                      className="flex-1 px-3 py-2 bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded text-sm"
                    />
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() =>
                        navigator.clipboard.writeText(
                          newTenantCredentials.email
                        )
                      }
                    >
                      Copiar
                    </Button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Senha:
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={newTenantCredentials.password}
                      readOnly
                      className="flex-1 px-3 py-2 bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded text-sm font-mono"
                    />
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() =>
                        navigator.clipboard.writeText(
                          newTenantCredentials.password
                        )
                      }
                    >
                      Copiar
                    </Button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    URL de Login:
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={newTenantCredentials.loginUrl}
                      readOnly
                      className="flex-1 px-3 py-2 bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded text-sm"
                    />
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() =>
                        navigator.clipboard.writeText(
                          newTenantCredentials.loginUrl
                        )
                      }
                    >
                      Copiar
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3 mb-6">
              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                <strong>Importante:</strong> Salve essas credenciais em local
                seguro. O proprietário poderá alterar a senha no primeiro login.
              </p>
            </div>

            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() =>
                  window.open(newTenantCredentials.loginUrl, "_blank")
                }
                className="flex-1"
              >
                Abrir Login
              </Button>
              <Button
                onClick={() => {
                  setShowCredentials(false);
                  setNewTenantCredentials(null);
                  toast.success("Restaurante criado com sucesso!");
                }}
                className="flex-1 bg-gradient-to-r from-green-500 to-green-600"
              >
                Concluir
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TenantManager;
