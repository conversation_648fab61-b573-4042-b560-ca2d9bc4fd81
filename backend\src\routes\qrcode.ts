import { Router, Request, Response } from "express";
import { body, param, validationResult } from "../utils/validation";
import { AppDataSource } from "../config/database";
import { Restaurant } from "../models/Restaurant";
import { QRCode, QRCodeType } from "../models/QRCode";
import asyncHandler from "../middleware/asyncHandler";
import * as QRCodeLib from "qrcode";

const router = Router();

interface QRCodeData {
  id: string;
  type: "table" | "restaurant";
  name: string;
  url: string;
  qrCodeDataURL: string;
  createdAt: string;
  isActive: boolean;
  tableNumber?: string;
  restaurantId: string;
  restaurant: {
    id: string;
    name: string;
  };
}

// Função para acessar dados mock
function getMockData() {
  return (
    (global as any).mockData || {
      restaurants: [],
      qrCodes: [],
      suggestions: [],
      votes: [],
      analytics: {
        songPlays: [],
        votes: [],
        suggestions: [],
        sessions: [],
        dailyStats: [],
        popularSongs: [],
        peakHours: [],
      },
    }
  );
}

/**
 * @route GET /api/v1/qrcode/:restaurantId
 * @desc Listar QR Codes de um restaurante
 */
router.get(
  "/:restaurantId",
  asyncHandler(async (req, res) => {
    const { restaurantId } = req.params;

    if (!restaurantId) {
      return res.status(400).json({ error: "ID do restaurante é obrigatório" });
    }

    const restaurantRepository = AppDataSource.getRepository(Restaurant);
    const restaurant = await restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      return res.status(404).json({ error: "Restaurante não encontrado" });
    }

    // Buscar QR codes do banco de dados
    const qrCodeRepository = AppDataSource.getRepository(QRCode);
    const qrCodes = await qrCodeRepository.find({
      where: { restaurant: { id: restaurantId }, isActive: true },
      order: { createdAt: "DESC" },
    });

    res.json({
      success: true,
      qrCodes: qrCodes.map((qr) => qr.toPublicJSON()),
      total: qrCodes.length,
    });
  })
);

/**
 * @route POST /api/v1/qrcode/table
 * @desc Gerar QR Code para uma mesa específica
 */
router.post(
  "/table",
  asyncHandler(async (req, res) => {
    console.log("🚀 POST /table endpoint executado!");
    console.log("📝 Body recebido:", req.body);

    const { restaurantId, tableNumber, tableName, options } = req.body;

    if (!restaurantId || !tableNumber) {
      console.log("❌ Dados obrigatórios faltando");
      return res
        .status(400)
        .json({ error: "RestaurantId e tableNumber são obrigatórios" });
    }

    const restaurantRepository = AppDataSource.getRepository(Restaurant);
    const restaurant = await restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      return res.status(404).json({ error: "Restaurante não encontrado" });
    }

    // URL que o cliente acessará (porta 8000 do frontend)
    const clientUrl = `http://localhost:8000/client/${restaurantId}?table=${tableNumber}`;

    // Gerar QR Code real usando a biblioteca qrcode
    console.log("🔄 Gerando QR Code para URL:", clientUrl);
    const qrCodeDataURL = await QRCodeLib.toDataURL(clientUrl, {
      errorCorrectionLevel: "M",
      type: "image/png",
      
      margin: 1,
      color: {
        dark: "#000000",
        light: "#FFFFFF",
      },
    });
    console.log("✅ QR Code gerado, tamanho:", qrCodeDataURL.length);

    // Verificar se já existe QR code para esta mesa
    const qrCodeRepository = AppDataSource.getRepository(QRCode);
    let existingQRCode = await qrCodeRepository.findOne({
      where: {
        restaurant: { id: restaurantId },
        tableNumber: tableNumber.toString(),
        type: QRCodeType.TABLE,
      },
    });

    if (existingQRCode) {
      // Atualizar QR code existente
      existingQRCode.name = tableName || `Mesa ${tableNumber}`;
      existingQRCode.url = clientUrl;
      existingQRCode.qrCodeData = qrCodeDataURL;
      existingQRCode.isActive = true;
      await qrCodeRepository.save(existingQRCode);

      res.json({
        success: true,
        message: "QR Code atualizado com sucesso",
        qrCode: existingQRCode.toPublicJSON(),
      });
    } else {
      // Criar novo QR code
      const newQRCode = qrCodeRepository.create({
        restaurant,
        type: QRCodeType.TABLE,
        name: tableName || `Mesa ${tableNumber}`,
        tableNumber: tableNumber.toString(),
        url: clientUrl,
        qrCodeData: qrCodeDataURL,
        isActive: true,
      });

      await qrCodeRepository.save(newQRCode);

      res.json({
        success: true,
        message: "QR Code gerado com sucesso",
        qrCode: newQRCode.toPublicJSON(),
      });
    }
  })
);
/**
 * @route POST /api/v1/qrcode/bulk-tables
 * @desc Gerar QR Codes em lote para múltiplas mesas
 */
router.post(
  "/bulk-tables",
  asyncHandler(async (req, res) => {
    const { restaurantId, tableCount, tablePrefix = "Mesa" } = req.body;

    if (!restaurantId || !tableCount) {
      return res
        .status(400)
        .json({ error: "RestaurantId e tableCount são obrigatórios" });
    }

    if (tableCount < 1 || tableCount > 100) {
      return res
        .status(400)
        .json({ error: "Número de mesas deve ser entre 1 e 100" });
    }

    const restaurantRepository = AppDataSource.getRepository(Restaurant);
    const restaurant = await restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      return res.status(404).json({ error: "Restaurante não encontrado" });
    }

    const qrCodeRepository = AppDataSource.getRepository(QRCode);
    const generatedQRCodes: QRCode[] = [];

    for (let i = 1; i <= tableCount; i++) {
      const tableNumber = i.toString();
      const tableName = `${tablePrefix} ${i}`;
      const clientUrl = `http://localhost:8000/client/${restaurantId}?table=${tableNumber}`;

      // Gerar QR Code real
      const qrCodeDataURL = await QRCodeLib.toDataURL(clientUrl, {
        errorCorrectionLevel: "M",
        type: "image/png",
        
        margin: 1,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
      });

      // Verificar se já existe QR code para esta mesa
      let existingQRCode = await qrCodeRepository.findOne({
        where: {
          restaurant: { id: restaurantId },
          tableNumber: tableNumber,
          type: QRCodeType.TABLE,
        },
      });

      if (existingQRCode) {
        // Atualizar QR code existente
        existingQRCode.name = tableName;
        existingQRCode.url = clientUrl;
        existingQRCode.qrCodeData = qrCodeDataURL;
        existingQRCode.isActive = true;
        await qrCodeRepository.save(existingQRCode);
        generatedQRCodes.push(existingQRCode);
      } else {
        // Criar novo QR code
        const newQRCode = qrCodeRepository.create({
          restaurant,
          type: QRCodeType.TABLE,
          name: tableName,
          tableNumber: tableNumber,
          url: clientUrl,
          qrCodeData: qrCodeDataURL,
          isActive: true,
        });

        await qrCodeRepository.save(newQRCode);
        generatedQRCodes.push(newQRCode);
      }
    }

    res.json({
      success: true,
      message: `${tableCount} QR Codes gerados com sucesso`,
      totalGenerated: tableCount,
      qrCodes: generatedQRCodes.map((qr) => qr.toPublicJSON()),
    });
  })
);

/**
 * @route POST /api/v1/qrcode/restaurant
 * @desc Gerar QR Code principal do restaurante
 */
router.post(
  "/restaurant",
  asyncHandler(async (req, res) => {
    const { restaurantId, restaurantName, customization } = req.body;

    if (!restaurantId) {
      return res.status(400).json({ error: "ID do restaurante é obrigatório" });
    }

    const restaurantRepository = AppDataSource.getRepository(Restaurant);
    const restaurant = await restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      return res.status(404).json({ error: "Restaurante não encontrado" });
    }

    // URL principal do restaurante (porta 8000 do frontend)
    const clientUrl = `http://localhost:8000/client/${restaurantId}`;

    // Gerar QR Code real
    const qrCodeDataURL = await QRCodeLib.toDataURL(clientUrl, {
      errorCorrectionLevel: "M",
      type: "image/png",
      
      margin: 1,
      color: {
        dark: "#000000",
        light: "#FFFFFF",
      },
    });

    const qrCodeRepository = AppDataSource.getRepository(QRCode);

    // Verificar se já existe QR code principal do restaurante
    let existingQRCode = await qrCodeRepository.findOne({
      where: {
        restaurant: { id: restaurantId },
        type: QRCodeType.RESTAURANT,
      },
    });

    if (existingQRCode) {
      // Atualizar QR code existente
      existingQRCode.name = restaurantName || restaurant.name;
      existingQRCode.url = clientUrl;
      existingQRCode.qrCodeData = qrCodeDataURL;
      existingQRCode.isActive = true;
      await qrCodeRepository.save(existingQRCode);

      res.json({
        success: true,
        message: "QR Code principal atualizado com sucesso",
        qrCode: existingQRCode.toPublicJSON(),
      });
    } else {
      // Criar novo QR code
      const newQRCode = qrCodeRepository.create({
        restaurant,
        type: QRCodeType.RESTAURANT,
        name: restaurantName || restaurant.name,
        url: clientUrl,
        qrCodeData: qrCodeDataURL,
        isActive: true,
      });

      await qrCodeRepository.save(newQRCode);

      res.json({
        success: true,
        message: "QR Code principal gerado com sucesso",
        qrCode: newQRCode.toPublicJSON(),
      });
    }
  })
);

export = router;
