import React, { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import {
  BarChart3,
  TrendingUp,
  Users,
  Music,
  Clock,
  Download,
} from "lucide-react";

interface DashboardData {
  summary: {
    totalPlays: number;
    totalVotes: number;
    totalSuggestions: number;
    upvotes: number;
    downvotes: number;
    engagementRate: number;
  };
  trends: {
    playsOverTime: Array<{ time: string; label: string; value: number }>;
    votesOverTime: Array<{ time: string; label: string; value: number }>;
    suggestionsOverTime: Array<{ time: string; label: string; value: number }>;
  };
}

interface PopularSong {
  id: string;
  title: string;
  artist: string;
  stats: {
    plays: number;
    upvotes: number;
    downvotes: number;
    totalVotes: number;
    popularityScore: number;
  };
}

interface EngagementData {
  metrics: {
    totalSuggestions: number;
    totalVotes: number;
    upvotes: number;
    downvotes: number;
    engagementRate: number;
    positivityRate: number;
    averageVotesPerSuggestion: number;
  };
}

const AnalyticsDashboard: React.FC = () => {
  const { restaurantId } = useParams<{ restaurantId: string }>();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(
    null
  );
  const [popularSongs, setPopularSongs] = useState<PopularSong[]>([]);
  const [engagementData, setEngagementData] = useState<EngagementData | null>(
    null
  );
  const [period, setPeriod] = useState("7d");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const finalRestaurantId = restaurantId || "demo-restaurant";

  useEffect(() => {
    fetchAnalyticsData();
  }, [period, finalRestaurantId]);

  const fetchAnalyticsData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Fetch dashboard data
      const dashboardResponse = await fetch(
        `http://localhost:8001/api/v1/analytics/dashboard/${finalRestaurantId}?period=${period}`
      );
      const dashboardResult = await dashboardResponse.json();
      setDashboardData(dashboardResult);

      // Fetch popular songs
      const songsResponse = await fetch(
        `http://localhost:8001/api/v1/analytics/popular-songs/${finalRestaurantId}?limit=5`
      );
      const songsResult = await songsResponse.json();
      setPopularSongs(songsResult.songs);

      // Fetch engagement data
      const engagementResponse = await fetch(
        `http://localhost:8001/api/v1/analytics/engagement/${finalRestaurantId}?period=${period}`
      );
      const engagementResult = await engagementResponse.json();
      setEngagementData(engagementResult);
    } catch (err) {
      setError("Erro ao carregar dados de analytics");
      console.error("Analytics error:", err);
    } finally {
      setLoading(false);
    }
  };

  const generateTestData = async () => {
    try {
      await fetch(
        `http://localhost:8001/api/v1/analytics/generate-test-data/${finalRestaurantId}`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ days: 7 }),
        }
      );

      // Refresh data after generating test data
      fetchAnalyticsData();
    } catch (err) {
      console.error("Error generating test data:", err);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando analytics...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchAnalyticsData}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Tentar Novamente
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Analytics Dashboard
              </h1>
              <p className="text-gray-600">
                Restaurante Demo - Métricas e Relatórios
              </p>
            </div>

            <div className="flex items-center space-x-4">
              {/* Period Selector */}
              <select
                value={period}
                onChange={(e) => setPeriod(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="24h">Últimas 24h</option>
                <option value="7d">Últimos 7 dias</option>
                <option value="30d">Últimos 30 dias</option>
                <option value="90d">Últimos 90 dias</option>
              </select>

              {/* Generate Test Data Button */}
              <button
                onClick={generateTestData}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2"
              >
                <BarChart3 className="w-4 h-4" />
                <span>Gerar Dados Teste</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Summary Cards */}
        {dashboardData && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <Music className="w-8 h-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">
                    Total de Plays
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {dashboardData.summary.totalPlays}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <TrendingUp className="w-8 h-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">
                    Total de Votos
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {dashboardData.summary.totalVotes}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <Users className="w-8 h-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Sugestões</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {dashboardData.summary.totalSuggestions}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <BarChart3 className="w-8 h-8 text-orange-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">
                    Engajamento
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {dashboardData.summary.engagementRate}%
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Popular Songs */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                Músicas Mais Populares
              </h3>
            </div>
            <div className="p-6">
              {popularSongs.length > 0 ? (
                <div className="space-y-4">
                  {popularSongs.map((song, index) => (
                    <div
                      key={song.id}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                          {index + 1}
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">
                            {song.title}
                          </p>
                          <p className="text-sm text-gray-600">{song.artist}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900">
                          Score: {song.stats.popularityScore}
                        </p>
                        <p className="text-xs text-gray-600">
                          {song.stats.plays} plays • {song.stats.upvotes}{" "}
                          upvotes
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-8">
                  Nenhum dado disponível
                </p>
              )}
            </div>
          </div>

          {/* Engagement Metrics */}
          {engagementData && (
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">
                  Métricas de Engajamento
                </h3>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Taxa de Engajamento</span>
                    <span className="font-semibold text-green-600">
                      {engagementData.metrics.engagementRate}%
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Taxa de Positividade</span>
                    <span className="font-semibold text-blue-600">
                      {engagementData.metrics.positivityRate}%
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Votos por Sugestão</span>
                    <span className="font-semibold text-purple-600">
                      {engagementData.metrics.averageVotesPerSuggestion}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Upvotes</span>
                    <span className="font-semibold text-green-600">
                      {engagementData.metrics.upvotes}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Downvotes</span>
                    <span className="font-semibold text-red-600">
                      {engagementData.metrics.downvotes}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Export Section */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                Exportar Dados
              </h3>
              <p className="text-gray-600">
                Baixe relatórios detalhados em formato JSON
              </p>
            </div>
            <div className="flex space-x-3">
              <a
                href={`http://localhost:8001/api/v1/analytics/export/${finalRestaurantId}?type=all&format=json`}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Download className="w-4 h-4" />
                <span>Exportar Tudo</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
