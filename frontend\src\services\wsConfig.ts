// Configuração do endpoint do WebSocket/Socket.IO
export function getWsConfig() {
  const isLocal =
    window.location.hostname === "localhost" ||
    window.location.hostname === "127.0.0.1";
  const envBase = (import.meta as any).env?.VITE_API_URL as string | undefined;
  const origin = (envBase && envBase.trim() !== ""
    ? envBase
    : isLocal
    ? "http://localhost:8001"
    : window.location.origin
  ).replace(/\/$/, "");
  const wsUrl = origin; // socket.io no mesmo host da API
  return { wsUrl, isLocal };
}
