import { redisClient } from "../config/redis";

/**
 * Serviço de Rate Limit para votos normais (gratuitos).
 *
 * Regras:
 * - Rate limit por identidade: (restaurantId + clientSessionId) ou (restaurantId + IP)
 * - <PERSON><PERSON> e limite configuráveis por env.
 *
 * ENV:
 * - NORMAL_VOTE_RATE_LIMIT (default: 1)
 * - NORMAL_VOTE_RATE_WINDOW_SECONDS (default: 180)
 */
export interface VoteRateResult {
	allowed: boolean;
	remaining: number;
	retryAfter?: number; // segundos
}

export interface VoteRateCheckOptions {
	clientSessionId?: string;
	ip?: string;
}

function getWindowSeconds(): number {
	const s = Number(process.env.NORMAL_VOTE_RATE_WINDOW_SECONDS || 180);
	return Number.isFinite(s) && s > 0 ? s : 180;
}

function getLimit(): number {
	const n = Number(process.env.NORMAL_VOTE_RATE_LIMIT || 1);
	return Number.isFinite(n) && n > 0 ? n : 1;
}

class VoteRateLimiterService {
	/**
	 * Aplica rate limit por (restaurantId + clientSessionId OU ip) para votos normais.
	 */
	async checkNormalVote(
		restaurantId: string,
		opts: VoteRateCheckOptions = {}
	): Promise<VoteRateResult> {
		const limit = getLimit();
		const windowSec = getWindowSeconds();

		const identity = opts.clientSessionId || opts.ip || "anonymous";
		const key = `rate:vote:normal:${restaurantId}:${identity}`;

		const { allowed, remaining } = await redisClient.checkRateLimit(
			key,
			limit,
			windowSec
		);

		if (allowed) {
			return { allowed, remaining };
		}

		// Calcular retryAfter aproximado pelo TTL restante
		try {
			const ttl = await redisClient.getClient().ttl(key);
			return { allowed, remaining, retryAfter: ttl > 0 ? ttl : windowSec };
		} catch {
			return { allowed, remaining, retryAfter: windowSec };
		}
	}
}

export const voteRateLimiter = new VoteRateLimiterService();
export default voteRateLimiter;

