import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { User, Music, Heart, Clock, Settings, X } from 'lucide-react';
import { toast } from 'react-hot-toast';

// Componentes
import Button from '@/components/ui/Button';

// Hooks e serviços
import { useAppStore } from '@/store';

interface UserSessionProps {
  isOpen: boolean;
  onClose: () => void;
  restaurantId: string;
}

interface UserStats {
  suggestionsToday: number;
  votesToday: number;
  favoriteGenres: string[];
  sessionDuration: string;
}

const UserSession: React.FC<UserSessionProps> = ({ isOpen, onClose, restaurantId }) => {
  const [userName, setUserName] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [userStats, setUserStats] = useState<UserStats>({
    suggestionsToday: 0,
    votesToday: 0,
    favoriteGenres: [],
    sessionDuration: '0 min'
  });

  const { suggestions, settings } = useAppStore();

  useEffect(() => {
    // Carregar nome do usuário do localStorage
    const savedName = localStorage.getItem(`userName_${restaurantId}`);
    if (savedName) {
      setUserName(savedName);
    }

    // Calcular estatísticas da sessão
    const sessionStart = localStorage.getItem(`sessionStart_${restaurantId}`);
    if (sessionStart) {
      const duration = Date.now() - parseInt(sessionStart);
      const minutes = Math.floor(duration / 60000);
      setUserStats(prev => ({
        ...prev,
        sessionDuration: minutes > 0 ? `${minutes} min` : '< 1 min'
      }));
    }

    // Contar sugestões e votos do usuário hoje
    const today = new Date().toDateString();
    const userSuggestions = suggestions.filter(s => 
      new Date(s.createdAt).toDateString() === today
    );
    
    setUserStats(prev => ({
      ...prev,
      suggestionsToday: userSuggestions.length,
      votesToday: parseInt(localStorage.getItem(`votesToday_${restaurantId}`) || '0')
    }));
  }, [restaurantId, suggestions]);

  const handleSaveName = () => {
    if (userName.trim()) {
      localStorage.setItem(`userName_${restaurantId}`, userName.trim());
      toast.success('Nome salvo!');
      setIsEditing(false);
    }
  };

  const handleStartSession = () => {
    if (!localStorage.getItem(`sessionStart_${restaurantId}`)) {
      localStorage.setItem(`sessionStart_${restaurantId}`, Date.now().toString());
    }
  };

  useEffect(() => {
    handleStartSession();
  }, [restaurantId]);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-md w-full p-6"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              Sua Sessão
            </h2>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Avatar e Nome */}
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-3">
              <User className="w-8 h-8 text-white" />
            </div>
            
            {isEditing ? (
              <div className="space-y-3">
                <input
                  type="text"
                  value={userName}
                  onChange={(e) => setUserName(e.target.value)}
                  placeholder="Digite seu nome"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-center"
                  autoFocus
                />
                <div className="flex space-x-2">
                  <Button
                    onClick={handleSaveName}
                    size="sm"
                    className="flex-1"
                  >
                    Salvar
                  </Button>
                  <Button
                    onClick={() => setIsEditing(false)}
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    Cancelar
                  </Button>
                </div>
              </div>
            ) : (
              <div>
                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                  {userName || 'Usuário Anônimo'}
                </p>
                <button
                  onClick={() => setIsEditing(true)}
                  className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
                >
                  {userName ? 'Alterar nome' : 'Definir nome'}
                </button>
              </div>
            )}
          </div>

          {/* Estatísticas da Sessão */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Estatísticas de Hoje
            </h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 text-center">
                <Music className="w-6 h-6 text-blue-600 dark:text-blue-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                  {userStats.suggestionsToday}
                </p>
                <p className="text-sm text-blue-600 dark:text-blue-400">
                  Sugestões
                </p>
              </div>
              
              <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center">
                <Heart className="w-6 h-6 text-green-600 dark:text-green-400 mx-auto mb-2" />
                <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                  {userStats.votesToday}
                </p>
                <p className="text-sm text-green-600 dark:text-green-400">
                  Votos
                </p>
              </div>
            </div>

            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Clock className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                <span className="text-sm font-medium text-purple-900 dark:text-purple-100">
                  Tempo de Sessão
                </span>
              </div>
              <p className="text-lg font-bold text-purple-900 dark:text-purple-100">
                {userStats.sessionDuration}
              </p>
            </div>
          </div>

          {/* Configurações Rápidas */}
          <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
              Configurações Rápidas
            </h3>
            
            <div className="space-y-3">
              <label className="flex items-center justify-between">
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  Notificações de voto
                </span>
                <input
                  type="checkbox"
                  checked={settings.showNotifications}
                  onChange={(e) => {
                    // Atualizar configurações no store
                    // Esta funcionalidade seria implementada no store
                  }}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </label>
              
              <label className="flex items-center justify-between">
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  Sons de interface
                </span>
                <input
                  type="checkbox"
                  checked={settings.soundEnabled}
                  onChange={(e) => {
                    // Atualizar configurações no store
                  }}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </label>
            </div>
          </div>

          {/* Footer */}
          <div className="mt-6 text-center">
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Suas informações são armazenadas localmente no seu dispositivo
            </p>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default UserSession;
