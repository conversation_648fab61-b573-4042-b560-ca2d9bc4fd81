import { AppDataSource } from "../config/database";
import {
  IsString,
  IsOptional,
  IsDateString,
  IsEnum,
  Is<PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  validate,
} from "class-validator";
import { plainToClass } from "class-transformer";
import { AnalyticsDaily } from "../models/AnalyticsDaily";
import { Restaurant } from "../models/Restaurant";
import { Suggestion, SuggestionStatus } from "../models/Suggestion";
import { Vote } from "../models/Vote";
import { ClientSession } from "../models/ClientSession";
import { redisClient } from "../config/redis";
import { Between, MoreThan } from "typeorm";
import { logger } from "../utils/logger";

export enum AnalyticsPeriod {
  HOUR = "hour",
  DAY = "day",
  WEEK = "week",
  MONTH = "month",
  YEAR = "year",
  CUSTOM = "custom",
  LAST_DAY = "1d",
  LAST_7_DAYS = "7d",
  LAST_30_DAYS = "30d",
  LAST_90_DAYS = "90d",
}

export enum AnalyticsMetricType {
  SUGGESTIONS = "suggestions",
  VOTES = "votes",
  SESSIONS = "sessions",
  PLAYBACK = "playback",
  ENGAGEMENT = "engagement",
  REVENUE = "revenue",
}

export enum DeviceType {
  MOBILE = "mobile",
  TABLET = "tablet",
  DESKTOP = "desktop",
  UNKNOWN = "unknown",
}

export interface IAnalyticsMetrics {
  totalSuggestions: number;
  approvedSuggestions: number;
  rejectedSuggestions: number;
  totalVotes: number;
  upvotes: number;
  downvotes: number;
  uniqueSessions: number;
  totalSessions: number;
  averageSessionDuration: number;
  totalPlayTime: number;
  songsPlayed: number;
  songsSkipped: number;
  peakHour: number | null;
  topGenres: ITopItem[];
  topArtists: ITopItem[];
  hourlyActivity: IHourlyActivity[];
  dailyActivity: IDailyActivity[];
  deviceStats: IDeviceStats;
  engagementRate: number;
  approvalRate: number;
  skipRate: number;
  period: AnalyticsPeriod;
  startDate: Date;
  endDate: Date;
  restaurantId?: string;
}

export interface ITopItem {
  name: string;
  count: number;
  percentage: number;
  trend?: number;
}

export interface IHourlyActivity {
  hour: number;
  suggestions: number;
  votes: number;
  sessions: number;
  playback: number;
  revenue?: number;
}

export interface IDailyActivity {
  date: string;
  suggestions: number;
  votes: number;
  sessions: number;
  playback: number;
  revenue?: number;
  weekday: string;
}

export interface IDeviceStats {
  mobile: number;
  tablet: number;
  desktop: number;
  unknown: number;
  total: number;
  mobilePercentage: number;
  tabletPercentage: number;
  desktopPercentage: number;
}

export interface IEngagementMetrics {
  averageSessionDuration: number;
  averageVotesPerSession: number;
  averageSuggestionsPerSession: number;
  returnUserRate: number;
  bounceRate: number;
  conversionRate: number;
}

export interface IRevenueMetrics {
  totalRevenue: number;
  averageRevenuePerUser: number;
  paidSuggestions: number;
  conversionRate: number;
  revenueByHour: Array<{ hour: number; revenue: number }>;
  revenueByDay: Array<{ date: string; revenue: number }>;
}

export interface IDashboardSummary {
  totalPlays: number;
  totalSuggestions: number;
  totalVotes: number;
  pendingSuggestions: number;
  dailySuggestions: number;
  dailyVotes: number;
  activeUsers: number;
  averageRating: number;
  growthRate: number;
  peakHour: string;
  topGenre: string;
  lastUpdated: Date;
}

export interface IPopularSong {
  id: string;
  title: string;
  artist: string;
  votes: number;
  plays: number;
  score: number;
  lastPlayed?: Date;
  thumbnail?: string;
  genre?: string;
}

export interface IEngagementData {
  totalUsers: number;
  activeUsers: number;
  engagementRate: number;
  retentionRate: number;
  period: AnalyticsPeriod;
  daily: Array<{ date: string; engagement: number }>;
  hourly: Array<{ hour: number; engagement: number }>;
  byGenre: Array<{ genre: string; engagement: number }>;
  averageSessionTime: number;
  returnRate: number;
  interactionRate: number;
}

export class AnalyticsQueryDto {
  @IsOptional()
  @IsString()
  restaurantId?: string;

  @IsOptional()
  @IsEnum(AnalyticsPeriod)
  period: AnalyticsPeriod = AnalyticsPeriod.DAY;

  @IsOptional()
  @IsDateString()
  startDate?: string;

  @IsOptional()
  @IsDateString()
  endDate?: string;

  @IsOptional()
  @IsEnum(AnalyticsMetricType)
  metricType?: AnalyticsMetricType;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  limit: number = 10;
}

export class DashboardQueryDto {
  @IsString()
  restaurantId: string;

  @IsOptional()
  @IsEnum(AnalyticsPeriod)
  period: AnalyticsPeriod = AnalyticsPeriod.DAY;
}

export class PopularSongsQueryDto {
  @IsOptional()
  @IsString()
  restaurantId?: string;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(50)
  limit: number = 10;

  @IsOptional()
  @IsString()
  genre?: string;

  @IsOptional()
  @IsEnum(AnalyticsPeriod)
  period: AnalyticsPeriod = AnalyticsPeriod.WEEK;
}

export class AnalyticsError extends Error {
  public readonly code: string;
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly restaurantId?: string;
  public readonly query?: any;

  constructor(
    message: string,
    code: string = "ANALYTICS_ERROR",
    statusCode: number = 500,
    isOperational: boolean = true,
    restaurantId?: string,
    query?: any
  ) {
    super(message);
    this.name = "AnalyticsError";
    this.code = code;
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.restaurantId = restaurantId;
    this.query = query;

    Error.captureStackTrace(this, this.constructor);
  }
}

class AnalyticsService {
  private static instance: AnalyticsService;
  private analyticsRepository = AppDataSource.getRepository(AnalyticsDaily);
  private restaurantRepository = AppDataSource.getRepository(Restaurant);
  private suggestionRepository = AppDataSource.getRepository(Suggestion);
  private voteRepository = AppDataSource.getRepository(Vote);
  private sessionRepository = AppDataSource.getRepository(ClientSession);

  private readonly cacheEnabled: boolean = true;
  private readonly cacheTTL = {
    realtime: 60,
    hourly: 300,
    daily: 1800,
    weekly: 3600,
    monthly: 7200,
  };

  constructor() {
    this.validateConfiguration();
  }

  static getInstance(): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService();
    }
    return AnalyticsService.instance;
  }

  private validateConfiguration(): void {
    if (this.cacheEnabled && !redisClient.isReady) {
      logger.warn("Redis não disponível - cache de analytics desabilitado");
    }
  }

  private async validateInput<T extends object>(
    dto: new () => T,
    data: any
  ): Promise<T> {
    const instance = plainToClass(dto, data);
    const errors = await validate(instance);

    if (errors.length > 0) {
      const errorMessages = errors
        .map((error) => Object.values(error.constraints || {}).join(", "))
        .join("; ");

      throw new AnalyticsError(
        `Dados inválidos: ${errorMessages}`,
        "VALIDATION_ERROR",
        400
      );
    }

    return instance;
  }

  private generateCacheKey(prefix: string, params: any): string {
    const paramString = JSON.stringify(params);
    const hash = Buffer.from(paramString).toString("base64").slice(0, 16);
    return `analytics:${prefix}:${hash}`;
  }

  private async getCachedOrExecute<T>(
    cacheKey: string,
    ttl: number,
    executor: () => Promise<T>
  ): Promise<T> {
    if (!this.cacheEnabled || !redisClient.isReady) {
      return await executor();
    }

    try {
      const cached = await redisClient.getClient().get(cacheKey);
      if (cached) {
        logger.debug(`Cache hit para ${cacheKey}`);
        return JSON.parse(cached);
      }

      const result = await executor();
      await redisClient
        .getClient()
        .setEx(cacheKey, ttl, JSON.stringify(result));
      logger.debug(`Cache miss para ${cacheKey} - dados armazenados`);

      return result;
    } catch (error) {
      logger.error("Erro no cache, executando query diretamente:", error);
      return await executor();
    }
  }

  async getMetrics(
    restaurantId: string,
    period: AnalyticsPeriod = AnalyticsPeriod.DAY,
    customStartDate?: Date,
    customEndDate?: Date
  ): Promise<IAnalyticsMetrics> {
    let endDate = customEndDate || new Date();
    let startDate = customStartDate || new Date();

    switch (period) {
      case AnalyticsPeriod.LAST_DAY:
        startDate.setDate(endDate.getDate() - 1);
        break;
      case AnalyticsPeriod.LAST_7_DAYS:
        startDate.setDate(endDate.getDate() - 7);
        break;
      case AnalyticsPeriod.LAST_30_DAYS:
        startDate.setDate(endDate.getDate() - 30);
        break;
      case AnalyticsPeriod.LAST_90_DAYS:
        startDate.setDate(endDate.getDate() - 90);
        break;
      default:
        startDate.setDate(endDate.getDate() - 7);
    }

    const [suggestions, votes, sessions] = await Promise.all([
      this.suggestionRepository.find({
        where: {
          restaurant: { id: restaurantId },
          createdAt: Between(startDate, endDate),
        },
        relations: ["votes"],
      }),
      this.voteRepository.find({
        where: {
          suggestion: {
            restaurant: { id: restaurantId },
          },
          createdAt: Between(startDate, endDate),
        },
        relations: ["suggestion"],
      }),
      this.sessionRepository.find({
        where: {
          restaurant: { id: restaurantId },
          createdAt: Between(startDate, endDate),
        },
      }),
    ]);

    const totalSuggestions = suggestions.length;
    const approvedSuggestions = suggestions.filter(
      (s) => s.status === SuggestionStatus.APPROVED
    ).length;
    const rejectedSuggestions = suggestions.filter(
      (s) => s.status === SuggestionStatus.REJECTED
    ).length;
    const totalVotes = votes.length;
    const upvotes = votes.filter((v) => v.voteType === "up").length;
    const downvotes = votes.filter((v) => v.voteType === "down").length;
    const uniqueSessions = sessions.length;

    const hourlyActivity: IHourlyActivity[] = Array.from(
      { length: 24 },
      (_, hour) => {
        const hourSuggestions = suggestions.filter(
          (s) => new Date(s.createdAt).getHours() === hour
        ).length;
        const hourVotes = votes.filter(
          (v) => new Date(v.createdAt).getHours() === hour
        ).length;
        const hourSessions = sessions.filter(
          (s) => new Date(s.createdAt).getHours() === hour
        ).length;

        return {
          hour,
          suggestions: hourSuggestions,
          votes: hourVotes,
          sessions: hourSessions,
          playback: 0,
        };
      }
    );

    const dailyActivity: IDailyActivity[] = [];
    for (
      let d = new Date(startDate);
      d <= endDate;
      d.setDate(d.getDate() + 1)
    ) {
      const dateStr = d.toISOString().split("T")[0];
      const daySuggestions = suggestions.filter(
        (s) => s.createdAt.toISOString().split("T")[0] === dateStr
      ).length;
      const dayVotes = votes.filter(
        (v) => v.createdAt.toISOString().split("T")[0] === dateStr
      ).length;
      const daySessions = sessions.filter(
        (s) => s.createdAt.toISOString().split("T")[0] === dateStr
      ).length;

      dailyActivity.push({
        date: dateStr,
        suggestions: daySuggestions,
        votes: dayVotes,
        sessions: daySessions,
        playback: 0,
        revenue: 0,
        weekday: d.toLocaleString("en-US", { weekday: "long" }),
      });
    }

    const genreCount: Record<string, number> = {};
    suggestions.forEach((suggestion) => {
      if (suggestion.genre) {
        genreCount[suggestion.genre] = (genreCount[suggestion.genre] || 0) + 1;
      }
    });

    const topGenres = Object.entries(genreCount)
      .map(([name, count]) => ({
        name,
        count,
        percentage:
          totalSuggestions > 0
            ? Math.round((count / totalSuggestions) * 100)
            : 0,
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    const artistCount: Record<string, number> = {};
    suggestions.forEach((suggestion) => {
      if (suggestion.artist) {
        artistCount[suggestion.artist] =
          (artistCount[suggestion.artist] || 0) + 1;
      }
    });

    const topArtists = Object.entries(artistCount)
      .map(([name, count]) => ({
        name,
        count,
        percentage:
          totalSuggestions > 0
            ? Math.round((count / totalSuggestions) * 100)
            : 0,
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    const peakHour = hourlyActivity.reduce((peak, current) =>
      current.suggestions + current.votes > peak.suggestions + peak.votes
        ? current
        : peak
    ).hour;

    const totalSessionDuration = sessions.reduce((total, session) => {
      if (session.lastActivity && session.createdAt) {
        return (
          total + (session.lastActivity.getTime() - session.createdAt.getTime())
        );
      }
      return total;
    }, 0);
    const averageSessionDuration =
      uniqueSessions > 0
        ? Math.round(totalSessionDuration / uniqueSessions / 1000 / 60)
        : 0;

    const mobile = Math.round(uniqueSessions * 0.6);
    const tablet = Math.round(uniqueSessions * 0.2);
    const desktop = Math.round(uniqueSessions * 0.2);
    const unknown = 0;
    const total = mobile + tablet + desktop + unknown;

    const deviceStats: IDeviceStats = {
      mobile,
      tablet,
      desktop,
      unknown,
      total,
      mobilePercentage: total > 0 ? Math.round((mobile / total) * 100) : 0,
      tabletPercentage: total > 0 ? Math.round((tablet / total) * 100) : 0,
      desktopPercentage: total > 0 ? Math.round((desktop / total) * 100) : 0,
    };

    const engagementRate =
      uniqueSessions > 0
        ? Math.round(((totalSuggestions + totalVotes) / uniqueSessions) * 100) /
          100
        : 0;
    const approvalRate =
      totalSuggestions > 0
        ? Math.round((approvedSuggestions / totalSuggestions) * 100)
        : 0;
    const skipRate = 0;

    return {
      totalSuggestions,
      approvedSuggestions,
      rejectedSuggestions,
      totalVotes,
      upvotes,
      downvotes,
      uniqueSessions,
      totalSessions: uniqueSessions,
      averageSessionDuration,
      totalPlayTime: 0,
      songsPlayed: approvedSuggestions,
      songsSkipped: 0,
      peakHour,
      topGenres,
      topArtists,
      hourlyActivity,
      dailyActivity,
      deviceStats,
      engagementRate,
      approvalRate,
      skipRate,
      period,
      startDate,
      endDate,
      restaurantId,
    };
  }

  async generateDashboardSummary(
    restaurantId: string,
    period: string = "7d"
  ): Promise<IDashboardSummary> {
    const cacheKey = `dashboard:${restaurantId}:${period}`;

    try {
      const cached = await redisClient.getClient().get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }
    } catch (error) {
      logger.warn("Redis cache not available:", error);
    }

    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case "1d":
        startDate.setDate(endDate.getDate() - 1);
        break;
      case "7d":
        startDate.setDate(endDate.getDate() - 7);
        break;
      case "30d":
        startDate.setDate(endDate.getDate() - 30);
        break;
      case "90d":
        startDate.setDate(endDate.getDate() - 90);
        break;
      default:
        startDate.setDate(endDate.getDate() - 7);
    }

    try {
      const [totalSuggestions, totalVotes, pendingSuggestions, activeUsers] =
        await Promise.all([
          AppDataSource.query(
            `
            SELECT COUNT(*) as count
            FROM suggestions
            WHERE restaurant_id = $1 AND COALESCE("createdAt", created_at) >= $2
          `,
            [restaurantId, startDate]
          ),
          AppDataSource.query(
            `
            SELECT COUNT(*) as count
            FROM votes v
            JOIN suggestions s ON v.suggestion_id = s.id
            WHERE s.restaurant_id = $1 AND v.created_at >= $2
          `,
            [restaurantId, startDate]
          ),
          AppDataSource.query(
            `
            SELECT COUNT(*) as count 
            FROM suggestions 
            WHERE restaurant_id = $1 AND status = $2
          `,
            [restaurantId, SuggestionStatus.PENDING]
          ),
          AppDataSource.query(
            `
            SELECT COUNT(DISTINCT "ipAddress") as count
            FROM client_sessions
            WHERE restaurant_id = $1 AND "createdAt" >= $2
          `,
            [restaurantId, startDate]
          ),
        ]);

      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const dailySuggestionsResult = await AppDataSource.query(
        `
        SELECT COUNT(*) as count
        FROM suggestions
        WHERE restaurant_id = $1 AND COALESCE("createdAt", created_at) >= $2
      `,
        [restaurantId, today]
      );

      // Votos do dia (normal + supervotos) baseados em votes.created_at hoje
      const dailyVotesResult = await AppDataSource.query(
        `
        SELECT COUNT(*) as count
        FROM votes v
        JOIN suggestions s ON v.suggestion_id = s.id
        WHERE s.restaurant_id = $1 AND v.created_at >= $2
      `,
        [restaurantId, today]
      );

      const result: IDashboardSummary = {
        totalPlays: 0,
        totalSuggestions: parseInt(totalSuggestions[0]?.count || 0),
        totalVotes: parseInt(totalVotes[0]?.count || 0),
        pendingSuggestions: parseInt(pendingSuggestions[0]?.count || 0),
        dailySuggestions: parseInt(dailySuggestionsResult[0]?.count || 0),
        dailyVotes: parseInt(dailyVotesResult[0]?.count || 0),
        activeUsers: parseInt(activeUsers[0]?.count || 0),
        averageRating: 4.2,
        growthRate: 5.2,
        peakHour: "20:00",
        topGenre: "Pop",
        lastUpdated: new Date(),
      };

      try {
        await redisClient
          .getClient()
          .setEx(cacheKey, 120, JSON.stringify(result));
      } catch (error) {
        logger.warn("Failed to cache result:", error);
      }

      return result;
    } catch (error) {
      logger.error("Erro ao gerar resumo do dashboard:", error);
      return {
        totalPlays: 0,
        totalSuggestions: 0,
        totalVotes: 0,
        pendingSuggestions: 0,
        dailySuggestions: 0,
        dailyVotes: 0,
        activeUsers: 0,
        averageRating: 0,
        growthRate: 0,
        peakHour: "12:00",
        topGenre: "N/A",
        lastUpdated: new Date(),
      };
    }
  }

  async getPopularSongs(
    restaurantId: string,
    limit: number = 10
  ): Promise<IPopularSong[]> {
    const cacheKey = `popular:${restaurantId}:${limit}`;

    try {
      const cached = await redisClient.getClient().get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }
    } catch (error) {
      logger.warn("Redis cache not available:", error);
    }

    try {
    const results = await AppDataSource.query(
        `
        SELECT 
          s.id,
          s.title,
          s.artist,
      s.thumbnail_url as thumbnail,
          s.genre,
          COUNT(v.id) as total_votes,
      SUM(CASE WHEN v.vote_type = 'up' THEN 1 ELSE 0 END) as upvotes,
      SUM(CASE WHEN v.vote_type = 'down' THEN 1 ELSE 0 END) as downvotes,
      (SUM(CASE WHEN v.vote_type = 'up' THEN 1 ELSE 0 END) - 
       SUM(CASE WHEN v.vote_type = 'down' THEN 1 ELSE 0 END)) as score
        FROM suggestions s
        LEFT JOIN votes v ON s.id = v.suggestion_id
        WHERE s.restaurant_id = $1
  GROUP BY s.id, s.title, s.artist, s.thumbnail_url, s.genre
        ORDER BY score DESC, total_votes DESC
        LIMIT $2
      `,
        [restaurantId, limit]
      );

      const popularSongs: IPopularSong[] = results.map((row: any) => ({
        id: row.id,
        title: row.title,
        artist: row.artist,
        votes: parseInt(row.total_votes) || 0,
        plays: 0,
        score: parseInt(row.score) || 0,
        thumbnail: row.thumbnail,
        genre: row.genre,
      }));

      try {
        await redisClient
          .getClient()
          .setEx(cacheKey, 300, JSON.stringify(popularSongs));
      } catch (error) {
        logger.warn("Failed to cache popular songs:", error);
      }

      return popularSongs;
    } catch (error) {
      logger.error("Erro ao obter músicas populares:", error);
      return [];
    }
  }

  async getEngagementData(
    restaurantId: string,
    period: string = "7d"
  ): Promise<IEngagementData> {
    const metrics = await this.getMetrics(
      restaurantId,
      AnalyticsPeriod.LAST_7_DAYS
    );

    const daily = metrics.dailyActivity.map((day) => ({
      date: day.date,
      engagement: day.suggestions + day.votes,
    }));

    const hourly = metrics.hourlyActivity.map((hour) => ({
      hour: hour.hour,
      engagement: hour.suggestions + hour.votes,
    }));

    const byGenre = metrics.topGenres.map((genre) => ({
      genre: genre.name,
      engagement: genre.count,
    }));

    return {
      totalUsers: metrics.uniqueSessions,
      activeUsers: metrics.uniqueSessions,
      engagementRate: metrics.engagementRate,
      retentionRate: 0,
      period: AnalyticsPeriod.LAST_7_DAYS,
      daily,
      hourly,
      byGenre,
      averageSessionTime: metrics.averageSessionDuration,
      returnRate: 0,
      interactionRate: metrics.engagementRate,
    };
  }

  async saveDailyAnalytics(
    restaurantId: string,
    date: Date = new Date()
  ): Promise<void> {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    const dateStr = startOfDay.toISOString().split("T")[0]; // Convert to YYYY-MM-DD string

    const existing = await this.analyticsRepository.findOne({
      where: {
        restaurant: { id: restaurantId },
        date: dateStr,
      },
    });

    if (existing) {
      logger.info(
        `Analytics já existem para ${restaurantId} em ${
          date.toISOString().split("T")[0]
        }`
      );
      return;
    }

    const metrics = await this.getMetrics(
      restaurantId,
      AnalyticsPeriod.LAST_DAY
    );

    const restaurant = await this.restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      throw new AnalyticsError(
        `Restaurante não encontrado: ${restaurantId}`,
        "RESTAURANT_NOT_FOUND",
        404
      );
    }

    const analytics = this.analyticsRepository.create({
      restaurant: restaurant,
      date: dateStr, // Use the string format
      totalSuggestions: metrics.totalSuggestions,
      approvedSuggestions: metrics.approvedSuggestions,
      rejectedSuggestions: metrics.rejectedSuggestions,
      totalVotes: metrics.totalVotes,
      upvotes: metrics.upvotes,
      downvotes: metrics.downvotes,
      uniqueSessions: metrics.uniqueSessions,
      averageSessionDuration: metrics.averageSessionDuration,
      totalPlayTime: metrics.totalPlayTime,
      songsPlayed: metrics.songsPlayed,
      songsSkipped: metrics.songsSkipped,
      topGenres: metrics.topGenres,
      topArtists: metrics.topArtists,
      hourlyActivity: metrics.hourlyActivity,
      deviceStats: metrics.deviceStats,
    });

    await this.analyticsRepository.save(analytics);
    logger.info(
      `Analytics salvos para ${restaurantId} em ${
        date.toISOString().split("T")[0]
      }`
    );
  }

  async getHistoricalData(
    restaurantId: string,
    startDate: Date,
    endDate: Date
  ): Promise<AnalyticsDaily[]> {
    const startDateStr = startDate.toISOString().split("T")[0];
    const endDateStr = endDate.toISOString().split("T")[0];

    return this.analyticsRepository.find({
      where: {
        restaurant: { id: restaurantId },
        date: Between(startDateStr, endDateStr),
      },
      order: { date: "ASC" },
    });
  }

  async exportData(
    restaurantId: string,
    format: "json" | "csv" = "json",
    period: string = "30d"
  ): Promise<any> {
    const metrics = await this.getMetrics(
      restaurantId,
      AnalyticsPeriod.LAST_30_DAYS
    );
    const popularSongs = await this.getPopularSongs(restaurantId, 20);
    const engagementData = await this.getEngagementData(restaurantId, period);

    const exportData = {
      restaurant: restaurantId,
      period,
      generatedAt: new Date().toISOString(),
      summary: {
        totalSuggestions: metrics.totalSuggestions,
        totalVotes: metrics.totalVotes,
        uniqueSessions: metrics.uniqueSessions,
        engagementRate: metrics.engagementRate,
        approvalRate: metrics.approvalRate,
      },
      metrics,
      popularSongs,
      engagementData,
    };

    if (format === "csv") {
      return this.convertToCSV(exportData);
    }

    return exportData;
  }

  private convertToCSV(data: any): string {
    const headers = Object.keys(data.summary);
    const values = Object.values(data.summary);
    return [headers.join(","), values.join(",")].join("\n");
  }

  async cleanupOldData(daysToKeep: number = 365): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
    const cutoffDateStr = cutoffDate.toISOString().split("T")[0];

    await this.analyticsRepository.delete({
      date: MoreThan(cutoffDateStr),
    });

    logger.info(
      `Dados de analytics anteriores a ${
        cutoffDate.toISOString().split("T")[0]
      } foram removidos`
    );
  }

  async getTracksAnalytics(restaurantId: string): Promise<any[]> {
    try {
      const suggestions = await this.suggestionRepository
        .createQueryBuilder("suggestion")
        .leftJoinAndSelect("suggestion.votes", "vote")
        .where("suggestion.restaurant_id = :restaurantId", { restaurantId })
        .getMany();

      const trackAnalytics = suggestions.map((suggestion) => {
        const votes = suggestion.votes || [];
        const upvotes = votes.filter((v) => v.voteType === "up").length;
        const downvotes = votes.filter((v) => v.voteType === "down").length;
        const totalVotes = upvotes + downvotes;

        const score = upvotes - downvotes;
        const negativeVoteRatio = totalVotes > 0 ? downvotes / totalVotes : 0;
        const positiveVoteRatio = totalVotes > 0 ? upvotes / totalVotes : 0;

        const playCount = Math.floor(Math.random() * 50) + totalVotes;
        const skipCount = Math.floor(playCount * 0.2);
        const completionRate = 1 - skipCount / Math.max(playCount, 1);

        let performance = "average";
        if (negativeVoteRatio <= 0.1) performance = "excellent";
        else if (negativeVoteRatio <= 0.25) performance = "good";
        else if (negativeVoteRatio <= 0.5) performance = "average";
        else if (negativeVoteRatio <= 0.75) performance = "poor";
        else performance = "terrible";

        let recommendation = "monitor";
        if (performance === "excellent") recommendation = "keep";
        else if (performance === "good") recommendation = "keep";
        else if (performance === "average") recommendation = "monitor";
        else if (performance === "poor") recommendation = "remove";
        else recommendation = "blacklist";

        return {
          id: suggestion.id,
          title: suggestion.title,
          artist: suggestion.artist,
          youtubeVideoId: suggestion.youtubeVideoId,
          totalVotes,
          upvotes,
          downvotes,
          score,
          negativeVoteRatio,
          positiveVoteRatio,
          playCount,
          skipCount,
          completionRate,
          averagePlayDuration: Math.floor(Math.random() * 180) + 60,
          lastPlayed: new Date(
            Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000
          ),
          suggestedCount: 1,
          performance,
          recommendation,
        };
      });

      return trackAnalytics;
    } catch (error) {
      logger.error("Erro ao buscar analytics de tracks:", error);
      return [];
    }
  }

  async getPlaylistHealth(restaurantId: string): Promise<any> {
    try {
      const trackAnalytics = await this.getTracksAnalytics(restaurantId);

      const totalTracks = trackAnalytics.length;
      const excellentTracks = trackAnalytics.filter(
        (t) => t.performance === "excellent"
      ).length;
      const goodTracks = trackAnalytics.filter(
        (t) => t.performance === "good"
      ).length;
      const averageTracks = trackAnalytics.filter(
        (t) => t.performance === "average"
      ).length;
      const poorTracks = trackAnalytics.filter(
        (t) => t.performance === "poor"
      ).length;
      const terribleTracks = trackAnalytics.filter(
        (t) => t.performance === "terrible"
      ).length;

      const overallScore =
        totalTracks > 0
          ? Math.round(
              (excellentTracks * 5 +
                goodTracks * 4 +
                averageTracks * 3 +
                poorTracks * 2 +
                terribleTracks * 1) /
                totalTracks
            )
          : 0;

      let healthRating = "needs_attention";
      if (overallScore >= 4.5) healthRating = "excellent";
      else if (overallScore >= 3.5) healthRating = "good";
      else if (overallScore >= 2.5) healthRating = "needs_attention";
      else healthRating = "critical";

      const recommendations: string[] = [];
      const terriblePercentage =
        totalTracks > 0 ? (terribleTracks / totalTracks) * 100 : 0;
      const poorPercentage =
        totalTracks > 0 ? (poorTracks / totalTracks) * 100 : 0;

      if (terriblePercentage > 20) {
        recommendations.push(
          `${terribleTracks} músicas com performance terrível precisam ser removidas`
        );
      }
      if (poorPercentage > 30) {
        recommendations.push(
          `${poorTracks} músicas com performance ruim precisam de atenção`
        );
      }
      if (excellentTracks < totalTracks * 0.3) {
        recommendations.push(
          "Adicione mais músicas populares para melhorar o engajamento"
        );
      }
      if (totalTracks < 20) {
        recommendations.push(
          "Playlist pequena - considere adicionar mais variedade musical"
        );
      }

      return {
        totalTracks,
        excellentTracks,
        goodTracks,
        averageTracks,
        poorTracks,
        terribleTracks,
        overallScore,
        healthRating,
        recommendations,
      };
    } catch (error) {
      logger.error("Erro ao calcular saúde da playlist:", error);
      return {
        totalTracks: 0,
        excellentTracks: 0,
        goodTracks: 0,
        averageTracks: 0,
        poorTracks: 0,
        terribleTracks: 0,
        overallScore: 0,
        healthRating: "critical",
        recommendations: [],
      };
    }
  }

  async getWorstRatedTracks(restaurantId: string): Promise<any[]> {
    try {
      const problematicTracks = await this.suggestionRepository
        .createQueryBuilder("suggestion")
        .select([
          "suggestion.id",
          "suggestion.title",
          "suggestion.artist",
          "suggestion.youtubeVideoId",
          "suggestion.upvotes",
          "suggestion.downvotes",
          "(suggestion.upvotes + suggestion.downvotes) as totalVotes",
          "CASE WHEN (suggestion.upvotes + suggestion.downvotes) > 0 THEN suggestion.downvotes * 100.0 / (suggestion.upvotes + suggestion.downvotes) ELSE 0 END as negativeVoteRatio",
          "(suggestion.upvotes - suggestion.downvotes) as score",
        ])
        .where("suggestion.restaurant_id = :restaurantId", { restaurantId })
        .andWhere("(suggestion.upvotes + suggestion.downvotes) >= 3")
        .andWhere(
          "suggestion.downvotes * 100.0 / (suggestion.upvotes + suggestion.downvotes) >= 60"
        )
        .orderBy("negativeVoteRatio", "DESC")
        .limit(10)
        .getRawMany();

      return problematicTracks.map((track) => ({
        id: track.suggestion_id,
        title: track.suggestion_title,
        artist: track.suggestion_artist,
        youtubeVideoId: track.suggestion_youtubeVideoId,
        totalVotes: parseInt(track.totalVotes) || 0,
        upvotes: track.suggestion_upvotes,
        downvotes: track.suggestion_downvotes,
        score: parseInt(track.score) || 0,
        negativeVoteRatio: parseFloat(track.negativeVoteRatio) / 100,
        playCount: 1,
        skipCount: 0,
        completionRate: 0.5,
        performance: this.getPerformanceLevel(
          parseFloat(track.negativeVoteRatio)
        ),
        recommendation: this.getRecommendation(
          parseFloat(track.negativeVoteRatio)
        ),
      }));
    } catch (error) {
      logger.error("Erro ao buscar músicas problemáticas:", error);
      return [];
    }
  }

  async getTracksToRemove(restaurantId: string): Promise<any[]> {
    try {
      const tracksToRemove = await this.suggestionRepository
        .createQueryBuilder("suggestion")
        .select([
          "suggestion.id",
          "suggestion.title",
          "suggestion.artist",
          "suggestion.youtubeVideoId",
        ])
        .where("suggestion.restaurant_id = :restaurantId", { restaurantId })
        .andWhere("(suggestion.upvotes + suggestion.downvotes) >= 5")
        .andWhere(
          "suggestion.downvotes * 100.0 / (suggestion.upvotes + suggestion.downvotes) >= 80"
        )
        .getRawMany();

      return tracksToRemove.map((track) => ({
        id: track.suggestion_id,
        title: track.suggestion_title,
        artist: track.suggestion_artist,
        youtubeVideoId: track.suggestion_youtubeVideoId,
      }));
    } catch (error) {
      logger.error("Erro ao buscar músicas para remoção:", error);
      return [];
    }
  }

  private getPerformanceLevel(negativeRatio: number): string {
    if (negativeRatio >= 80) return "terrible";
    if (negativeRatio >= 60) return "poor";
    if (negativeRatio >= 40) return "average";
    if (negativeRatio >= 20) return "good";
    return "excellent";
  }

  private getRecommendation(negativeRatio: number): string {
    if (negativeRatio >= 80) return "blacklist";
    if (negativeRatio >= 60) return "remove";
    if (negativeRatio >= 40) return "monitor";
    return "keep";
  }

  async getDashboardSummary(
    restaurantId: string,
    period: AnalyticsPeriod = AnalyticsPeriod.LAST_30_DAYS
  ): Promise<IDashboardSummary> {
    return this.generateDashboardSummary(restaurantId, period);
  }

  async generateRealTimeMetrics(restaurantId: string): Promise<any> {
    try {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

      const [recentSuggestions, recentVotes, activeSessions, currentPlaying] =
        await Promise.all([
          this.suggestionRepository.count({
            where: {
              restaurant: { id: restaurantId },
              createdAt: MoreThan(oneHourAgo),
            },
          }),
          this.voteRepository.count({
            where: {
              suggestion: {
                restaurant: { id: restaurantId },
              },
              createdAt: MoreThan(oneHourAgo),
            },
          }),
          this.sessionRepository.count({
            where: {
              restaurant: { id: restaurantId },
              lastActivity: MoreThan(oneHourAgo),
            },
          }),
          this.suggestionRepository.findOne({
            where: {
              restaurant: { id: restaurantId },
              status: SuggestionStatus.PLAYING,
            },
            order: { playedAt: "DESC" },
          }),
        ]);

      return {
        timestamp: now.toISOString(),
        recentActivity: {
          suggestions: recentSuggestions,
          votes: recentVotes,
          activeSessions: activeSessions,
        },
        currentPlaying: currentPlaying
          ? {
              id: currentPlaying.id,
              title: currentPlaying.title,
              artist: currentPlaying.artist,
              playedAt: currentPlaying.playedAt,
              duration: currentPlaying.duration,
            }
          : null,
        status: "active",
      };
    } catch (error) {
      logger.error("Erro ao gerar métricas em tempo real:", error);
      return {
        timestamp: new Date().toISOString(),
        recentActivity: {
          suggestions: 0,
          votes: 0,
          activeSessions: 0,
        },
        currentPlaying: null,
        status: "error",
      };
    }
  }

  async clearCache(restaurantId: string): Promise<void> {
    if (redisClient.isReady) {
      try {
        const keys = await redisClient
          .getClient()
          .keys(`analytics:${restaurantId}:*`);
        if (keys.length > 0) {
          await redisClient.getClient().del(keys);
        }
        logger.info(
          `Cache de analytics limpo para restaurante ${restaurantId}: ${keys.length} chaves removidas`
        );
      } catch (error) {
        logger.error("Erro ao limpar cache de analytics:", error);
      }
    }
  }

  private async getWorstRatedTracksV2(restaurantId: string): Promise<any[]> {
    return this.getWorstRatedTracks(restaurantId);
  }

  private async getTracksToRemoveV2(restaurantId: string): Promise<any[]> {
    return this.getTracksToRemove(restaurantId);
  }
}

export const analyticsService = AnalyticsService.getInstance();
export { AnalyticsService };
export default AnalyticsService;
