-- Versão simples idempotente para garantir existência da tabela genres


CREATE TABLE IF NOT EXISTS genres (
	id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
	name VARCHAR(100) UNIQUE NOT NULL,
	display_name VARCHAR(100),
	category VARCHAR(50) DEFAULT 'music',
	created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
	updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_genres_name ON genres(name);

