import { Router } from "express";
import { body, param, query, validationResult } from "../utils/validation";
import {
  businessHoursService,
  BusinessHours,
  SpecialHours,
} from "../services/BusinessHoursService";
import asyncHandler from "../middleware/asyncHandler";
import { optionalAuth } from "../middleware/auth";
import { ValidationError, NotFoundError } from "../utils/errors";

const router = Router();

/**
 * @swagger
 * /api/v1/business-hours/{restaurantId}:
 *   get:
 *     summary: Obter horários de funcionamento
 *     tags: [Business Hours]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Horários de funcionamento
 */
router.get(
  "/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    try {
      const hours = await businessHoursService.getBusinessHours(restaurantId);

      // Formatar para exibição
      const daysOfWeek = [
        { key: "monday", name: "Segunda-feira", short: "Seg" },
        { key: "tuesday", name: "Terça-feira", short: "Ter" },
        { key: "wednesday", name: "Quarta-feira", short: "Qua" },
        { key: "thursday", name: "Quinta-feira", short: "Qui" },
        { key: "friday", name: "Sexta-feira", short: "Sex" },
        { key: "saturday", name: "Sábado", short: "Sáb" },
        { key: "sunday", name: "Domingo", short: "Dom" },
      ];

      const formattedHours = daysOfWeek.map((day) => ({
        day: day.key,
        name: day.name,
        short: day.short,
        ...hours.regular[day.key as keyof BusinessHours],
      }));

      res.json({
        success: true,
        hours: {
          regular: formattedHours,
          special: hours.special,
          timezone: hours.timezone,
          lastUpdated: new Date().toISOString(),
        },
      });
    } catch (error) {
      if (
        error instanceof Error &&
        error.message === "Restaurante não encontrado"
      ) {
        throw new NotFoundError("Restaurante não encontrado");
      }
      throw error;
    }
  })
);

/**
 * @swagger
 * /api/v1/business-hours/{restaurantId}/status:
 *   get:
 *     summary: Obter status atual do restaurante
 *     tags: [Business Hours]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Status atual do restaurante
 */
router.get(
  "/:restaurantId/status",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    try {
      const status = await businessHoursService.getBusinessStatus(restaurantId);

      res.json({
        success: true,
        status,
      });
    } catch (error) {
      if (
        error instanceof Error &&
        error.message === "Restaurante não encontrado"
      ) {
        throw new NotFoundError("Restaurante não encontrado");
      }
      throw error;
    }
  })
);

/**
 * @swagger
 * /api/v1/business-hours/{restaurantId}/schedule:
 *   get:
 *     summary: Obter cronograma dos próximos dias
 *     tags: [Business Hours]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Cronograma dos próximos 7 dias
 */
router.get(
  "/:restaurantId/schedule",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    try {
      const schedule = await businessHoursService.getUpcomingSchedule(
        restaurantId
      );

      res.json({
        success: true,
        schedule,
        generatedAt: new Date().toISOString(),
      });
    } catch (error) {
      if (
        error instanceof Error &&
        error.message === "Restaurante não encontrado"
      ) {
        throw new NotFoundError("Restaurante não encontrado");
      }
      throw error;
    }
  })
);

/**
 * @swagger
 * /api/v1/business-hours/{restaurantId}:
 *   put:
 *     summary: Atualizar horários de funcionamento
 *     tags: [Business Hours]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               businessHours:
 *                 type: object
 *               specialHours:
 *                 type: array
 *               timezone:
 *                 type: string
 *     responses:
 *       200:
 *         description: Horários atualizados
 */
router.put(
  "/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("businessHours")
      .isObject()
      .withMessage("Horários de funcionamento são obrigatórios"),
    body("specialHours")
      .optional()
      .isArray()
      .withMessage("Horários especiais devem ser um array"),
    body("timezone")
      .optional()
      .isString()
      .withMessage("Timezone deve ser uma string"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const { businessHours, specialHours, timezone } = req.body;

    try {
      await businessHoursService.updateBusinessHours(
        restaurantId,
        businessHours,
        specialHours,
        timezone
      );

      const updatedHours = await businessHoursService.getBusinessHours(
        restaurantId
      );

      res.json({
        success: true,
        message: "Horários atualizados com sucesso",
        hours: updatedHours,
      });
    } catch (error) {
      if (error instanceof Error) {
        if (error.message === "Restaurante não encontrado") {
          throw new NotFoundError("Restaurante não encontrado");
        }
        if (
          error.message.includes("obrigatório") ||
          error.message.includes("inválido")
        ) {
          throw new ValidationError(error.message);
        }
      }
      throw error;
    }
  })
);

/**
 * @swagger
 * /api/v1/business-hours/{restaurantId}/special:
 *   post:
 *     summary: Adicionar horário especial
 *     tags: [Business Hours]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - date
 *               - name
 *               - hours
 *             properties:
 *               date:
 *                 type: string
 *                 format: date
 *               name:
 *                 type: string
 *               hours:
 *                 type: object
 *               description:
 *                 type: string
 *               isRecurring:
 *                 type: boolean
 *               recurringType:
 *                 type: string
 *                 enum: [yearly, monthly, weekly]
 *     responses:
 *       201:
 *         description: Horário especial criado
 */
router.post(
  "/:restaurantId/special",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("date")
      .isISO8601()
      .withMessage("Data deve estar no formato YYYY-MM-DD"),
    body("name").notEmpty().withMessage("Nome é obrigatório"),
    body("hours").isObject().withMessage("Horários são obrigatórios"),
    body("description").optional().isString(),
    body("isRecurring").optional().isBoolean(),
    body("recurringType").optional().isIn(["yearly", "monthly", "weekly"]),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;
    const specialHoursData = req.body;

    try {
      const specialId = await businessHoursService.addSpecialHours(
        restaurantId,
        specialHoursData
      );

      res.status(201).json({
        success: true,
        message: "Horário especial adicionado com sucesso",
        specialId,
      });
    } catch (error) {
      if (error instanceof Error) {
        if (error.message === "Restaurante não encontrado") {
          throw new NotFoundError("Restaurante não encontrado");
        }
        if (
          error.message.includes("obrigatório") ||
          error.message.includes("inválido")
        ) {
          throw new ValidationError(error.message);
        }
      }
      throw error;
    }
  })
);

/**
 * @swagger
 * /api/v1/business-hours/{restaurantId}/special/{specialId}:
 *   delete:
 *     summary: Remover horário especial
 *     tags: [Business Hours]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *       - in: path
 *         name: specialId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Horário especial removido
 */
router.delete(
  "/:restaurantId/special/:specialId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    param("specialId")
      .notEmpty()
      .withMessage("ID do horário especial é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId, specialId } = req.params;

    try {
      await businessHoursService.removeSpecialHours(restaurantId, specialId);

      res.json({
        success: true,
        message: "Horário especial removido com sucesso",
      });
    } catch (error) {
      if (
        error instanceof Error &&
        error.message === "Restaurante não encontrado"
      ) {
        throw new NotFoundError("Restaurante não encontrado");
      }
      throw error;
    }
  })
);

/**
 * @swagger
 * /api/v1/business-hours/{restaurantId}/is-open:
 *   get:
 *     summary: Verificar se está aberto agora (endpoint simples)
 *     tags: [Business Hours]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Status de abertura
 */
router.get(
  "/:restaurantId/is-open",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    try {
      const isOpen = await businessHoursService.isOpenNow(restaurantId);

      res.json({
        success: true,
        isOpen,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      if (
        error instanceof Error &&
        error.message === "Restaurante não encontrado"
      ) {
        throw new NotFoundError("Restaurante não encontrado");
      }
      throw error;
    }
  })
);

export default router;
