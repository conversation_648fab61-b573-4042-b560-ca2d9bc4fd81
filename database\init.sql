-- ============================================
-- SCRIPT DE INICIALIZAÇÃO DO BANCO DE DADOS
-- Sistema de Playlist Interativa para Restaurantes
-- ============================================

-- <PERSON><PERSON><PERSON> extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Função para gerar UUID v4 (compatibilidade)
CREATE OR REPLACE FUNCTION gen_random_uuid() RETURNS UUID AS $$
BEGIN
    RETURN uuid_generate_v4();
END;
$$ LANGUAGE plpgsql;

-- Criar tipos ENUM
DO $$ BEGIN
    CREATE TYPE user_role AS ENUM ('super_admin', 'admin', 'moderator', 'staff');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE restaurant_status AS ENUM ('active', 'inactive', 'suspended', 'trial');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE playlist_type AS ENUM ('custom', 'youtube_import', 'auto_generated', 'suggestions');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE playlist_status AS ENUM ('active', 'inactive', 'archived');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE suggestion_status AS ENUM ('pending', 'approved', 'rejected', 'playing', 'played', 'skipped');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE suggestion_source AS ENUM ('client', 'admin', 'auto', 'import');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE vote_type AS ENUM ('up', 'down');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE rule_type AS ENUM ('blacklist_word', 'blacklist_artist', 'blacklist_channel', 'genre_restriction', 'duration_limit', 'content_rating', 'language_filter', 'time_restriction');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE rule_action AS ENUM ('auto_reject', 'flag_for_review', 'require_approval', 'auto_approve');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE play_status AS ENUM ('completed', 'skipped', 'interrupted', 'error');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- ============================================
-- CRIAÇÃO DE TABELAS ESSENCIAIS
-- (TypeORM criará as demais automaticamente)
-- ============================================

-- Função para atualizar timestamp automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updatedAt" = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ============================================
-- TABELAS PRINCIPAIS (criadas manualmente para garantir estrutura)
-- ============================================

-- Tabela de usuários
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role user_role DEFAULT 'staff',
    "isActive" BOOLEAN DEFAULT true,
    "emailVerified" BOOLEAN DEFAULT false,
    "emailVerifiedAt" TIMESTAMP WITHOUT TIME ZONE,
    "lastLoginAt" TIMESTAMP WITHOUT TIME ZONE,
    "loginCount" INTEGER DEFAULT 0,
    "resetPasswordToken" VARCHAR(255),
    "resetPasswordExpires" TIMESTAMP WITHOUT TIME ZONE,
    "twoFactorSecret" VARCHAR(255),
    "twoFactorEnabled" BOOLEAN DEFAULT false,
    preferences JSON DEFAULT '{}',
    metadata JSON DEFAULT '{}',
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- Tabela de restaurantes
CREATE TABLE IF NOT EXISTS restaurants (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    phone VARCHAR(20),
    address TEXT,
    status restaurant_status DEFAULT 'trial',
    settings JSON DEFAULT '{}',
    language VARCHAR(10) DEFAULT 'pt-BR',
    timezone VARCHAR(50) DEFAULT 'America/Sao_Paulo',
    "isActive" BOOLEAN DEFAULT true,
    "subscriptionPlan" VARCHAR(50) DEFAULT 'free',
    "subscriptionExpires" TIMESTAMP WITHOUT TIME ZONE,
    "maxUsers" INTEGER DEFAULT 5,
    "maxPlaylists" INTEGER DEFAULT 3,
    "maxSuggestions" INTEGER DEFAULT 100,
    "storageUsed" BIGINT DEFAULT 0,
    "storageLimit" BIGINT DEFAULT 1073741824, -- 1GB
    "apiUsage" JSON DEFAULT '{}',
    "lastActivity" TIMESTAMP WITHOUT TIME ZONE,
    metadata JSON DEFAULT '{}',
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- Tabela de playlists
CREATE TABLE IF NOT EXISTS playlists (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    restaurant_id VARCHAR(255) NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
    type playlist_type DEFAULT 'custom',
    status playlist_status DEFAULT 'active',
    "isPublic" BOOLEAN DEFAULT false,
    "isDefault" BOOLEAN DEFAULT false,
    "trackCount" INTEGER DEFAULT 0,
    "totalDuration" INTEGER DEFAULT 0,
    "playCount" INTEGER DEFAULT 0,
    "lastPlayedAt" TIMESTAMP WITHOUT TIME ZONE,
    execution_order INTEGER DEFAULT NULL,
    settings JSON DEFAULT '{}',
    metadata JSON DEFAULT '{}',
    "createdBy" UUID REFERENCES users(id) ON DELETE SET NULL,
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- Tabela de sugestões
CREATE TABLE IF NOT EXISTS suggestions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "youtubeVideoId" VARCHAR(50) NOT NULL,
    title VARCHAR(300) NOT NULL,
    artist VARCHAR(200),
    "channelName" VARCHAR(200),
    duration INTEGER DEFAULT 0,
    "thumbnailUrl" VARCHAR(500),
    status suggestion_status DEFAULT 'pending',
    source suggestion_source DEFAULT 'client',
    "voteCount" INTEGER DEFAULT 0,
    upvotes INTEGER DEFAULT 0,
    downvotes INTEGER DEFAULT 0,
    "queuePosition" INTEGER,
    "playedAt" TIMESTAMP WITHOUT TIME ZONE,
    "skipReason" VARCHAR(100),
    restaurant_id VARCHAR(255) NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
    "sessionId" UUID,
    "clientIp" INET,
    "userAgent" TEXT,
    "suggestedBy" VARCHAR(100),
    "moderatedBy" UUID REFERENCES users(id) ON DELETE SET NULL,
    "moderatedAt" TIMESTAMP WITHOUT TIME ZONE,
    "moderationReason" TEXT,
    metadata JSON DEFAULT '{}',
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- Tabela de sessões de clientes
CREATE TABLE IF NOT EXISTS client_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "sessionToken" VARCHAR(255) UNIQUE NOT NULL,
    "ipAddress" INET,
    "userAgent" TEXT,
    "deviceInfo" JSON,
    location JSON,
    "lastActivity" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    "suggestionsCount" INTEGER DEFAULT 0,
    "votesCount" INTEGER DEFAULT 0,
    "pageViews" INTEGER DEFAULT 0,
    "sessionDuration" INTEGER DEFAULT 0,
    preferences JSON DEFAULT '{}',
    "activityLog" JSON DEFAULT '[]',
    "isActive" BOOLEAN DEFAULT true,
    restaurant_id VARCHAR(255) NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
    tablenumber VARCHAR(10),
    clientname VARCHAR(100),
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- ============================================
-- ÍNDICES PARA PERFORMANCE
-- ============================================

-- Índices para usuários
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_active ON users("isActive");

-- Índices para restaurantes
CREATE INDEX IF NOT EXISTS idx_restaurants_status ON restaurants(status);
CREATE INDEX IF NOT EXISTS idx_restaurants_active ON restaurants("isActive");
CREATE INDEX IF NOT EXISTS idx_restaurants_subscription ON restaurants("subscriptionPlan");

-- Índices para playlists
CREATE INDEX IF NOT EXISTS idx_playlists_restaurant ON playlists(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_playlists_type ON playlists(type);
CREATE INDEX IF NOT EXISTS idx_playlists_status ON playlists(status);
CREATE INDEX IF NOT EXISTS idx_playlists_default ON playlists("isDefault");
CREATE INDEX IF NOT EXISTS idx_playlists_execution_order ON playlists(restaurant_id, execution_order);

-- Índices para sugestões
CREATE INDEX IF NOT EXISTS idx_suggestions_restaurant ON suggestions(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_suggestions_youtube ON suggestions("youtubeVideoId");
CREATE INDEX IF NOT EXISTS idx_suggestions_status ON suggestions(status);
CREATE INDEX IF NOT EXISTS idx_suggestions_queue ON suggestions("queuePosition");
CREATE INDEX IF NOT EXISTS idx_suggestions_votes ON suggestions("voteCount");
CREATE INDEX IF NOT EXISTS idx_suggestions_created ON suggestions("createdAt");

-- Índices para sessões de clientes
CREATE INDEX IF NOT EXISTS idx_client_sessions_token ON client_sessions("sessionToken");
CREATE INDEX IF NOT EXISTS idx_client_sessions_restaurant ON client_sessions(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_client_sessions_active ON client_sessions("isActive");
CREATE INDEX IF NOT EXISTS idx_client_sessions_activity ON client_sessions("lastActivity");

-- ============================================
-- TRIGGERS PARA ATUALIZAÇÃO AUTOMÁTICA
-- ============================================

-- Trigger para usuários
CREATE TRIGGER trigger_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Trigger para restaurantes
CREATE TRIGGER trigger_restaurants_updated_at
    BEFORE UPDATE ON restaurants
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Trigger para playlists
CREATE TRIGGER trigger_playlists_updated_at
    BEFORE UPDATE ON playlists
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Trigger para sugestões
CREATE TRIGGER trigger_suggestions_updated_at
    BEFORE UPDATE ON suggestions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Trigger para sessões de clientes
CREATE TRIGGER trigger_client_sessions_updated_at
    BEFORE UPDATE ON client_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ============================================
-- MENSAGEM DE SUCESSO
-- ============================================

SELECT 'Banco de dados inicializado com sucesso! Tabelas principais criadas.' as status;
