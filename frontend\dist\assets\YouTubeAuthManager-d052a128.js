import{V as r,b as m,g as y,j as e}from"./index-3b37f57e.js";import{r as i}from"./vendor-66b0ef43.js";import{R as f,au as k,m as v,a9 as Y,i as P,P as $,T as V}from"./ui-1cb796d3.js";const U=({restaurantId:l,onAuthStatusChange:o})=>{console.log("🎵 YouTubeAuthManager renderizado com restaurantId:",l);const[t,p]=i.useState(null),[c,n]=i.useState(!1),[g,j]=i.useState(!1),[T,b]=i.useState(!1),[u,w]=i.useState(""),[h,N]=i.useState(""),x=i.useCallback(async()=>{if(!l){r.error("Restaurant ID is required");return}console.log("🎵 Verificando status do YouTube para restaurantId:",l),n(!0);try{const a=await fetch(m(`/youtube-auth/${l}/status`),{headers:y()}),s=await a.json();console.log("🎵 Resposta da API YouTube Status:",{status:a.status,data:s}),a.ok&&s.success?(p(s),o==null||o(s.isAuthenticated)):(p({isAuthenticated:!1,capabilities:[],message:s.message||"Não autenticado"}),o==null||o(!1))}catch(a){console.error("Erro ao verificar status:",a),p({isAuthenticated:!1,capabilities:[],message:"Erro ao verificar status"}),o==null||o(!1),r.error("Erro ao verificar status da autenticação")}finally{n(!1)}},[l,o]),E=i.useCallback(async()=>{if(!l){r.error("Restaurant ID is required");return}n(!0);try{const s=await(await fetch(m(`/youtube-auth/${l}/authorize`),{headers:y()})).json();if(s.success&&s.authUrl){window.open(s.authUrl,"youtube-auth","width=600,height=700"),r.success("Janela de autorização aberta! Siga as instruções.");const d=setInterval(async()=>{await x(),t!=null&&t.isAuthenticated&&(clearInterval(d),r.success("Autenticação concluída com sucesso!"))},3e3);setTimeout(()=>clearInterval(d),3e5)}else r.error(s.message||"Erro ao iniciar autorização")}catch(a){console.error("Erro ao iniciar autorização:",a),r.error("Erro ao iniciar processo de autorização")}finally{n(!1)}},[l,t,x]),R=i.useCallback(async()=>{if(!u.trim()){r.error("Título da playlist é obrigatório");return}n(!0);try{const s=await(await fetch(m(`/youtube-auth/${l}/create-playlist`),{method:"POST",headers:y("application/json"),body:JSON.stringify({title:u,description:h})})).json();s.success?(r.success("Playlist criada com sucesso!"),b(!1),w(""),N(""),s.playlistUrl&&r.success(e.jsxs("div",{children:[e.jsx("p",{children:"Playlist criada!"}),e.jsx("a",{href:s.playlistUrl,target:"_blank",rel:"noopener noreferrer",className:"text-blue-500 underline",children:"Ver no YouTube"})]}),{duration:5e3})):r.error(s.message||"Erro ao criar playlist")}catch(a){console.error("Erro ao criar playlist:",a),r.error("Erro ao criar playlist")}finally{n(!1)}},[l,u,h]),C=i.useCallback(async a=>{if(!a){r.error("Playlist ID is required");return}j(!0);try{const d=await(await fetch(m(`/youtube-auth/${l}/playlists/${a}/reorder`),{method:"POST",headers:y()})).json();d.success?d.tracksReordered>0?r.success(`${d.tracksReordered} músicas reordenadas baseado nos votos!`):r.success("Playlist já está na ordem ideal!"):r.error(d.message||"Erro ao reordenar playlist")}catch(s){console.error("Erro ao reordenar playlist:",s),r.error("Erro ao reordenar playlist")}finally{j(!1)}},[l]);return i.useEffect(()=>(console.log("🎵 useEffect YouTubeAuthManager executado para restaurantId:",l),x(),()=>{}),[x]),c&&!t?e.jsxs("div",{className:"flex items-center justify-center p-8",children:[e.jsx(f,{className:"w-6 h-6 animate-spin text-purple-500"}),e.jsx("span",{className:"ml-2",children:"Verificando autenticação..."})]}):e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(k,{className:"w-8 h-8 text-red-500 mr-3"}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"Controle de Playlist YouTube"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Gerencie playlists com controle total baseado em votações"})]})]}),e.jsx("button",{onClick:x,disabled:c,className:"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200","aria-label":"Refresh authentication status",children:e.jsx(f,{className:`w-5 h-5 ${c?"animate-spin":""}`})})]}),t?e.jsxs(v.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:"space-y-6",children:[e.jsx("div",{className:`p-4 rounded-lg border-2 ${t.isAuthenticated?"border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20":"border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20"}`,children:e.jsxs("div",{className:"flex items-center",children:[t.isAuthenticated?e.jsx(Y,{className:"w-6 h-6 text-green-500 mr-3"}):e.jsx(P,{className:"w-6 h-6 text-yellow-500 mr-3"}),e.jsxs("div",{children:[e.jsx("h3",{className:`font-semibold ${t.isAuthenticated?"text-green-800 dark:text-green-200":"text-yellow-800 dark:text-yellow-200"}`,children:t.isAuthenticated?"Autenticado com YouTube":"Não Autenticado"}),e.jsx("p",{className:`text-sm ${t.isAuthenticated?"text-green-600 dark:text-green-300":"text-yellow-600 dark:text-yellow-300"}`,children:t.message})]})]})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:t.capabilities.length>0?t.capabilities.map((a,s)=>e.jsxs("div",{className:"flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[e.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full mr-3"}),e.jsx("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:a})]},s)):e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Nenhuma capacidade disponível"})}),e.jsx("div",{className:"flex flex-wrap gap-4",children:t.isAuthenticated?e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>b(!0),className:"flex items-center px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors",children:[e.jsx($,{className:"w-4 h-4 mr-2"}),"Criar Playlist"]}),e.jsxs("button",{onClick:()=>C("current-playlist-id"),disabled:g,className:"flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50 transition-colors",children:[e.jsx(V,{className:`w-4 h-4 mr-2 ${g?"animate-spin":""}`}),g?"Reordenando...":"Reordenar por Votos"]})]}):e.jsxs("button",{onClick:E,disabled:c,className:"flex items-center px-6 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[e.jsx(k,{className:"w-5 h-5 mr-2"}),c?"Processando...":"Conectar com YouTube"]})})]}):e.jsxs("div",{className:"p-4 rounded-lg border-2 border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(P,{className:"w-6 h-6 text-gray-500 mr-3"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-gray-800 dark:text-gray-200",children:"Carregando Status"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Verificando conexão com YouTube..."})]})]}),e.jsx("div",{className:"mt-4",children:e.jsxs("button",{onClick:x,disabled:c,className:"flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 transition-colors",children:[e.jsx(f,{className:`w-4 h-4 mr-2 ${c?"animate-spin":""}`}),c?"Verificando...":"Tentar Novamente"]})})]}),T&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs(v.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.3},className:"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4",children:[e.jsx("h3",{className:"text-lg font-bold text-gray-900 dark:text-white mb-4",children:"Criar Nova Playlist"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"playlist-title",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Título da Playlist *"}),e.jsx("input",{id:"playlist-title",type:"text",value:u,onChange:a=>w(a.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white",placeholder:"Ex: Playlist Interativa - Restaurante"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"playlist-description",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Descrição"}),e.jsx("textarea",{id:"playlist-description",value:h,onChange:a=>N(a.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white",placeholder:"Playlist controlada pelos clientes através de votações..."})]})]}),e.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[e.jsx("button",{onClick:()=>b(!1),className:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200",children:"Cancelar"}),e.jsx("button",{onClick:R,disabled:c||!u.trim(),className:"px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed",children:c?"Criando...":"Criar Playlist"})]})]})})]})};export{U as Y};
//# sourceMappingURL=YouTubeAuthManager-d052a128.js.map
