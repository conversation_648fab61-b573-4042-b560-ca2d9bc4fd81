import React, { useState, useEffect, useCallback, memo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useParams } from "react-router-dom";
import { useRestaurantContext } from "./RestaurantDashboard";
import { buildApiUrl, API_CONFIG } from "../../config/api";
import CompetitiveAnalytics from "./CompetitiveAnalytics";
import {
  BarChart3,
  Heart,
  Music,
  Users,
  RefreshCw,
  Award,
  Activity,
  CheckCircle,
  XCircle,
  Trophy,
} from "lucide-react";
import { toast } from "react-hot-toast";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

// Enums for Advanced Analytics
enum Performance {
  Excellent = "excellent",
  Good = "good",
  Average = "average",
  Poor = "poor",
  Terrible = "terrible",
}

enum Recommendation {
  Keep = "keep",
  Monitor = "monitor",
  Remove = "remove",
  Blacklist = "blacklist",
}

enum HealthRating {
  Excellent = "excellent",
  Good = "good",
  NeedsAttention = "needs_attention",
  Critical = "critical",
}

enum Period {
  Today = "1d",
  Week = "7d",
  Month = "30d",
  Quarter = "90d",
}

interface AnalyticsSummary {
  totalPlays: number;
  totalSuggestions: number;
  totalVotes: number;
  activeUsers: number;
  averageRating: number;
  growthRate: number;
  peakHour: string;
  topGenre: string;
}

interface AnalyticsMetrics {
  totalSuggestions: number;
  approvedSuggestions: number;
  rejectedSuggestions: number;
  totalVotes: number;
  upvotes: number;
  downvotes: number;
  uniqueSessions: number;
  averageSessionDuration: number;
  engagementRate: number;
  approvalRate: number;
  topGenres: Array<{ genre: string; count: number; percentage: number }>;
  topArtists: Array<{ artist: string; count: number; percentage: number }>;
  hourlyActivity: Array<{
    hour: number;
    suggestions: number;
    votes: number;
    sessions: number;
  }>;
  dailyActivity: Array<{
    date: string;
    suggestions: number;
    votes: number;
    sessions: number;
  }>;
}

interface PopularSong {
  id: string;
  title: string;
  artist: string;
  votes: number;
  plays: number;
  score: number;
}

interface TrackAnalytics {
  id: string;
  title: string;
  artist: string;
  youtubeVideoId: string;
  totalVotes: number;
  upvotes: number;
  downvotes: number;
  score: number;
  negativeVoteRatio: number;
  positiveVoteRatio: number;
  playCount: number;
  skipCount: number;
  completionRate: number;
  averagePlayDuration: number;
  lastPlayed?: string;
  suggestedCount: number;
  performance: Performance;
  recommendation: Recommendation;
}

interface PlaylistHealth {
  totalTracks: number;
  excellentTracks: number;
  goodTracks: number;
  averageTracks: number;
  poorTracks: number;
  terribleTracks: number;
  overallScore: number;
  healthRating: HealthRating;
  recommendations: string[];
}

interface UnifiedAnalyticsProps {
  restaurantId?: string;
}

const UnifiedAnalytics: React.FC<UnifiedAnalyticsProps> = memo(
  ({ restaurantId: propRestaurantId }) => {
    const { restaurantId: contextRestaurantId } = useRestaurantContext();
    const { restaurantId: urlRestaurantId } = useParams<{
      restaurantId: string;
    }>();
    const finalRestaurantId =
      propRestaurantId || contextRestaurantId || urlRestaurantId;

    // Validar que temos um restaurantId
    if (!finalRestaurantId) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-600">Erro</h1>
            <p className="text-gray-600 mt-2">
              ID do restaurante não fornecido
            </p>
          </div>
        </div>
      );
    }

    const [activeTab, setActiveTab] = useState<
      "dashboard" | "advanced" | "competitive"
    >("dashboard");
    const [summary, setSummary] = useState<AnalyticsSummary | null>(null);
    const [metrics, setMetrics] = useState<AnalyticsMetrics | null>(null);
    const [popularSongs, setPopularSongs] = useState<PopularSong[]>([]);
    const [selectedPeriod, setSelectedPeriod] = useState<Period>(Period.Week);
    const [dashboardTab, setDashboardTab] = useState<
      "overview" | "engagement" | "music" | "users"
    >("overview");
    const [trackAnalytics, setTrackAnalytics] = useState<TrackAnalytics[]>([]);
    const [playlistHealth, setPlaylistHealth] = useState<PlaylistHealth | null>(
      null
    );
    const [filter, setFilter] = useState<Performance | "all">("all");
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const periods = [
      { value: Period.Today, label: "Hoje" },
      { value: Period.Week, label: "7 dias" },
      { value: Period.Month, label: "30 dias" },
      { value: Period.Quarter, label: "90 dias" },
    ];

    const mainTabs = [
      { id: "dashboard", label: "Dashboard Geral", icon: BarChart3 },
      { id: "advanced", label: "Analytics Avançado", icon: Activity },
      { id: "competitive", label: "Analytics Competitivos", icon: Trophy },
    ] as const;

    const dashboardTabs: { id: "overview" | "engagement" | "music" | "users"; label: string; icon: any }[] = [
      { id: "overview", label: "Visão Geral", icon: BarChart3 },
      { id: "engagement", label: "Engajamento", icon: Heart },
      { id: "music", label: "Músicas", icon: Music },
      { id: "users", label: "Usuários", icon: Users },
    ];

    const loadDashboardAnalytics = useCallback(async () => {
      if (!finalRestaurantId) {
        toast.error("Restaurant ID is required");
        return;
      }

      setLoading(true);
      setError(null);
      try {
        const [summaryRes, metricsRes, songsRes] = await Promise.all([
          fetch(
            buildApiUrl(
              `/analytics/dashboard/${finalRestaurantId}?period=${selectedPeriod}`
            )
          ),
          fetch(
            buildApiUrl(
              `/analytics/metrics/${finalRestaurantId}?period=${selectedPeriod}`
            )
          ),
          fetch(buildApiUrl(`/analytics/stats/${finalRestaurantId}`)),
        ]);

        if (!summaryRes.ok || !metricsRes.ok || !songsRes.ok) {
          throw new Error("Erro ao carregar dados do dashboard");
        }

        const [summaryData, metricsData, songsData] = await Promise.all([
          summaryRes.json(),
          metricsRes.json(),
          songsRes.json(),
        ]);

        setSummary(summaryData.success ? summaryData.summary : null);
        setMetrics(metricsData.success ? metricsData.metrics : null);
        setPopularSongs(
          songsData.success ? songsData.stats?.topSongs || [] : []
        );
      } catch (error) {
        console.error("Erro ao carregar analytics dashboard:", error);
        setError("Erro ao carregar dados do dashboard");
        toast.error("Erro ao carregar dados do dashboard");
      } finally {
        setLoading(false);
      }
    }, [finalRestaurantId, selectedPeriod]);

    const loadAdvancedAnalytics = useCallback(async () => {
      if (!finalRestaurantId) {
        toast.error("Restaurant ID is required");
        return;
      }

      setLoading(true);
      setError(null);
      try {
        const [tracksRes, healthRes] = await Promise.all([
          fetch(buildApiUrl(`/analytics/tracks/${finalRestaurantId}`)),
          fetch(buildApiUrl(`/analytics/playlist-health/${finalRestaurantId}`)),
        ]);

        if (!tracksRes.ok || !healthRes.ok) {
          throw new Error("Erro ao carregar dados do servidor");
        }

        const [tracksData, healthData] = await Promise.all([
          tracksRes.json(),
          healthRes.json(),
        ]);

        setTrackAnalytics(
          tracksData.success && tracksData.trackAnalytics
            ? Object.values(tracksData.trackAnalytics)
            : []
        );
        setPlaylistHealth(
          healthData.success && healthData.playlistHealth
            ? healthData.playlistHealth
            : null
        );
      } catch (error) {
        console.error("Erro ao carregar advanced analytics:", error);
        setError("Erro ao carregar dados de analytics avançado");
        toast.error("Erro ao carregar analytics avançado");
      } finally {
        setLoading(false);
      }
    }, [finalRestaurantId]);

    useEffect(() => {
      if (activeTab === "dashboard") {
        loadDashboardAnalytics();
      } else {
        loadAdvancedAnalytics();
      }
    }, [activeTab, loadDashboardAnalytics, loadAdvancedAnalytics]);

    const formatDuration = (seconds: number): string => {
      const mins = Math.floor(seconds / 60);
      const secs = seconds % 60;
      return `${mins}:${secs.toString().padStart(2, "0")}`;
    };

    const getPerformanceColor = (performance: Performance): string => {
      switch (performance) {
        case Performance.Excellent:
          return "text-green-600 bg-green-100 dark:bg-green-900/30";
        case Performance.Good:
          return "text-blue-600 bg-blue-100 dark:bg-blue-900/30";
        case Performance.Average:
          return "text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30";
        case Performance.Poor:
          return "text-orange-600 bg-orange-100 dark:bg-orange-900/30";
        case Performance.Terrible:
          return "text-red-600 bg-red-100 dark:bg-red-900/30";
        default:
          return "text-gray-600 bg-gray-100 dark:bg-gray-900/30";
      }
    };

    const getRecommendationColor = (recommendation: Recommendation): string => {
      switch (recommendation) {
        case Recommendation.Keep:
          return "text-green-600 bg-green-100 dark:bg-green-900/30";
        case Recommendation.Monitor:
          return "text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30";
        case Recommendation.Remove:
          return "text-orange-600 bg-orange-100 dark:bg-orange-900/30";
        case Recommendation.Blacklist:
          return "text-red-600 bg-red-100 dark:bg-red-900/30";
        default:
          return "text-gray-600 bg-gray-100 dark:bg-gray-900/30";
      }
    };

    const filteredTracks = trackAnalytics.filter(
      (track) => filter === "all" || track.performance === filter
    );

    if (loading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <RefreshCw className="w-6 h-6 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600 dark:text-gray-300">
            Carregando analytics...
          </span>
        </div>
      );
    }

    return (
      <div className="max-w-7xl mx-auto p-4 sm:p-6 space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
              Analytics Completo
            </h1>
            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
              Análise detalhada do desempenho do restaurante
            </p>
          </div>
          <button
            onClick={() =>
              activeTab === "dashboard"
                ? loadDashboardAnalytics()
                : loadAdvancedAnalytics()
            }
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            aria-label="Atualizar dados"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Atualizar</span>
          </button>
        </div>

        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-4 sm:space-x-8">
            {mainTabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-3 px-2 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? "border-blue-500 text-blue-600 dark:text-blue-400"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                }`}
                aria-current={activeTab === tab.id ? "page" : undefined}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        <AnimatePresence mode="wait">
          {activeTab === "dashboard" && (
            <motion.div
              key="dashboard"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="space-y-6"
            >
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div className="flex flex-wrap gap-2 sm:gap-4">
                  {dashboardTabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setDashboardTab(tab.id)}
                      className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                        dashboardTab === tab.id
                          ? "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400"
                          : "text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800"
                      }`}
                    >
                      <tab.icon className="w-4 h-4" />
                      <span>{tab.label}</span>
                    </button>
                  ))}
                </div>
                <select
                  value={selectedPeriod}
                  onChange={(e) => setSelectedPeriod(e.target.value as Period)}
                  className="px-3 py-2 border border-gray-300 rounded-lg text-sm bg-white dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                >
                  {periods.map((period) => (
                    <option key={period.value} value={period.value}>
                      {period.label}
                    </option>
                  ))}
                </select>
              </div>

              {summary && (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
                  {[
                    {
                      title: "Total de Plays",
                      value: summary.totalPlays,
                      icon: Music,
                      color: "blue",
                    },
                    {
                      title: "Sugestões",
                      value: summary.totalSuggestions,
                      icon: Heart,
                      color: "green",
                    },
                    {
                      title: "Usuários Ativos",
                      value: summary.activeUsers,
                      icon: Users,
                      color: "purple",
                    },
                    {
                      title: "Nota Média",
                      value: summary.averageRating.toFixed(1),
                      icon: Award,
                      color: "yellow",
                    },
                  ].map((stat, index) => (
                    <motion.div
                      key={stat.title}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            {stat.title}
                          </p>
                          <p className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">
                            {stat.value}
                          </p>
                        </div>
                        <div
                          className={`p-3 rounded-full bg-${stat.color}-100 dark:bg-${stat.color}-900/30`}
                        >
                          <stat.icon
                            className={`w-5 h-5 sm:w-6 sm:h-6 text-${stat.color}-600 dark:text-${stat.color}-400`}
                          />
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}

              {dashboardTab === "overview" && metrics && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                  <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      Top Gêneros
                    </h3>
                    <div className="space-y-3">
                      {metrics.topGenres.slice(0, 5).map((genre) => (
                        <div
                          key={genre.genre}
                          className="flex items-center justify-between"
                        >
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {genre.genre}
                          </span>
                          <div className="flex items-center space-x-2">
                            <div className="w-24 sm:w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                              <div
                                className="bg-blue-600 h-2 rounded-full"
                                style={{ width: `${genre.percentage}%` }}
                              />
                            </div>
                            <span className="text-sm font-medium text-gray-900 dark:text-white">
                              {genre.percentage}%
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      Top Artistas
                    </h3>
                    <div className="space-y-3">
                      {metrics.topArtists.slice(0, 5).map((artist) => (
                        <div
                          key={artist.artist}
                          className="flex items-center justify-between"
                        >
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {artist.artist}
                          </span>
                          <div className="flex items-center space-x-2">
                            <div className="w-24 sm:w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                              <div
                                className="bg-green-600 h-2 rounded-full"
                                style={{ width: `${artist.percentage}%` }}
                              />
                            </div>
                            <span className="text-sm font-medium text-gray-900 dark:text-white">
                              {artist.percentage}%
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {dashboardTab === "music" && popularSongs.length > 0 && (
                <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Músicas Populares
                  </h3>
                  <div className="space-y-3">
                    {popularSongs.slice(0, 10).map((song, index) => (
                      <div
                        key={song.id}
                        className="flex items-center space-x-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                      >
                        <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                            {index + 1}
                          </span>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {song.title}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                            {song.artist}
                          </p>
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                          <span>{song.plays} plays</span>
                          <span>{song.votes} votos</span>
                          <span className="font-medium">{song.score}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </motion.div>
          )}

          {activeTab === "advanced" && (
            <motion.div
              key="advanced"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="space-y-6"
            >
              {playlistHealth && (
                <div className="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Saúde da Playlist
                  </h3>
                  <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4 mb-4">
                    {[
                      {
                        label: "Excelentes",
                        value: playlistHealth.excellentTracks,
                        color: "green",
                      },
                      {
                        label: "Boas",
                        value: playlistHealth.goodTracks,
                        color: "blue",
                      },
                      {
                        label: "Médias",
                        value: playlistHealth.averageTracks,
                        color: "yellow",
                      },
                      {
                        label: "Ruins",
                        value: playlistHealth.poorTracks,
                        color: "orange",
                      },
                      {
                        label: "Péssimas",
                        value: playlistHealth.terribleTracks,
                        color: "red",
                      },
                      {
                        label: "Total",
                        value: playlistHealth.totalTracks,
                        color: "gray",
                      },
                    ].map((item) => (
                      <div key={item.label} className="text-center">
                        <div
                          className={`text-xl sm:text-2xl font-bold text-${item.color}-600 dark:text-${item.color}-400`}
                        >
                          {item.value}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {item.label}
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Score Geral:
                    </span>
                    <span className="text-lg font-bold text-gray-900 dark:text-white">
                      {playlistHealth.overallScore}/100
                    </span>
                  </div>
                </div>
              )}

              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <select
                  value={filter}
                  onChange={(e) =>
                    setFilter(e.target.value as Performance | "all")
                  }
                  className="px-3 py-2 border border-gray-300 rounded-lg text-sm bg-white dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                >
                  <option value="all">Todas as performances</option>
                  {Object.values(Performance).map((perf) => (
                    <option key={perf} value={perf}>
                      {perf.charAt(0).toUpperCase() + perf.slice(1)}
                    </option>
                  ))}
                </select>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {filteredTracks.length} músicas encontradas
                </span>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-900">
                      <tr>
                        <th className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Música
                        </th>
                        <th className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Performance
                        </th>
                        <th className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Votos
                        </th>
                        <th className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Plays
                        </th>
                        <th className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Taxa Conclusão
                        </th>
                        <th className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Recomendação
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                      {filteredTracks.map((track) => (
                        <tr
                          key={track.id}
                          className="hover:bg-gray-50 dark:hover:bg-gray-700"
                        >
                          <td className="px-4 sm:px-6 py-4">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {track.title}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {track.artist}
                            </div>
                          </td>
                          <td className="px-4 sm:px-6 py-4">
                            <span
                              className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPerformanceColor(
                                track.performance
                              )}`}
                            >
                              {track.performance.charAt(0).toUpperCase() +
                                track.performance.slice(1)}
                            </span>
                          </td>
                          <td className="px-4 sm:px-6 py-4 text-sm text-gray-900 dark:text-white">
                            <div className="flex items-center space-x-2">
                              <span className="text-green-600">
                                ↑{track.upvotes}
                              </span>
                              <span className="text-red-600">
                                ↓{track.downvotes}
                              </span>
                            </div>
                          </td>
                          <td className="px-4 sm:px-6 py-4 text-sm text-gray-900 dark:text-white">
                            {track.playCount}
                          </td>
                          <td className="px-4 sm:px-6 py-4 text-sm text-gray-900 dark:text-white">
                            {(track.completionRate * 100).toFixed(1)}%
                          </td>
                          <td className="px-4 sm:px-6 py-4">
                            <span
                              className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRecommendationColor(
                                track.recommendation
                              )}`}
                            >
                              {track.recommendation.charAt(0).toUpperCase() +
                                track.recommendation.slice(1)}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                {filteredTracks.length === 0 && (
                  <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                    Nenhuma música encontrada para este filtro
                  </div>
                )}
              </div>
            </motion.div>
          )}

          {activeTab === "competitive" && (
            <motion.div
              key="competitive"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <CompetitiveAnalytics />
            </motion.div>
          )}
        </AnimatePresence>

        {error && (
          <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700 rounded-lg p-4">
            <div className="flex items-center">
              <XCircle className="w-5 h-5 text-red-600 dark:text-red-400 mr-2" />
              <span className="text-red-700 dark:text-red-300">{error}</span>
            </div>
          </div>
        )}
      </div>
    );
  }
);

UnifiedAnalytics.displayName = "UnifiedAnalytics";

export default UnifiedAnalytics;
