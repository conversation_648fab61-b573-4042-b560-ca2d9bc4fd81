-- Utilitário idempotente: inspeção e pequenos ajustes em playlists

-- Lista colunas atuais
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name='playlists' AND table_schema='public'
ORDER BY ordinal_position;

-- Adiciona sinalizadores comuns se ausentes (idempotente)
DO $$
BEGIN
	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='is_public'
	) THEN
		ALTER TABLE playlists ADD COLUMN is_public BOOLEAN NOT NULL DEFAULT true;
	END IF;
	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='is_default'
	) THEN
		ALTER TABLE playlists ADD COLUMN is_default BOOLEAN NOT NULL DEFAULT false;
	END IF;
	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='execution_order'
	) THEN
		ALTER TABLE playlists ADD COLUMN execution_order INTEGER NOT NULL DEFAULT 0;
	END IF;
END $$;

