import{u as Ne,c,b as D,j as e,Y as Ce}from"./index-3b37f57e.js";import{r as o}from"./vendor-66b0ef43.js";import{i as X,m as H,c as _,as as Ee,ax as $e,ay as Se,au as Ae,Z as Pe,az as Ie,n as Me,P as Ve,Y as Te,_ as Re,H as De,aA as Le,k as Be,a1 as Ue,a2 as Fe,h as _e,R as q,A as Oe,X as ze,j as Qe}from"./ui-1cb796d3.js";import"./router-f729e475.js";import"./utils-08f61814.js";const Ye={}.VITE_ENABLE_MOCKS==="true",Xe=()=>{const{restaurantId:b}=Ne(),[g,L]=o.useState(!1),[f,ee]=o.useState(.7),[y,B]=o.useState(!1),[A,w]=o.useState(0),[te,O]=o.useState(!0),[k,ae]=o.useState(!1),[j,se]=o.useState("none"),[E,re]=o.useState(!1),[u,oe]=o.useState(!1),[P,U]=o.useState(!1),[I,M]=o.useState([]),[a,$]=o.useState(null),[C,v]=o.useState(0),[V,ie]=o.useState(""),[z,S]=o.useState(!1),[Q,T]=o.useState(!1),[Y,K]=o.useState(null),[i,le]=o.useState(null),ne=o.useRef(null),x=(a==null?void 0:a.tracks[C])||{youtubeVideoId:"",title:"Nenhuma música selecionada",artist:"Selecione uma playlist",thumbnailUrl:"",duration:0,addedAt:"",position:0},de={height:"0",width:"0",playerVars:{autoplay:g?1:0,controls:0,disablekb:1,enablejsapi:1,iv_load_policy:3,modestbranding:1,rel:0,showinfo:0,fs:0,cc_load_policy:0,playsinline:1,origin:window.location.origin,host:"https://www.youtube.com"}},G=o.useCallback(async t=>{if(!t.trim()){S(!1);return}S(!0);try{const s=I.filter(m=>m.name.toLowerCase().includes(t.toLowerCase())||m.description.toLowerCase().includes(t.toLowerCase())||m.tracks.some(r=>r.title.toLowerCase().includes(t.toLowerCase())||r.artist.toLowerCase().includes(t.toLowerCase())));M(s),s.length>0?c.success(`Encontradas ${s.length} playlist(s) com "${t}"`):c(`Nenhuma playlist encontrada com "${t}"`,{icon:"ℹ️"})}catch(s){console.error("Erro ao filtrar playlists:",s),c.error("Erro ao filtrar playlists")}finally{S(!1)}},[I]),R=o.useCallback(async()=>{if(!b){c.error("ID do restaurante não encontrado");return}T(!0),K(null);try{console.log(`🔄 Carregando playlists para restaurante: ${b}`);const t=D(`/playlists/restaurant/${b}`),s=await fetch(t,{method:"GET",headers:{"Content-Type":"application/json"}});if(!s.ok)throw new Error(`Erro ao carregar playlists: ${s.status}`);const r=(await s.json()).playlists||[];console.log(`✅ Encontradas ${r.length} playlists`),console.log("📊 Dados das playlists:",r),r.forEach((l,n)=>{console.log(`📋 Playlist ${n+1}:`,{id:l.id,name:l.name,isActive:l.isActive,videoCount:l.videoCount})});const p=r.filter(l=>l.isActive?!0:(console.log(`⚠️ Playlist ${l.name} filtrada: não está ativa (isActive=${l.isActive})`),!1));console.log(`🔄 Carregando tracks para ${p.length} playlists ativas...`);const d=[];for(const l of p)try{console.log(`🎵 Carregando tracks da playlist: ${l.name} (${l.id})`);const n=await fetch(D(`/playlists/${l.id}?limit=10&offset=0`),{method:"GET",headers:{"Content-Type":"application/json"}});if(!n.ok){console.log(`⚠️ Erro ao carregar tracks da playlist ${l.name}: ${n.status}`);continue}const h=await n.json();if(!h.tracks||!Array.isArray(h.tracks)||h.tracks.length===0){console.log(`⚠️ Playlist ${l.name} não tem tracks válidos`);continue}console.log(`✅ Playlist ${l.name} carregada com ${h.tracks.length} tracks (primeiros 10)`);const je={id:h.id,name:h.name,description:h.description||"",thumbnail:h.thumbnail||"",tracks:h.tracks.map(N=>({youtubeVideoId:N.youtubeVideoId,title:N.title,artist:N.artist,duration:N.duration,thumbnailUrl:N.thumbnailUrl||"",addedAt:N.addedAt,position:N.position})),totalDuration:h.totalDuration||0,videoCount:h.videoCount||h.tracks.length,isActive:h.isActive};d.push(je)}catch(n){console.error(`❌ Erro ao carregar tracks da playlist ${l.name}:`,n)}M(d),d.length>0?(a||($(d[0]),v(0)),c.success(`Carregadas ${d.length} playlists`)):{}.VITE_ENABLE_MOCKS==="true"?(console.log("⚠️ Nenhuma playlist válida encontrada, carregando dados de exemplo (VITE_ENABLE_MOCKS=true)"),J(),c("Carregadas playlists de exemplo (nenhuma playlist com músicas encontrada)",{icon:"ℹ️",duration:4e3})):(console.warn("⚠️ Nenhuma playlist com músicas encontrada e mocks desativados (VITE_ENABLE_MOCKS=false)"),c("Nenhuma playlist com músicas encontrada",{icon:"⚠️"}))}catch(t){console.error("❌ Erro ao carregar playlists:",t),K(t.message||"Erro ao carregar playlists"),c.error("Erro ao carregar playlists do restaurante")}finally{T(!1)}},[b]);o.useCallback(async t=>{T(!0);try{console.log(`🔄 Carregando tracks da playlist: ${t}`);const s=D(`/playlists/${t}`),m=await fetch(s,{method:"GET",headers:{"Content-Type":"application/json"}});if(!m.ok)throw new Error(`Erro ao carregar playlist: ${m.status}`);const r=await m.json();if(r.tracks&&r.tracks.length>0){const p={id:r.id,name:r.name,description:r.description||"",thumbnail:r.thumbnail||"",tracks:r.tracks.map(d=>({youtubeVideoId:d.youtubeVideoId,title:d.title,artist:d.artist,duration:d.duration,thumbnailUrl:d.thumbnailUrl||"",addedAt:d.addedAt,position:d.position})),totalDuration:r.totalDuration||0,videoCount:r.videoCount||r.tracks.length,isActive:r.isActive};$(p),v(0),c.success(`Playlist "${r.name}" carregada`)}else c.error("Playlist não possui músicas")}catch(s){console.error("❌ Erro ao carregar playlist:",s),c.error("Erro ao carregar playlist")}finally{T(!1)}},[]);const Z=o.useCallback(async(t,s=10,m=20)=>{try{console.log(`🔄 Carregando mais tracks da playlist: ${t} (offset: ${s})`);const r=await fetch(D(`/playlists/${t}?limit=${m}&offset=${s}`),{method:"GET",headers:{"Content-Type":"application/json"}});if(!r.ok)throw new Error(`Erro ao carregar mais tracks: ${r.status}`);const p=await r.json();p.tracks&&p.tracks.length>0&&($(d=>{if(!d||d.id!==t)return d;const l=p.tracks.map(n=>({youtubeVideoId:n.youtubeVideoId,title:n.title,artist:n.artist,duration:n.duration,thumbnailUrl:n.thumbnailUrl||"",addedAt:n.addedAt,position:n.position}));return{...d,tracks:[...d.tracks,...l]}}),M(d=>d.map(l=>l.id===t?{...l,tracks:[...l.tracks,...p.tracks.map(n=>({youtubeVideoId:n.youtubeVideoId,title:n.title,artist:n.artist,duration:n.duration,thumbnailUrl:n.thumbnailUrl||"",addedAt:n.addedAt,position:n.position}))]}:l)),console.log(`✅ Carregadas mais ${p.tracks.length} tracks`),c.success(`Carregadas mais ${p.tracks.length} músicas`))}catch(r){console.error("❌ Erro ao carregar mais tracks:",r),c.error("Erro ao carregar mais músicas")}},[]);o.useEffect(()=>{console.log(`🎵 MusicPlayer: restaurantId = ${b}`),b?(console.log(`🎵 MusicPlayer: Iniciando carregamento de playlists para ${b}`),R()):Ye?(console.log("🎵 MusicPlayer: restaurantId não disponível, carregando mocks"),J()):console.log("🎵 MusicPlayer: restaurantId não disponível, mocks desabilitados")},[b,R]);const J=o.useCallback(()=>{const t=[{id:"example-playlist-1",name:"Playlist Demo - Ambiente",description:"Playlist de exemplo para demonstração",thumbnail:"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='300'%3E%3Crect width='100%25' height='100%25' fill='%238B5CF6'/%3E%3Ctext x='50%25' y='50%25' dominant-baseline='middle' text-anchor='middle' fill='white' font-size='48'%3E♪%3C/text%3E%3C/svg%3E",tracks:[{youtubeVideoId:"dQw4w9WgXcQ",title:"Never Gonna Give You Up",artist:"Rick Astley",duration:213,thumbnailUrl:"https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg",addedAt:new Date().toISOString(),position:1},{youtubeVideoId:"9bZkp7q19f0",title:"Gangnam Style",artist:"PSY",duration:253,thumbnailUrl:"https://img.youtube.com/vi/9bZkp7q19f0/mqdefault.jpg",addedAt:new Date().toISOString(),position:2},{youtubeVideoId:"kJQP7kiw5Fk",title:"Despacito",artist:"Luis Fonsi ft. Daddy Yankee",duration:281,thumbnailUrl:"https://img.youtube.com/vi/kJQP7kiw5Fk/mqdefault.jpg",addedAt:new Date().toISOString(),position:3}],totalDuration:747,videoCount:3,isActive:!0},{id:"example-playlist-2",name:"Playlist Demo - Animada",description:"Músicas mais animadas para o ambiente",thumbnail:"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='300'%3E%3Crect width='100%25' height='100%25' fill='%23E74C3C'/%3E%3Ctext x='50%25' y='50%25' dominant-baseline='middle' text-anchor='middle' fill='white' font-size='48'%3E♫%3C/text%3E%3C/svg%3E",tracks:[{youtubeVideoId:"fJ9rUzIMcZQ",title:"Bohemian Rhapsody",artist:"Queen",duration:355,thumbnailUrl:"https://img.youtube.com/vi/fJ9rUzIMcZQ/mqdefault.jpg",addedAt:new Date().toISOString(),position:1},{youtubeVideoId:"hTWKbfoikeg",title:"Smells Like Teen Spirit",artist:"Nirvana",duration:301,thumbnailUrl:"https://img.youtube.com/vi/hTWKbfoikeg/mqdefault.jpg",addedAt:new Date().toISOString(),position:2}],totalDuration:656,videoCount:2,isActive:!0}];M(t),a||($(t[0]),v(0)),console.log("✅ Playlists de exemplo carregadas:",t.length)},[a]),ce=t=>{le(t.target),t.target.setVolume(f*100)},ue=t=>{switch(t.data){case 0:j==="one"?t.target.playVideo():j==="all"||C<((a==null?void 0:a.tracks.length)||0)-1?W():(L(!1),w(0));break;case 1:L(!0);break;case 2:L(!1);break}};o.useEffect(()=>{const t=s=>{s.origin==="https://www.youtube.com"||s.origin};return window.addEventListener("message",t),()=>window.removeEventListener("message",t)},[]),o.useEffect(()=>{if(g&&i){const t=setInterval(()=>{const s=i.getCurrentTime();w(s)},1e3);return()=>clearInterval(t)}},[g,i]);const me=()=>{i&&(g?i.pauseVideo():i.playVideo(),c.success(g?"Pausado":"Reproduzindo"))},he=()=>{i&&(y?(i.unMute(),i.setVolume(f*100)):i.mute(),B(!y))},ge=t=>{const s=parseFloat(t.target.value);ee(s),i&&(i.setVolume(s*100),s===0?(i.mute(),B(!0)):y&&(i.unMute(),B(!1)))},F=t=>{const s=Math.floor(t/60),m=Math.floor(t%60);return`${s}:${m.toString().padStart(2,"0")}`},xe=t=>{if(i&&x.duration>0){const s=parseFloat(t.target.value)*x.duration;i.seekTo(s),w(s)}},pe=()=>{ae(!k),c.success(k?"Aleatório desativado":"Aleatório ativado")},ye=()=>{const t=["none","one","all"],s=t.indexOf(j),m=t[(s+1)%t.length];se(m);const r={none:"Repetição desativada",one:"Repetir uma música",all:"Repetir todas"};c.success(r[m])},be=()=>{re(!E),c.success(E?"Removido dos favoritos":"Adicionado aos favoritos")},fe=()=>{oe(!u)},ve=()=>{if(a){if(k){const t=Math.floor(Math.random()*a.tracks.length);v(t)}else v(t=>t>0?t-1:a.tracks.length-1);w(0),g&&i&&setTimeout(()=>i.playVideo(),100),c.success("Música anterior")}},W=()=>{if(!a)return;const t=k?Math.floor(Math.random()*a.tracks.length):C<a.tracks.length-1?C+1:0;!k&&t>=a.tracks.length-5&&a.tracks.length<(a.videoCount||50)&&(console.log("🔄 Próximo do fim da playlist, carregando mais tracks..."),Z(a.id,a.tracks.length)),v(t),w(0),g&&i&&setTimeout(()=>i.playVideo(),100),c.success("Próxima música")},we=t=>{$(t),v(0),w(0),U(!1),i&&g&&setTimeout(()=>i.playVideo(),100),c.success(`Playlist "${t.name}" selecionada`)},ke=t=>{v(t),w(0),i&&g&&setTimeout(()=>i.playVideo(),100),c.success(`Música "${a==null?void 0:a.tracks[t].title}" selecionada`)};return te?e.jsx(H.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:`space-y-6 ${u?"fixed inset-0 z-50 bg-black bg-opacity-95 p-8":""}`,children:e.jsxs("div",{className:`bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden ${u?"h-full flex flex-col":""}`,children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700",children:[e.jsxs("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-2",children:[e.jsx(_,{className:"w-6 h-6 text-purple-600"}),"Player de Música"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("button",{onClick:()=>U(!P),className:"p-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors","aria-label":"Mostrar playlist",title:"Mostrar playlist",children:e.jsx(Ee,{className:"w-5 h-5"})}),e.jsx("button",{onClick:fe,className:"p-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors","aria-label":u?"Sair da tela cheia":"Tela cheia",title:u?"Sair da tela cheia":"Tela cheia",children:u?e.jsx($e,{className:"w-5 h-5"}):e.jsx(Se,{className:"w-5 h-5"})}),e.jsx("button",{onClick:()=>O(!1),className:"text-red-600 hover:text-red-700 text-sm px-3 py-1 rounded-md border border-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors","aria-label":"Simular erro",children:"Simular Erro"})]})]}),e.jsxs("div",{className:`flex ${u?"flex-1":""}`,children:[e.jsx("div",{className:`${P?"w-2/3":"w-full"} ${u?"flex flex-col":""}`,children:e.jsxs("div",{className:`${u?"flex-1 flex flex-col":"p-6"}`,children:[e.jsx("div",{className:"hidden",children:e.jsx(Ce,{videoId:x.youtubeVideoId,opts:de,onReady:ce,onStateChange:ue,ref:ne})}),e.jsx("div",{className:`bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700 rounded-xl p-8 text-white mb-6 ${u?"flex-1 flex items-center justify-center":""}`,children:e.jsxs("div",{className:`${u?"text-center max-w-2xl":"flex items-center space-x-6"}`,children:[e.jsx("div",{className:`${u?"w-80 h-80 mx-auto mb-8":"w-24 h-24"} bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm border border-white/30 shadow-2xl`,children:e.jsx(_,{className:`${u?"w-32 h-32":"w-12 h-12"} text-white/80`})}),e.jsxs("div",{className:`flex-1 ${u?"text-center":""}`,children:[e.jsx("h3",{className:`${u?"text-4xl mb-2":"text-xl"} font-bold`,children:x.title}),e.jsx("p",{className:`${u?"text-2xl text-purple-200 mb-1":"text-lg text-blue-200"}`,children:x.artist}),e.jsx("p",{className:`${u?"text-lg text-purple-300":"text-sm text-blue-300"}`,children:(a==null?void 0:a.name)||"Nenhuma playlist selecionada"}),e.jsxs("div",{className:`flex items-center justify-center gap-1 mt-2 ${u?"mt-4":""}`,children:[e.jsx(Ae,{className:"w-4 h-4 text-red-400"}),e.jsx("span",{className:"text-xs text-white/70",children:"YouTube"})]})]})]})}),e.jsxs("div",{className:"mb-6 px-2",children:[e.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-2",children:[e.jsx("span",{children:F(A)}),e.jsx("span",{children:F(x.duration)})]}),e.jsx("input",{type:"range",min:"0",max:"1",step:"0.001",value:x.duration>0?A/x.duration:0,onChange:xe,className:"w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full appearance-none cursor-pointer",style:{background:`linear-gradient(to right, #8B5CF6 0%, #3B82F6 ${A/Math.max(1,x.duration)*100}%, #E5E7EB ${A/Math.max(1,x.duration)*100}%, #E5E7EB 100%)`}})]}),e.jsxs("div",{className:"flex items-center justify-center space-x-6 mb-6",children:[e.jsx("button",{onClick:pe,className:`p-3 rounded-full transition-all ${k?"bg-purple-600 text-white shadow-lg":"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600"}`,title:k?"Desativar modo aleatório":"Ativar modo aleatório",children:e.jsx(Pe,{className:"w-5 h-5"})}),e.jsx("button",{onClick:ve,className:"p-3 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-full transition-colors",title:"Música anterior",children:e.jsx(Ie,{className:"w-6 h-6"})}),e.jsx("button",{onClick:me,className:"bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white rounded-full p-4 transition-all transform hover:scale-105 shadow-lg",title:g?"Pausar":"Reproduzir",children:g?e.jsx(Me,{className:"w-8 h-8"}):e.jsx(Ve,{className:"w-8 h-8 ml-1"})}),e.jsx("button",{onClick:W,className:"p-3 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-full transition-colors",title:"Próxima música",children:e.jsx(Te,{className:"w-6 h-6"})}),e.jsxs("button",{onClick:ye,className:`p-3 rounded-full transition-all relative ${j!=="none"?"bg-purple-600 text-white shadow-lg":"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600"}`,title:j==="none"?"Ativar repetição":j==="one"?"Repetir todas as músicas":"Desativar repetição",children:[e.jsx(Re,{className:"w-5 h-5"}),j==="one"&&e.jsx("span",{className:"absolute -top-1 -right-1 w-4 h-4 bg-purple-400 rounded-full text-xs flex items-center justify-center text-white",children:"1"})]})]}),e.jsxs("div",{className:"flex items-center justify-between px-2 mb-6",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("button",{onClick:be,className:`p-2 rounded-full transition-all ${E?"text-red-500 bg-red-50 dark:bg-red-900/20":"text-gray-400 hover:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700"}`,title:E?"Remover dos favoritos":"Adicionar aos favoritos",children:e.jsx(De,{className:`w-5 h-5 ${E?"fill-current":""}`})}),e.jsx("button",{className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors",title:"Compartilhar",children:e.jsx(Le,{className:"w-5 h-5"})}),e.jsx("a",{href:`https://www.youtube.com/watch?v=${x.youtubeVideoId}`,target:"_blank",rel:"noopener noreferrer",className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors",title:"Abrir no YouTube",children:e.jsx(Be,{className:"w-5 h-5"})})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("button",{onClick:he,className:"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors",title:y?"Ativar som":"Desativar som",children:y||f===0?e.jsx(Ue,{className:"w-5 h-5"}):e.jsx(Fe,{className:"w-5 h-5"})}),e.jsx("input",{type:"range",min:"0",max:"1",step:"0.01",value:y?0:f,onChange:ge,className:"w-24 h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer",style:{background:`linear-gradient(to right, #8B5CF6 0%, #3B82F6 ${(y?0:f)*100}%, #E5E7EB ${(y?0:f)*100}%, #E5E7EB 100%)`},title:`Volume: ${Math.round((y?0:f)*100)}%`}),e.jsxs("span",{className:"text-sm text-gray-600 dark:text-gray-400 w-8 text-right",children:[Math.round((y?0:f)*100),"%"]})]})]}),e.jsxs("div",{className:`mt-6 p-2 ${P&&!u?"hidden md:block":""}`,children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Buscar playlist...",value:V,onChange:t=>ie(t.target.value),onKeyDown:t=>{t.key==="Enter"&&(S(!0),G(V))},className:"w-full px-4 py-2 pl-10 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"}),e.jsx(_e,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("button",{onClick:()=>{S(!0),G(V)},disabled:z||!V.trim(),className:"absolute right-2 top-1/2 transform -translate-y-1/2 px-2 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:z?e.jsx(q,{className:"w-4 h-4 animate-spin"}):"Buscar"})]}),Y&&e.jsxs("div",{className:"mt-2 text-sm text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 p-2 rounded",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(X,{className:"w-4 h-4"}),e.jsx("span",{children:Y})]}),e.jsx("button",{onClick:R,className:"mt-1 text-blue-600 dark:text-blue-400 hover:underline",children:"Tentar novamente"})]})]})]})}),e.jsx(Oe,{children:P&&e.jsxs(H.div,{initial:{width:0,opacity:0},animate:{width:"33.333333%",opacity:1},exit:{width:0,opacity:0},transition:{duration:.3},className:"border-l border-gray-200 dark:border-gray-700 h-full overflow-hidden",children:[e.jsxs("div",{className:"p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between",children:[e.jsx("h3",{className:"font-medium text-gray-900 dark:text-white",children:"Playlists"}),e.jsx("button",{onClick:()=>U(!1),className:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200",children:e.jsx(ze,{className:"w-5 h-5"})})]}),e.jsxs("div",{className:"h-full overflow-y-auto",style:{maxHeight:"calc(100vh - 250px)"},children:[e.jsxs("div",{className:"space-y-2 p-2",children:[I.map(t=>e.jsxs("div",{onClick:()=>we(t),className:`flex items-center p-2 rounded-lg cursor-pointer ${(a==null?void 0:a.id)===t.id?"bg-purple-100 dark:bg-purple-900/30":"hover:bg-gray-100 dark:hover:bg-gray-700"}`,children:[e.jsx("div",{className:"w-12 h-12 rounded overflow-hidden mr-3 flex-shrink-0",children:e.jsx("img",{src:t.thumbnail,alt:t.name,className:"w-full h-full object-cover",onError:s=>{s.currentTarget.src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='300'%3E%3Crect width='100%25' height='100%25' fill='%238B5CF6'/%3E%3Ctext x='50%25' y='50%25' dominant-baseline='middle' text-anchor='middle' fill='white' font-size='48'%3E♪%3C/text%3E%3C/svg%3E"}})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:`text-sm font-medium truncate ${(a==null?void 0:a.id)===t.id?"text-purple-800 dark:text-purple-300":"text-gray-900 dark:text-white"}`,children:t.name}),e.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-400 truncate",children:[t.tracks.length," músicas"]})]})]},t.id)),I.length===0&&!Q&&e.jsxs("div",{className:"text-center py-8 text-gray-500 dark:text-gray-400",children:[e.jsx(_,{className:"w-12 h-12 mx-auto mb-2 opacity-50"}),e.jsx("p",{children:"Nenhuma playlist encontrada"}),e.jsx("button",{onClick:R,className:"mt-2 text-blue-600 dark:text-blue-400 hover:underline text-sm",children:"Recarregar playlists"})]}),Q&&e.jsxs("div",{className:"text-center py-8",children:[e.jsx(q,{className:"w-8 h-8 animate-spin mx-auto mb-2 text-purple-600"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Buscando playlists..."})]})]}),a&&e.jsxs("div",{className:"mt-4 p-2",children:[e.jsxs("h3",{className:"font-medium text-gray-900 dark:text-white mb-2 px-2",children:["Músicas de ",a.name]}),e.jsxs("div",{className:"space-y-1",children:[a.tracks.map((t,s)=>e.jsxs("div",{onClick:()=>ke(s),className:`flex items-center p-2 rounded-lg cursor-pointer ${C===s?"bg-blue-100 dark:bg-blue-900/30":"hover:bg-gray-100 dark:hover:bg-gray-700"}`,children:[e.jsx("div",{className:"mr-3 text-xs text-gray-500 dark:text-gray-400 w-5 text-center",children:s+1}),e.jsx("div",{className:"w-8 h-8 rounded bg-gray-600 mr-2 flex-shrink-0 flex items-center justify-center",children:e.jsx("span",{className:"text-xs",children:"🎵"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:`text-xs font-medium truncate ${C===s?"text-blue-800 dark:text-blue-300":"text-gray-900 dark:text-white"}`,children:t.title}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 truncate",children:t.artist})]}),e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:F(t.duration)})]},t.youtubeVideoId)),a.tracks.length<(a.videoCount||50)&&e.jsx("div",{className:"text-center py-4",children:e.jsxs("button",{onClick:()=>Z(a.id,a.tracks.length),className:"px-4 py-2 text-sm bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2 mx-auto",children:[e.jsx(Qe,{className:"w-4 h-4"}),"Carregar mais músicas (",a.videoCount-a.tracks.length," ","restantes)"]})})]})]})]})]})})]})]})}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(X,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"Player não disponível"}),e.jsx("button",{onClick:()=>O(!0),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:"Recarregar Player"})]})};export{Xe as default};
//# sourceMappingURL=MusicPlayer-bc235a29.js.map
