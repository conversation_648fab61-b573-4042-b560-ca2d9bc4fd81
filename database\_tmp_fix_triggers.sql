-- Função e trigger para manter contadores de votos em suggestions
-- Idempotente

DO $$ BEGIN
  CREATE OR REPLACE FUNCTION update_suggestion_vote_counts()
  RETURNS TRIGGER AS $$
  DECLARE
    tgt UUID;
  BEGIN
    -- Determina o suggestion alvo
    IF (TG_OP = 'INSERT') THEN
      tgt := NEW.suggestion_id;
    ELSIF (TG_OP = 'UPDATE') THEN
      -- Se mudou de suggestion, atualiza os dois lados
      IF NEW.suggestion_id IS DISTINCT FROM OLD.suggestion_id THEN
        IF OLD.suggestion_id IS NOT NULL THEN
          UPDATE suggestions s SET
            upvotes    = (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = OLD.suggestion_id AND v.vote_type = 'up'),
            downvotes  = (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = OLD.suggestion_id AND v.vote_type = 'down'),
            vote_count = (
              (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = OLD.suggestion_id AND v.vote_type = 'up') -
              (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = OLD.suggestion_id AND v.vote_type = 'down')
            ),
            updated_at = NOW()
          WHERE s.id = OLD.suggestion_id;
        END IF;
        tgt := NEW.suggestion_id;
      ELSE
        tgt := NEW.suggestion_id;
      END IF;
    ELSIF (TG_OP = 'DELETE') THEN
      tgt := OLD.suggestion_id;
    END IF;

    IF tgt IS NOT NULL THEN
      UPDATE suggestions s SET
        upvotes    = (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = tgt AND v.vote_type = 'up'),
        downvotes  = (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = tgt AND v.vote_type = 'down'),
        vote_count = (
          (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = tgt AND v.vote_type = 'up') -
          (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = tgt AND v.vote_type = 'down')
        ),
        updated_at = NOW()
      WHERE s.id = tgt;
    END IF;

    RETURN NULL;
  END;
  $$ LANGUAGE plpgsql;
END $$;

-- Drop/recreate trigger de forma segura
DO $$ BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.triggers
    WHERE event_object_table = 'votes' AND trigger_name = 'trg_update_vote_counts'
  ) THEN
    DROP TRIGGER trg_update_vote_counts ON votes;
  END IF;
END $$;

CREATE TRIGGER trg_update_vote_counts
AFTER INSERT OR UPDATE OR DELETE ON votes
FOR EACH ROW EXECUTE FUNCTION update_suggestion_vote_counts();

-- Backfill inicial (idempotente)
UPDATE suggestions s SET
  upvotes    = COALESCE(uv.ups, 0),
  downvotes  = COALESCE(dv.downs, 0),
  vote_count = COALESCE(uv.ups, 0) - COALESCE(dv.downs, 0),
  updated_at = NOW()
FROM (
  SELECT suggestion_id, COUNT(*) AS ups
  FROM votes WHERE vote_type = 'up' AND suggestion_id IS NOT NULL
  GROUP BY suggestion_id
) uv
FULL JOIN (
  SELECT suggestion_id, COUNT(*) AS downs
  FROM votes WHERE vote_type = 'down' AND suggestion_id IS NOT NULL
  GROUP BY suggestion_id
) dv ON dv.suggestion_id = uv.suggestion_id
WHERE s.id = COALESCE(uv.suggestion_id, dv.suggestion_id);
