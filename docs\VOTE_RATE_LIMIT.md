# VOTE RATE LIMIT

Bloqueio de votos normais consecutivos por sessão/IP para evitar spam de votos.

## Comportamento
- Aplica-se apenas a votos normais (gratuitos): `POST /api/v1/collaborative-playlist/{restaurantId}/vote`.
- Identidade avaliada por prioridade: `clientSessionId` > IP do cliente > "anonymous".
- Janela deslizante com contador em Redis: chave `rate:vote:normal:{restaurantId}:{identity}`.
- Quando o limite é excedido, a API responde 429 (Too Many Requests) com `retryAfter` em segundos.

## Variáveis de ambiente
- `NORMAL_VOTE_RATE_LIMIT` (default: `1`) — quantos votos normais são permitidos por janela.
- `NORMAL_VOTE_RATE_WINDOW_SECONDS` (default: `60`) — duração da janela em segundos.

## Integração
- Utilitário: `backend/src/utils/voteRateLimit.ts` (função `checkNormalVoteRateLimit`).
- Serviço: `backend/src/services/VoteRateLimiter.ts` (classe `VoteRateLimiterService`).
- Rota: `backend/src/routes/collaborativePlaylist.ts` usa o util para bloquear antes de processar o voto.

## Resposta de erro (exemplo)
```json
{
  "success": false,
  "message": "Muitos votos em sequência. Tente novamente em 57s",
  "retryAfter": 57
}
```

## Observações
- Supervotos (pagos) não são afetados por este limitador.
- Para ambientes sem Redis, `checkRateLimit` é permissivo em caso de erro (fail-open), registrado em log.
