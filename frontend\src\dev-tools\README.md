# 🛠️ DEV TOOLS

Esta pasta contém ferramentas e componentes utilizados apenas durante o desenvolvimento.

## 📁 **CONTEÚDO**

### 1. **NotificationTester.tsx**
- **Propósito**: Testar sistema de notificações em tempo real
- **Uso**: Desenvolvimento e debug de WebSocket
- **Status**: Ativo durante desenvolvimento

## 🚨 **IMPORTANTE**

- ❌ **NÃO INCLUIR** estes componentes no build de produção
- ✅ **USAR** apenas durante desenvolvimento
- 🔄 **REMOVER** imports destes componentes antes do deploy

## 📋 **GUIDELINES**

1. Todos os componentes aqui devem ter prefixo `Dev` ou `Test`
2. Não devem ser importados em componentes de produção
3. Podem ser removidos do build final via webpack/vite config

## 🔧 **CONFIGURAÇÃO DE BUILD**

Para excluir esta pasta do build de produção, adicionar no vite.config.ts:

```typescript
build: {
  rollupOptions: {
    external: ['./src/dev-tools/**']
  }
}
```
