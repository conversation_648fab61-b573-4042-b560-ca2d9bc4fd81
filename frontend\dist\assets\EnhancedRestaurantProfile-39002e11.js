import{d as F,c as x,j as e}from"./index-3b37f57e.js";import{r as n}from"./vendor-66b0ef43.js";import{K as G,aD as K,X as L,u as q,G as I,ad as Q,m as B,a8 as _,j as J,av as V,q as Y,i as z,R as M,B as U,A as W,M as X,aF as Z,aG as ee,x as se,d as ae,o as re,aH as te,l as oe}from"./ui-1cb796d3.js";import"./router-f729e475.js";import"./utils-08f61814.js";const ne=n.memo(({restaurantId:h,initialHours:o,onSave:f})=>{const[c,j]=n.useState({monday:{open:"11:00",close:"23:00",isOpen:!0},tuesday:{open:"11:00",close:"23:00",isOpen:!0},wednesday:{open:"11:00",close:"23:00",isOpen:!0},thursday:{open:"11:00",close:"23:00",isOpen:!0},friday:{open:"11:00",close:"24:00",isOpen:!0},saturday:{open:"11:00",close:"24:00",isOpen:!0},sunday:{open:"11:00",close:"22:00",isOpen:!0}}),[b,k]=n.useState([]),[p,P]=n.useState("America/Sao_Paulo"),[g,v]=n.useState(!1),[N,y]=n.useState(!1),[$,O]=n.useState({isOpen:!1,message:"Carregando..."}),H=[{key:"monday",label:"Segunda-feira",short:"Seg"},{key:"tuesday",label:"Terça-feira",short:"Ter"},{key:"wednesday",label:"Quarta-feira",short:"Qua"},{key:"thursday",label:"Quinta-feira",short:"Qui"},{key:"friday",label:"Sexta-feira",short:"Sex"},{key:"saturday",label:"Sábado",short:"Sáb"},{key:"sunday",label:"Domingo",short:"Dom"}],D=[{value:"America/Sao_Paulo",label:"São Paulo (GMT-3)"},{value:"America/New_York",label:"Nova York (GMT-5)"},{value:"Europe/London",label:"Londres (GMT+0)"},{value:"Asia/Tokyo",label:"Tóquio (GMT+9)"},{value:"Australia/Sydney",label:"Sydney (GMT+10)"}],A=n.useCallback(async()=>{var s,r,l;try{y(!0);const[t,i]=await Promise.all([F.client.get(`/business-hours/${h}`),F.client.get(`/business-hours/${h}/status`)]);if((s=t.data.hours)!=null&&s.regular){const m={};t.data.hours.regular.forEach(w=>{m[w.day]={open:w.open,close:w.close,isOpen:w.isOpen}}),j(m)}(r=t.data.hours)!=null&&r.special&&k(t.data.hours.special.map(m=>({...m,date:new Date(m.date).toISOString().split("T")[0]}))),(l=t.data.hours)!=null&&l.timezone&&P(t.data.hours.timezone),i.data.status&&O(i.data.status)}catch(t){console.error("Erro ao carregar horários:",t),x.error("Erro ao carregar horários de funcionamento")}finally{y(!1)}},[h]);n.useEffect(()=>{o&&j(o),A()},[o,A]);const E=n.useCallback((s,r,l)=>{j(t=>({...t,[s]:{...t[s],[r]:l}}))},[]);n.useCallback((s,r)=>{var l,t;j(i=>({...i,[r]:{...i[s]}})),x.success(`Horários de ${(l=H.find(i=>i.key===s))==null?void 0:l.label} copiados para ${(t=H.find(i=>i.key===r))==null?void 0:t.label}`)},[]);const S=n.useCallback(s=>{const r=c[s],l={...c};H.forEach(t=>{t.key!==s&&(l[t.key]={...r})}),j(l),x.success("Horários aplicados a todos os dias")},[c]),a=n.useCallback(()=>{j({monday:{open:"11:00",close:"23:00",isOpen:!0},tuesday:{open:"11:00",close:"23:00",isOpen:!0},wednesday:{open:"11:00",close:"23:00",isOpen:!0},thursday:{open:"11:00",close:"23:00",isOpen:!0},friday:{open:"11:00",close:"24:00",isOpen:!0},saturday:{open:"11:00",close:"24:00",isOpen:!0},sunday:{open:"11:00",close:"22:00",isOpen:!0}}),x.success("Horários resetados para padrão")},[]),d=n.useCallback(()=>{const s={id:crypto.randomUUID(),date:new Date().toISOString().split("T")[0],name:"Horário Especial",hours:{open:"11:00",close:"23:00",isOpen:!0},description:""};k(r=>[...r,s]),x.success("Novo horário especial adicionado")},[]),u=n.useCallback((s,r,l)=>{k(t=>t.map(i=>i.id===s?{...i,[r]:l}:i))},[]),C=n.useCallback(s=>{k(r=>r.filter(l=>l.id!==s)),x.success("Horário especial removido")},[]),R=n.useCallback(async()=>{y(!0);try{const s=await F.client.put(`/business-hours/${h}`,{businessHours:c,specialHours:b,timezone:p});x.success("Horários salvos com sucesso!"),v(!1),f&&f(c,b),await A()}catch(s){console.error("Erro ao salvar:",s),x.error("Erro ao salvar horários")}finally{y(!1)}},[c,b,p,h,f,A]),T=n.useCallback(()=>{const s=new Date,r=s.toLocaleDateString("en-US",{weekday:"long"}).toLowerCase(),l=s.toTimeString().slice(0,5),t=b.find(w=>w.date===s.toISOString().split("T")[0]);if(t){if(!t.hours.isOpen)return{isOpen:!1,message:`Fechado - ${t.name}`};const w=l>=t.hours.open&&l<=t.hours.close;return{isOpen:w,message:w?`Aberto até ${t.hours.close} (${t.name})`:`Fechado - Abre às ${t.hours.open} (${t.name})`}}const i=c[r];if(!(i!=null&&i.isOpen))return{isOpen:!1,message:"Fechado hoje"};const m=l>=i.open&&l<=i.close;return{isOpen:m,message:m?`Aberto até ${i.close}`:`Fechado - Abre às ${i.open}`}},[c,b]);return n.useEffect(()=>{O(T())},[c,b,T]),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg",children:e.jsx(G,{className:"w-6 h-6 text-blue-600 dark:text-blue-400"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Horários de Funcionamento"}),e.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[e.jsx("div",{className:`w-2 h-2 rounded-full ${$.isOpen?"bg-green-500":"bg-red-500"}`}),e.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:$.message})]})]})]}),e.jsx("div",{className:"flex items-center space-x-2",children:g?e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>v(!1),onKeyDown:s=>s.key==="Enter"&&v(!1),className:"flex items-center space-x-2 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors","aria-label":"Cancelar edição",children:[e.jsx(L,{className:"w-4 h-4"}),e.jsx("span",{children:"Cancelar"})]}),e.jsxs("button",{onClick:R,disabled:N,className:"flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors","aria-label":"Salvar horários",children:[e.jsx(q,{className:"w-4 h-4"}),e.jsx("span",{children:N?"Salvando...":"Salvar"})]})]}):e.jsxs("button",{onClick:()=>v(!0),onKeyDown:s=>s.key==="Enter"&&v(!0),className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors","aria-label":"Editar horários",children:[e.jsx(K,{className:"w-4 h-4"}),e.jsx("span",{children:"Editar"})]})})]}),g&&e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-3",children:[e.jsx(I,{className:"w-5 h-5 text-gray-600 dark:text-gray-400"}),e.jsx("h3",{className:"font-medium text-gray-900 dark:text-white",children:"Fuso Horário"})]}),e.jsx("select",{value:p,onChange:s=>P(s.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent","aria-label":"Selecionar fuso horário",children:D.map(s=>e.jsx("option",{value:s.value,children:s.label},s.value))})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"font-medium text-gray-900 dark:text-white",children:"Horários Regulares"}),g&&e.jsx("div",{className:"flex items-center space-x-2",children:e.jsxs("button",{onClick:a,onKeyDown:s=>s.key==="Enter"&&a(),className:"flex items-center space-x-1 px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors","aria-label":"Resetar horários",children:[e.jsx(Q,{className:"w-3 h-3"}),e.jsx("span",{children:"Resetar"})]})})]}),e.jsx("div",{className:"space-y-3",children:H.map(s=>{var r,l,t,i;return e.jsxs(B.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},className:"flex items-center space-x-4 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg",children:[e.jsx("div",{className:"w-24",children:e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:s.short})}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:((r=c[s.key])==null?void 0:r.isOpen)||!1,onChange:m=>E(s.key,"isOpen",m.target.checked),disabled:!g,className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500","aria-label":`Aberto na ${s.label}`}),e.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Aberto"})]}),((l=c[s.key])==null?void 0:l.isOpen)&&e.jsxs("div",{className:"flex items-center space-x-2 flex-1",children:[e.jsx("input",{type:"time",value:((t=c[s.key])==null?void 0:t.open)||"",onChange:m=>E(s.key,"open",m.target.value),disabled:!g,className:"px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent","aria-label":`Horário de abertura na ${s.label}`}),e.jsx("span",{className:"text-gray-500 text-sm",children:"às"}),e.jsx("input",{type:"time",value:((i=c[s.key])==null?void 0:i.close)||"",onChange:m=>E(s.key,"close",m.target.value),disabled:!g,className:"px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent","aria-label":`Horário de fechamento na ${s.label}`})]}),g&&e.jsx("div",{className:"flex items-center space-x-1",children:e.jsx("button",{onClick:()=>S(s.key),onKeyDown:m=>m.key==="Enter"&&S(s.key),className:"p-1 text-gray-400 hover:text-blue-600 transition-colors",title:"Aplicar a todos os dias","aria-label":`Aplicar horários de ${s.label} a todos os dias`,children:e.jsx(_,{className:"w-4 h-4"})})})]},s.key)})})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"font-medium text-gray-900 dark:text-white",children:"Horários Especiais"}),g&&e.jsxs("button",{onClick:d,onKeyDown:s=>s.key==="Enter"&&d(),className:"flex items-center space-x-2 px-3 py-1 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm","aria-label":"Adicionar horário especial",children:[e.jsx(J,{className:"w-4 h-4"}),e.jsx("span",{children:"Adicionar"})]})]}),b.length===0?e.jsxs("div",{className:"text-center py-8 text-gray-500 dark:text-gray-400",children:[e.jsx(V,{className:"w-12 h-12 mx-auto mb-4 opacity-50"}),e.jsx("p",{children:"Nenhum horário especial configurado"}),e.jsx("p",{className:"text-sm",children:"Adicione feriados ou eventos especiais"})]}):e.jsx("div",{className:"space-y-3",children:b.map(s=>e.jsxs(B.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},className:"flex items-center space-x-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800",children:[e.jsx("input",{type:"date",value:s.date,onChange:r=>u(s.id,"date",r.target.value),disabled:!g,className:"px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent","aria-label":`Data do horário especial ${s.name}`}),e.jsx("input",{type:"text",value:s.name,onChange:r=>u(s.id,"name",r.target.value),disabled:!g,placeholder:"Nome do evento",className:"flex-1 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent","aria-label":"Nome do horário especial"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",checked:s.hours.isOpen,onChange:r=>u(s.id,"hours",{...s.hours,isOpen:r.target.checked}),disabled:!g,className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500","aria-label":`Aberto para ${s.name}`}),s.hours.isOpen&&e.jsxs(e.Fragment,{children:[e.jsx("input",{type:"time",value:s.hours.open,onChange:r=>u(s.id,"hours",{...s.hours,open:r.target.value}),disabled:!g,className:"px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent","aria-label":`Horário de abertura para ${s.name}`}),e.jsx("span",{className:"text-gray-500 text-sm",children:"às"}),e.jsx("input",{type:"time",value:s.hours.close,onChange:r=>u(s.id,"hours",{...s.hours,close:r.target.value}),disabled:!g,className:"px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent","aria-label":`Horário de fechamento para ${s.name}`})]})]}),g&&e.jsx("button",{onClick:()=>C(s.id),onKeyDown:r=>r.key==="Enter"&&C(s.id),className:"p-1 text-red-400 hover:text-red-600 transition-colors","aria-label":`Remover horário especial ${s.name}`,children:e.jsx(Y,{className:"w-4 h-4"})})]},s.id))})]}),e.jsx("div",{className:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(z,{className:"w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5"}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-blue-900 dark:text-blue-100 mb-1",children:"Informações Importantes"}),e.jsxs("ul",{className:"text-sm text-blue-800 dark:text-blue-200 space-y-1",children:[e.jsx("li",{children:"• Os horários são aplicados no fuso horário selecionado"}),e.jsx("li",{children:"• Horários especiais têm prioridade sobre horários regulares"}),e.jsx("li",{children:"• Clientes verão uma mensagem quando o restaurante estiver fechado"}),e.jsx("li",{children:'• Use "24:00" para meia-noite do dia seguinte'})]})]})]})})]})}),xe=({restaurantId:h="demo-restaurant"})=>{const[o,f]=n.useState(null),[c,j]=n.useState(!0),[b,k]=n.useState(!1),[p,P]=n.useState("basic"),[g,v]=n.useState(null),[N,y]=n.useState({}),$=[{id:"basic",label:"Informações Básicas",icon:U},{id:"hours",label:"Horários",icon:G},{id:"settings",label:"Configurações",icon:ae},{id:"advanced",label:"Avançado",icon:re}];n.useEffect(()=>{O()},[h]);const O=async()=>{try{j(!0);const a=await fetch(`http://localhost:8001/api/v1/restaurants/${h}/profile`);if(a.ok){const d=await a.json();console.log("Profile loaded:",d),f(d.profile)}else{console.error("Failed to load profile, status:",a.status);const d=await a.json().catch(()=>({}));console.error("Error details:",d),f({id:h,name:"Restaurante Demo",email:"<EMAIL>",phone:"(11) 99999-9999",address:"Rua das Flores, 123 - São Paulo, SP",description:"Um restaurante aconchegante com música ambiente personalizada pelos clientes.",businessHours:{monday:{open:"11:00",close:"23:00",isOpen:!0},tuesday:{open:"11:00",close:"23:00",isOpen:!0},wednesday:{open:"11:00",close:"23:00",isOpen:!0},thursday:{open:"11:00",close:"23:00",isOpen:!0},friday:{open:"11:00",close:"24:00",isOpen:!0},saturday:{open:"11:00",close:"24:00",isOpen:!0},sunday:{open:"12:00",close:"22:00",isOpen:!0}},settings:{allowSuggestions:!0,maxSuggestionsPerUser:3,autoPlayEnabled:!0}}),x.error("Usando dados de demonstração")}}catch(a){console.error("Erro ao carregar perfil:",a),x.error("Erro de conexão ao carregar perfil")}finally{j(!1)}},H=(a,d)=>{v(a),y({[a]:d})},D=()=>{v(null),y({})},A=async a=>{if(o)try{k(!0);const d={[a]:N[a]},u=await fetch(`http://localhost:8001/api/v1/restaurants/${h}/profile`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(d)});if(u.ok){const C=await u.json();f(C.profile||{...o,[a]:N[a]}),v(null),y({}),x.success("Campo atualizado com sucesso!")}else{const C=await u.json().catch(()=>({}));x.error(C.message||"Erro ao salvar")}}catch(d){console.error("Erro ao salvar campo:",d),x.error("Erro de conexão")}finally{k(!1)}},E=async a=>{if(o)try{k(!0);const d=await fetch(`http://localhost:8001/api/v1/restaurants/${h}/profile`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({settings:a})});if(d.ok){const u=await d.json();f(u.profile||{...o,settings:a}),x.success("Configurações salvas!")}else{const u=await d.json().catch(()=>({}));x.error(u.message||"Erro ao salvar configurações")}}catch(d){console.error("Erro ao salvar configurações:",d),x.error("Erro de conexão")}finally{k(!1)}},S=({field:a,label:d,value:u,icon:C,type:R="text",multiline:T=!1})=>{const s=g===a;return e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:[e.jsx(C,{className:"w-4 h-4 inline mr-2"}),d]}),e.jsx("div",{className:"flex items-center space-x-2",children:s?e.jsxs(e.Fragment,{children:[T?e.jsx("textarea",{value:N[a]||"",onChange:r=>y({...N,[a]:r.target.value}),className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:3}):e.jsx("input",{type:R,value:N[a]||"",onChange:r=>y({...N,[a]:r.target.value}),className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),e.jsx("button",{onClick:()=>A(a),disabled:b,className:"p-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors",children:e.jsx(te,{className:"w-4 h-4"})}),e.jsx("button",{onClick:D,className:"p-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors",children:e.jsx(L,{className:"w-4 h-4"})})]}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"flex-1 px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded-lg text-gray-900 dark:text-white",children:u||"Não informado"}),e.jsx("button",{onClick:()=>H(a,u),className:"p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:e.jsx(oe,{className:"w-4 h-4"})})]})})]})};return c?e.jsxs("div",{className:"flex items-center justify-center p-8",children:[e.jsx(M,{className:"w-6 h-6 animate-spin text-blue-600 mr-2"}),e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Carregando perfil..."})]}):o?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg",children:e.jsx(U,{className:"w-6 h-6 text-blue-600 dark:text-blue-400"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Perfil do Restaurante"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Gerencie as informações do seu estabelecimento"})]})]}),e.jsxs("button",{onClick:O,disabled:c,className:"flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50",children:[e.jsx(M,{className:`w-4 h-4 ${c?"animate-spin":""}`}),e.jsx("span",{children:"Atualizar"})]})]}),e.jsx("div",{className:"border-b border-gray-200 dark:border-gray-700",children:e.jsx("nav",{className:"flex space-x-8",children:$.map(a=>e.jsxs("button",{onClick:()=>P(a.id),className:`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors ${p===a.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"}`,children:[e.jsx(a.icon,{className:"w-4 h-4"}),e.jsx("span",{children:a.label})]},a.id))})}),e.jsx(W,{mode:"wait",children:e.jsxs(B.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},transition:{duration:.2},children:[p==="basic"&&e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Informações Básicas"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsx(S,{field:"name",label:"Nome do Restaurante",value:o.name,icon:U}),e.jsx(S,{field:"email",label:"Email",value:o.email,icon:X,type:"email"}),e.jsx(S,{field:"phone",label:"Telefone",value:o.phone,icon:Z,type:"tel"}),e.jsx(S,{field:"address",label:"Endereço",value:o.address,icon:ee})]}),e.jsx("div",{className:"mt-6",children:e.jsx(S,{field:"description",label:"Descrição",value:o.description,icon:se,multiline:!0})})]})}),p==="hours"&&e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700",children:e.jsx(ne,{restaurantId:h,initialHours:o.businessHours,onSave:a=>{f({...o,businessHours:a}),x.success("Horários atualizados!")}})}),p==="settings"&&e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Configurações da Playlist"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Permitir Sugestões"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Clientes podem sugerir músicas"})]}),e.jsx("button",{onClick:()=>E({...o.settings,allowSuggestions:!o.settings.allowSuggestions}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${o.settings.allowSuggestions?"bg-blue-600":"bg-gray-200 dark:bg-gray-700"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${o.settings.allowSuggestions?"translate-x-6":"translate-x-1"}`})})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Reprodução Automática"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Tocar próxima música automaticamente"})]}),e.jsx("button",{onClick:()=>E({...o.settings,autoPlayEnabled:!o.settings.autoPlayEnabled}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${o.settings.autoPlayEnabled?"bg-blue-600":"bg-gray-200 dark:bg-gray-700"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${o.settings.autoPlayEnabled?"translate-x-6":"translate-x-1"}`})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Máximo de Sugestões por Cliente"}),e.jsxs("select",{value:o.settings.maxSuggestionsPerUser,onChange:a=>E({...o.settings,maxSuggestionsPerUser:parseInt(a.target.value)}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:1,children:"1 sugestão"}),e.jsx("option",{value:2,children:"2 sugestões"}),e.jsx("option",{value:3,children:"3 sugestões"}),e.jsx("option",{value:5,children:"5 sugestões"}),e.jsx("option",{value:10,children:"10 sugestões"}),e.jsx("option",{value:-1,children:"Ilimitado"})]})]})]})]})}),p==="advanced"&&e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Configurações Avançadas"}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white mb-2",children:"Informações do Sistema"}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-2",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"ID:"}),e.jsx("span",{className:"text-sm font-mono text-gray-900 dark:text-white",children:o.id})]}),o.createdAt&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Criado em:"}),e.jsx("span",{className:"text-sm text-gray-900 dark:text-white",children:new Date(o.createdAt).toLocaleDateString("pt-BR")})]}),o.updatedAt&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Atualizado em:"}),e.jsx("span",{className:"text-sm text-gray-900 dark:text-white",children:new Date(o.updatedAt).toLocaleDateString("pt-BR")})]})]})]})})]})})]},p)})]}):e.jsxs("div",{className:"text-center p-8",children:[e.jsx(z,{className:"w-12 h-12 text-red-500 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Erro ao carregar perfil"}),e.jsx("button",{onClick:O,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Tentar novamente"})]})};export{xe as default};
//# sourceMappingURL=EnhancedRestaurantProfile-39002e11.js.map
