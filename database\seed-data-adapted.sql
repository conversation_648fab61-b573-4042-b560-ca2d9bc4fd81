-- Seed adaptado para schema atual
INSERT INTO restaurants (id, name, description, status, settings, language, timezone, "isActive", "createdAt", "updatedAt") VALUES
('demo-restaurant', 'Restaurante Demo', 'Restaurante de demonstração do sistema de playlist interativa', 'trial', 
'{}', 'pt-BR', 'America/Sao_Paulo', true, NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Us<PERSON><PERSON><PERSON>s (senhas já com hash bcrypt de 'admin123')
INSERT INTO users (id, name, email, password, role, "isActive", "emailVerifiedAt", "createdAt", "updatedAt", restaurant_id) VALUES
(gen_random_uuid(), 'Administrador', '<EMAIL>', '$2b$12$LQv3c1yqBwEHFl5ePEjNNONciJ0MGhppMn5rjJ9TndsqKJbvHEubS', 'admin', true, NOW(), NOW(), NOW(), 'demo-restaurant')
ON CONFLICT (email) DO NOTHING;

INSERT INTO users (id, name, email, password, role, "isActive", "emailVerifiedAt", "createdAt", "updatedAt", restaurant_id) VALUES
(gen_random_uuid(), 'Gerente do Restaurante', '<EMAIL>', '$2b$12$LQv3c1yqBwEHFl5ePEjNNONciJ0MGhppMn5rjJ9TndsqKJbvHEubS', 'admin', true, NOW(), NOW(), NOW(), 'demo-restaurant')
ON CONFLICT (email) DO NOTHING;

-- Sugestões exemplo mínimas
INSERT INTO suggestions (
  id, youtube_video_id, title, artist, channel_name, duration,
  thumbnail_url, status, vote_count, upvotes, downvotes,
  restaurant_id, created_at, updated_at, metadata
) VALUES
(gen_random_uuid(), 'dQw4w9WgXcQ', 'Never Gonna Give You Up (Official Video)', 'Rick Astley', 'Rick Astley', 214,
'https://i.ytimg.com/vi/dQw4w9WgXcQ/hqdefault.jpg', 'approved', 5, 5, 0,
'demo-restaurant', NOW(), NOW(), '{"language": "en"}'),

(gen_random_uuid(), '9bZkp7q19f0', 'PSY - GANGNAM STYLE (강남스타일) M/V', 'PSY', 'officialpsy', 253,
'https://i.ytimg.com/vi/9bZkp7q19f0/hqdefault.jpg', 'approved', 3, 3, 0,
'demo-restaurant', NOW(), NOW(), '{"language": "ko"}'),

(gen_random_uuid(), 'kJQP7kiw5Fk', 'Luis Fonsi - Despacito ft. Daddy Yankee', 'Luis Fonsi', 'LuisFonsiVEVO', 282,
'https://i.ytimg.com/vi/kJQP7kiw5Fk/hqdefault.jpg', 'approved', 7, 7, 0,
'demo-restaurant', NOW(), NOW(), '{"language": "es"}');
