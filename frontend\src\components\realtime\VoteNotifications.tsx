import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ThumbsUp, ThumbsDown, TrendingUp, Music } from 'lucide-react';
import { wsService } from '@/services/websocket';

interface VoteNotification {
  id: string;
  suggestionId: string;
  songTitle: string;
  voteType: 'up' | 'down';
  voteCount: number;
  timestamp: number;
}

const VoteNotifications: React.FC = () => {
  const [notifications, setNotifications] = useState<VoteNotification[]>([]);

  useEffect(() => {
    // Escutar eventos de voto via WebSocket
    const handleVoteUpdate = (data: any) => {
      const notification: VoteNotification = {
        id: `vote-${Date.now()}-${Math.random()}`,
        suggestionId: data.suggestionId,
        songTitle: data.songTitle || 'Música',
        voteType: data.voteType || 'up',
        voteCount: data.voteCount,
        timestamp: Date.now()
      };

      setNotifications(prev => [notification, ...prev.slice(0, 4)]); // Manter apenas 5 notificações

      // Remover notificação após 3 segundos
      setTimeout(() => {
        setNotifications(prev => prev.filter(n => n.id !== notification.id));
      }, 3000);
    };

  wsService.on('vote-update', handleVoteUpdate as any);

    return () => {
  wsService.off('vote-update', handleVoteUpdate as any);
    };
  }, []);

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 pointer-events-none">
      <AnimatePresence>
        {notifications.map((notification) => (
          <motion.div
            key={notification.id}
            initial={{ opacity: 0, x: 100, scale: 0.8 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            exit={{ opacity: 0, x: 100, scale: 0.8 }}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4 min-w-[280px] pointer-events-auto"
          >
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-full ${
                notification.voteType === 'up' 
                  ? 'bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400'
                  : 'bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400'
              }`}>
                {notification.voteType === 'up' ? (
                  <ThumbsUp className="w-4 h-4" />
                ) : (
                  <ThumbsDown className="w-4 h-4" />
                )}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <Music className="w-4 h-4 text-gray-400" />
                  <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {notification.songTitle}
                  </p>
                </div>
                
                <div className="flex items-center space-x-2 mt-1">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {notification.voteType === 'up' ? 'Voto positivo' : 'Voto negativo'}
                  </span>
                  <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                    Total: {notification.voteCount}
                  </span>
                </div>
              </div>
              
              <div className="flex items-center">
                <TrendingUp className={`w-4 h-4 ${
                  notification.voteType === 'up' ? 'text-green-500' : 'text-red-500'
                }`} />
              </div>
            </div>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};

export default VoteNotifications;
