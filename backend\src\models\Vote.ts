import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  <PERSON>umn,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  Unique,
} from "typeorm";
import { Suggestion } from "./Suggestion";
import { User } from "./User";

export enum VoteType {
  UP = "up",
  DOWN = "down",
}

@Entity("votes")
@Unique(["suggestionId", "clientSessionId"])
export class Vote {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({
    name: "vote_type",
    type: "enum",
    enum: VoteType,
  })
  voteType: VoteType;

  @Column({ name: "client_session_id", type: "varchar", nullable: true })
  clientSessionId?: string;

  @Column({ name: "session_id", type: "varchar", nullable: true })
  sessionId?: string;

  @Column({ name: "table_number", type: "integer", nullable: true })
  tableNumber?: number;

  @Column({ name: "suggestion_id", type: "uuid", nullable: true })
  suggestionId?: string;

  @Column({ name: "client_ip", type: "inet", nullable: true })
  clientIp?: string;

  @Column({ name: "client_user_agent", type: "text", nullable: true })
  clientUserAgent?: string;

  @ManyToOne(() => Suggestion, (suggestion) => suggestion.votes, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "suggestion_id" })
  suggestion: Suggestion;

  @ManyToOne(() => User, {
    nullable: true,
    onDelete: "SET NULL",
  })
  @JoinColumn({ name: "user_id" })
  user?: User;

  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  // Verifica se é um upvote
  isUpvote(): boolean {
    return this.voteType === VoteType.UP;
  }

  // Verifica se é um downvote
  isDownvote(): boolean {
    return this.voteType === VoteType.DOWN;
  }

  // Obtém o identificador do votante (usuário ou sessão)
  getVoterIdentifier(): string {
    return (
      this.user?.id ?? this.clientSessionId ?? this.sessionId ?? "anonymous"
    );
  }

  // Serialização para JSON
  toJSON() {
    return {
      id: this.id,
      voteType: this.voteType,
      createdAt: this.createdAt.toISOString(),
      voter: this.user
        ? {
            id: this.user.id,
            name: this.user.name,
            type: "user",
          }
        : {
            sessionId: this.clientSessionId ?? this.sessionId,
            type: "session",
          },
    };
  }
}
