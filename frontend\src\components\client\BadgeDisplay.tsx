import React, { useState, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Award,
  Star,
  Music,
  ThumbsUp,
  Crown,
  Trophy,
  Target,
  Zap,
  Heart,
  TrendingUp,
  X,
  Lock,
  CheckCircle,
} from "lucide-react";
import { toast } from "react-hot-toast";

interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  requirement: number;
  currentProgress: number;
  unlocked: boolean;
  unlockedAt?: string;
  progressPercentage: number;
  category: "suggestions" | "votes" | "engagement" | "special";
}

interface BadgeDisplayProps {
  badges: Badge[];
  onClose?: () => void;
  showModal?: boolean;
}

const BadgeDisplay: React.FC<BadgeDisplayProps> = ({ badges, onClose, showModal = false }) => {
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedBadge, setSelectedBadge] = useState<Badge | null>(null);

  const getIconComponent = useCallback(
    (iconName: string) => {
      const iconMap: { [key: string]: React.ComponentType<{ className?: string }> } = {
        Music,
        Heart,
        Crown,
        ThumbsUp,
        Target,
        Trophy,
        Star,
        TrendingUp,
        Award,
        Zap,
      };
      return iconMap[iconName] || Award;
    },
    []
  );

  const getColorClasses = useCallback(
    (color: string, unlocked: boolean) => {
      if (!unlocked) {
        return {
          bg: "bg-gray-200 dark:bg-gray-700",
          text: "text-gray-400 dark:text-gray-500",
          border: "border-gray-300 dark:border-gray-600",
        };
      }

      const colorMap: { [key: string]: { bg: string; text: string; border: string } } = {
        blue: { bg: "bg-blue-100 dark:bg-blue-900/30", text: "text-blue-600 dark:text-blue-300", border: "border-blue-200 dark:border-blue-800" },
        red: { bg: "bg-red-100 dark:bg-red-900/30", text: "text-red-600 dark:text-red-300", border: "border-red-200 dark:border-red-800" },
        purple: { bg: "bg-purple-100 dark:bg-purple-900/30", text: "text-purple-600 dark:text-purple-300", border: "border-purple-200 dark:border-purple-800" },
        green: { bg: "bg-green-100 dark:bg-green-900/30", text: "text-green-600 dark:text-green-300", border: "border-green-200 dark:border-green-800" },
        orange: { bg: "bg-orange-100 dark:bg-orange-900/30", text: "text-orange-600 dark:text-orange-300", border: "border-orange-200 dark:border-orange-800" },
        yellow: { bg: "bg-yellow-100 dark:bg-yellow-900/30", text: "text-yellow-600 dark:text-yellow-300", border: "border-yellow-200 dark:border-yellow-800" },
        indigo: { bg: "bg-indigo-100 dark:bg-indigo-900/30", text: "text-indigo-600 dark:text-indigo-300", border: "border-indigo-200 dark:border-indigo-800" },
        pink: { bg: "bg-pink-100 dark:bg-pink-900/30", text: "text-pink-600 dark:text-pink-300", border: "border-pink-200 dark:border-pink-800" },
      };

      return colorMap[color] || colorMap.blue;
    },
    []
  );

  const categories = [
    { id: "all", name: "Todos", count: badges.length },
    { id: "suggestions", name: "Sugestões", count: badges.filter((b) => b.category === "suggestions").length },
    { id: "votes", name: "Votos", count: badges.filter((b) => b.category === "votes").length },
    { id: "engagement", name: "Engajamento", count: badges.filter((b) => b.category === "engagement").length },
    { id: "special", name: "Especiais", count: badges.filter((b) => b.category === "special").length },
  ];

  const filteredBadges = selectedCategory === "all" ? badges : badges.filter((b) => b.category === selectedCategory);
  const unlockedCount = badges.filter((b) => b.unlocked).length;
  const completionPercentage = badges.length > 0 ? (unlockedCount / badges.length) * 100 : 0;

  const BadgeCard: React.FC<{ badge: Badge }> = ({ badge }) => {
    const IconComponent = getIconComponent(badge.icon);
    const colors = getColorClasses(badge.color, badge.unlocked);

    return (
      <motion.div
        layout
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        whileHover={{ scale: badge.unlocked ? 1.05 : 1.02 }}
        onClick={() => {
          setSelectedBadge(badge);
          if (badge.unlocked) toast.success(`🏆 ${badge.name} desbloqueado!`);
        }}
        className={`relative p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 ${colors.bg} ${colors.border} ${
          badge.unlocked ? "shadow-lg hover:shadow-xl" : "opacity-80"
        }`}
        role="button"
        aria-label={`Ver detalhes do badge ${badge.name}`}
        tabIndex={0}
        onKeyPress={(e) => e.key === "Enter" && setSelectedBadge(badge)}
      >
        <div className="flex items-center justify-center mb-3">
          <div
            className={`relative w-12 h-12 rounded-full flex items-center justify-center ${
              badge.unlocked ? colors.bg : "bg-gray-300 dark:bg-gray-600"
            }`}
          >
            <IconComponent className={`w-6 h-6 ${colors.text}`} aria-hidden="true" />
            {!badge.unlocked && (
              <div className="absolute inset-0 bg-gray-900/30 rounded-full flex items-center justify-center">
                <Lock className="w-4 h-4 text-gray-500" aria-hidden="true" />
              </div>
            )}
            {badge.unlocked && (
              <div className="absolute -top-1 -right-1 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                <CheckCircle className="w-3 h-3 text-white" aria-hidden="true" />
              </div>
            )}
          </div>
        </div>
        <div className="text-center">
          <h3 className={`font-semibold text-sm mb-1 ${colors.text}`}>{badge.name}</h3>
          <p className="text-xs text-gray-500 dark:text-gray-400 mb-2 line-clamp-2">{badge.description}</p>
          <div className="space-y-1">
            <div className="flex justify-between text-xs">
              <span className="text-gray-500">{badge.currentProgress}/{badge.requirement}</span>
              <span className={colors.text}>{Math.round(badge.progressPercentage)}%</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
              <div
                className={`h-1.5 rounded-full transition-all duration-300 ${badge.unlocked ? "bg-green-500" : "bg-gray-400"}`}
                style={{ width: `${Math.min(badge.progressPercentage, 100)}%` }}
                role="progressbar"
                aria-valuenow={Math.round(badge.progressPercentage)}
                aria-valuemin={0}
                aria-valuemax={100}
              />
            </div>
          </div>
        </div>
      </motion.div>
    );
  };

  const content = (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
            <Trophy className="w-6 h-6 text-yellow-500" aria-hidden="true" />
            Conquistas
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {unlockedCount} de {badges.length} badges desbloqueados ({Math.round(completionPercentage)}%)
          </p>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
            aria-label="Fechar painel de conquistas"
          >
            <X className="w-5 h-5 text-gray-500" aria-hidden="true" />
          </button>
        )}
      </div>
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
        <div
          className="h-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full transition-all duration-500"
          style={{ width: `${completionPercentage}%` }}
          role="progressbar"
          aria-valuenow={Math.round(completionPercentage)}
          aria-valuemin={0}
          aria-valuemax={100}
        />
      </div>
      <div className="flex flex-wrap gap-2">
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => setSelectedCategory(category.id)}
            className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 ${
              selectedCategory === category.id
                ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-md"
                : "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
            }`}
            aria-label={`Filtrar por ${category.name}`}
          >
            {category.name} ({category.count})
          </button>
        ))}
      </div>
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4">
        <AnimatePresence>
          {filteredBadges.length === 0 ? (
            <div className="col-span-full text-center py-6">
              <Award className="w-12 h-12 text-gray-400 mx-auto mb-3" aria-hidden="true" />
              <p className="text-gray-500 dark:text-gray-400">Nenhum badge encontrado para esta categoria.</p>
            </div>
          ) : (
            filteredBadges.map((badge) => <BadgeCard key={badge.id} badge={badge} />)
          )}
        </AnimatePresence>
      </div>
      <AnimatePresence>
        {selectedBadge && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setSelectedBadge(null)}
            role="dialog"
            aria-modal="true"
            aria-labelledby={`badge-detail-${selectedBadge.id}`}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-md w-full shadow-xl"
            >
              <div className="text-center">
                <div
                  className={`w-20 h-20 mx-auto mb-4 rounded-full flex items-center justify-center ${
                    getColorClasses(selectedBadge.color, selectedBadge.unlocked).bg
                  }`}
                >
                  {React.createElement(getIconComponent(selectedBadge.icon), {
                    className: `w-10 h-10 ${getColorClasses(selectedBadge.color, selectedBadge.unlocked).text}`,
                    "aria-hidden": true,
                  })}
                </div>
                <h3 id={`badge-detail-${selectedBadge.id}`} className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                  {selectedBadge.name}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">{selectedBadge.description}</p>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progresso:</span>
                    <span className="font-medium">
                      {selectedBadge.currentProgress}/{selectedBadge.requirement}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        selectedBadge.unlocked ? "bg-green-500" : "bg-blue-500"
                      }`}
                      style={{ width: `${Math.min(selectedBadge.progressPercentage, 100)}%` }}
                      role="progressbar"
                      aria-valuenow={Math.round(selectedBadge.progressPercentage)}
                      aria-valuemin={0}
                      aria-valuemax={100}
                    />
                  </div>
                  {selectedBadge.unlocked && selectedBadge.unlockedAt && (
                    <p className="text-xs text-green-600 dark:text-green-400 mt-2">
                      ✅ Desbloqueado em {new Date(selectedBadge.unlockedAt).toLocaleDateString("pt-BR", {
                        day: "2-digit",
                        month: "long",
                        year: "numeric",
                      })}
                    </p>
                  )}
                </div>
                <button
                  onClick={() => setSelectedBadge(null)}
                  className="mt-4 px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
                  aria-label="Fechar detalhes do badge"
                >
                  Fechar
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );

  return showModal ? (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      role="dialog"
      aria-modal="true"
      aria-labelledby="badges-title"
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-white dark:bg-gray-900 rounded-xl p-4 sm:p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto"
      >
        {content}
      </motion.div>
    </motion.div>
  ) : (
    <div className="p-4 sm:p-6">{content}</div>
  );
};

export default BadgeDisplay;
