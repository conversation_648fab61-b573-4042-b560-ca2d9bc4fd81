import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Bell,
  Send,
  Wifi,
  WifiOff,
  AlertCircle,
  CheckCircle,
  Music,
  Heart,
  Award,
  Info,
  AlertTriangle,
  X,
} from "lucide-react";
import { toast } from "react-hot-toast";
import { useNotifications } from "../hooks/useNotifications";

interface NotificationTesterProps {
  restaurantId?: string;
}

const NotificationTester: React.FC<NotificationTesterProps> = ({
  restaurantId = "demo-restaurant",
}) => {
  const {
    isConnected,
    connectionStatus,
    notifications,
    notify,
    testNotification,
    clearNotifications,
  } = useNotifications({
    restaurantId,
    sessionId: sessionStorage.getItem("sessionId") || undefined,
    userType: "client",
    enableWebSocket: true,
    enableToasts: true,
  });

  const [testMessage, setTestMessage] = useState("");
  const [testType, setTestType] = useState<
    "success" | "error" | "warning" | "info" | "music" | "vote" | "badge"
  >("info");
  const [testPriority, setTestPriority] = useState<
    "low" | "normal" | "high" | "urgent"
  >("normal");

  const notificationTypes = [
    {
      value: "success",
      label: "Sucesso",
      icon: CheckCircle,
      color: "text-green-600",
    },
    { value: "error", label: "Erro", icon: AlertCircle, color: "text-red-600" },
    {
      value: "warning",
      label: "Aviso",
      icon: AlertTriangle,
      color: "text-yellow-600",
    },
    { value: "info", label: "Info", icon: Info, color: "text-blue-600" },
    { value: "music", label: "Música", icon: Music, color: "text-purple-600" },
    { value: "vote", label: "Voto", icon: Heart, color: "text-pink-600" },
    { value: "badge", label: "Badge", icon: Award, color: "text-orange-600" },
  ];

  const priorities = [
    { value: "low", label: "Baixa", color: "text-gray-500" },
    { value: "normal", label: "Normal", color: "text-blue-500" },
    { value: "high", label: "Alta", color: "text-orange-500" },
    { value: "urgent", label: "Urgente", color: "text-red-500" },
  ];

  const sendTestNotification = async () => {
    if (!testMessage.trim()) {
      toast.error("Digite uma mensagem para testar");
      return;
    }

    try {
      await notify[testType](
        `Teste ${testType.charAt(0).toUpperCase() + testType.slice(1)}`,
        testMessage,
        {
          priority: testPriority,
          category: "test",
          data: {
            timestamp: new Date().toISOString(),
            source: "notification-tester",
          },
        }
      );

      toast.success("Notificação enviada!");
      setTestMessage("");
    } catch (error) {
      console.error("Erro ao enviar notificação:", error);
      toast.error("Erro ao enviar notificação");
    }
  };

  const sendSystemTest = async () => {
    try {
      await testNotification();
      toast.success("Notificação de teste do sistema enviada!");
    } catch (error) {
      console.error("Erro ao enviar teste do sistema:", error);
      toast.error("Erro ao enviar teste do sistema");
    }
  };

  const simulateVote = async () => {
    await notify.vote(
      "Novo Voto",
      "Alguém votou em 'Bohemian Rhapsody - Queen'",
      {
        priority: "normal",
        data: {
          songTitle: "Bohemian Rhapsody",
          artist: "Queen",
          voteType: "up",
          voteCount: 15,
        },
      }
    );
  };

  const simulateSuggestion = async () => {
    await notify.music(
      "Nova Sugestão",
      "Cliente sugeriu 'Hotel California - Eagles'",
      {
        priority: "high",
        data: {
          songTitle: "Hotel California",
          artist: "Eagles",
          clientName: "João",
        },
      }
    );
  };

  const simulateBadge = async () => {
    await notify.badge(
      "Badge Desbloqueado!",
      "Você ganhou o badge 'Descobridor Musical'",
      {
        priority: "urgent",
        data: {
          badgeName: "Descobridor Musical",
          badgeIcon: "🎵",
        },
      }
    );
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <Bell className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Testador de Notificações
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Teste o sistema de notificações em tempo real
              </p>
            </div>
          </div>

          {/* Status da conexão */}
          <div className="flex items-center space-x-2">
            {isConnected ? (
              <div className="flex items-center space-x-2 text-green-600">
                <Wifi className="w-5 h-5" />
                <span className="text-sm font-medium">Conectado</span>
              </div>
            ) : (
              <div className="flex items-center space-x-2 text-red-600">
                <WifiOff className="w-5 h-5" />
                <span className="text-sm font-medium">
                  {connectionStatus === "error" ? "Erro" : "Desconectado"}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Formulário de teste */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Enviar Notificação Personalizada
        </h3>

        <div className="space-y-4">
          {/* Tipo de notificação */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Tipo de Notificação
            </label>
            <div className="grid grid-cols-3 md:grid-cols-4 gap-2">
              {notificationTypes.map((type) => {
                const Icon = type.icon;
                return (
                  <button
                    key={type.value}
                    onClick={() => setTestType(type.value as any)}
                    className={`flex items-center space-x-2 p-3 rounded-lg border transition-colors ${
                      testType === type.value
                        ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                        : "border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
                    }`}
                  >
                    <Icon className={`w-4 h-4 ${type.color}`} />
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {type.label}
                    </span>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Prioridade */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Prioridade
            </label>
            <div className="flex space-x-2">
              {priorities.map((priority) => (
                <button
                  key={priority.value}
                  onClick={() => setTestPriority(priority.value as any)}
                  className={`px-4 py-2 rounded-lg border transition-colors ${
                    testPriority === priority.value
                      ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                      : "border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
                  }`}
                >
                  <span className={`text-sm font-medium ${priority.color}`}>
                    {priority.label}
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* Mensagem */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Mensagem
            </label>
            <textarea
              value={testMessage}
              onChange={(e) => setTestMessage(e.target.value)}
              placeholder="Digite sua mensagem de teste..."
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows={3}
            />
          </div>

          {/* Botão enviar */}
          <button
            onClick={sendTestNotification}
            disabled={!testMessage.trim() || !isConnected}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Send className="w-4 h-4" />
            <span>Enviar Notificação</span>
          </button>
        </div>
      </div>

      {/* Testes rápidos */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Testes Rápidos
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button
            onClick={sendSystemTest}
            disabled={!isConnected}
            className="flex items-center space-x-2 p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Info className="w-5 h-5 text-blue-600" />
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              Teste Sistema
            </span>
          </button>

          <button
            onClick={simulateVote}
            disabled={!isConnected}
            className="flex items-center space-x-2 p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Heart className="w-5 h-5 text-pink-600" />
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              Simular Voto
            </span>
          </button>

          <button
            onClick={simulateSuggestion}
            disabled={!isConnected}
            className="flex items-center space-x-2 p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Music className="w-5 h-5 text-purple-600" />
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              Simular Sugestão
            </span>
          </button>

          <button
            onClick={simulateBadge}
            disabled={!isConnected}
            className="flex items-center space-x-2 p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Award className="w-5 h-5 text-orange-600" />
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              Simular Badge
            </span>
          </button>
        </div>
      </div>

      {/* Lista de notificações recebidas */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Notificações Recebidas ({notifications.length})
          </h3>
          {notifications.length > 0 && (
            <button
              onClick={clearNotifications}
              className="flex items-center space-x-2 px-3 py-1 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
            >
              <X className="w-4 h-4" />
              <span>Limpar</span>
            </button>
          )}
        </div>

        <div className="space-y-3 max-h-96 overflow-y-auto">
          {notifications.length === 0 ? (
            <p className="text-gray-500 dark:text-gray-400 text-center py-8">
              Nenhuma notificação recebida ainda
            </p>
          ) : (
            notifications.map((notification, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg"
              >
                <div className="flex items-start space-x-3">
                  <div
                    className={`p-2 rounded-lg ${
                      notification.type === "success"
                        ? "bg-green-100 dark:bg-green-900/20"
                        : notification.type === "error"
                        ? "bg-red-100 dark:bg-red-900/20"
                        : notification.type === "warning"
                        ? "bg-yellow-100 dark:bg-yellow-900/20"
                        : notification.type === "music"
                        ? "bg-purple-100 dark:bg-purple-900/20"
                        : notification.type === "vote"
                        ? "bg-pink-100 dark:bg-pink-900/20"
                        : notification.type === "badge"
                        ? "bg-orange-100 dark:bg-orange-900/20"
                        : "bg-blue-100 dark:bg-blue-900/20"
                    }`}
                  >
                    {notification.type === "success" && (
                      <CheckCircle className="w-4 h-4 text-green-600" />
                    )}
                    {notification.type === "error" && (
                      <AlertCircle className="w-4 h-4 text-red-600" />
                    )}
                    {notification.type === "warning" && (
                      <AlertTriangle className="w-4 h-4 text-yellow-600" />
                    )}
                    {notification.type === "music" && (
                      <Music className="w-4 h-4 text-purple-600" />
                    )}
                    {notification.type === "vote" && (
                      <Heart className="w-4 h-4 text-pink-600" />
                    )}
                    {notification.type === "badge" && (
                      <Award className="w-4 h-4 text-orange-600" />
                    )}
                    {notification.type === "info" && (
                      <Info className="w-4 h-4 text-blue-600" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {notification.title}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {notification.message}
                    </p>
                    <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
                      <span>Tipo: {notification.type}</span>
                      <span>
                        Prioridade: {notification.priority || "normal"}
                      </span>
                      <span>Categoria: {notification.category || "geral"}</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default NotificationTester;
