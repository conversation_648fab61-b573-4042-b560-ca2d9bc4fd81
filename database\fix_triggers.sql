-- <PERSON><PERSON><PERSON><PERSON> trigger genérica de updated_at em tabelas que a possuem

-- Função única e idempotente
CREATE OR REPLACE FUNCTION set_updated_at()
RETURNS TRIGGER AS $$
BEGIN
	NEW.updated_at = NOW();
	RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Aplicar em tabelas conhecidas, se coluna existir
DO $$
DECLARE
	t TEXT;
BEGIN
	FOR t IN SELECT table_name FROM information_schema.columns WHERE column_name='updated_at' LOOP
		EXECUTE format('DROP TRIGGER IF EXISTS set_updated_at_%I ON %I', t, t);
		EXECUTE format('CREATE TRIGGER set_updated_at_%I BEFORE UPDATE ON %I FOR EACH ROW EXECUTE FUNCTION set_updated_at()', t, t);
	END LOOP;
END $$;

