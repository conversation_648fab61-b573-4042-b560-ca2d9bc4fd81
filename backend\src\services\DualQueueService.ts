import { AppDataSource } from "../config/database";
import { Suggestion, SuggestionStatus } from "../models/Suggestion";
import { Restaurant } from "../models/Restaurant";
import { Playlist } from "../models/Playlist";
import { logger } from "../utils/logger";
import { notificationService } from "./NotificationService";
import { NotificationType, NotificationPriority } from "./NotificationService";

export interface QueueTrack {
  id: string;
  youtubeVideoId: string;
  title: string;
  artist: string;
  duration: number;
  thumbnailUrl?: string;
  source: "playlist" | "suggestion_free" | "suggestion_paid";
  priority: 0 | 1;
  clientName?: string;
  tableNumber?: string;
  paymentAmount?: number;
  addedAt: Date;
  position: number;
}

export interface DualQueueState {
  currentTrack?: QueueTrack;
  priorityQueue: QueueTrack[];
  normalQueue: QueueTrack[];
  playlistTracks: QueueTrack[];
  stats: {
    totalPriorityItems: number;
    totalNormalItems: number;
    totalPlaylistItems: number;
    estimatedWaitTime: number;
    revenue: number;
  };
}

class DualQueueService {
  private suggestionRepository = AppDataSource.getRepository(Suggestion);
  private restaurantRepository = AppDataSource.getRepository(Restaurant);
  private playlistRepository = AppDataSource.getRepository(Playlist);

  async getDualQueueState(restaurantId: string): Promise<DualQueueState> {
    try {
      const [
        prioritySuggestions,
        normalSuggestions,
        activePlaylist,
        currentTrack,
      ] = await Promise.all([
        this.suggestionRepository
          .createQueryBuilder("suggestion")
          .leftJoinAndSelect("suggestion.clientSession", "session")
          .where("suggestion.restaurant_id = :restaurantId", { restaurantId })
          .andWhere("suggestion.status = :status", {
            status: SuggestionStatus.APPROVED,
          })
          .andWhere("suggestion.playedAt IS NULL")
          .andWhere("suggestion.isPaid = true")
          .andWhere("suggestion.paymentStatus = :paymentStatus", {
            paymentStatus: "paid",
          })
          .orderBy("suggestion.createdAt", "ASC")
          .getMany(),

        this.suggestionRepository
          .createQueryBuilder("suggestion")
          .leftJoinAndSelect("suggestion.clientSession", "session")
          .where("suggestion.restaurant_id = :restaurantId", { restaurantId })
          .andWhere("suggestion.status = :status", {
            status: SuggestionStatus.APPROVED,
          })
          .andWhere("suggestion.playedAt IS NULL")
          .andWhere("suggestion.isPaid = false")
          .orderBy("suggestion.createdAt", "ASC")
          .getMany(),

        this.playlistRepository
          .createQueryBuilder("playlist")
          .where("playlist.restaurant_id = :restaurantId", { restaurantId })
          .andWhere("playlist.status = :status", { status: "active" })
          .orderBy("playlist.isDefault", "DESC") // Priorizar padrão se existir
          .addOrderBy("playlist.createdAt", "DESC") // Mais recente primeiro
          .getOne(),

        this.suggestionRepository
          .createQueryBuilder("suggestion")
          .leftJoinAndSelect("suggestion.clientSession", "session")
          .where("suggestion.restaurant_id = :restaurantId", { restaurantId })
          .andWhere("suggestion.status = :status", {
            status: SuggestionStatus.PLAYING,
          })
          .orderBy("suggestion.playedAt", "DESC")
          .getOne(),
      ]);

      const priorityQueue: QueueTrack[] = prioritySuggestions.map(
        (suggestion, index) => ({
          id: `priority_${suggestion.id}`,
          youtubeVideoId: suggestion.youtubeVideoId,
          title: suggestion.title,
          artist: suggestion.artist || "Artista Desconhecido",
          duration: suggestion.duration || 180,
          thumbnailUrl: suggestion.thumbnailUrl,
          source: "suggestion_paid" as const,
          priority: 1,
          clientName: suggestion.clientSession?.clientName,
          tableNumber: suggestion.clientSession?.tableNumber,
          paymentAmount: suggestion.paymentAmount
            ? suggestion.paymentAmount / 100
            : 2.0,
          addedAt: suggestion.createdAt,
          position: index + 1,
        })
      );

      const normalQueue: QueueTrack[] = normalSuggestions.map(
        (suggestion, index) => ({
          id: `normal_${suggestion.id}`,
          youtubeVideoId: suggestion.youtubeVideoId,
          title: suggestion.title,
          artist: suggestion.artist || "Artista Desconhecido",
          duration: suggestion.duration || 180,
          thumbnailUrl: suggestion.thumbnailUrl,
          source: "suggestion_free" as const,
          priority: 0,
          clientName: suggestion.clientSession?.clientName,
          tableNumber: suggestion.clientSession?.tableNumber,
          addedAt: suggestion.createdAt,
          position: index + 1,
        })
      );

      let playlistTracks: QueueTrack[] = [];
      if (activePlaylist && activePlaylist.tracks) {
        const tracks = Array.isArray(activePlaylist.tracks)
          ? activePlaylist.tracks
          : JSON.parse(activePlaylist.tracks as string);

        playlistTracks = tracks.map((track: any, index: number) => ({
          id: `playlist_${track.id || index}`,
          youtubeVideoId: track.id,
          title: track.title,
          artist: track.artist || "Artista Desconhecido",
          duration: track.duration || 180,
          thumbnailUrl: track.thumbnailUrl,
          source: "playlist" as const,
          priority: 0,
          addedAt: new Date(),
          position: index + 1,
        }));
      }

      const currentQueueTrack: QueueTrack | undefined = currentTrack
        ? {
            id: currentTrack.isPaid
              ? `priority_${currentTrack.id}`
              : `normal_${currentTrack.id}`,
            youtubeVideoId: currentTrack.youtubeVideoId,
            title: currentTrack.title,
            artist: currentTrack.artist || "Artista Desconhecido",
            duration: currentTrack.duration || 180,
            thumbnailUrl: currentTrack.thumbnailUrl,
            source: currentTrack.isPaid ? "suggestion_paid" : "suggestion_free",
            priority: currentTrack.isPaid ? 1 : 0,
            clientName: currentTrack.clientSession?.clientName,
            tableNumber: currentTrack.clientSession?.tableNumber,
            paymentAmount: currentTrack.isPaid
              ? currentTrack.paymentAmount
                ? currentTrack.paymentAmount / 100
                : 2.0
              : undefined,
            addedAt: currentTrack.createdAt,
            position: 0,
          }
        : undefined;

      const stats = {
        totalPriorityItems: priorityQueue.length,
        totalNormalItems: normalQueue.length,
        totalPlaylistItems: playlistTracks.length,
        estimatedWaitTime: this.calculateEstimatedWaitTime(
          priorityQueue,
          normalQueue
        ),
        revenue: priorityQueue.reduce(
          (sum, track) => sum + (track.paymentAmount || 0),
          0
        ),
      };

      return {
        currentTrack: currentQueueTrack,
        priorityQueue,
        normalQueue,
        playlistTracks,
        stats,
      };
    } catch (error) {
      logger.error("Erro ao obter estado das filas:", error);
      throw error;
    }
  }

  async getNextTrack(restaurantId: string): Promise<QueueTrack | null> {
    const state = await this.getDualQueueState(restaurantId);

    if (state.priorityQueue.length > 0) {
      return state.priorityQueue[0];
    }

    if (state.normalQueue.length > 0) {
      return state.normalQueue[0];
    }

    if (state.playlistTracks.length > 0) {
      return state.playlistTracks[0];
    }

    return null;
  }

  async markAsPlayed(trackId: string): Promise<void> {
    try {
      if (trackId.startsWith("priority_") || trackId.startsWith("normal_")) {
        const suggestionId = trackId.replace(/^(priority_|normal_)/, "");

        const suggestion = await this.suggestionRepository.findOne({
          where: { id: suggestionId },
        });

        if (suggestion) {
          suggestion.status = SuggestionStatus.PLAYED;
          suggestion.playedAt = new Date();
          await this.suggestionRepository.save(suggestion);

          logger.info(`Música ${trackId} marcada como tocada`, {
            suggestionId,
            title: suggestion.title,
            isPaid: suggestion.isPaid,
          });

          await notificationService.sendToRestaurant(suggestion.restaurant.id, {
            type: NotificationType.MUSIC,
            title: "Música Tocada",
            message: `"${suggestion.title}" foi reproduzida`,
            priority: NotificationPriority.LOW,
            category: "playback",
            data: { suggestionId, isPaid: suggestion.isPaid },
            autoClose: true,
            duration: 3000,
          });
        }
      }
    } catch (error) {
      logger.error("Erro ao marcar música como tocada:", error);
      throw error;
    }
  }

  async addToPriorityQueue(suggestionId: string): Promise<QueueTrack | null> {
    try {
      const suggestion = await this.suggestionRepository.findOne({
        where: { id: suggestionId },
        relations: ["clientSession", "restaurant"],
      });

      if (!suggestion) {
        throw new Error("Sugestão não encontrada");
      }

      suggestion.status = SuggestionStatus.APPROVED;
      suggestion.isPaid = true;
      suggestion.paymentStatus = "paid";
      suggestion.paymentAmount = 200;

      await this.suggestionRepository.save(suggestion);

      await notificationService.sendToRestaurant(suggestion.restaurant.id, {
        type: NotificationType.SUCCESS,
        title: "💰 Música PAGA na fila!",
        message: `"${suggestion.title}" foi adicionada à FILA PRIORITÁRIA`,
        priority: NotificationPriority.HIGH,
        category: "priority_queue",
        data: { suggestionId, isPaid: true },
        autoClose: true,
        duration: 5000,
      });

      logger.info("Música adicionada à fila prioritária", {
        suggestionId,
        title: suggestion.title,
        clientName: suggestion.clientSession?.clientName,
      });

      const state = await this.getDualQueueState(suggestion.restaurant.id);
      const position = state.priorityQueue.length + 1;

      return {
        id: `priority_${suggestion.id}`,
        youtubeVideoId: suggestion.youtubeVideoId,
        title: suggestion.title,
        artist: suggestion.artist || "Artista Desconhecido",
        duration: suggestion.duration || 180,
        thumbnailUrl: suggestion.thumbnailUrl,
        source: "suggestion_paid" as const,
        priority: 1,
        clientName: suggestion.clientSession?.clientName,
        tableNumber: suggestion.clientSession?.tableNumber,
        paymentAmount: 2.0,
        addedAt: suggestion.createdAt,
        position,
      };
    } catch (error) {
      logger.error("Erro ao adicionar à fila prioritária:", error);
      throw error;
    }
  }

  async addToNormalQueue(suggestionId: string): Promise<QueueTrack | null> {
    try {
      const suggestion = await this.suggestionRepository.findOne({
        where: { id: suggestionId },
        relations: ["clientSession", "restaurant"],
      });

      if (!suggestion) {
        throw new Error("Sugestão não encontrada");
      }

      suggestion.status = SuggestionStatus.APPROVED;
      suggestion.isPaid = false;

      await this.suggestionRepository.save(suggestion);

      await notificationService.sendToRestaurant(suggestion.restaurant.id, {
        type: NotificationType.INFO,
        title: "🎵 Música na fila normal",
        message: `"${suggestion.title}" foi adicionada à fila gratuita`,
        priority: NotificationPriority.NORMAL,
        category: "normal_queue",
        data: { suggestionId, isPaid: false },
        autoClose: true,
        duration: 5000,
      });

      logger.info("Música adicionada à fila normal", {
        suggestionId,
        title: suggestion.title,
        clientName: suggestion.clientSession?.clientName,
      });

      const state = await this.getDualQueueState(suggestion.restaurant.id);
      const position = state.normalQueue.length + 1;

      return {
        id: `normal_${suggestion.id}`,
        youtubeVideoId: suggestion.youtubeVideoId,
        title: suggestion.title,
        artist: suggestion.artist || "Artista Desconhecido",
        duration: suggestion.duration || 180,
        thumbnailUrl: suggestion.thumbnailUrl,
        source: "suggestion_free" as const,
        priority: 0,
        clientName: suggestion.clientSession?.clientName,
        tableNumber: suggestion.clientSession?.tableNumber,
        addedAt: suggestion.createdAt,
        position,
      };
    } catch (error) {
      logger.error("Erro ao adicionar à fila normal:", error);
      throw error;
    }
  }

  private calculateEstimatedWaitTime(
    priorityQueue: QueueTrack[],
    normalQueue: QueueTrack[]
  ): number {
    const priorityTime = priorityQueue.reduce(
      (sum, track) => sum + (track.duration || 0),
      0
    );
    const normalTime = normalQueue.reduce(
      (sum, track) => sum + (track.duration || 0),
      0
    );
    return priorityTime + normalTime;
  }
}

export const dualQueueService = new DualQueueService();
export default dualQueueService;
