import { Router } from "express";
import { body, param, query, validationResult } from "../utils/validation";
import { AppDataSource } from "../config/database";
import { Genre } from "../models/Genre";
import asyncHandler from "../middleware/asyncHandler";
import { optionalAuth, authMiddleware } from "../middleware/auth";
import { ValidationError, NotFoundError } from "../utils/errors";

const router = Router();

/**
 * @swagger
 * /api/v1/genres:
 *   get:
 *     summary: Listar todos os gêneros
 *     tags: [Gêneros]
 *     parameters:
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *           enum: [music, mood, energy, time, custom]
 *         description: Filtrar por categoria
 *       - in: query
 *         name: active
 *         schema:
 *           type: boolean
 *         description: Filtrar por status ativo
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Buscar por nome
 *     responses:
 *       200:
 *         description: Lista de gêneros
 */
router.get(
  "/",
  [
    query("category")
      .optional()
      .isIn(["music", "mood", "energy", "time", "custom"]),
    query("active").optional().isBoolean(),
    query("search").optional().isString(),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { category, active, search } = req.query;
    const genreRepository = AppDataSource.getRepository(Genre);

    let queryBuilder = genreRepository
      .createQueryBuilder("genre")
      .orderBy("genre.priority", "ASC")
      .addOrderBy("genre.display_name", "ASC");

    // Filtros
    if (category) {
      queryBuilder.andWhere("genre.category = :category", { category });
    }

    if (active !== undefined) {
      queryBuilder.andWhere("genre.is_active = :active", {
        active: active === "true",
      });
    }

    if (search) {
      queryBuilder.andWhere(
        "(LOWER(genre.name) LIKE LOWER(:search) OR LOWER(genre.display_name) LIKE LOWER(:search))",
        { search: `%${search}%` }
      );
    }

    const genres = await queryBuilder.getMany();

    // Agrupar por categoria
    const groupedGenres = genres.reduce((acc, genre) => {
      if (!acc[genre.category]) {
        acc[genre.category] = [];
      }
      acc[genre.category].push(genre.getDisplayInfo());
      return acc;
    }, {} as Record<string, any[]>);

    res.json({
      success: true,
      genres: groupedGenres,
      total: genres.length,
      categories: {
        music: "Gêneros Musicais",
        mood: "Humor",
        energy: "Energia",
        time: "Horário",
        custom: "Personalizado",
      },
    });
  })
);

/**
 * @swagger
 * /api/v1/genres/{id}:
 *   get:
 *     summary: Obter gênero por ID
 *     tags: [Gêneros]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Dados do gênero
 *       404:
 *         description: Gênero não encontrado
 */
router.get(
  "/:id",
  [param("id").notEmpty().withMessage("ID do gênero é obrigatório")],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { id } = req.params;
    const genreRepository = AppDataSource.getRepository(Genre);

  const genre = await genreRepository.findOne({ where: { id } });

    if (!genre) {
      throw new NotFoundError("Gênero não encontrado");
    }

    res.json({
      success: true,
      genre: genre.getDisplayInfo(),
    });
  })
);

/**
 * @swagger
 * /api/v1/genres:
 *   post:
 *     summary: Criar novo gênero
 *     tags: [Gêneros]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - displayName
 *               - category
 *             properties:
 *               name:
 *                 type: string
 *               displayName:
 *                 type: string
 *               description:
 *                 type: string
 *               category:
 *                 type: string
 *                 enum: [music, mood, energy, time, custom]
 *               color:
 *                 type: string
 *               icon:
 *                 type: string
 *     responses:
 *       201:
 *         description: Gênero criado
 *       400:
 *         description: Dados inválidos
 */
router.post(
  "/",
  [
    body("name").notEmpty().withMessage("Nome é obrigatório"),
    body("displayName")
      .notEmpty()
      .withMessage("Nome de exibição é obrigatório"),
    body("category")
      .isIn(["music", "mood", "energy", "time", "custom"])
      .withMessage("Categoria inválida"),
    body("description").optional().isString(),
    body("color")
      .optional()
      .matches(/^#[0-9A-F]{6}$/i)
      .withMessage("Cor deve ser hexadecimal"),
    body("icon").optional().isString(),
    body("priority").optional().isInt({ min: 0 }),
  ],
  authMiddleware,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { name, displayName, description, category, color, icon, priority } =
      req.body;
    const genreRepository = AppDataSource.getRepository(Genre);

    // Verificar se já existe
    const existingGenre = await genreRepository.findOne({
      where: { name: name.toLowerCase() },
    });

    if (existingGenre) {
      throw new ValidationError("Gênero já existe", []);
    }

    // Criar novo gênero
    const genre = genreRepository.create({
      name: name.toLowerCase(),
      displayName,
      description,
      category,
      color: color || "#3B82F6",
      icon,
      priority: priority || 0,
      isActive: true,
      isDefault: false,
    });

    await genreRepository.save(genre);

    res.status(201).json({
      success: true,
      message: "Gênero criado com sucesso",
      genre: genre.getDisplayInfo(),
    });
  })
);

/**
 * @swagger
 * /api/v1/genres/{id}:
 *   put:
 *     summary: Atualizar gênero
 *     tags: [Gêneros]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               displayName:
 *                 type: string
 *               description:
 *                 type: string
 *               color:
 *                 type: string
 *               icon:
 *                 type: string
 *               priority:
 *                 type: integer
 *               isActive:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Gênero atualizado
 *       404:
 *         description: Gênero não encontrado
 */
router.put(
  "/:id",
  [
    param("id").notEmpty().withMessage("ID do gênero é obrigatório"),
    body("displayName").optional().isString(),
    body("description").optional().isString(),
    body("color")
      .optional()
      .matches(/^#[0-9A-F]{6}$/i)
      .withMessage("Cor deve ser hexadecimal"),
    body("icon").optional().isString(),
    body("priority").optional().isInt({ min: 0 }),
    body("isActive").optional().isBoolean(),
  ],
  authMiddleware,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { id } = req.params;
    const { displayName, description, color, icon, priority, isActive } =
      req.body;
    const genreRepository = AppDataSource.getRepository(Genre);

    const genre = await genreRepository.findOne({ where: { id } });

    if (!genre) {
      throw new NotFoundError("Gênero não encontrado");
    }

    // Atualizar campos
    if (displayName !== undefined) genre.displayName = displayName;
    if (description !== undefined) genre.description = description;
    if (color !== undefined) genre.color = color;
    if (icon !== undefined) genre.icon = icon;
    if (priority !== undefined) genre.priority = priority;
    if (isActive !== undefined) genre.isActive = isActive;

    await genreRepository.save(genre);

    res.json({
      success: true,
      message: "Gênero atualizado com sucesso",
      genre: genre.getDisplayInfo(),
    });
  })
);

/**
 * @swagger
 * /api/v1/genres/{id}:
 *   delete:
 *     summary: Deletar gênero
 *     tags: [Gêneros]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Gênero deletado
 *       404:
 *         description: Gênero não encontrado
 *       400:
 *         description: Não é possível deletar gênero padrão
 */
router.delete(
  "/:id",
  [param("id").notEmpty().withMessage("ID do gênero é obrigatório")],
  authMiddleware,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { id } = req.params;
    const genreRepository = AppDataSource.getRepository(Genre);

    const genre = await genreRepository.findOne({ where: { id } });

    if (!genre) {
      throw new NotFoundError("Gênero não encontrado");
    }

    if (genre.isDefault) {
      throw new ValidationError(
        "Não é possível deletar gêneros padrão do sistema",
        []
      );
    }

    await genreRepository.remove(genre);

    res.json({
      success: true,
      message: "Gênero deletado com sucesso",
    });
  })
);

/**
 * @swagger
 * /api/v1/genres/seed:
 *   post:
 *     summary: Criar gêneros padrão do sistema
 *     tags: [Gêneros]
 *     responses:
 *       200:
 *         description: Gêneros padrão criados
 */
router.post(
  "/seed",
  optionalAuth,
  asyncHandler(async (req, res) => {
    const genreRepository = AppDataSource.getRepository(Genre);

    const defaultGenres = Genre.getDefaultGenres();
    let created = 0;
    let skipped = 0;

    for (const genreData of defaultGenres) {
      const existingGenre = await genreRepository.findOne({
        where: { name: genreData.name },
      });

      if (!existingGenre) {
        const genre = genreRepository.create(genreData);
        await genreRepository.save(genre);
        created++;
      } else {
        skipped++;
      }
    }

    res.json({
      success: true,
      message: `Gêneros padrão processados: ${created} criados, ${skipped} já existiam`,
      stats: { created, skipped, total: defaultGenres.length },
    });
  })
);

export default router;
