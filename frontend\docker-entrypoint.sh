#!/bin/sh

# Script de inicialização do container frontend

# Substituir variáveis de ambiente no runtime
if [ -n "$VITE_API_URL" ]; then
    echo "Configurando VITE_API_URL: $VITE_API_URL"
    find /usr/share/nginx/html -name "*.js" -exec sed -i "s|__VITE_API_URL__|$VITE_API_URL|g" {} \;
fi

if [ -n "$VITE_WS_URL" ]; then
    echo "Configurando VITE_WS_URL: $VITE_WS_URL"
    find /usr/share/nginx/html -name "*.js" -exec sed -i "s|__VITE_WS_URL__|$VITE_WS_URL|g" {} \;
fi

# Iniciar nginx
exec "$@"
