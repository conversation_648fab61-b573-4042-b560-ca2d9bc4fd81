// Test script to verify paymentAmount formatting functions
console.log("Testing paymentAmount formatting...");

// Helper functions (same as in PlaybackController.tsx)
const toNumber = (v) => {
  const n = Number(v);
  return Number.isFinite(n) ? n : 0;
};

const formatBRL = (v) =>
  toNumber(v).toLocaleString("pt-BR", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });

// Test cases that were causing the error
const testCases = [
  5.5, // normal number
  "10.25", // string number
  "abc", // invalid string
  null, // null
  undefined, // undefined
  "", // empty string
  0, // zero
  "0", // string zero
];

console.log("Testing toNumber function:");
testCases.forEach((test) => {
  const result = toNumber(test);
  console.log(`toNumber(${JSON.stringify(test)}) = ${result}`);
});

console.log("\nTesting formatBRL function:");
testCases.forEach((test) => {
  try {
    const result = formatBRL(test);
    console.log(`formatBRL(${JSON.stringify(test)}) = "R$ ${result}"`);
  } catch (e) {
    console.error(`formatBRL(${JSON.stringify(test)}) = ERROR: ${e.message}`);
  }
});

console.log("\nAll tests completed successfully!");
