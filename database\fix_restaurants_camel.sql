-- Ensure restaurants columns use exact camelCase names expected by TypeORM
DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='restaurants' AND column_name='youtubeChannelId'
  ) AND EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='restaurants' AND column_name='youtubechannelid'
  ) THEN
    ALTER TABLE public.restaurants RENAME COLUMN youtubechannelid TO "youtubeChannelId";
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='restaurants' AND column_name='youtubePremiumToken'
  ) AND EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='restaurants' AND column_name='youtubepremiumtoken'
  ) THEN
    ALTER TABLE public.restaurants RENAME COLUMN youtubepremiumtoken TO "youtubePremiumToken";
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='restaurants' AND column_name='youtubeCredentials'
  ) AND EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='restaurants' AND column_name='youtubecredentials'
  ) THEN
    ALTER TABLE public.restaurants RENAME COLUMN youtubecredentials TO "youtubeCredentials";
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='restaurants' AND column_name='socialMedia'
  ) AND EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='restaurants' AND column_name='socialmedia'
  ) THEN
    ALTER TABLE public.restaurants RENAME COLUMN socialmedia TO "socialMedia";
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='restaurants' AND column_name='businessHours'
  ) AND EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='restaurants' AND column_name='businesshours'
  ) THEN
    ALTER TABLE public.restaurants RENAME COLUMN businesshours TO "businessHours";
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='restaurants' AND column_name='trialExpiresAt'
  ) AND EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='restaurants' AND column_name='trialexpiresat'
  ) THEN
    ALTER TABLE public.restaurants RENAME COLUMN trialexpiresat TO "trialExpiresAt";
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='restaurants' AND column_name='lastActivityAt'
  ) AND EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_schema='public' AND table_name='restaurants' AND column_name='lastactivityat'
  ) THEN
    ALTER TABLE public.restaurants RENAME COLUMN lastactivityat TO "lastActivityAt";
  END IF;
END $$;
