import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from "typeorm";
import { Restaurant } from "./Restaurant";

@Entity("rewards")
@Index(["restaurantId"])
@Index(["sessionId"])
@Index(["awardedDate"])
@Index(["status"])
export class Reward {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ name: "restaurant_id", type: "uuid" })
  restaurantId: string;

  @ManyToOne(() => Restaurant, { onDelete: "CASCADE" })
  @JoinColumn({ name: "restaurant_id" })
  restaurant: Restaurant;

  @Column({ name: "session_id", type: "uuid" })
  sessionId: string;

  @Column({ name: "client_name", type: "varchar", nullable: true })
  clientName?: string;

  @Column({ name: "table_name", type: "varchar", nullable: true })
  tableName?: string;

  @Column({ type: "varchar" })
  type: "discount" | "free_song" | "priority_queue" | "badge" | "custom";

  @Column({ type: "varchar" })
  title: string;

  @Column({ type: "text" })
  description: string;

  @Column({ type: "json" })
  rewardData: {
    discountPercentage?: number;
    discountAmount?: number;
    freeSongsCount?: number;
    priorityDuration?: number; // em minutos
    badgeIcon?: string;
    badgeColor?: string;
    customMessage?: string;
    validUntil?: string;
    usageLimit?: number;
  };

  @Column({ name: "awarded_for", type: "varchar" })
  awardedFor: "daily_winner" | "weekly_winner" | "monthly_winner" | "best_performance" | "most_votes" | "participation";

  @Column({ name: "awarded_date", type: "timestamp" })
  awardedDate: Date;

  @Column({ name: "expires_at", type: "timestamp", nullable: true })
  expiresAt?: Date;

  @Column({ type: "varchar", default: "active" })
  status: "active" | "used" | "expired" | "cancelled";

  @Column({ name: "used_at", type: "timestamp", nullable: true })
  usedAt?: Date;

  @Column({ name: "usage_count", type: "integer", default: 0 })
  usageCount: number;

  // Dados da performance que gerou o prêmio
  @Column({ name: "performance_data", type: "json", nullable: true })
  performanceData?: {
    songTitle?: string;
    artist?: string;
    averageRating?: number;
    totalVotes?: number;
    rank?: number;
    competitionDate?: string;
  };

  // Dados de compartilhamento social
  @Column({ name: "social_sharing", type: "json", nullable: true })
  socialSharing?: {
    shared?: boolean;
    sharedAt?: string;
    platform?: string;
    shareUrl?: string;
    shareText?: string;
  };

  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at" })
  updatedAt: Date;

  // Métodos auxiliares
  isActive(): boolean {
    return this.status === "active" && !this.isExpired();
  }

  isExpired(): boolean {
    if (!this.expiresAt) return false;
    return new Date() > this.expiresAt;
  }

  canBeUsed(): boolean {
    if (!this.isActive()) return false;
    
    const usageLimit = this.rewardData.usageLimit;
    if (usageLimit && this.usageCount >= usageLimit) {
      return false;
    }
    
    return true;
  }

  getRewardIcon(): string {
    const icons = {
      discount: "💰",
      free_song: "🎵",
      priority_queue: "⚡",
      badge: "🏆",
      custom: "🎁",
    };
    return icons[this.type] || "🎁";
  }

  getRewardColor(): string {
    const colors = {
      discount: "#10B981", // green
      free_song: "#3B82F6", // blue
      priority_queue: "#F59E0B", // amber
      badge: "#EF4444", // red
      custom: "#8B5CF6", // purple
    };
    return colors[this.type] || "#6B7280";
  }

  getFormattedValue(): string {
    switch (this.type) {
      case "discount":
        if (this.rewardData.discountPercentage) {
          return `${this.rewardData.discountPercentage}% OFF`;
        }
        if (this.rewardData.discountAmount) {
          return `R$ ${this.rewardData.discountAmount.toFixed(2)} OFF`;
        }
        return "Desconto";
      
      case "free_song":
        const count = this.rewardData.freeSongsCount || 1;
        return `${count} música${count > 1 ? 's' : ''} grátis`;
      
      case "priority_queue":
        const duration = this.rewardData.priorityDuration || 60;
        return `${duration} min de prioridade`;
      
      case "badge":
        return this.rewardData.badgeIcon || "🏆";
      
      default:
        return this.title;
    }
  }

  getDaysUntilExpiry(): number {
    if (!this.expiresAt) return -1;
    const now = new Date();
    const diffTime = this.expiresAt.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  markAsUsed(): void {
    this.usageCount++;
    this.usedAt = new Date();
    
    const usageLimit = this.rewardData.usageLimit;
    if (usageLimit && this.usageCount >= usageLimit) {
      this.status = "used";
    }
  }

  // Método para JSON público
  toPublicJSON() {
    return {
      id: this.id,
      type: this.type,
      title: this.title,
      description: this.description,
      icon: this.getRewardIcon(),
      color: this.getRewardColor(),
      formattedValue: this.getFormattedValue(),
      awardedFor: this.awardedFor,
      awardedDate: this.awardedDate,
      expiresAt: this.expiresAt,
      status: this.status,
      isActive: this.isActive(),
      isExpired: this.isExpired(),
      canBeUsed: this.canBeUsed(),
      usageCount: this.usageCount,
      usageLimit: this.rewardData.usageLimit,
      daysUntilExpiry: this.getDaysUntilExpiry(),
      performanceData: this.performanceData,
      socialSharing: this.socialSharing,
    };
  }

  // Método para JSON do restaurante (com mais detalhes)
  toRestaurantJSON() {
    return {
      ...this.toPublicJSON(),
      sessionId: this.sessionId,
      clientName: this.clientName,
      tableName: this.tableName,
      rewardData: this.rewardData,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      usedAt: this.usedAt,
    };
  }

  // Método estático para criar prêmio
  static createReward(data: {
    restaurantId: string;
    sessionId: string;
    clientName?: string;
    tableName?: string;
    type: Reward["type"];
    title: string;
    description: string;
    rewardData: Reward["rewardData"];
    awardedFor: Reward["awardedFor"];
    expiresAt?: Date;
    performanceData?: Reward["performanceData"];
  }): Reward {
    const reward = new Reward();
    
    reward.restaurantId = data.restaurantId;
    reward.sessionId = data.sessionId;
    reward.clientName = data.clientName;
    reward.tableName = data.tableName;
    reward.type = data.type;
    reward.title = data.title;
    reward.description = data.description;
    reward.rewardData = data.rewardData;
    reward.awardedFor = data.awardedFor;
    reward.awardedDate = new Date();
    reward.expiresAt = data.expiresAt;
    reward.status = "active";
    reward.performanceData = data.performanceData;
    
    return reward;
  }

  // Método para gerar texto de compartilhamento social
  generateSocialShareText(): string {
    const baseText = `🏆 Ganhei um prêmio no ${this.restaurant?.name || "restaurante"}!`;
    
    switch (this.awardedFor) {
      case "daily_winner":
        return `${baseText} 🥇 Fui o campeão do dia cantando "${this.performanceData?.songTitle}"! ${this.getFormattedValue()}`;
      
      case "best_performance":
        return `${baseText} 🎤 Melhor performance com ${this.performanceData?.averageRating}⭐ cantando "${this.performanceData?.songTitle}"! ${this.getFormattedValue()}`;
      
      case "most_votes":
        return `${baseText} 🗳️ Recebi ${this.performanceData?.totalVotes} votos cantando "${this.performanceData?.songTitle}"! ${this.getFormattedValue()}`;
      
      default:
        return `${baseText} ${this.getFormattedValue()}`;
    }
  }

  // Método para marcar como compartilhado
  markAsShared(platform: string, shareUrl?: string): void {
    this.socialSharing = {
      shared: true,
      sharedAt: new Date().toISOString(),
      platform,
      shareUrl,
      shareText: this.generateSocialShareText(),
    };
  }
}
