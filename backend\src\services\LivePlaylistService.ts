import { AppDataSource } from "../config/database";
import { Suggestion, SuggestionStatus } from "../models/Suggestion";
import { Restaurant } from "../models/Restaurant";
import { Playlist, PlaylistStatus } from "../models/Playlist";
import { logger } from "../utils/logger";
import {
  notificationService,
  NotificationType,
  NotificationPriority,
} from "./NotificationService";
import { youtubeOAuthService } from "./YouTubeOAuthService";
import { collaborativePlaylistService } from "./CollaborativePlaylistService";

export interface LivePlaylistState {
  currentTrack?: {
    id: string;
    title: string;
    artist: string;
    position: number;
    source: "playlist" | "paid_suggestion" | "free_suggestion";
  };
  nextTracks: Array<{
    id: string;
    title: string;
    artist: string;
    position: number;
    source: "playlist" | "paid_suggestion" | "free_suggestion";
    isPaid?: boolean;
    clientName?: string;
    tableNumber?: string;
  }>;
  playlistId: string;
  totalTracks: number;
  paidSuggestionsCount: number;
  freeSuggestionsCount: number;
}

class LivePlaylistService {
  private suggestionRepository = AppDataSource.getRepository(Suggestion);
  private restaurantRepository = AppDataSource.getRepository(Restaurant);
  private playlistRepository = AppDataSource.getRepository(Playlist);

  /**
   * REORDENAR PLAYLIST PARA TOCAR MÚSICA PAGA IMEDIATAMENTE
   * Move a música escolhida para a próxima posição na playlist do YouTube
   */
  async insertPaidSuggestionNext(
    restaurantId: string,
    suggestionId: string
  ): Promise<{ success: boolean; message: string; newPosition?: number }> {
    try {
      // 1. Buscar a sugestão
      const suggestion = await this.suggestionRepository.findOne({
        where: { id: suggestionId },
        relations: ["restaurant", "clientSession", "playlist"],
      });

      if (!suggestion) {
        return { success: false, message: "Sugestão não encontrada" };
      }

      // 2. Verificar se o restaurante está autenticado com YouTube
      const isAuthenticated = await youtubeOAuthService.isAuthenticated(
        restaurantId
      );
      if (!isAuthenticated) {
        return {
          success: false,
          message:
            "Restaurante não autenticado com YouTube. Configure OAuth primeiro.",
        };
      }

      // 3. Buscar playlist a ser utilizada: preferir a selecionada na sugestão
      let activePlaylist: Playlist | null = null;
      if (suggestion.playlist?.id) {
        activePlaylist = await this.playlistRepository.findOne({
          where: {
            id: suggestion.playlist.id,
            restaurant: { id: restaurantId },
            status: PlaylistStatus.ACTIVE,
          },
          relations: ["restaurant"],
        });
      }
      if (!activePlaylist) {
        activePlaylist = await this.playlistRepository.findOne({
          where: {
            restaurant: { id: restaurantId },
            status: PlaylistStatus.ACTIVE,
          },
          order: {
            isDefault: "DESC", // Priorizar padrão se existir, senão pegar qualquer ativa
            createdAt: "DESC", // Mais recente primeiro
          },
          relations: ["restaurant"], // Incluir relação
        });
      }

      if (!activePlaylist || !activePlaylist.youtubePlaylistId) {
        return {
          success: false,
          message: "Playlist ativa não encontrada ou não vinculada ao YouTube",
        };
      }

      // 4. Verificar se a música JÁ ESTÁ na playlist
      const playlistTracks = Array.isArray(activePlaylist.tracks)
        ? activePlaylist.tracks
        : JSON.parse((activePlaylist.tracks as string) || "[]");

      const trackIndex = playlistTracks.findIndex(
        (track: any) =>
          track.id === suggestion.youtubeVideoId ||
          track.youtubeVideoId === suggestion.youtubeVideoId
      );

      if (trackIndex === -1) {
        return {
          success: false,
          message:
            "Música não encontrada na playlist do restaurante. Apenas músicas da playlist podem ser escolhidas.",
        };
      }

      // 5. Respeitar cooldown autoritativo (10 min) antes de reordenar
      const yid = suggestion.youtubeVideoId;
      if (yid) {
        try {
          const inCooldown = await collaborativePlaylistService.isSongInCooldown(
            restaurantId,
            yid
          );
          if (inCooldown) {
            return {
              success: false,
              message:
                "Esta música acabou de tocar e está em cooldown de 10 minutos. Tente novamente em breve.",
            };
          }
        } catch (e) {
          // Em caso de falha no Redis, seguir de forma conservadora e permitir a operação
        }
      }

      // 6. REORDENAR PLAYLIST: Mover a música para a próxima posição
      const currentPosition = await this.getCurrentPlayingPosition(
        restaurantId,
        activePlaylist.youtubePlaylistId
      );

      const nextPosition = currentPosition + 1;

      // Mover a música da posição atual para a próxima posição na playlist do YouTube
      const newOrder = playlistTracks.map((track: any, index: number) => ({
        videoId: track.id || track.youtubeVideoId,
        position:
          index === trackIndex
            ? nextPosition
            : index < trackIndex && index >= nextPosition
            ? index + 1
            : index,
      }));

      const reorderSuccess = await youtubeOAuthService.reorderPlaylist(
        restaurantId,
        activePlaylist.youtubePlaylistId,
        newOrder
      );

      if (!reorderSuccess) {
        return {
          success: false,
          message: "Falha ao reordenar playlist do YouTube",
        };
      }

  // 7. Marcar sugestão como aprovada e paga
      suggestion.status = SuggestionStatus.APPROVED;
      suggestion.isPaid = true;
      suggestion.paymentStatus = "paid";
      // Não sobrescrever o valor pago; manter o que veio da sugestão (em reais)
      suggestion.queuePosition = nextPosition;

      await this.suggestionRepository.save(suggestion);

  // 8. Notificar em tempo real
      await this.notifyPlaylistUpdate(restaurantId, {
        type: "paid_insertion",
        track: {
          title: suggestion.title,
          artist: suggestion.artist,
          position: nextPosition,
          clientName: suggestion.clientSession?.clientName,
          tableNumber: suggestion.clientSession?.tableNumber,
        },
      });

      logger.info("Música paga inserida na playlist do YouTube", {
        suggestionId,
        youtubeVideoId: suggestion.youtubeVideoId,
        title: suggestion.title,
        position: nextPosition,
        playlistId: activePlaylist.youtubePlaylistId,
      });

  return {
        success: true,
        message: `Música "${suggestion.title}" inserida na posição ${nextPosition} da playlist!`,
        newPosition: nextPosition,
      };
    } catch (error) {
      logger.error("Erro ao inserir música paga na playlist:", error);
      return {
        success: false,
        message: "Erro interno ao processar pagamento",
      };
    }
  }

  /**
   * REORDENAR MÚSICA GRATUITA PARA O FINAL DA FILA NORMAL
   * Move a música escolhida para depois das músicas prioritárias
   */
  async addFreeSuggestionToEnd(
    restaurantId: string,
    suggestionId: string
  ): Promise<{ success: boolean; message: string; position?: number }> {
    try {
      const suggestion = await this.suggestionRepository.findOne({
        where: { id: suggestionId },
        relations: ["restaurant", "clientSession", "playlist"],
      });

      if (!suggestion) {
        return { success: false, message: "Sugestão não encontrada" };
      }

      const isAuthenticated = await youtubeOAuthService.isAuthenticated(
        restaurantId
      );
      if (!isAuthenticated) {
        return {
          success: false,
          message: "Restaurante não autenticado com YouTube",
        };
      }

      let activePlaylist: Playlist | null = null;
      if (suggestion.playlist?.id) {
        activePlaylist = await this.playlistRepository.findOne({
          where: {
            id: suggestion.playlist.id,
            restaurant: { id: restaurantId },
            status: PlaylistStatus.ACTIVE,
          },
          relations: ["restaurant"],
        });
      }
      if (!activePlaylist) {
        activePlaylist = await this.playlistRepository.findOne({
          where: {
            restaurant: { id: restaurantId },
            status: PlaylistStatus.ACTIVE,
          },
          order: {
            isDefault: "DESC", // Priorizar padrão se existir
            createdAt: "DESC", // Mais recente primeiro
          },
          relations: ["restaurant"], // Incluir relação
        });
      }

      if (!activePlaylist || !activePlaylist.youtubePlaylistId) {
        return {
          success: false,
          message: "Playlist ativa não encontrada",
        };
      }

      // Verificar se a música JÁ ESTÁ na playlist
      const playlistTracks = Array.isArray(activePlaylist.tracks)
        ? activePlaylist.tracks
        : JSON.parse((activePlaylist.tracks as string) || "[]");

      const trackIndex = playlistTracks.findIndex(
        (track: any) =>
          track.id === suggestion.youtubeVideoId ||
          track.youtubeVideoId === suggestion.youtubeVideoId
      );

      if (trackIndex === -1) {
        return {
          success: false,
          message:
            "Música não encontrada na playlist do restaurante. Apenas músicas da playlist podem ser escolhidas.",
        };
      }

      // Respeitar cooldown autoritativo (10 min) — evitar reordenar se a faixa está em cooldown
      const yid = suggestion.youtubeVideoId;
      if (yid) {
        try {
          const inCooldown = await collaborativePlaylistService.isSongInCooldown(
            restaurantId,
            yid
          );
          if (inCooldown) {
            return {
              success: false,
              message:
                "Esta música está em cooldown de 10 minutos (tocou recentemente). Aguarde para adicioná-la novamente.",
            };
          }
        } catch (e) {
          // Em caso de falha no Redis, continuar para não bloquear fluxo gratuito
        }
      }

      // Reordenar para posição após as músicas prioritárias (final da fila normal)
      const currentPosition = await this.getCurrentPlayingPosition(
        restaurantId,
        activePlaylist.youtubePlaylistId
      );

      // Posição no final da fila normal (após prioritárias, mas antes do resto da playlist)
      const normalQueuePosition = currentPosition + 5; // Deixa espaço para 4 músicas prioritárias

      const newOrder = playlistTracks.map((track: any, index: number) => ({
        videoId: track.id || track.youtubeVideoId,
        position: index === trackIndex ? normalQueuePosition : index,
      }));

      const reorderSuccess = await youtubeOAuthService.reorderPlaylist(
        restaurantId,
        activePlaylist.youtubePlaylistId,
        newOrder
      );

      if (!reorderSuccess) {
        return {
          success: false,
          message: "Falha ao reordenar playlist do YouTube",
        };
      }

      // Marcar como aprovada e gratuita
      suggestion.status = SuggestionStatus.APPROVED;
      suggestion.isPaid = false;

      await this.suggestionRepository.save(suggestion);

      await this.notifyPlaylistUpdate(restaurantId, {
        type: "free_addition",
        track: {
          title: suggestion.title,
          artist: suggestion.artist,
          clientName: suggestion.clientSession?.clientName,
          tableNumber: suggestion.clientSession?.tableNumber,
        },
      });

      logger.info("Música gratuita adicionada ao final da playlist", {
        suggestionId,
        title: suggestion.title,
        playlistId: activePlaylist.youtubePlaylistId,
      });

      return {
        success: true,
        message: `Música "${suggestion.title}" adicionada ao final da playlist!`,
      };
    } catch (error) {
      logger.error("Erro ao adicionar música gratuita:", error);
      return {
        success: false,
        message: "Erro interno ao processar sugestão",
      };
    }
  }

  /**
   * Obter posição atual da música tocando
   * (Simulado - em produção seria integrado com o player)
   */
  private async getCurrentPlayingPosition(
    restaurantId: string,
    playlistId: string
  ): Promise<number> {
    // TODO: Integrar com o player real para obter posição atual
    // Por enquanto, retorna posição simulada
    return 0; // Primeira música
  }

  /**
   * Obter estado atual da playlist ao vivo
   */
  async getLivePlaylistState(
    restaurantId: string
  ): Promise<LivePlaylistState | null> {
    try {
      console.log(
        `🔍 Buscando playlist ativa para restaurante: ${restaurantId}`
      );

      // Buscar qualquer playlist ativa do restaurante
      const activePlaylist = await this.playlistRepository.findOne({
        where: {
          restaurant: { id: restaurantId },
          status: PlaylistStatus.ACTIVE,
        },
        order: {
          isDefault: "DESC", // Priorizar padrão se existir
          createdAt: "DESC", // Mais recente primeiro
        },
        relations: ["restaurant"], // Incluir relação
      });

      console.log(
        `📋 Playlist encontrada:`,
        activePlaylist
          ? {
              id: activePlaylist.id,
              name: activePlaylist.name,
              status: activePlaylist.status,
              restaurantId: activePlaylist.restaurant?.id,
            }
          : "null"
      );

      if (!activePlaylist) {
        // Retorna estado padrão em vez de null para evitar 404 no consumidor
        return {
          playlistId: "",
          totalTracks: 0,
          paidSuggestionsCount: 0,
          freeSuggestionsCount: 0,
          nextTracks: [],
        };
      }

      // Buscar sugestões pagas pendentes
      const paidSuggestions = await this.suggestionRepository.count({
        where: {
          restaurant: { id: restaurantId },
          status: SuggestionStatus.APPROVED,
          isPaid: true,
          playedAt: null,
        },
      });

      // Buscar sugestões gratuitas pendentes
      const freeSuggestions = await this.suggestionRepository.count({
        where: {
          restaurant: { id: restaurantId },
          status: SuggestionStatus.APPROVED,
          isPaid: false,
          playedAt: null,
        },
      });

      const tracks = Array.isArray(activePlaylist.tracks)
        ? activePlaylist.tracks
        : JSON.parse((activePlaylist.tracks as string) || "[]");

      return {
        playlistId: activePlaylist.youtubePlaylistId || "",
        totalTracks: tracks.length,
        paidSuggestionsCount: paidSuggestions,
        freeSuggestionsCount: freeSuggestions,
        nextTracks: tracks.slice(0, 5).map((track: any, index: number) => ({
          id: track.id,
          title: track.title,
          artist: track.artist,
          position: index,
          source: "playlist" as const,
        })),
      };
    } catch (error) {
      logger.error("Erro ao obter estado da playlist:", error);
      return null;
    }
  }

  /**
   * Notificar atualizações da playlist em tempo real
   */
  private async notifyPlaylistUpdate(
    restaurantId: string,
    update: {
      type: "paid_insertion" | "free_addition";
      track: {
        title: string;
        artist: string;
        position?: number;
        clientName?: string;
        tableNumber?: string;
      };
    }
  ): Promise<void> {
    const isPaid = update.type === "paid_insertion";

    // Notificar restaurante
    await notificationService.sendToRestaurant(restaurantId, {
      type: isPaid ? NotificationType.SUCCESS : NotificationType.INFO,
      title: isPaid ? "💰 MÚSICA PAGA ADICIONADA!" : "🎵 Música adicionada",
      message: isPaid
        ? `"${update.track.title}" foi inserida na PRÓXIMA POSIÇÃO da playlist!`
        : `"${update.track.title}" foi adicionada ao final da playlist`,
      priority: isPaid
        ? NotificationPriority.HIGH
        : NotificationPriority.NORMAL,
      category: "live_playlist",
      data: {
        type: update.type,
        track: update.track,
        isPaid,
      },
    });

    // Notificar todos os clientes conectados
    await notificationService.sendToRestaurant(restaurantId, {
      type: NotificationType.INFO,
      title: isPaid ? "🚀 Música paga na frente!" : "🎵 Nova música",
      message: isPaid
        ? `Uma música paga foi adicionada e tocará em breve!`
        : `Nova música foi adicionada à playlist`,
      priority: NotificationPriority.NORMAL,
      category: "playlist_update",
      data: {
        isPaid,
        title: update.track.title,
      },
    });
  }
}

export const livePlaylistService = new LivePlaylistService();
export default livePlaylistService;
