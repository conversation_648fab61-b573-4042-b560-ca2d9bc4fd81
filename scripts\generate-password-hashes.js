const bcrypt = require('bcryptjs');

async function generateHashes() {
    console.log('Gerando hashes para senhas padrão...\n');
    
    // <NAME_EMAIL>
    const demoPassword = 'demo123';
    const demoHash = await bcrypt.hash(demoPassword, 12);
    console.log(`<EMAIL> (senha: ${demoPassword})`);
    console.log(`Hash: ${demoHash}\n`);
    
    // <NAME_EMAIL>
    const adminPassword = 'Adm!n2024#Secure$';
    const adminHash = await bcrypt.hash(adminPassword, 12);
    console.log(`<EMAIL> (senha: ${adminPassword})`);
    console.log(`Hash: ${adminHash}\n`);
    
    // Testar se os hashes funcionam
    console.log('Testando hashes...');
    const demoTest = await bcrypt.compare(demoPassword, demoHash);
    const adminTest = await bcrypt.compare(adminPassword, adminHash);
    
    console.log(`Demo hash válido: ${demoTest}`);
    console.log(`Admin hash válido: ${adminTest}`);
}

generateHashes().catch(console.error);
