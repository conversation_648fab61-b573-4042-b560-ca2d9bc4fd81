import React, { useState, useEffect } from "react";
import { Music2, Heart, Zap, Sun, Search, X } from "lucide-react";
import { toast } from "react-hot-toast";

interface MusicFilter {
  id: string;
  name: string;
  displayName: string;
  icon: React.ComponentType<any>;
  color: string;
  bgColor: string;
  category: "genre" | "mood" | "energy" | "time";
}

interface MusicFiltersProps {
  onFiltersChange: (filters: string[]) => void;
  activeFilters: string[];
}

const MusicFilters: React.FC<MusicFiltersProps> = ({
  onFiltersChange,
  activeFilters,
}) => {
  const [filterSearch, setFilterSearch] = useState("");
  const [dynamicFilters, setDynamicFilters] = useState<MusicFilter[]>([]);

  // Filtros estáticos como fallback
  const staticFilters: MusicFilter[] = [
    // Gêneros
    {
      id: "rock",
      name: "Rock",
  displayName: "Rock",
      icon: Music2,
      color: "text-red-600",
      bgColor: "bg-red-100",
      category: "genre",
    },
    {
      id: "pop",
      name: "Pop",
  displayName: "Pop",
      icon: Music2,
      color: "text-pink-600",
      bgColor: "bg-pink-100",
      category: "genre",
    },
    {
      id: "sertanejo",
      name: "Sertanejo",
  displayName: "Sertanejo",
      icon: Music2,
      color: "text-yellow-600",
      bgColor: "bg-yellow-100",
      category: "genre",
    },
    {
      id: "mpb",
      name: "MPB",
  displayName: "MPB",
      icon: Music2,
      color: "text-green-600",
      bgColor: "bg-green-100",
      category: "genre",
    },
    {
      id: "eletronica",
      name: "Eletrônica",
  displayName: "Eletrônica",
      icon: Music2,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
      category: "genre",
    },
    {
      id: "funk",
      name: "Funk",
  displayName: "Funk",
      icon: Music2,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
      category: "genre",
    },
    // Humor/Mood
    {
      id: "happy",
      name: "Alegre",
  displayName: "Alegre",
      icon: Heart,
      color: "text-yellow-500",
      bgColor: "bg-yellow-100",
      category: "mood",
    },
    {
      id: "romantic",
      name: "Romântico",
  displayName: "Romântico",
      icon: Heart,
      color: "text-red-500",
      bgColor: "bg-red-100",
      category: "mood",
    },
    {
      id: "chill",
      name: "Relaxante",
  displayName: "Relaxante",
      icon: Heart,
      color: "text-blue-500",
      bgColor: "bg-blue-100",
      category: "mood",
    },
    {
      id: "party",
      name: "Festa",
  displayName: "Festa",
      icon: Heart,
      color: "text-purple-500",
      bgColor: "bg-purple-100",
      category: "mood",
    },
    // Energia
    {
      id: "high_energy",
      name: "Alta Energia",
  displayName: "Alta Energia",
      icon: Zap,
      color: "text-red-500",
      bgColor: "bg-red-100",
      category: "energy",
    },
    {
      id: "medium_energy",
      name: "Energia Média",
  displayName: "Energia Média",
      icon: Zap,
      color: "text-orange-500",
      bgColor: "bg-orange-100",
      category: "energy",
    },
    {
      id: "low_energy",
      name: "Baixa Energia",
  displayName: "Baixa Energia",
      icon: Zap,
      color: "text-blue-500",
      bgColor: "bg-blue-100",
      category: "energy",
    },
    // Horário
    {
      id: "morning",
      name: "Manhã",
  displayName: "Manhã",
      icon: Sun,
      color: "text-yellow-500",
      bgColor: "bg-yellow-100",
      category: "time",
    },
    {
      id: "afternoon",
      name: "Tarde",
  displayName: "Tarde",
      icon: Sun,
      color: "text-orange-500",
      bgColor: "bg-orange-100",
      category: "time",
    },
    {
      id: "evening",
      name: "Noite",
  displayName: "Noite",
      icon: Sun,
      color: "text-indigo-500",
      bgColor: "bg-indigo-100",
      category: "time",
    },
  ];

  // Carregar gêneros dinâmicos do backend
  const loadGenresFromBackend = async () => {
    try {
  // carregando
      const response = await fetch(
        "http://localhost:8001/api/v1/restaurants/genres"
      );

      if (response.ok) {
        const data = await response.json();
        const backendFilters: MusicFilter[] = [];

        // Converter gêneros do backend para filtros
        Object.entries(data.genres).forEach(([category, items]) => {
          if (Array.isArray(items)) {
            items.forEach((item: any) => {
              const iconMap: Record<string, React.ComponentType<any>> = {
                music: Music2,
                mood: Heart,
                energy: Zap,
                time: Sun,
              };

              backendFilters.push({
                id: item.id || item.name,
                name: item.name,
                displayName: item.displayName || item.name,
                icon: iconMap[category] || Music2,
                color: item.color ? `text-[${item.color}]` : "text-purple-600",
                bgColor: item.color ? `bg-[${item.color}]/10` : "bg-purple-100",
                category: category as "genre" | "mood" | "energy" | "time",
              });
            });
          }
        });

        setDynamicFilters(backendFilters);
        console.log("🎵 Gêneros carregados do backend:", backendFilters.length);
      } else {
        console.warn(
          "⚠️ Falha ao carregar gêneros do backend, usando estáticos"
        );
        setDynamicFilters(staticFilters);
      }
    } catch (error) {
      console.error("❌ Erro ao carregar gêneros:", error);
      setDynamicFilters(staticFilters);
    } finally {
      // fim carregando
    }
  };

  // Carregar gêneros na inicialização
  useEffect(() => {
    loadGenresFromBackend();
  }, []);

  // Usar filtros dinâmicos se disponíveis, senão usar estáticos
  const filters = dynamicFilters.length > 0 ? dynamicFilters : staticFilters;

  const categories = {
    genre: { name: "Gêneros", icon: Music2 },
    mood: { name: "Humor", icon: Heart },
    energy: { name: "Energia", icon: Zap },
    time: { name: "Horário", icon: Sun },
  };

  const getFiltersByCategory = (category: string) => {
    return filters.filter(
      (filter) =>
        filter.category === category &&
        filter.name.toLowerCase().includes(filterSearch.toLowerCase())
    );
  };

  const toggleFilter = (filterId: string) => {
    const newFilters = activeFilters.includes(filterId)
      ? activeFilters.filter((id) => id !== filterId)
      : [...activeFilters, filterId];
    onFiltersChange(newFilters);
    const filter = filters.find((f) => f.id === filterId);
    toast.success(
      activeFilters.includes(filterId)
        ? `Filtro "${filter?.name}" removido`
        : `Filtro "${filter?.name}" adicionado`
    );
  };

  const clearAllFilters = () => {
    onFiltersChange([]);
    setFilterSearch("");
    toast.success("Filtros limpos");
  };

  return (
    <div className="space-y-4">
      {/* Busca por Filtros */}
      <div className="flex items-center space-x-3">
        <div className="relative flex-1">
          <input
            type="text"
            value={filterSearch}
            onChange={(e) => setFilterSearch(e.target.value)}
            placeholder="Buscar filtros (ex.: Rock, Alegre)"
            className="w-full px-4 py-3 pl-10 bg-white/20 border border-white/30 rounded-lg text-white placeholder-purple-200 focus:outline-none focus:ring-2 focus:ring-purple-500"
            aria-label="Buscar filtros por nome"
          />
          <Search className="w-5 h-5 text-purple-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
        </div>
      </div>

      {/* Filtros Ativos */}
      {activeFilters.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {activeFilters.map((filterId) => {
            const filter = filters.find((f) => f.id === filterId);
            if (!filter) return null;
            const Icon = filter.icon;
            return (
              <div
                key={filterId}
                className="flex items-center space-x-1 px-3 py-1 rounded-full text-sm bg-gradient-to-r from-purple-600 to-pink-600 text-white border border-purple-500"
              >
                <Icon className="w-3 h-3" />
                <span>{filter.name}</span>
                <button
                  onClick={() => toggleFilter(filterId)}
                  className="ml-1 hover:bg-white/20 rounded-full p-0.5"
                  aria-label={`Remover filtro ${filter.name}`}
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            );
          })}
        </div>
      )}

      {/* Categorias de Filtros */}
      {Object.entries(categories).map(
        ([categoryKey, { name, icon: CategoryIcon }]) => {
          const categoryFilters = getFiltersByCategory(categoryKey);
          return (
            <div key={categoryKey}>
              <h3 className="text-sm font-semibold text-white mb-2 flex items-center space-x-2">
                <CategoryIcon className="w-4 h-4 text-purple-400" />
                <span>{name}</span>
              </h3>
              {categoryFilters.length === 0 ? (
                <p className="text-purple-200 text-sm">
                  Nenhum filtro encontrado para "{filterSearch}"
                </p>
              ) : (
                <div className="flex flex-wrap gap-2 overflow-x-auto pb-2">
                  {categoryFilters.map((filter) => {
                    const Icon = filter.icon;
                    return (
                      <button
                        key={filter.id}
                        onClick={() => toggleFilter(filter.id)}
                        className={`px-4 py-2 rounded-full text-sm font-medium transition-colors border min-w-[80px] flex items-center space-x-1 ${
                          activeFilters.includes(filter.id)
                            ? "bg-gradient-to-r from-purple-600 to-pink-600 text-white border-purple-500"
                            : "bg-white/10 text-purple-200 border-white/20 hover:bg-purple-500 hover:text-white"
                        }`}
                        aria-pressed={activeFilters.includes(filter.id)}
                        aria-label={`Filtrar por ${filter.name}`}
                      >
                        <Icon className="w-3 h-3" />
                        <span>{filter.name}</span>
                      </button>
                    );
                  })}
                </div>
              )}
            </div>
          );
        }
      )}

      {/* Botão Limpar Filtros */}
      {activeFilters.length > 0 && (
        <div className="text-center">
          <button
            onClick={clearAllFilters}
            className="flex items-center justify-center space-x-1 px-4 py-2 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-lg text-sm hover:from-red-700 hover:to-red-800 transition-colors"
            aria-label="Limpar todos os filtros"
          >
            <X className="w-4 h-4" />
            <span>Limpar Filtros</span>
          </button>
        </div>
      )}
    </div>
  );
};

export default MusicFilters;
