import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Star, Users, Clock, Trophy, X } from "lucide-react";
import { toast } from "react-hot-toast";

interface VotingSession {
  id: string;
  suggestionId: string;
  performerClientName?: string;
  performerTableName?: string;
  songTitle: string;
  artist: string;
  startTime: string;
  endTime: string;
  isActive: boolean;
  totalVotes: number;
  averageRating: number;
  ratings: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
}

interface VotingWidgetProps {
  sessionId: string;
  restaurantId: string;
  onVoteSubmitted?: (rating: number) => void;
}

const VotingWidget: React.FC<VotingWidgetProps> = ({
  sessionId,
  restaurantId,
  onVoteSubmitted,
}) => {
  const [activeSession, setActiveSession] = useState<VotingSession | null>(null);
  const [selectedRating, setSelectedRating] = useState<number>(0);
  const [comment, setComment] = useState("");
  const [isVoting, setIsVoting] = useState(false);
  const [hasVoted, setHasVoted] = useState(false);
  const [timeLeft, setTimeLeft] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  // Verificar se há votação ativa
  useEffect(() => {
    checkActiveVoting();
    const interval = setInterval(checkActiveVoting, 5000); // Verificar a cada 5s
    return () => clearInterval(interval);
  }, [restaurantId]);

  // Timer para tempo restante
  useEffect(() => {
    if (activeSession && activeSession.isActive) {
      const updateTimer = () => {
        const now = new Date().getTime();
        const end = new Date(activeSession.endTime).getTime();
        const remaining = Math.max(0, Math.floor((end - now) / 1000));
        setTimeLeft(remaining);

        if (remaining === 0) {
          setActiveSession(null);
          setIsVisible(false);
        }
      };

      updateTimer();
      const timer = setInterval(updateTimer, 1000);
      return () => clearInterval(timer);
    }
  }, [activeSession]);

  const checkActiveVoting = async () => {
    try {
      const response = await fetch(
        `http://localhost:8001/api/v1/competitive-voting/active/${restaurantId}`
      );

      if (response.ok) {
        const data = await response.json();
        if (data.activeSessions && data.activeSessions.length > 0) {
          const session = data.activeSessions[0]; // Pegar a primeira sessão ativa
          setActiveSession(session);
          setIsVisible(true);
        } else {
          setActiveSession(null);
          setIsVisible(false);
        }
      }
    } catch (error) {
      console.error("Erro ao verificar votação ativa:", error);
      setActiveSession(null);
      setIsVisible(false);
    }
  };

  const submitVote = async () => {
    if (!activeSession || selectedRating === 0) return;

    try {
      setIsVoting(true);

      const response = await fetch(
        "http://localhost:8001/api/v1/competitive-voting/vote",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            votingSessionId: activeSession.id,
            suggestionId: activeSession.suggestionId,
            voterSessionId: sessionId,
            rating: selectedRating,
            comment: comment.trim() || undefined,
            voterTableName: `Mesa ${Math.floor(Math.random() * 20) + 1}`, // Mock
          }),
        }
      );

      if (response.ok) {
        const data = await response.json();
        setHasVoted(true);
        toast.success(`Voto de ${selectedRating} estrelas enviado!`);
        onVoteSubmitted?.(selectedRating);

        // Atualizar dados da sessão
        if (data.votingSession) {
          setActiveSession(data.votingSession);
        }
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Erro ao enviar voto");
      }
    } catch (error) {
      console.error("Erro ao enviar voto:", error);
      toast.error("Erro ao enviar voto");
    } finally {
      setIsVoting(false);
    }
  };

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  const getRatingText = (rating: number): string => {
    const texts = {
      1: "Ruim",
      2: "Regular",
      3: "Bom",
      4: "Muito Bom",
      5: "Excelente",
    };
    return texts[rating as keyof typeof texts] || "";
  };

  if (!isVisible || !activeSession) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 100 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 100 }}
        transition={{ type: "spring", stiffness: 100, damping: 20 }}
        className="fixed bottom-4 right-4 left-4 mx-auto max-w-md bg-gradient-to-br from-purple-900/90 to-indigo-900/90 rounded-xl shadow-2xl border border-white/10 p-4 z-50 sm:max-w-sm"
        role="dialog"
        aria-labelledby="voting-title"
        aria-describedby="voting-description"
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <Trophy className="w-5 h-5 text-yellow-400" />
            <span
              id="voting-title"
              className="font-semibold text-white text-sm"
            >
              Votação Ativa
            </span>
          </div>
          <button
            onClick={() => setIsVisible(false)}
            className="p-1 text-white/70 hover:text-white transition-colors"
            aria-label="Fechar widget de votação"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Song Info */}
        <div className="mb-3 p-3 bg-white/10 rounded-lg">
          <p
            id="voting-description"
            className="font-medium text-white text-sm truncate"
          >
            {activeSession.songTitle}
          </p>
          <p className="text-xs text-purple-200 truncate">
            {activeSession.artist}
          </p>
          <p className="text-xs text-purple-300">
            Por {activeSession.performerClientName || "Anônimo"} (
            {activeSession.performerTableName || "Mesa desconhecida"})
          </p>
        </div>

        {/* Timer */}
        <div className="flex items-center justify-center space-x-2 mb-3 text-orange-400">
          <Clock className="w-4 h-4" />
          <span className="font-mono text-sm">{formatTime(timeLeft)}</span>
          <span className="text-xs">restantes</span>
        </div>

        {hasVoted ? (
          /* Already Voted */
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 mb-2">
              {[1, 2, 3, 4, 5].map((star) => (
                <Star
                  key={star}
                  className={`w-6 h-6 ${
                    star <= selectedRating
                      ? "text-yellow-400 fill-current"
                      : "text-white/30"
                  }`}
                  aria-hidden="true"
                />
              ))}
            </div>
            <p className="text-sm text-green-400 font-medium">
              Voto enviado: {selectedRating} estrelas
            </p>
            <div className="mt-2 text-xs text-purple-200">
              <div className="flex items-center justify-center space-x-2">
                <Users className="w-3 h-3" />
                <span>{activeSession.totalVotes} votos</span>
                <span>•</span>
                <span>Média: {activeSession.averageRating.toFixed(1)}⭐</span>
              </div>
            </div>
          </div>
        ) : (
          /* Voting Interface */
          <div>
            <p className="text-sm text-white text-center mb-3">
              Como foi a performance?
            </p>

            {/* Star Rating */}
            <div className="flex items-center justify-center space-x-2 mb-3">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  onClick={() => setSelectedRating(star)}
                  className="p-2 hover:scale-110 transition-transform focus:outline-none focus:ring-2 focus:ring-purple-500 rounded-full"
                  disabled={isVoting}
                  aria-label={`Avaliar com ${star} estrela${star > 1 ? "s" : ""}`}
                >
                  <Star
                    className={`w-7 h-7 ${
                      star <= selectedRating
                        ? "text-yellow-400 fill-current"
                        : "text-white/50 hover:text-yellow-300"
                    }`}
                  />
                </button>
              ))}
            </div>

            {selectedRating > 0 && (
              <p className="text-center text-sm text-purple-200 mb-3">
                {getRatingText(selectedRating)}
              </p>
            )}

            {/* Comment */}
            <textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Comentário opcional..."
              className="w-full px-3 py-2 text-sm bg-white/10 border border-white/20 rounded-lg text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
              rows={3}
              maxLength={100}
              disabled={isVoting}
              aria-label="Comentário opcional sobre a performance"
            />

            {/* Submit Button */}
            <button
              onClick={submitVote}
              disabled={selectedRating === 0 || isVoting}
              className="w-full mt-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white py-2 px-4 rounded-lg text-sm font-medium hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2"
              aria-label="Enviar votação"
            >
              {isVoting ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <>
                  <Star className="w-4 h-4" />
                  <span>Enviar Voto</span>
                </>
              )}
            </button>
          </div>
        )}

        {/* Current Stats */}
        {activeSession.totalVotes > 0 && (
          <div className="mt-3 pt-3 border-t border-white/20">
            <div className="flex items-center justify-between text-xs text-purple-200">
              <div className="flex items-center space-x-1">
                <Users className="w-3 h-3" />
                <span>{activeSession.totalVotes} votos</span>
              </div>
              <div className="flex items-center space-x-1">
                <Star className="w-3 h-3 text-yellow-400 fill-current" />
                <span>{activeSession.averageRating.toFixed(1)}</span>
              </div>
            </div>
          </div>
        )}
      </motion.div>
    </AnimatePresence>
  );
};

export default VotingWidget;