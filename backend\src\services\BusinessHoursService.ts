import { AppDataSource } from "../config/database";
import { Restaurant } from "../models/Restaurant";
import { redisClient } from "../config/redis";

export interface DayHours {
  open: string;
  close: string;
  isOpen: boolean;
}

export interface BusinessHours {
  monday: DayHours;
  tuesday: DayHours;
  wednesday: DayHours;
  thursday: DayHours;
  friday: DayHours;
  saturday: DayHours;
  sunday: DayHours;
}

export interface SpecialHours {
  id: string;
  date: string; // YYYY-MM-DD
  name: string;
  hours: DayHours;
  description?: string;
  isRecurring?: boolean; // Para feriados anuais
  recurringType?: "yearly" | "monthly" | "weekly";
}

export interface BusinessHoursDB {
  [key: string]: { open?: string; close?: string; closed?: boolean };
}

export interface SpecialHoursDB {
  date: string;
  name?: string;
  open?: string;
  close?: string;
  closed?: boolean;
  description?: string;
  hours?: DayHours;
}

export interface BusinessStatus {
  isOpen: boolean;
  message: string;
  nextChange?: {
    action: "open" | "close";
    time: string;
    message: string;
    minutesUntil: number;
  };
  currentHours?: DayHours;
  todaySchedule?: DayHours;
  timezone: string;
  lastUpdated: string;
}

class BusinessHoursService {
  private restaurantRepository = AppDataSource.getRepository(Restaurant);

  // Horários padrão
  private defaultHours: BusinessHours = {
    monday: { open: "11:00", close: "23:00", isOpen: true },
    tuesday: { open: "11:00", close: "23:00", isOpen: true },
    wednesday: { open: "11:00", close: "23:00", isOpen: true },
    thursday: { open: "11:00", close: "23:00", isOpen: true },
    friday: { open: "11:00", close: "00:00", isOpen: true }, // Fix: 24:00 is invalid, use 00:00
    saturday: { open: "11:00", close: "00:00", isOpen: true }, // Fix: 24:00 is invalid, use 00:00
    sunday: { open: "12:00", close: "22:00", isOpen: true },
  };

  // Converter horários estruturados para o formato do banco
  private getDefaultHoursAsObject(): BusinessHoursDB {
    return {
      monday: { open: "11:00", close: "23:00", closed: false },
      tuesday: { open: "11:00", close: "23:00", closed: false },
      wednesday: { open: "11:00", close: "23:00", closed: false },
      thursday: { open: "11:00", close: "23:00", closed: false },
      friday: { open: "11:00", close: "00:00", closed: false }, // Fix: 24:00 is invalid, use 00:00
      saturday: { open: "11:00", close: "00:00", closed: false }, // Fix: 24:00 is invalid, use 00:00
      sunday: { open: "12:00", close: "22:00", closed: false },
    };
  }

  // Obter horários de funcionamento
  async getBusinessHours(restaurantId: string): Promise<{
    regular: BusinessHoursDB;
    special: SpecialHoursDB[];
    timezone: string;
  }> {
    const restaurant = await this.restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      throw new Error("Restaurante não encontrado");
    }

    const businessHours =
      restaurant.businessHours || this.getDefaultHoursAsObject();
    const specialHours = restaurant.settings?.specialHours || [];
    const timezone =
      restaurant.settings?.schedule?.timezone || "America/Sao_Paulo";

    return {
      regular: businessHours,
      special: specialHours,
      timezone,
    };
  }

  // Atualizar horários de funcionamento
  async updateBusinessHours(
    restaurantId: string,
    businessHours: BusinessHoursDB,
    specialHours?: SpecialHoursDB[],
    timezone?: string
  ): Promise<void> {
    const restaurant = await this.restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      throw new Error("Restaurante não encontrado");
    }

    // Validar horários
    this.validateBusinessHoursObject(businessHours);

    if (specialHours) {
      this.validateSpecialHoursArray(specialHours);
    }

    // Atualizar horários regulares
    restaurant.businessHours = businessHours;

    // Atualizar configurações
    restaurant.settings = {
      ...restaurant.settings,
      schedule: {
        ...restaurant.settings?.schedule,
        timezone:
          timezone ||
          restaurant.settings?.schedule?.timezone ||
          "America/Sao_Paulo",
        operatingHours: businessHours,
      },
      specialHours: specialHours || restaurant.settings?.specialHours || [],
    };

    await this.restaurantRepository.save(restaurant);

    // Limpar cache
    await this.clearStatusCache(restaurantId);
  }

  // Verificar se está aberto agora
  async isOpenNow(restaurantId: string): Promise<boolean> {
    const status = await this.getBusinessStatus(restaurantId);
    return status.isOpen;
  }

  // Obter status completo do negócio
  async getBusinessStatus(restaurantId: string): Promise<BusinessStatus> {
    // Verificar cache primeiro
    const cacheKey = `business_status:${restaurantId}`;
    const cached = await redisClient.getClient().get(cacheKey);

    if (cached) {
      return JSON.parse(cached);
    }

    const restaurant = await this.restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      throw new Error("Restaurante não encontrado");
    }

    const businessHours = restaurant.businessHours || this.defaultHours;
    const specialHours = restaurant.settings?.specialHours || [];
    const timezone =
      restaurant.settings?.schedule?.timezone || "America/Sao_Paulo";

    const now = new Date();
    const today = now.toISOString().split("T")[0]; // YYYY-MM-DD
    const dayOfWeek = this.getDayOfWeek(now);
    const currentTime = this.formatTime(now);

    // Verificar horários especiais primeiro
    const specialToday = specialHours.find((special: any) => {
      if (special.isRecurring) {
        return this.matchesRecurringDate(special, now);
      }
      return special.date === today;
    });

    let todaySchedule: DayHours;
    let isSpecialDay = false;

    if (specialToday) {
      // Se há horários especiais definidos no formato completo, usar eles
      if (
        "hours" in specialToday &&
        specialToday.hours &&
        typeof specialToday.hours === "object" &&
        "open" in specialToday.hours &&
        "close" in specialToday.hours &&
        "isOpen" in specialToday.hours
      ) {
        todaySchedule = specialToday.hours as DayHours;
      } else {
        // Converter do formato DB para DayHours
        todaySchedule = {
          open: specialToday.open || "09:00",
          close: specialToday.close || "18:00",
          isOpen: !specialToday.closed,
        };
      }
      isSpecialDay = true;
    } else {
      const daySchedule = businessHours[dayOfWeek as keyof BusinessHours];
      todaySchedule = (daySchedule as DayHours) || {
        open: "09:00",
        close: "18:00",
        isOpen: true,
      };
    }

    // Verificar se está aberto
    const isOpen = this.isCurrentlyOpen(todaySchedule, currentTime);

    // Calcular próxima mudança
    const nextChange = this.calculateNextChange(
      businessHours as BusinessHours,
      specialHours as SpecialHours[],
      now,
      timezone
    );

    const status: BusinessStatus = {
      isOpen,
      message: isOpen
        ? isSpecialDay
          ? `Aberto (${
              (specialToday as any)?.name ||
              (specialToday as any)?.description ||
              "Horário especial"
            })`
          : "Aberto"
        : isSpecialDay
        ? `Fechado (${
            (specialToday as any)?.name ||
            (specialToday as any)?.description ||
            "Horário especial"
          })`
        : "Fechado",
      nextChange,
      currentHours: todaySchedule,
      todaySchedule,
      timezone,
      lastUpdated: now.toISOString(),
    };

    // Cache por 5 minutos
    await redisClient.getClient().setEx(cacheKey, 300, JSON.stringify(status));

    return status;
  }

  // Obter próximos horários (próximos 7 dias)
  async getUpcomingSchedule(restaurantId: string): Promise<
    Array<{
      date: string;
      dayName: string;
      hours: DayHours;
      isSpecial: boolean;
      specialName?: string;
    }>
  > {
    const { regular, special, timezone } = await this.getBusinessHours(
      restaurantId
    );
    const schedule = [];
    const now = new Date();

    for (let i = 0; i < 7; i++) {
      const date = new Date(now);
      date.setDate(date.getDate() + i);

      const dateStr = date.toISOString().split("T")[0];
      const dayOfWeek = this.getDayOfWeek(date);
      const dayName = this.getDayName(date);

      // Verificar horários especiais
      const specialDay = special.find((s: SpecialHours) => {
        if (s.isRecurring) {
          return this.matchesRecurringDate(s, date);
        }
        return s.date === dateStr;
      });

      // Definir horários para o dia
      let dayHours: DayHours;
      if (specialDay) {
        dayHours = specialDay.hours;
      } else {
        dayHours = regular[dayOfWeek] as DayHours;
      }

      schedule.push({
        date: dateStr,
        dayName,
        hours: dayHours,
        isSpecial: !!specialDay,
        specialName: specialDay?.name || specialDay?.description,
      });
    }

    return schedule;
  }

  // Adicionar horário especial
  async addSpecialHours(
    restaurantId: string,
    specialHours: Omit<SpecialHours, "id">
  ): Promise<string> {
    const restaurant = await this.restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      throw new Error("Restaurante não encontrado");
    }

    const id = `special_${Date.now()}_${Math.random()
      .toString(36)
      .substring(2, 11)}`; // Fix: Use substring instead of deprecated substr
    const newSpecialHours: SpecialHours = { id, ...specialHours };

    this.validateSpecialHours([newSpecialHours]);

    const currentSpecialHours = restaurant.settings?.specialHours || [];

    restaurant.settings = {
      ...restaurant.settings,
      specialHours: [...currentSpecialHours, newSpecialHours],
    };

    await this.restaurantRepository.save(restaurant);
    await this.clearStatusCache(restaurantId);

    return id;
  }

  // Remover horário especial
  async removeSpecialHours(
    restaurantId: string,
    specialId: string
  ): Promise<void> {
    const restaurant = await this.restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      throw new Error("Restaurante não encontrado");
    }

    const currentSpecialHours = restaurant.settings?.specialHours || [];
    const filteredSpecialHours = currentSpecialHours.filter(
      (s: SpecialHours) => s.id !== specialId
    );

    restaurant.settings = {
      ...restaurant.settings,
      specialHours: filteredSpecialHours,
    };

    await this.restaurantRepository.save(restaurant);
    await this.clearStatusCache(restaurantId);
  }

  // Métodos auxiliares privados
  private validateBusinessHours(hours: BusinessHours): void {
    const days = [
      "monday",
      "tuesday",
      "wednesday",
      "thursday",
      "friday",
      "saturday",
      "sunday",
    ];

    for (const day of days) {
      const dayHours = hours[day as keyof BusinessHours];
      if (!dayHours) {
        throw new Error(`Horários para ${day} são obrigatórios`);
      }

      if (dayHours.isOpen) {
        if (!dayHours.open || !dayHours.close) {
          throw new Error(
            `Horários de abertura e fechamento são obrigatórios para ${day}`
          );
        }

        if (
          !this.isValidTime(dayHours.open) ||
          !this.isValidTime(dayHours.close)
        ) {
          throw new Error(`Formato de horário inválido para ${day}`);
        }
      }
    }
  }

  private validateSpecialHours(specialHours: SpecialHours[]): void {
    for (const special of specialHours) {
      if (!special.date || !special.name) {
        throw new Error("Data e nome são obrigatórios para horários especiais");
      }

      if (!this.isValidDate(special.date)) {
        throw new Error("Formato de data inválido");
      }

      if (special.hours.isOpen) {
        if (!special.hours.open || !special.hours.close) {
          throw new Error("Horários de abertura e fechamento são obrigatórios");
        }

        if (
          !this.isValidTime(special.hours.open) ||
          !this.isValidTime(special.hours.close)
        ) {
          throw new Error("Formato de horário inválido");
        }
      }
    }
  }

  private isValidTime(time: string): boolean {
    // Fix: Allow 00:00 for midnight, improve regex
    return /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(time) || time === "00:00";
  }

  private isValidDate(date: string): boolean {
    return /^\d{4}-\d{2}-\d{2}$/.test(date) && !isNaN(Date.parse(date));
  }

  private getDayOfWeek(date: Date): keyof BusinessHours {
    const days: (keyof BusinessHours)[] = [
      "sunday",
      "monday",
      "tuesday",
      "wednesday",
      "thursday",
      "friday",
      "saturday",
    ];
    return days[date.getDay()];
  }

  private getDayName(date: Date): string {
    return date.toLocaleDateString("pt-BR", { weekday: "long" });
  }

  private formatTime(date: Date): string {
    return date.toTimeString().slice(0, 5);
  }

  private isCurrentlyOpen(dayHours: DayHours, currentTime: string): boolean {
    if (!dayHours.isOpen) return false;

    const current = this.timeToMinutes(currentTime);
    const open = this.timeToMinutes(dayHours.open);
    let close = this.timeToMinutes(dayHours.close);

    // Handle midnight crossing (e.g., 23:00 to 02:00)
    if (close <= open) {
      close += 24 * 60; // Add 24 hours
      if (current < open) {
        return current + 24 * 60 < close;
      }
    }

    return current >= open && current < close;
  }

  private timeToMinutes(time: string): number {
    const [hours, minutes] = time.split(":").map(Number);
    // Handle midnight (00:00) as 24:00 for comparison purposes
    const adjustedHours = hours === 0 && time === "00:00" ? 24 : hours;
    return adjustedHours * 60 + minutes;
  }

  private calculateNextChange(
    businessHours: BusinessHours,
    specialHours: SpecialHours[],
    now: Date,
    timezone: string
  ): BusinessStatus["nextChange"] {
    // Implementação simplificada - pode ser expandida
    const dayOfWeek = this.getDayOfWeek(now);
    const todayHours = businessHours[dayOfWeek];
    const currentTime = this.formatTime(now);

    if (!todayHours.isOpen) return undefined;

    const current = this.timeToMinutes(currentTime);
    const open = this.timeToMinutes(todayHours.open);
    const close = this.timeToMinutes(todayHours.close);

    if (current < open) {
      return {
        action: "open",
        time: todayHours.open,
        message: `Abre às ${todayHours.open}`,
        minutesUntil: open - current,
      };
    } else if (current < close) {
      return {
        action: "close",
        time: todayHours.close,
        message: `Fecha às ${todayHours.close}`,
        minutesUntil: close - current,
      };
    }

    return undefined;
  }

  private matchesRecurringDate(special: SpecialHours, date: Date): boolean {
    if (!special.isRecurring) return false;

    const specialDate = new Date(special.date);

    switch (special.recurringType) {
      case "yearly":
        return (
          date.getMonth() === specialDate.getMonth() &&
          date.getDate() === specialDate.getDate()
        );
      case "monthly":
        return date.getDate() === specialDate.getDate();
      case "weekly":
        return date.getDay() === specialDate.getDay();
      default:
        return false;
    }
  }

  // Add missing validation methods
  private validateBusinessHoursObject(businessHours: BusinessHoursDB): void {
    if (!businessHours || typeof businessHours !== "object") {
      throw new Error("Business hours must be an object");
    }

    const validDays = [
      "monday",
      "tuesday",
      "wednesday",
      "thursday",
      "friday",
      "saturday",
      "sunday",
    ];

    for (const [day, hours] of Object.entries(businessHours)) {
      if (!validDays.includes(day)) {
        throw new Error(`Invalid day: ${day}`);
      }

      if (hours && !hours.closed && (!hours.open || !hours.close)) {
        throw new Error(`Open and close times required for ${day}`);
      }

      if (hours?.open && !this.isValidTime(hours.open)) {
        throw new Error(`Invalid open time for ${day}: ${hours.open}`);
      }

      if (hours?.close && !this.isValidTime(hours.close)) {
        throw new Error(`Invalid close time for ${day}: ${hours.close}`);
      }
    }
  }

  private validateSpecialHoursArray(specialHours: SpecialHoursDB[]): void {
    if (!Array.isArray(specialHours)) {
      throw new Error("Special hours must be an array");
    }

    for (const special of specialHours) {
      if (!special.date || !this.isValidDate(special.date)) {
        throw new Error("Valid date is required for special hours");
      }

      if (!special.closed && (!special.open || !special.close)) {
        throw new Error("Open and close times required for special hours");
      }

      if (special.open && !this.isValidTime(special.open)) {
        throw new Error(`Invalid open time for special hours: ${special.open}`);
      }

      if (special.close && !this.isValidTime(special.close)) {
        throw new Error(
          `Invalid close time for special hours: ${special.close}`
        );
      }
    }
  }

  private async clearStatusCache(restaurantId: string): Promise<void> {
    const cacheKey = `business_status:${restaurantId}`;
    await redisClient.getClient().del(cacheKey);
  }
}

export const businessHoursService = new BusinessHoursService();
export default BusinessHoursService;
