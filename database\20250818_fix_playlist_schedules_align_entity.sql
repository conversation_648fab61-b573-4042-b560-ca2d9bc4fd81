-- Ali<PERSON>a playlist_schedules ao modelo PlaylistSchedule (idempotente)
-- - Adiciona colunas: name, description, timeSlots (JSON), mode, settings, isActive
-- - Adiciona timestamps "createdAt" e "updatedAt" (mantendo created_at/updated_at legados)
-- - Torna playlist_id opcional (modelo usa timeSlots.playlistId)
-- - Backfill de dados a partir de colunas legadas quando possível

DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name='playlist_schedules') THEN
    -- name
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_name='playlist_schedules' AND column_name='name'
    ) THEN
      ALTER TABLE playlist_schedules ADD COLUMN name VARCHAR NULL;
    END IF;

    -- description
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_name='playlist_schedules' AND column_name='description'
    ) THEN
      ALTER TABLE playlist_schedules ADD COLUMN description TEXT NULL;
    END IF;

    -- timeSlots (camelCase)
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_name='playlist_schedules' AND column_name='timeSlots'
    ) THEN
      ALTER TABLE playlist_schedules ADD COLUMN "timeSlots" JSON NULL;
    END IF;

    -- isActive (camelCase) espelha is_active legado
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_name='playlist_schedules' AND column_name='isActive'
    ) THEN
      ALTER TABLE playlist_schedules ADD COLUMN "isActive" BOOLEAN DEFAULT TRUE;
    END IF;

    -- mode
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_name='playlist_schedules' AND column_name='mode'
    ) THEN
      ALTER TABLE playlist_schedules ADD COLUMN mode VARCHAR DEFAULT 'normal';
    END IF;

    -- settings
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_name='playlist_schedules' AND column_name='settings'
    ) THEN
      ALTER TABLE playlist_schedules ADD COLUMN settings JSON NULL;
    END IF;

    -- createdAt / updatedAt (camelCase)
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_name='playlist_schedules' AND column_name='createdAt'
    ) THEN
      ALTER TABLE playlist_schedules ADD COLUMN "createdAt" TIMESTAMP DEFAULT NOW();
    END IF;
    IF NOT EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_name='playlist_schedules' AND column_name='updatedAt'
    ) THEN
      ALTER TABLE playlist_schedules ADD COLUMN "updatedAt" TIMESTAMP DEFAULT NOW();
    END IF;

    -- Tornar playlist_id opcional (modelo usa timeSlots.playlistId)
    IF EXISTS (
      SELECT 1 FROM information_schema.columns 
      WHERE table_name='playlist_schedules' AND column_name='playlist_id' AND is_nullable='NO'
    ) THEN
      ALTER TABLE playlist_schedules ALTER COLUMN playlist_id DROP NOT NULL;
    END IF;
  END IF;
END $$;

-- Backfill de dados a partir de colunas legadas
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name='playlist_schedules') THEN
    -- Backfill timeSlots a partir de schedule.timeSlots ou schedule inteiro quando aplicável
    IF EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='playlist_schedules' AND column_name='schedule'
    ) THEN
      UPDATE playlist_schedules
      SET "timeSlots" = CASE
        WHEN jsonb_typeof(schedule) = 'object' AND (schedule ? 'timeSlots') THEN (schedule -> 'timeSlots')::json
        WHEN jsonb_typeof(schedule) = 'array' THEN schedule::json
        ELSE COALESCE("timeSlots", '[]'::json)
      END
      WHERE "timeSlots" IS NULL;
    END IF;

    -- Backfill isActive a partir de is_active legado
    IF EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='playlist_schedules' AND column_name='is_active'
    ) THEN
      UPDATE playlist_schedules
      SET "isActive" = COALESCE("isActive", is_active, TRUE)
      WHERE "isActive" IS NULL;
    END IF;

    -- Backfill createdAt/updatedAt a partir de created_at/updated_at
    IF EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='playlist_schedules' AND column_name='created_at'
    ) THEN
      UPDATE playlist_schedules
      SET "createdAt" = COALESCE("createdAt", created_at)
      WHERE created_at IS NOT NULL AND "createdAt" IS NULL;
    END IF;
    IF EXISTS (
      SELECT 1 FROM information_schema.columns WHERE table_name='playlist_schedules' AND column_name='updated_at'
    ) THEN
      UPDATE playlist_schedules
      SET "updatedAt" = COALESCE("updatedAt", updated_at)
      WHERE updated_at IS NOT NULL AND "updatedAt" IS NULL;
    END IF;
  END IF;
END $$;

-- Índices úteis
CREATE INDEX IF NOT EXISTS idx_playlist_schedules_restaurant ON playlist_schedules(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_playlist_schedules_createdat ON playlist_schedules("createdAt");

-- Trigger para manter updatedAt (usa função já definida em init.sql)
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger WHERE tgname = 'trigger_playlist_schedules_updated_at'
  ) THEN
    CREATE TRIGGER trigger_playlist_schedules_updated_at
      BEFORE UPDATE ON playlist_schedules
      FOR EACH ROW
      EXECUTE FUNCTION update_updated_at_column();
  END IF;
END $$;
