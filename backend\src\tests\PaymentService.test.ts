import { PaymentService, PaymentError, CreatePaymentDto, IPaymentResponse } from '../services/PaymentService';
import { AppDataSource } from '../config/database';
import { Payment } from '../models/Payment';
import { Suggestion } from '../models/Suggestion';
import { ClientSession } from '../models/ClientSession';
import { Repository } from 'typeorm';

// Mock das dependências
jest.mock('../config/database');
jest.mock('../utils/logger');
jest.mock('qrcode');
jest.mock('axios');

describe('PaymentService', () => {
  let paymentService: PaymentService;
  let mockPaymentRepository: jest.Mocked<Repository<Payment>>;
  let mockSuggestionRepository: jest.Mocked<Repository<Suggestion>>;
  let mockSessionRepository: jest.Mocked<Repository<ClientSession>>;

  const mockSuggestion = {
    id: 'suggestion-123',
    title: 'Test Song',
    artist: 'Test Artist',
    restaurant: { id: 'restaurant-123', name: 'Test Restaurant' }
  } as Suggestion;

  const mockSession: Partial<ClientSession> = {
    id: 'session-456',
    sessionToken: 'token-abc',
    clientName: 'Test Client'
  } as any;

  beforeEach(() => {
    // Setup mocks
    mockPaymentRepository = {
      findOne: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
      find: jest.fn(),
    } as any;

    mockSuggestionRepository = {
      findOne: jest.fn(),
    } as any;

    mockSessionRepository = {
      findOne: jest.fn(),
    } as any;

    (AppDataSource.getRepository as jest.Mock)
      .mockReturnValueOnce(mockPaymentRepository)
      .mockReturnValueOnce(mockSuggestionRepository)
      .mockReturnValueOnce(mockSessionRepository);

    paymentService = new PaymentService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Configuração', () => {
    it('deve inicializar com configurações padrão', () => {
      expect(paymentService).toBeInstanceOf(PaymentService);
    });

    it('deve validar configuração do Mercado Pago', () => {
      // Teste implícito - se chegou até aqui, a configuração foi validada
      expect(paymentService).toBeDefined();
    });
  });

  describe('createPixPayment', () => {
    beforeEach(() => {
      mockSuggestionRepository.findOne.mockResolvedValue(mockSuggestion);
  mockSessionRepository.findOne.mockResolvedValue(mockSession as any);
      mockPaymentRepository.findOne.mockResolvedValue(null); // Não existe pagamento anterior
    });

  it('deve criar pagamento PIX com sucesso', async () => {
      const mockPayment = {
        id: 'payment-123',
        qrCode: 'mock-qr-code',
        qrCodeBase64: 'mock-qr-base64',
        ticketUrl: 'mock-ticket-url',
        amount: 200
      };

  mockPaymentRepository.create.mockReturnValue({} as any);
  mockPaymentRepository.save.mockResolvedValue(mockPayment as any);

      const result = await paymentService.createPixPayment('suggestion-123', 'session-456');

  expect(result).toMatchObject({ amount: 200 });
  expect(typeof result.paymentId).toBe('string');
  expect(typeof result.qrCode).toBe('string');
  expect(typeof result.qrCodeBase64).toBe('string');

      expect(mockSuggestionRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'suggestion-123' },
        relations: ['restaurant']
      });
    });

    it('deve lançar erro para sugestão não encontrada', async () => {
      mockSuggestionRepository.findOne.mockResolvedValue(null);

      await expect(
        paymentService.createPixPayment('invalid-suggestion', 'session-456')
      ).rejects.toThrow(PaymentError);

      await expect(
        paymentService.createPixPayment('invalid-suggestion', 'session-456')
      ).rejects.toMatchObject({
        code: 'SUGGESTION_NOT_FOUND',
        statusCode: 404
      });
    });

    it('deve lançar erro para música já paga', async () => {
      const existingPayment = {
        id: 'existing-payment',
        status: 'approved'
      };

      mockPaymentRepository.findOne.mockResolvedValue(existingPayment as any);

      await expect(
        paymentService.createPixPayment('suggestion-123', 'session-456')
      ).rejects.toThrow(PaymentError);

      await expect(
        paymentService.createPixPayment('suggestion-123', 'session-456')
      ).rejects.toMatchObject({
        code: 'ALREADY_PAID',
        statusCode: 409
      });
    });

    it('deve validar dados de entrada', async () => {
      await expect(
        paymentService.createPixPayment('', 'session-456')
      ).rejects.toThrow(PaymentError);

      await expect(
        paymentService.createPixPayment('suggestion-123', '')
      ).rejects.toThrow(PaymentError);
    });

  it('deve criar pagamento mock em caso de falha do Mercado Pago', async () => {
      // Simular falha na API do Mercado Pago
      const mockError = new Error('API Error');
      
      // Mock para simular falha e fallback para mock
      jest.spyOn(paymentService as any, 'createMercadoPagoPayment')
        .mockRejectedValue(mockError);

      const mockPayment = {
        id: 'mock-payment-123',
        qrCode: 'mock-qr-code',
        qrCodeBase64: 'mock-qr-base64',
        ticketUrl: 'mock-ticket-url',
        amount: 200
      };

  mockPaymentRepository.create.mockReturnValue({} as any);
  mockPaymentRepository.save.mockResolvedValue(mockPayment as any);

      const result = await paymentService.createPixPayment('suggestion-123', 'session-456');

  expect(typeof result.paymentId).toBe('string');
      expect(result.amount).toBe(200);
    });
  });

  describe('checkPaymentStatus', () => {
  it('deve retornar status de pagamento existente', async () => {
      const mockPayment = {
        id: 'payment-123',
        status: 'approved',
        statusDetail: 'accredited',
        externalReference: 'suggestion-123',
        amount: 200
      };

      mockPaymentRepository.findOne.mockResolvedValue(mockPayment as any);

      const result = await paymentService.checkPaymentStatus('payment-123');

      expect(result).toMatchObject({
        id: 'payment-123',
        status: 'approved',
        statusDetail: 'accredited',
        externalReference: 'suggestion-123',
        transactionAmount: 2.00
      });
    });

    it('deve lançar erro para pagamento não encontrado', async () => {
      mockPaymentRepository.findOne.mockResolvedValue(null);

      await expect(
        paymentService.checkPaymentStatus('invalid-payment')
      ).rejects.toThrow(PaymentError);
    });
  });

  describe('processWebhook', () => {
    it('deve processar webhook de pagamento aprovado', async () => {
      const webhookData = {
        id: 'webhook-123',
        live_mode: false,
        type: 'payment',
        date_created: new Date().toISOString(),
        application_id: 'app-1',
        user_id: 'user-1',
        version: '1',
        api_version: 'v1',
        action: 'payment.updated',
        data: { id: 'payment-123' }
      } as any;

      const mockPayment = {
        id: 'payment-123',
        status: 'pending',
        suggestionId: 'suggestion-123'
      };

      mockPaymentRepository.findOne.mockResolvedValue(mockPayment as any);
      mockPaymentRepository.save.mockResolvedValue({ ...mockPayment, status: 'approved' } as any);

      await expect(
        paymentService.processWebhook(webhookData)
      ).resolves.not.toThrow();

      expect(mockPaymentRepository.save).toHaveBeenCalled();
    });

    it('deve ignorar webhook de tipo inválido', async () => {
      const webhookData = {
        id: 'webhook-123',
        live_mode: false,
        type: 'invalid-type',
        date_created: new Date().toISOString(),
        application_id: 'app-1',
        user_id: 'user-1',
        version: '1',
        api_version: 'v1',
        action: 'some.action',
        data: { id: 'payment-123' }
      } as any;

      await expect(
        paymentService.processWebhook(webhookData)
      ).resolves.not.toThrow();

      expect(mockPaymentRepository.findOne).not.toHaveBeenCalled();
    });
  });

  describe('getPaymentStats', () => {
  it('deve retornar estatísticas de pagamento', async () => {
      const now = new Date();
      const mockPayments = [
        { amount: 200, status: 'approved', createdAt: now },
        { amount: 200, status: 'approved', createdAt: now },
        { amount: 200, status: 'rejected', createdAt: now }
      ];

      (mockPaymentRepository.createQueryBuilder as any) = jest.fn().mockReturnValue({
        leftJoin: function(){ return this; },
        leftJoinAndSelect: function(){ return this; },
        where: function(){ return this; },
        andWhere: function(){ return this; },
        getMany: async () => mockPayments as any,
      });

  const stats = await paymentService.getPaymentStats({ restaurantId: 'restaurant-123', period: '7d' } as any);

      expect(stats).toMatchObject({
        totalRevenue: 4.00, // R$ 4,00
        totalPayments: 3,
        successfulPayments: 2,
        failedPayments: 1,
        averageAmount: 2.00,
        period: '7d',
        restaurantId: 'restaurant-123'
      });
    });
  });

  describe('Tratamento de Erros', () => {
    it('deve lançar PaymentError com código específico', () => {
      const error = new PaymentError('Test error', 'TEST_ERROR', 400);

      expect(error).toBeInstanceOf(PaymentError);
      expect(error.code).toBe('TEST_ERROR');
      expect(error.statusCode).toBe(400);
      expect(error.isOperational).toBe(true);
    });

    it('deve incluir paymentId no erro quando disponível', () => {
      const error = new PaymentError('Test error', 'TEST_ERROR', 400, true, 'payment-123');

      expect(error.paymentId).toBe('payment-123');
    });
  });
});
