import { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-hot-toast';
import { wsService } from '@/services/websocket';
import { PlayQueue, Suggestion, QueueItem } from '@/types';
import { useAppStore } from '@/store';

// Mantemos um tipo leve para casos em que o backend envia um shape parcial
interface QueueUpdateLike {
  queue: Suggestion[];
  currentlyPlaying?: Suggestion | null;
  estimatedWaitTime?: number;
  estimatedDuration?: number;
}

interface NowPlayingUpdate {
  suggestion: Suggestion;
  position: number;
  estimatedEndTime: string;
}

export const useRealTimeQueue = (restaurantId: string) => {
  const [queue, setQueue] = useState<PlayQueue | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [currentlyPlaying, setCurrentlyPlaying] = useState<Suggestion | QueueItem | null>(null);
  const { setPlayQueue, setCurrentlyPlaying: setStoreCurrentlyPlaying } = useAppStore();

  // Atualizar fila de reprodução
  const updateQueue = useCallback((newQueue: PlayQueue) => {
    setQueue(newQueue);
    setStoreCurrentlyPlaying((newQueue.currentlyPlaying as any) || null);
    setPlayQueue(newQueue);
  }, [setPlayQueue, setStoreCurrentlyPlaying]);

  // Atualizar música atual
  const updateCurrentlyPlaying = useCallback((suggestion: Suggestion | QueueItem | null) => {
    setCurrentlyPlaying(suggestion);
    setStoreCurrentlyPlaying(suggestion as any);
  }, [setStoreCurrentlyPlaying]);

  // Configurar listeners do WebSocket
  useEffect(() => {
    const handleQueueUpdate = (data: PlayQueue | QueueUpdateLike) => {
      console.log('📋 Fila atualizada em tempo real:', data);
      const q = (data as any).queue as Suggestion[];
      const updatedQueue: PlayQueue = {
        queue: q,
        totalItems: q.length,
        currentlyPlaying: (data as any).currentlyPlaying ?? null,
        estimatedDuration:
          (data as any).estimatedDuration ?? q.reduce((total, song) => total + (song.duration || 180), 0),
        estimatedWaitTime: (data as any).estimatedWaitTime || 0,
        lastUpdated: new Date().toISOString(),
      };

      updateQueue(updatedQueue);
      
      // Mostrar notificação se a fila cresceu
      if ((data as any).queue && q.length > (queue?.queue.length || 0)) {
        toast.success('Nova música adicionada à fila!', {
          duration: 3000,
          icon: '🎵'
        });
      }
    };

    const handleNowPlaying = (data: NowPlayingUpdate) => {
      console.log('🎵 Tocando agora:', data);
      
      updateCurrentlyPlaying(data.suggestion);
      
      toast(`Tocando agora: ${data.suggestion.title}`, {
        icon: '🎵',
        duration: 5000,
      });
    };

    const handleSongEnded = (data: { suggestionId: string; nextSong?: Suggestion }) => {
      console.log('⏭️ Música finalizada:', data);
      
      if (data.nextSong) {
        updateCurrentlyPlaying(data.nextSong);
        toast(`Próxima música: ${data.nextSong.title}`, {
          icon: '⏭️',
          duration: 4000,
        });
      } else {
        updateCurrentlyPlaying(null);
      }
    };

    const handleNewSuggestion = (suggestion: Suggestion) => {
      console.log('🎵 Nova sugestão recebida:', suggestion);
      
      // Adicionar à fila apenas quando o backend reemitir 'queue-update'.
      // Aqui notificamos o usuário da nova sugestão.
      toast.success(`Nova música sugerida: ${suggestion.title}`, {
        duration: 4000,
        icon: '🎵'
      });
    };

    const handleConnectionChange = (connected: boolean) => {
      setIsConnected(connected);
      
      if (connected) {
        // Reconectar à sala do restaurante
        wsService.joinRestaurant(restaurantId);
      }
    };

    // Conectar ao WebSocket se não estiver conectado
    if (!wsService.isConnected()) {
      wsService.reconnect();
    }
    
    // Entrar na sala do restaurante
    wsService.joinRestaurant(restaurantId);

    // Registrar listeners
    wsService.on('queue-update', handleQueueUpdate as any);
    wsService.on('now-playing', handleNowPlaying);
    wsService.on('song-ended', handleSongEnded);
    wsService.on('new-suggestion', handleNewSuggestion);
    
    // Listeners de conexão
    wsService.onConnectionChange(handleConnectionChange);
    setIsConnected(wsService.isConnected());

    return () => {
      // Remover listeners
      wsService.off('queue-update', handleQueueUpdate as any);
      wsService.off('now-playing', handleNowPlaying);
      wsService.off('song-ended', handleSongEnded);
      wsService.off('new-suggestion', handleNewSuggestion);
      
      // Sair da sala do restaurante
      wsService.leaveRestaurant(restaurantId);
    };
  }, [restaurantId, queue, updateQueue, updateCurrentlyPlaying]);

  // Função para solicitar atualização da fila
  const refreshQueue = useCallback(() => {
    if (wsService.isConnected()) {
      wsService.publicEmit?.('request-queue-update' as any, { restaurantId });
    }
  }, [restaurantId]);

  // Função para reportar posição atual da música
  const reportPlaybackPosition = useCallback((suggestionId: string, position: number) => {
    if (wsService.isConnected()) {
      wsService.publicEmit?.('playback-position' as any, {
        suggestionId,
        position,
        restaurantId,
      });
    }
  }, [restaurantId]);

  return {
    queue,
    currentlyPlaying,
    isConnected,
    refreshQueue,
    reportPlaybackPosition,
    updateQueue,
    updateCurrentlyPlaying
  };
};

export default useRealTimeQueue;
