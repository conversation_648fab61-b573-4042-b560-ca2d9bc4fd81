import { useEffect } from "react";
import { useAuth } from "@/store";
import { apiService } from "@/services/api";

/**
 * Hook para inicializar o estado de autenticação
 * Verifica se há um token válido e restaura o estado do usuário
 */
export const useAuthInit = () => {
  const { authToken, setUser, setAuthToken, isAuthenticated } = useAuth();

  useEffect(() => {
    const initializeAuth = () => {
      // Verificar se há token no localStorage
      const storedToken = localStorage.getItem("authToken");

      // Se não há token armazenado, limpar estado
      if (!storedToken) {
        if (authToken) {
          setAuthToken(null);
          setUser(null);
        }
        return;
      }

      // Se já temos o token correto, não fazer nada
      if (authToken === storedToken) {
        return;
      }

      // Definir o token no serviço da API
      apiService.setAuthToken(storedToken);

      // Restaurar estado de autenticação
      setAuthToken(storedToken);

      // Simular usuário admin para desenvolvimento
      setUser({
        id: "admin-user",
        name: "Admin",
        email: "<EMAIL>",
        role: "admin",
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      } as any);

      console.log("🔐 Token de autenticação restaurado");
    };

    initializeAuth();
  }, []); // Remover dependências para evitar loops
};

export default useAuthInit;
