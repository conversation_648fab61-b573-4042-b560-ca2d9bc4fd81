import { NotificationService, NotificationError, NotificationType, NotificationPriority, NotificationChannel, INotificationData } from '../services/NotificationService';
import { WebSocketService } from '../services/WebSocketService';

// Mock das dependências
jest.mock('../services/WebSocketService');
jest.mock('../utils/logger');
jest.mock('../config/redis');

describe('NotificationService', () => {
  let notificationService: NotificationService;
  let mockWsService: jest.Mocked<WebSocketService>;

  const mockNotification: INotificationData = {
    type: NotificationType.SUCCESS,
    title: 'Test Notification',
    message: 'This is a test notification',
    priority: NotificationPriority.NORMAL,
    targetRestaurants: ['restaurant-123'],
    channels: [NotificationChannel.WEBSOCKET]
  };

  beforeEach(() => {
    // Setup WebSocket service mock
    mockWsService = {
      emitToRestaurant: jest.fn().mockResolvedValue(undefined),
      emitToAdmins: jest.fn().mockResolvedValue(undefined),
      emitToTable: jest.fn().mockResolvedValue(undefined)
    } as any;

    (WebSocketService.getInstance as jest.Mock).mockReturnValue(mockWsService);

    notificationService = NotificationService.getInstance();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Singleton', () => {
    it('deve ser singleton', () => {
      const instance1 = NotificationService.getInstance();
      const instance2 = NotificationService.getInstance();
      
      expect(instance1).toBe(instance2);
    });
  });

  describe('sendNotification', () => {
    it('deve enviar notificação com sucesso', async () => {
      const notificationId = await notificationService.sendNotification(mockNotification);

      expect(notificationId).toBeDefined();
      expect(typeof notificationId).toBe('string');
    });

    it('deve validar dados de entrada', async () => {
      const invalidNotification = {
        type: 'invalid-type',
        title: '',
        message: 'Test'
      } as any;

      await expect(
        notificationService.sendNotification(invalidNotification)
      ).rejects.toThrow(NotificationError);
    });

    it('deve processar notificação urgente imediatamente', async () => {
      const urgentNotification = {
        ...mockNotification,
        priority: NotificationPriority.URGENT
      };

      await notificationService.sendNotification(urgentNotification);

      // Verificar se foi enviada via WebSocket
      expect(mockWsService.emitToRestaurant).toHaveBeenCalledWith(
        'restaurant-123',
        'notification',
        expect.objectContaining({
          type: NotificationType.SUCCESS,
          priority: NotificationPriority.URGENT
        })
      );
    });

    it('deve adicionar notificação normal à fila', async () => {
      const normalNotification = {
        ...mockNotification,
        priority: NotificationPriority.NORMAL
      };

      const notificationId = await notificationService.sendNotification(normalNotification);

      expect(notificationId).toBeDefined();
      // A notificação deve ser processada pela fila (não imediatamente)
    });

    it('deve enviar para múltiplos restaurantes', async () => {
      const multiTargetNotification = {
        ...mockNotification,
        targetRestaurants: ['restaurant-123', 'restaurant-456']
      };

      await notificationService.sendNotification(multiTargetNotification);

      expect(mockWsService.emitToRestaurant).toHaveBeenCalledTimes(2);
      expect(mockWsService.emitToRestaurant).toHaveBeenCalledWith(
        'restaurant-123',
        'notification',
        expect.any(Object)
      );
      expect(mockWsService.emitToRestaurant).toHaveBeenCalledWith(
        'restaurant-456',
        'notification',
        expect.any(Object)
      );
    });
  });

  describe('Tipos de Notificação', () => {
    it('deve enviar notificação de sucesso', async () => {
      const successNotification = {
        ...mockNotification,
        type: NotificationType.SUCCESS
      };

      await notificationService.sendNotification(successNotification);

      expect(mockWsService.emitToRestaurant).toHaveBeenCalledWith(
        'restaurant-123',
        'notification',
        expect.objectContaining({
          type: NotificationType.SUCCESS
        })
      );
    });

    it('deve enviar notificação de erro', async () => {
      const errorNotification = {
        ...mockNotification,
        type: NotificationType.ERROR,
        priority: NotificationPriority.HIGH
      };

      await notificationService.sendNotification(errorNotification);

      expect(mockWsService.emitToRestaurant).toHaveBeenCalledWith(
        'restaurant-123',
        'notification',
        expect.objectContaining({
          type: NotificationType.ERROR,
          priority: NotificationPriority.HIGH
        })
      );
    });

    it('deve enviar notificação de música', async () => {
      const musicNotification = {
        ...mockNotification,
        type: NotificationType.MUSIC,
        data: {
          songTitle: 'Test Song',
          artist: 'Test Artist'
        }
      };

      await notificationService.sendNotification(musicNotification);

      expect(mockWsService.emitToRestaurant).toHaveBeenCalledWith(
        'restaurant-123',
        'notification',
        expect.objectContaining({
          type: NotificationType.MUSIC,
          data: expect.objectContaining({
            songTitle: 'Test Song',
            artist: 'Test Artist'
          })
        })
      );
    });

    it('deve enviar notificação de pagamento', async () => {
      const paymentNotification = {
        ...mockNotification,
        type: NotificationType.PAYMENT,
        data: {
          amount: 2.00,
          paymentId: 'payment-123'
        }
      };

      await notificationService.sendNotification(paymentNotification);

      expect(mockWsService.emitToRestaurant).toHaveBeenCalledWith(
        'restaurant-123',
        'notification',
        expect.objectContaining({
          type: NotificationType.PAYMENT
        })
      );
    });
  });

  describe('Sistema de Prioridades', () => {
    it('deve processar notificações por ordem de prioridade', async () => {
      // Adicionar notificações com diferentes prioridades
      const lowPriority = { ...mockNotification, priority: NotificationPriority.LOW };
      const highPriority = { ...mockNotification, priority: NotificationPriority.HIGH };
      const normalPriority = { ...mockNotification, priority: NotificationPriority.NORMAL };

      await notificationService.sendNotification(lowPriority);
      await notificationService.sendNotification(normalPriority);
      await notificationService.sendNotification(highPriority);

      // Aguardar processamento da fila
      await new Promise(resolve => setTimeout(resolve, 1100));

      // Verificar se foram processadas (ordem pode variar devido ao processamento assíncrono)
      expect(mockWsService.emitToRestaurant).toHaveBeenCalledTimes(3);
    });
  });

  describe('Tratamento de Erros', () => {
    it('deve lançar NotificationError com código específico', () => {
      const error = new NotificationError('Test error', 'TEST_ERROR', 400, true, 'notification-123');

      expect(error).toBeInstanceOf(NotificationError);
      expect(error.code).toBe('TEST_ERROR');
      expect(error.statusCode).toBe(400);
      expect(error.notificationId).toBe('notification-123');
    });

    it('deve lidar com falhas do WebSocket graciosamente', async () => {
      mockWsService.emitToRestaurant.mockRejectedValue(new Error('WebSocket error'));

      // Não deve lançar erro mesmo se WebSocket falhar
      await expect(
        notificationService.sendNotification(mockNotification)
      ).resolves.not.toThrow();
    });

    it('deve lançar erro para dados inválidos', async () => {
      const invalidNotification = {
        type: 'invalid',
        title: '',
        message: ''
      } as any;

      await expect(
        notificationService.sendNotification(invalidNotification)
      ).rejects.toThrow(NotificationError);

      await expect(
        notificationService.sendNotification(invalidNotification)
      ).rejects.toMatchObject({
        code: 'VALIDATION_ERROR',
        statusCode: 400
      });
    });
  });

  describe('Estatísticas', () => {
    it('deve retornar estatísticas de notificações', () => {
      const stats = notificationService.getStats();
      
      expect(stats).toEqual(
        expect.objectContaining({
          totalSent: expect.any(Number),
          totalDelivered: expect.any(Number),
          totalFailed: expect.any(Number),
          deliveryRate: expect.any(Number),
          byType: expect.any(Object),
          byPriority: expect.any(Object)
        })
      );
    });
  });

  describe('Histórico', () => {
    it('deve manter histórico de notificações', async () => {
      await notificationService.sendNotification(mockNotification);

      const history = notificationService.getHistory();
      expect(Array.isArray(history)).toBe(true);
    });

    it('deve limitar tamanho do histórico', async () => {
      // Este teste seria mais efetivo com um limite menor para teste
      const history = notificationService.getHistory();
      expect(history.length).toBeLessThanOrEqual(1000);
    });
  });

  describe('Integração com WebSocketService', () => {
    it('deve usar WebSocketService para entrega', async () => {
      await notificationService.sendNotification(mockNotification);

      expect(mockWsService.emitToRestaurant).toHaveBeenCalledWith(
        'restaurant-123',
        'notification',
        expect.objectContaining({
          type: NotificationType.SUCCESS,
          title: 'Test Notification',
          message: 'This is a test notification'
        })
      );
    });

    it('deve funcionar mesmo se WebSocketService falhar', async () => {
      mockWsService.emitToRestaurant.mockRejectedValue(new Error('Connection failed'));

      await expect(
        notificationService.sendNotification(mockNotification)
      ).resolves.not.toThrow();
    });
  });
});
