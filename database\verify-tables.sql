-- ============================================
-- SCRIPT DE VERIFICAÇÃO DE TABELAS
-- Sistema de Playlist Interativa para Restaurantes
-- ============================================

-- Verificar se todas as tabelas essenciais existem
SELECT 
    'Verificando tabelas do sistema...' as status;

-- Listar todas as tabelas criadas
SELECT 
    schemaname,
    tablename,
    tableowner,
    hasindexes,
    hasrules,
    hastriggers
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY tablename;

-- Verificar tabelas específicas
SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users') 
        THEN '✅ Tabela users existe'
        ELSE '❌ Tabela users NÃO existe'
    END as users_status;

SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'restaurants') 
        THEN '✅ Tabela restaurants existe'
        ELSE '❌ Tabela restaurants NÃO existe'
    END as restaurants_status;

SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'playlists') 
        THEN '✅ Tabela playlists existe'
        ELSE '❌ Tabela playlists NÃO existe'
    END as playlists_status;

SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'suggestions') 
        THEN '✅ Tabela suggestions existe'
        ELSE '❌ Tabela suggestions NÃO existe'
    END as suggestions_status;

SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'client_sessions') 
        THEN '✅ Tabela client_sessions existe'
        ELSE '❌ Tabela client_sessions NÃO existe'
    END as client_sessions_status;

SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'genres') 
        THEN '✅ Tabela genres existe'
        ELSE '❌ Tabela genres NÃO existe'
    END as genres_status;

SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'playlist_tracks') 
        THEN '✅ Tabela playlist_tracks existe'
        ELSE '❌ Tabela playlist_tracks NÃO existe'
    END as playlist_tracks_status;

SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'playlist_schedules') 
        THEN '✅ Tabela playlist_schedules existe'
        ELSE '❌ Tabela playlist_schedules NÃO existe'
    END as playlist_schedules_status;

-- Verificar tipos ENUM
SELECT 
    typname as enum_name,
    array_agg(enumlabel ORDER BY enumsortorder) as enum_values
FROM pg_type t
JOIN pg_enum e ON t.oid = e.enumtypid
WHERE typname IN ('user_role', 'restaurant_status', 'playlist_type', 'playlist_status', 'suggestion_status', 'suggestion_source', 'vote_type', 'rule_type', 'rule_action', 'play_status')
GROUP BY typname
ORDER BY typname;

-- Verificar extensões
SELECT 
    extname as extension_name,
    extversion as version
FROM pg_extension
WHERE extname IN ('uuid-ossp', 'pg_trgm');

-- Contar registros em cada tabela
SELECT 
    'users' as table_name,
    COUNT(*) as record_count
FROM users
UNION ALL
SELECT 
    'restaurants' as table_name,
    COUNT(*) as record_count
FROM restaurants
UNION ALL
SELECT 
    'playlists' as table_name,
    COUNT(*) as record_count
FROM playlists
UNION ALL
SELECT 
    'suggestions' as table_name,
    COUNT(*) as record_count
FROM suggestions
UNION ALL
SELECT 
    'client_sessions' as table_name,
    COUNT(*) as record_count
FROM client_sessions;

-- Verificar se há dados de exemplo
SELECT 
    'Verificando dados de exemplo...' as status;

SELECT 
    id,
    name,
    status,
    "isActive"
FROM restaurants
LIMIT 5;

SELECT 
    id,
    name,
    email,
    role,
    "isActive"
FROM users
LIMIT 5;

-- Verificar integridade referencial
SELECT 
    'Verificando integridade referencial...' as status;

-- Verificar se há playlists órfãs (sem restaurante)
SELECT 
    COUNT(*) as orphaned_playlists
FROM playlists p
LEFT JOIN restaurants r ON p.restaurant_id = r.id
WHERE r.id IS NULL;

-- Verificar se há sugestões órfãs (sem restaurante)
SELECT 
    COUNT(*) as orphaned_suggestions
FROM suggestions s
LEFT JOIN restaurants r ON s.restaurant_id = r.id
WHERE r.id IS NULL;

-- Verificar se há sessões órfãs (sem restaurante)
SELECT 
    COUNT(*) as orphaned_sessions
FROM client_sessions cs
LEFT JOIN restaurants r ON cs.restaurant_id = r.id
WHERE r.id IS NULL;

SELECT 
    'Verificação concluída!' as final_status;
