-- Corrige índices para playlist_tracks conforme colunas reais (snake_case)

-- Índices seguros e idempotentes
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_playlist ON playlist_tracks(playlist_id);
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_position ON playlist_tracks(playlist_id, position);
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_youtube ON playlist_tracks(youtube_video_id);
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_active ON playlist_tracks(is_active);
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_genre ON playlist_tracks(genre);
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_mood ON playlist_tracks(mood);
