import request from 'supertest';
import express from 'express';
import bodyParser from 'body-parser';
import { TestDataSource, initializeTestDatabase, cleanTestData, closeTestDatabase } from '../config/test-database';
import { Repository } from 'typeorm';
import { Restaurant, RestaurantStatus } from '../models/Restaurant';
import { Suggestion, SuggestionStatus } from '../models/Suggestion';
import { QueueItem } from '../models/QueueItem';

// Mock do AppDataSource para usar TestDataSource (usar require interno para evitar hoisting)
jest.mock('../config/database', () => ({
  AppDataSource: {
    getRepository: (entity: any) => {
      const { TestDataSource } = require('../config/test-database');
      return TestDataSource.getRepository(entity);
    }
  }
}));

let app: express.Express;
let playbackQueueRouter: any;

// Mock YouTubeService para não depender da API real
jest.mock('../services/YouTubeService', () => ({
  YouTubeService: jest.fn().mockImplementation(() => ({
    getVideoInfo: jest.fn().mockResolvedValue({
      title: 'Música Real de Teste',
      artist: 'Artista Real',
      duration: 180,
      thumbnailUrl: 'https://img.youtube.com/vi/test/mqdefault.jpg'
    })
  }))
}));

// Usa uma música "real" configurável por env; fallback para um ID conhecido
const REAL_VIDEO_ID = process.env.TEST_YT_VIDEO_ID || '9bZkp7q19f0'; // PSY - GANGNAM STYLE

describe('Fluxo real de QueueItem (criar, tocar, completar)', () => {
  let restaurantRepo: Repository<Restaurant>;
  let suggestionRepo: Repository<Suggestion>;
  let queueItemRepo: Repository<QueueItem>;
  let restaurant: Restaurant;
  const sessionToken = 'session-test-token';

  beforeAll(async () => {
    await initializeTestDatabase();
    restaurantRepo = TestDataSource.getRepository(Restaurant);
    suggestionRepo = TestDataSource.getRepository(Suggestion);
    queueItemRepo = TestDataSource.getRepository(QueueItem);

  // Carregar router somente após DB estar pronto para evitar getRepository prematuro
  playbackQueueRouter = (await import('../routes/playbackQueue')).default;
  // Montar app
  app = express();
  app.use(bodyParser.json());
  app.use('/api/v1/playback-queue', playbackQueueRouter);
  });

  afterAll(async () => {
    await closeTestDatabase();
  });

  beforeEach(async () => {
    await cleanTestData();

    restaurant = restaurantRepo.create({
      id: 'test-restaurant-' + Date.now(),
      name: 'R Teste',
      email: '<EMAIL>',
      status: RestaurantStatus.ACTIVE,
      isActive: true,
      settings: { playlist: { defaultVolume: 70 } }
    });
    await restaurantRepo.save(restaurant);
  });

  it('cria sugestão + queueItem e marca como playing/completed', async () => {
    // 1) Criar sugestão e adicionar à fila
    const resAdd = await request(app)
      .post('/api/v1/playback-queue/add')
      .set('x-session-id', sessionToken)
      .send({
        restaurantId: restaurant.id,
        song: {
          videoId: REAL_VIDEO_ID,
          title: 'Teste Song',
          artist: 'Teste Artist'
        },
        isPriority: false
      });

    expect(resAdd.status).toBe(201);
    expect(resAdd.body?.success).toBe(true);
    const queueItem = resAdd.body?.queueItem;
    expect(queueItem).toBeDefined();
    expect(queueItem.youtubeVideoId).toBe(REAL_VIDEO_ID);
    expect(queueItem.position).toBe(1);

    // Verificar no banco
    const savedQueue = await queueItemRepo.find();
    expect(savedQueue.length).toBe(1);

    // 2) Marcar como tocando
    const suggestionId = queueItem.suggestionId as string;
    const resPlay = await request(app)
      .post(`/api/v1/playback-queue/play/${suggestionId}`)
      .send({});

    expect(resPlay.status).toBe(200);

    const sAfterPlay = await suggestionRepo.findOne({ where: { id: suggestionId } });
    expect(sAfterPlay?.status).toBe(SuggestionStatus.PLAYING);

    // 3) Marcar como concluída
    const resComplete = await request(app)
      .post(`/api/v1/playback-queue/complete/${suggestionId}`)
      .send({});

    expect(resComplete.status).toBe(200);

    const sAfterComplete = await suggestionRepo.findOne({ where: { id: suggestionId } });
    expect(sAfterComplete?.status).toBe(SuggestionStatus.COMPLETED);
  });
});
