-- Idempotent fixes for suggestions table columns and enums
-- Ensure required columns exist and have correct types/constraints

-- suggestions.status: ensure allowed values include 'completed'
DO $$
BEGIN
	IF NOT EXISTS (
		SELECT 1 FROM pg_type t
		JOIN pg_enum e ON t.oid = e.enumtypid
		WHERE t.typname = 'suggestion_status' AND e.enumlabel = 'completed'
	) THEN
		ALTER TYPE suggestion_status ADD VALUE IF NOT EXISTS 'completed';
	END IF;
EXCEPTION WHEN duplicate_object THEN
	-- ignore concurrent add
END $$;

-- Ensure queue_position exists
ALTER TABLE IF EXISTS suggestions
	ADD COLUMN IF NOT EXISTS queue_position INTEGER NULL;

-- Ensure completed_at exists
ALTER TABLE IF EXISTS suggestions
	ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP NULL;

-- Ensure is_paid and payment_amount exists with correct types
ALTER TABLE IF EXISTS suggestions
	ADD COLUMN IF NOT EXISTS is_paid BOOLEAN DEFAULT FALSE,
	ADD COLUMN IF NOT EXISTS payment_amount DECIMAL(10,2) NULL;

-- Ensure client_session_id string token and session_id uuid both exist
ALTER TABLE IF EXISTS suggestions
	ADD COLUMN IF NOT EXISTS client_session_id VARCHAR NULL,
	ADD COLUMN IF NOT EXISTS session_id UUID NULL;

-- Ensure youtube_video_id exists (mirror camelCase if needed) and set NOT NULL on whichever exists
DO $$
BEGIN
	IF EXISTS (
		SELECT 1 FROM information_schema.tables WHERE table_name='suggestions'
	) THEN
		-- If snake_case column missing but camelCase exists, create and backfill
		IF NOT EXISTS (
			SELECT 1 FROM information_schema.columns 
			WHERE table_name='suggestions' AND column_name='youtube_video_id'
		) AND EXISTS (
			SELECT 1 FROM information_schema.columns 
			WHERE table_name='suggestions' AND column_name='youtubeVideoId'
		) THEN
			EXECUTE 'ALTER TABLE suggestions ADD COLUMN youtube_video_id VARCHAR(100)';
			EXECUTE 'UPDATE suggestions SET youtube_video_id = "youtubeVideoId" WHERE youtube_video_id IS NULL';
		END IF;

		-- Ensure there is an index for youtube_video_id if the column exists
		IF EXISTS (
			SELECT 1 FROM information_schema.columns 
			WHERE table_name='suggestions' AND column_name='youtube_video_id'
		) THEN
			-- create index if not exists
			CREATE INDEX IF NOT EXISTS idx_suggestions_youtube_snake ON suggestions(youtube_video_id);
		END IF;

		-- Finally, set NOT NULL on the existing youtube id column
		IF EXISTS (
			SELECT 1 FROM information_schema.columns 
			WHERE table_name='suggestions' AND column_name='youtube_video_id'
		) THEN
			EXECUTE 'ALTER TABLE suggestions ALTER COLUMN youtube_video_id SET NOT NULL';
		ELSIF EXISTS (
			SELECT 1 FROM information_schema.columns 
			WHERE table_name='suggestions' AND column_name='youtubeVideoId'
		) THEN
			EXECUTE 'ALTER TABLE suggestions ALTER COLUMN "youtubeVideoId" SET NOT NULL';
		END IF;
	END IF;
END $$;

