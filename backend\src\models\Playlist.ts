import {
  Entity,
  PrimaryGeneratedColumn,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from "typeorm";
import { Restaurant } from "./Restaurant";
import { Suggestion } from "./Suggestion";

export enum PlaylistType {
  CUSTOM = "custom",
  YOUTUBE = "youtube",
  YOUTUBE_IMPORT = "youtube_import",
  AUTO_GENERATED = "auto_generated",
  SUGGESTIONS = "suggestions",
}

export enum PlaylistStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
  ARCHIVED = "archived",
  DELETED = "deleted",
}

@Entity("playlists")
@Index(["restaurant", "status"])
@Index(["restaurant", "type"])
@Index(["isDefault"])
export class Playlist {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar" })
  name: string;

  @Column({ type: "text", nullable: true })
  description: string;

  @Column({
    type: "enum",
    enum: PlaylistType,
    default: PlaylistType.CUSTOM,
  })
  type: PlaylistType;

  @Column({
    type: "enum",
    enum: PlaylistStatus,
    default: PlaylistStatus.ACTIVE,
  })
  status: PlaylistStatus;

  @Column({ name: "youtubePlaylistId", type: "varchar", nullable: true })
  youtubePlaylistId: string;

  @Column({ name: "coverImage", type: "varchar", nullable: true })
  coverImage: string;

  @Column({ name: "genreTags", type: "json", nullable: true })
  genreTags: string[];

  @Column({ name: "moodTags", type: "json", nullable: true })
  moodTags: string[];

  @Column({ type: "boolean", default: false })
  isDefault: boolean;

  @Column({ type: "boolean", default: true })
  isPublic: boolean;

  @Column({ name: "execution_order", type: "integer", nullable: true })
  executionOrder: number;

  @Column({ type: "integer", default: 0 })
  trackCount: number;

  @Column({ type: "integer", default: 0 })
  totalDuration: number; // em segundos

  @Column({ type: "integer", default: 0 })
  playCount: number;

  @Column({ type: "float", default: 0 })
  averageRating: number;

  @Column({ type: "json", nullable: true })
  tracks: Array<{
    youtubeVideoId: string;
    title: string;
    artist: string;
    duration: number;
    thumbnailUrl: string;
    addedAt: string;
    addedBy?: string;
    position: number;
  }>;

  @Column({ type: "json", nullable: true })
  settings: {
    shuffle?: boolean;
    repeat?: "none" | "one" | "all";
    autoPlay?: boolean;
    crossfade?: number;
    volume?: number;
    allowVoting?: boolean;
    allowSuggestions?: boolean;
    maxSuggestionsPerUser?: number;
    autoReorderByVotes?: boolean;
    reorderThreshold?: number;
  };

  @Column({ type: "json", nullable: true })
  schedule: {
    enabled?: boolean;
    timeSlots?: Array<{
      dayOfWeek: number; // 0-6 (domingo-sábado)
      startTime: string; // HH:mm
      endTime: string; // HH:mm
    }>;
    dateRange?: {
      startDate?: string; // YYYY-MM-DD
      endDate?: string; // YYYY-MM-DD
    };
  };

  @ManyToOne(() => Restaurant, (restaurant) => restaurant.playlists, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "restaurant_id" })
  restaurant: Restaurant;

  @OneToMany(() => Suggestion, (suggestion) => suggestion.playlist)
  suggestions: Suggestion[];

  @CreateDateColumn({ name: "created_at" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updated_at" })
  updatedAt: Date;

  // Métodos da instância

  // Adicionar track à playlist
  addTrack(track: {
    youtubeVideoId: string;
    title: string;
    artist: string;
    duration: number;
    thumbnailUrl: string;
    addedBy?: string;
  }): void {
    if (!this.tracks) {
      this.tracks = [];
    }

    const newTrack = {
      ...track,
      addedAt: new Date().toISOString(),
      position: this.tracks.length,
    };

    this.tracks.push(newTrack);
    this.trackCount = this.tracks.length;
    this.totalDuration += track.duration;
  }

  // Remover track da playlist
  removeTrack(youtubeVideoId: string): boolean {
    if (!this.tracks) {
      return false;
    }

    const trackIndex = this.tracks.findIndex(
      (t) => t.youtubeVideoId === youtubeVideoId
    );
    if (trackIndex === -1) {
      return false;
    }

    const removedTrack = this.tracks[trackIndex];
    this.tracks.splice(trackIndex, 1);

    // Reordenar posições
    this.tracks.forEach((track, index) => {
      track.position = index;
    });

    this.trackCount = this.tracks.length;
    this.totalDuration -= removedTrack.duration;

    return true;
  }

  // Verificar se está ativa no horário atual
  isActiveNow(): boolean {
    if (this.status !== PlaylistStatus.ACTIVE) {
      return false;
    }

    if (!this.schedule?.enabled) {
      return true;
    }

    const now = new Date();
    const currentDay = now.getDay();
    const currentTime = now.toTimeString().slice(0, 5); // HH:mm

    // Verificar range de datas
    if (this.schedule.dateRange) {
      const today = now.toISOString().split("T")[0];
      if (
        this.schedule.dateRange.startDate &&
        today < this.schedule.dateRange.startDate
      ) {
        return false;
      }
      if (
        this.schedule.dateRange.endDate &&
        today > this.schedule.dateRange.endDate
      ) {
        return false;
      }
    }

    // Verificar horários do dia
    if (this.schedule.timeSlots) {
      const todaySlots = this.schedule.timeSlots.filter(
        (slot) => slot.dayOfWeek === currentDay
      );
      if (todaySlots.length === 0) {
        return false;
      }

      return todaySlots.some(
        (slot) => currentTime >= slot.startTime && currentTime <= slot.endTime
      );
    }

    return true;
  }

  // Obter duração formatada
  getFormattedDuration(): string {
    const hours = Math.floor(this.totalDuration / 3600);
    const minutes = Math.floor((this.totalDuration % 3600) / 60);

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  }

  // Serialização para JSON público
  toPublicJSON() {
    return {
      id: this.id,
      name: this.name,
      description: this.description,
      type: this.type,
      coverImage: this.coverImage,
      genreTags: this.genreTags,
      moodTags: this.moodTags,
      isPublic: this.isPublic,
      trackCount: this.trackCount,
      totalDuration: this.totalDuration,
      formattedDuration: this.getFormattedDuration(),
      playCount: this.playCount,
      averageRating: this.averageRating,
      isActiveNow: this.isActiveNow(),
      settings: {
        allowVoting: this.settings?.allowVoting,
        allowSuggestions: this.settings?.allowSuggestions,
      },
    };
  }
}
