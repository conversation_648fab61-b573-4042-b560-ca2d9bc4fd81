import { AppDataSource } from "../config/database";
import { User, UserRole } from "../models/User";
import { Restaurant } from "../models/Restaurant";

/**
 * Seed idempotente para criar um usuário admin no restaurante demo.
 * Variáveis de ambiente opcionais:
 *  - ADMIN_EMAIL (default: <EMAIL>)
 *  - ADMIN_PASSWORD (default: admin123)
 *  - ADMIN_NAME (default: Admin Demo)
 *  - ADMIN_RESTAURANT_ID (default: demo-restaurant)
 */
async function main() {
  const email = process.env.ADMIN_EMAIL || "<EMAIL>";
  const password = process.env.ADMIN_PASSWORD || "admin123";
  const name = process.env.ADMIN_NAME || "Admin Demo";
  const restaurantId = process.env.ADMIN_RESTAURANT_ID || "demo-restaurant";

  await AppDataSource.initialize();

  const userRepo = AppDataSource.getRepository(User);
  const restaurantRepo = AppDataSource.getRepository(Restaurant);

  let restaurant = await restaurantRepo.findOne({ where: { id: restaurantId } });
  if (!restaurant) {
    restaurant = restaurantRepo.create({
      id: restaurantId,
      name: "Restaurante Demo",
      isActive: true,
    } as Partial<Restaurant>);
    await restaurantRepo.save(restaurant);
    console.log(`✅ Restaurante '${restaurantId}' criado`);
  }

  let user = await userRepo.findOne({ where: { email } });
  if (!user) {
    user = userRepo.create({
      email,
      name,
      password, // será hasheada pelos hooks do modelo
      role: UserRole.ADMIN,
      isActive: true,
      restaurant,
    });
    await userRepo.save(user);
    console.log(`✅ Usuário admin criado: ${email}`);
  } else {
    let updated = false;
    if (!user.isActive) {
      user.isActive = true;
      updated = true;
    }
    if (restaurant && (!user.restaurant || user.restaurant.id !== restaurant.id)) {
      user.restaurant = restaurant;
      updated = true;
    }
    if (process.env.ADMIN_PASSWORD) {
      user.password = password; // rehash via hook
      updated = true;
    }
    if (updated) {
      await userRepo.save(user);
      console.log(`🔁 Usuário admin atualizado: ${email}`);
    } else {
      console.log(`ℹ️ Usuário admin já existe: ${email}`);
    }
  }

  await AppDataSource.destroy();
}

main()
  .then(() => {
    console.log("✅ Seed concluído com sucesso");
    process.exit(0);
  })
  .catch((err) => {
    console.error("❌ Erro no seed:", err);
    process.exit(1);
  });
