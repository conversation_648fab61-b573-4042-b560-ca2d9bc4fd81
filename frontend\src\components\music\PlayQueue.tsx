import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Music,
  Clock,
  Play,
  Users,
  Crown,
} from "lucide-react";
import { toast } from "react-hot-toast";

// Componentes
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import VoteButton from "@/components/ui/VoteButton";

// Hooks e serviços
import { apiService } from "@/services/api";
import { useAppStore } from "@/store";

// Tipos
import { PlayQueue as PlayQueueType } from "@/types";

interface PlayQueueProps {
  queue: PlayQueueType | null;
  isLoading: boolean;
  onVote?: () => void;
}

const PlayQueue: React.FC<PlayQueueProps> = ({ queue, isLoading, onVote }) => {
  const [votingStates, setVotingStates] = useState<Record<string, boolean>>({});
  const { setLoading } = useAppStore();

  // Função para votar em uma sugestão
  const handleVote = async (suggestionId: string, voteType: "up" | "down") => {
    if (votingStates[suggestionId]) return;

    setVotingStates((prev) => ({ ...prev, [suggestionId]: true }));
    setLoading("voting", true);

    try {
      await apiService.voteSuggestion(suggestionId, voteType);
      toast.success(
        `Voto ${voteType === "up" ? "positivo" : "negativo"} registrado!`
      );

      // Rastrear votos do usuário para estatísticas
  const votesTodayKey = `votesToday_${
        suggestionId.split("-")[0] || "demo-restaurant"
      }`;
      const currentVotes = parseInt(localStorage.getItem(votesTodayKey) || "0");
      localStorage.setItem(votesTodayKey, (currentVotes + 1).toString());

      onVote?.();
    } catch (error: any) {
      const message = error.response?.data?.message || "Erro ao votar";
      toast.error(message);
    } finally {
      setVotingStates((prev) => ({ ...prev, [suggestionId]: false }));
      setLoading("voting", false);
    }
  };

  // Duração é exibida já formatada nos dados; função removida para evitar noUnused

  // Função para formatar tempo estimado
  const formatEstimatedTime = (totalSeconds: number): string => {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  if (isLoading) {
    return (
      <div className="card p-8 text-center">
        <LoadingSpinner size="lg" />
        <p className="mt-4 text-gray-600 dark:text-gray-400">
          Carregando fila de reprodução...
        </p>
      </div>
    );
  }

  if (!queue || queue.queue.length === 0) {
    return (
      <div className="card p-8 text-center">
        <div className="w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
          <Music className="w-8 h-8 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Fila vazia
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Seja o primeiro a sugerir uma música! Use a aba "Buscar Música" para
          adicionar suas sugestões.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Estatísticas da fila */}
      <div className="card p-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Fila de Reprodução
        </h2>

        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
              {queue.totalItems ?? queue.queue.length}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Músicas na fila
            </div>
          </div>

          <div className="text-center">
            <div className="text-2xl font-bold text-secondary-600 dark:text-secondary-400">
              {formatEstimatedTime(queue.estimatedDuration || (Array.isArray((queue as any).stats?.totalDuration) ? 0 : ((queue as any).stats?.totalDuration ?? 0)))}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Tempo estimado
            </div>
          </div>

      <div className="text-center col-span-2 md:col-span-1">
            <div className="text-2xl font-bold text-accent-600 dark:text-accent-400">
  {queue.queue.reduce((sum, item: any) => sum + (item.voteCount ?? item.votes ?? 0), 0)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Total de votos
            </div>
          </div>
        </div>
      </div>

      {/* Lista da fila */}
      <div className="space-y-3">
        <AnimatePresence>
          {queue.queue.map((suggestion: any, index) => (
            <motion.div
              key={suggestion.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ delay: index * 0.05 }}
              className={`queue-item ${
                index === 0
                  ? "bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800"
                  : "bg-white dark:bg-gray-800"
              } rounded-lg shadow-sm`}
            >
              {/* Posição na fila */}
              <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                {index === 0 ? (
                  <Crown className="w-4 h-4 text-yellow-500 flex-shrink-0" />
                ) : (
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400 leading-none">
                    {index + 1}
                  </span>
                )}
              </div>

              {/* Thumbnail */}
              <div className="flex-shrink-0">
                <div className="relative w-16 h-12 rounded-lg overflow-hidden bg-gray-200 dark:bg-gray-700">
                  <img
                    src={suggestion.thumbnailUrl}
                    alt={suggestion.title}
                    className="w-full h-full object-cover"
                    loading="lazy"
                  />
                  {index === 0 && (
                    <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                      <Play className="w-4 h-4 text-white flex-shrink-0" />
                    </div>
                  )}
                </div>
              </div>

              {/* Informações da música */}
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-gray-900 dark:text-white truncate">
                  {suggestion.title}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                  {suggestion.artist}
                </p>

                <div className="flex items-center space-x-3 mt-1 text-xs text-gray-500 dark:text-gray-500">
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3" />
                    <span>{suggestion.formattedDuration ?? (typeof suggestion.duration === 'number' ? `${Math.floor(suggestion.duration/60)}:${String(Math.floor(suggestion.duration%60)).padStart(2,"0")}` : suggestion.duration)}</span>
                  </div>

                  {suggestion.isExplicit && (
                    <span className="px-1.5 py-0.5 bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 rounded text-xs">
                      Explícito
                    </span>
                  )}

                  {suggestion.isLive && (
                    <span className="px-1.5 py-0.5 bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 rounded text-xs">
                      Ao vivo
                    </span>
                  )}
                </div>
              </div>

              {/* Votos e ações */}
              <div className="flex items-center space-x-2">
                {/* Contador de votos */}
                <div className="text-center">
                  <div
                    className={`text-lg font-bold ${
                      (suggestion.voteCount ?? suggestion.votes ?? 0) > 0
                        ? "text-green-600 dark:text-green-400"
                        : (suggestion.voteCount ?? suggestion.votes ?? 0) < 0
                        ? "text-red-600 dark:text-red-400"
                        : "text-gray-600 dark:text-gray-400"
                    }`}
                  >
                    {(suggestion.voteCount ?? suggestion.votes ?? 0) > 0 ? "+" : ""}
                    {suggestion.voteCount ?? suggestion.votes ?? 0}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-500">
                    votos
                  </div>
                </div>

                {/* Botões de voto */}
                {(suggestion.canBeVoted ?? true) && (
                  <div className="flex flex-col space-y-1">
                    <VoteButton
                      type="up"
                      onClick={() => handleVote(suggestion.suggestionId ?? suggestion.id, "up")}
                      disabled={votingStates[suggestion.suggestionId ?? suggestion.id]}
                      count={suggestion.upvotes ?? 0}
                      size="sm"
                    />

                    <VoteButton
                      type="down"
                      onClick={() => handleVote(suggestion.suggestionId ?? suggestion.id, "down")}
                      disabled={votingStates[suggestion.suggestionId ?? suggestion.id]}
                      count={suggestion.downvotes ?? 0}
                      size="sm"
                    />
                  </div>
                )}
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Informações adicionais */}
      <div className="card p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
              <Users className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <div className="flex-1">
            <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">
              Como funciona a fila?
            </h4>
            <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
              As músicas são ordenadas por votos e tempo de sugestão. Vote nas
              suas favoritas para ajudá-las a subir na fila!
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PlayQueue;
