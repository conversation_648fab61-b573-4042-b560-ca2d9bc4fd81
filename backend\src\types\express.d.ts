import { User } from '../models/User';

declare global {
  namespace Express {
    interface Request {
      user?: User;
      restaurantId?: string;
      sessionId?: string;
      tenant?: {
        id: string;
        name: string;
        subdomain?: string;
        plan?: 'starter' | 'professional' | 'enterprise';
        isActive: boolean;
        settings?: {
          maxTables?: number;
          maxUsers?: number;
          features?: string[];
        };
      };
    }
  }
}
