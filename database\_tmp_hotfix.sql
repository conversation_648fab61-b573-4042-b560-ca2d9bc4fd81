-- Hotfix idempotente para completar schema antes de dormir
-- Enums e labels faltantes
DO $$ BEGIN
  IF NOT EXISTS(SELECT 1 FROM pg_type t WHERE t.typname='vote_type') THEN
    CREATE TYPE vote_type AS ENUM ('up','down');
  END IF;
END $$;

DO $$ BEGIN
  IF EXISTS (SELECT 1 FROM pg_type t WHERE t.typname='play_status') AND NOT EXISTS (
    SELECT 1 FROM pg_type t JOIN pg_enum e ON t.oid=e.enumtypid WHERE t.typname='play_status' AND e.enumlabel='playing') THEN
    ALTER TYPE play_status ADD VALUE IF NOT EXISTS 'playing';
  END IF;
END $$;
DO $$ BEGIN
  IF EXISTS (SELECT 1 FROM pg_type t WHERE t.typname='playlist_status') AND NOT EXISTS (
    SELECT 1 FROM pg_type t JOIN pg_enum e ON t.oid=e.enumtypid WHERE t.typname='playlist_status' AND e.enumlabel='deleted') THEN
    ALTER TYPE playlist_status ADD VALUE IF NOT EXISTS 'deleted';
  END IF;
END $$;
DO $$ BEGIN
  IF EXISTS (SELECT 1 FROM pg_type t WHERE t.typname='suggestion_status') AND NOT EXISTS (
    SELECT 1 FROM pg_type t JOIN pg_enum e ON t.oid=e.enumtypid WHERE t.typname='suggestion_status' AND e.enumlabel='expired') THEN
    ALTER TYPE suggestion_status ADD VALUE IF NOT EXISTS 'expired';
  END IF;
END $$;

-- Tabela lyrics (compatível com o model)
CREATE TABLE IF NOT EXISTS lyrics (
  id VARCHAR PRIMARY KEY,
  title VARCHAR NOT NULL,
  artist VARCHAR NOT NULL,
  album VARCHAR NULL,
  duration INTEGER NOT NULL,
  language VARCHAR DEFAULT 'pt',
  lines JSON NOT NULL,
  source VARCHAR NOT NULL,
  copyright TEXT NULL,
  is_explicit BOOLEAN DEFAULT FALSE,
  has_timestamps BOOLEAN DEFAULT FALSE,
  youtube_video_id VARCHAR NULL,
  metadata JSON NULL,
  view_count INTEGER DEFAULT 0,
  sing_along_count INTEGER DEFAULT 0,
  last_used TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
CREATE INDEX IF NOT EXISTS idx_lyrics_title_artist ON lyrics(title, artist);
CREATE INDEX IF NOT EXISTS idx_lyrics_youtube_video_id ON lyrics(youtube_video_id);
CREATE INDEX IF NOT EXISTS idx_lyrics_source ON lyrics(source);
CREATE INDEX IF NOT EXISTS idx_lyrics_language ON lyrics(language);

-- client_sessions.restaurant_id + FK + índice
ALTER TABLE client_sessions ADD COLUMN IF NOT EXISTS restaurant_id VARCHAR(255);
DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints WHERE table_name='client_sessions' AND constraint_name='fk_client_sessions_restaurant'
  ) THEN
    ALTER TABLE client_sessions ADD CONSTRAINT fk_client_sessions_restaurant FOREIGN KEY (restaurant_id) REFERENCES restaurants(id) ON DELETE CASCADE;
  END IF;
END $$;
CREATE INDEX IF NOT EXISTS idx_client_sessions_restaurant ON client_sessions(restaurant_id);

DO $$ BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='isDefault') THEN
    EXECUTE 'CREATE INDEX IF NOT EXISTS idx_playlists_default ON playlists("isDefault")';
  ELSIF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='is_default') THEN
    EXECUTE 'CREATE INDEX IF NOT EXISTS idx_playlists_default ON playlists(is_default)';
  END IF;
END $$;
CREATE INDEX IF NOT EXISTS idx_playlists_status ON playlists(status);
CREATE INDEX IF NOT EXISTS idx_playlists_type ON playlists(type);
CREATE INDEX IF NOT EXISTS idx_suggestions_created ON suggestions("createdAt");
CREATE INDEX IF NOT EXISTS idx_users_active ON users("isActive");
CREATE INDEX IF NOT EXISTS idx_users_restaurant_role ON users(restaurant_id, role);
-- votes(vote_type) pode ser VARCHAR ou ENUM dependendo do bootstrap; em ambos funciona
CREATE INDEX IF NOT EXISTS idx_votes_type ON votes(vote_type);
CREATE UNIQUE INDEX IF NOT EXISTS uq_vote_suggestion_client_session ON votes(suggestion_id, session_id);

-- Normalização de youtubeVideoId -> youtube_video_id (se ambos existirem)
DO $$ BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='youtubeVideoId'
  ) AND EXISTS (
    SELECT 1 FROM information_schema.columns WHERE table_name='suggestions' AND column_name='youtube_video_id'
  ) THEN
    EXECUTE 'UPDATE suggestions SET youtube_video_id = COALESCE(youtube_video_id, "youtubeVideoId")';
    EXECUTE 'ALTER TABLE suggestions DROP COLUMN "youtubeVideoId"';
  END IF;
END $$;
