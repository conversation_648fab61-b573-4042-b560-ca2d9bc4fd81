import React, { useState, useEffect, useCallback, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useRestaurantContext } from "./RestaurantDashboard";
import { buildApiUrl } from "../../config/api";
import { toast } from "react-hot-toast";
import {
  DragDropContext,
  Droppable,
  Draggable,
  // DropResult removido por não uso
} from "react-beautiful-dnd";
import {
  Plus,
  Music,
  Youtube,
  // Play, Pause removidos por não uso
  Edit,
  Trash2,
  // Eye, Clock removidos por não uso
  Users,
  // Settings, LinkIcon removidos por não uso
  CheckCircle,
  // XCircle removido por não uso
  AlertCircle,
  RefreshCw,
  Download,
  Power,
  Upload,
  Search,
  Calendar,
  // Filter removido por não uso
  ArrowUpDown,
  // Layers, ChevronDown, ChevronUp removidos por não uso
  BarChart,
  // Volume2, History, Share2 removidos por não uso
  Copy,
  X,
  // Info removido por não uso
  List,
  Grid,
  // MoreHorizontal removido por não uso
  Zap,
  // BookOpen, Save removidos por não uso
} from "lucide-react";
// Tipos locais usados neste arquivo
interface VideoItem {
  id: string;
  title: string;
  artist?: string;
  thumbnail?: string;
  duration?: number;
  isAvailable?: boolean;
  genre?: string;
}

interface Schedule {
  enabled: boolean;
  startTime: string;
  endTime: string;
  days: string[];
}

interface Playlist {
  id: string;
  name: string;
  description: string;
  youtubePlaylistId?: string;
  youtubeUrl?: string;
  videoCount: number;
  isActive: boolean;
  isDefault: boolean;
  createdAt: string;
  lastSync?: string;
  thumbnail?: string;
  tags: string[];
  schedule?: Schedule;
  suggestionsCount?: number;
  volume?: number;
  category?: string;
  order?: number;
  totalDuration?: number;
  playCount?: number;
  lastPlayed?: string;
  videos?: VideoItem[];
  // flag auxiliar para destacar playlist ativa via agendamento atual
  __activeBySchedule?: boolean;
}

interface Category {
  id: string;
  name: string;
  color: string; // classes tailwind para badge
}

interface PlaylistCardProps {
  playlist: Playlist;
  onDelete: (id: string) => void;
  onSync: (id: string) => void;
  onToggle: (playlist: Playlist) => void;
  onEdit: () => void;
  onSetDefault: (id: string) => void;
  onOpenDetails: (playlist: Playlist) => void;
  onSchedule: (playlist: Playlist) => void;
  onCopyLink: (id: string) => void;
  onSetCollaborative: (playlist: Playlist) => void;
  onReorderByVotes: (playlist: Playlist) => void;
  formatDate: (date: string) => string;
  getStatusColor: (active: boolean) => string;
  getCategoryColor: (category?: string) => string;
  formatDuration: (seconds: number) => string;
}

// Card em grade (grid)
const GridPlaylistCard: React.FC<PlaylistCardProps> = ({
  playlist,
  onDelete,
  onSync,
  onToggle,
  onEdit,
  onSetDefault,
  onOpenDetails,
  onSchedule,
  onCopyLink,
  onSetCollaborative,
  onReorderByVotes,
  formatDate,
  getStatusColor,
  getCategoryColor,
  formatDuration,
}) => {
  const copyText = useCallback((text: string, label: string) => {
    if (!text) return;
    try {
      navigator.clipboard?.writeText(text);
      toast.success(`${label} copiado`);
    } catch {
      toast.error(`Falha ao copiar ${label}`);
    }
  }, []);
  const [genreFilter, setGenreFilter] = useState<string | null>(null);
  const [showVideos, setShowVideos] = useState<boolean>(false);
  const [page, setPage] = useState<number>(1);
  const pageSize = 50;
  // Extrair gêneros únicos dos vídeos
  const genres = Array.from(new Set((playlist.videos || []).map(v => v.genre).filter(Boolean)));
  // Filtrar vídeos por gênero
  const filteredVideos = genreFilter ? (playlist.videos || []).filter(v => v.genre === genreFilter) : (playlist.videos || []);
  const totalPages = Math.max(1, Math.ceil(filteredVideos.length / pageSize));
  const currentPage = Math.min(page, totalPages);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const visibleVideos = filteredVideos.slice(startIndex, endIndex);
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden"
    >
      <div className="p-4">
        <div className="relative cursor-pointer" onClick={() => onOpenDetails(playlist)}>
          {playlist.thumbnail ? (
            <img
              src={playlist.thumbnail}
              alt={playlist.name}
              className="w-full aspect-video object-cover rounded"
            />
          ) : (
            <div className="w-full aspect-video bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center">
              <Music className="w-6 h-6 text-gray-400" />
            </div>
          )}
          <div className="absolute top-2 left-2 flex gap-1">
            {playlist.isDefault && (
              <span className="px-2 py-0.5 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 text-xs rounded-full">
                Padrão
              </span>
            )}
            <span className={`px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(playlist.isActive ?? true)}`}>
              {playlist.isActive ?? true ? "Ativa" : "Inativa"}
            </span>
            {playlist.category && (
              <span className={`px-2 py-0.5 rounded text-xs ${getCategoryColor(playlist.category)}`}>
                {playlist.category.charAt(0).toUpperCase() + playlist.category.slice(1)}
              </span>
            )}
          </div>
        </div>
        <h3 className="mt-3 font-semibold text-gray-900 dark:text-white truncate">
          {playlist.name}
        </h3>
        {/* IDs para identificação rápida */}
        <div className="mt-1 text-[11px] text-gray-500 dark:text-gray-400 flex flex-wrap gap-2">
          <button
            type="button"
            onClick={() => copyText(playlist.id, 'ID da playlist')}
            className="px-1.5 py-0.5 bg-gray-100 dark:bg-gray-700 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors font-mono"
            title="Copiar ID interno"
          >
            ID: {playlist.id?.slice(0, 8)}…
          </button>
          {playlist.youtubePlaylistId && (
            <button
              type="button"
              onClick={() => copyText(playlist.youtubePlaylistId!, 'YouTube Playlist ID')}
              className="px-1.5 py-0.5 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 rounded hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors font-mono"
              title="Copiar ID do YouTube"
            >
              YT: {playlist.youtubePlaylistId.slice(0, 8)}…
            </button>
          )}
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 min-h-[2.5rem]">
          {playlist.description}
        </p>
        <div className="mt-2 flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
          <span>{playlist.videoCount || 0} vídeos</span>
          {playlist.totalDuration && <span>{formatDuration(playlist.totalDuration)}</span>}
          {playlist.lastSync && (
            <span>Sincronizado: {formatDate(playlist.lastSync).split(" ")[0]}</span>
          )}
          {playlist.schedule?.enabled && (
            <span className="flex items-center"><Calendar className="w-3 h-3 mr-1" />{playlist.schedule.startTime} - {playlist.schedule.endTime}</span>
          )}
        </div>
        <div className="mt-3 flex items-center justify-between">
          <div className="flex items-center gap-1">
            <button
              onClick={() => onToggle(playlist)}
              className={`p-1.5 rounded-lg ${playlist.isActive ?? true ? "text-green-600 hover:bg-green-100 dark:hover:bg-green-900/20" : "text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"}`}
              title={playlist.isActive ?? true ? "Desativar" : "Ativar"}
            >
              <Power className="w-4 h-4" />
            </button>
            <button onClick={() => onSync(playlist.id)} className="p-1.5 text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900/20 rounded-lg" title="Sincronizar com YouTube">
              <RefreshCw className="w-4 h-4" />
            </button>
            <button onClick={() => onSchedule(playlist)} className="p-1.5 text-purple-600 hover:bg-purple-100 dark:hover:bg-purple-900/20 rounded-lg" title="Programar horários">
              <Calendar className="w-4 h-4" />
            </button>
            <button onClick={() => onSetCollaborative(playlist)} className="p-1.5 text-emerald-600 hover:bg-emerald-100 dark:hover:bg-emerald-900/20 rounded-lg" title="Definir como Playlist Colaborativa">
              <Users className="w-4 h-4" />
            </button>
            <button onClick={() => onReorderByVotes(playlist)} className="p-1.5 text-indigo-600 hover:bg-indigo-100 dark:hover:bg-indigo-900/20 rounded-lg" title="Reordenar por Votos (QA)">
              <Zap className="w-4 h-4" />
            </button>
          </div>
          <div className="flex items-center gap-1">
            {playlist.youtubeUrl && (
              <a href={playlist.youtubeUrl} target="_blank" rel="noopener noreferrer" className="p-1.5 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg" title="Ver no YouTube">
                <Youtube className="w-4 h-4" />
              </a>
            )}
            <button onClick={() => onCopyLink(playlist.id)} className="p-1.5 text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" title="Copiar link">
              <Copy className="w-4 h-4" />
            </button>
            <button onClick={onEdit} className="p-1.5 text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg" title="Editar">
              <Edit className="w-4 h-4" />
            </button>
            {!playlist.isDefault && (
              <button onClick={() => onDelete(playlist.id)} className="p-1.5 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg" title="Excluir">
                <Trash2 className="w-4 h-4" />
              </button>
            )}
            {!playlist.isDefault && (
              <button onClick={() => onSetDefault(playlist.id)} className="p-1.5 text-yellow-600 hover:bg-yellow-100 dark:hover:bg-yellow-900/20 rounded-lg" title="Definir como padrão">
                <CheckCircle className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>

        {/* Prévia de vídeos com filtro (colapsável) */}
        <div className="mt-4 border-t border-gray-200 dark:border-gray-700 pt-3">
          <button
            type="button"
            onClick={() => { setShowVideos(v => !v); setPage(1); }}
            className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
          >
            {showVideos ? "Ocultar vídeos" : `Mostrar vídeos (${filteredVideos.length})`}
          </button>

          {showVideos && (
            <div className="mt-3">
              <div className="flex items-center mb-2 gap-2">
                <h4 className="text-sm font-semibold text-gray-900 dark:text-white flex items-center">
                  <Music className="w-4 h-4 mr-2" />
                  Vídeos
                  <span className="ml-2 text-xs font-normal text-gray-500 dark:text-gray-400">
                    ({filteredVideos.length} no total)
                  </span>
                </h4>
                {genres.length > 0 && (
                  <select
                    value={genreFilter || ""}
                    onChange={e => { setGenreFilter(e.target.value || null); setPage(1); }}
                    className="ml-2 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-xs bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="">Todos os gêneros</option>
                    {genres.map((g: any) => (
                      <option key={String(g)} value={String(g)}>
                        {String(g).charAt(0).toUpperCase() + String(g).slice(1)}
                      </option>
                    ))}
                  </select>
                )}
              </div>

              {visibleVideos.length > 0 ? (
                <div className="space-y-2 max-h-60 overflow-y-auto pr-2">
      {visibleVideos.map((video, index) => (
                    <div
                      key={video.id}
                      className="flex items-center gap-3 p-2 hover:bg-gray-50 dark:hover:bg-gray-750 rounded-lg"
                    >
                      <span className="text-xs text-gray-500 dark:text-gray-400 w-6 text-center">
        {startIndex + index + 1}
                      </span>
                      <div className="w-10 h-10 flex-shrink-0">
                        <img
                          src={video.thumbnail}
                          alt={video.title}
                          className="w-full h-full object-cover rounded"
                          onError={(e) => {
                            const target = e.currentTarget as HTMLImageElement;
                            target.src =
                              "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100'%3E%3Crect width='100%25' height='100%25' fill='%23888888'/%3E%3Ctext x='50%25' y='50%25' dominant-baseline='middle' text-anchor='middle' fill='white' font-size='12'%3EVideo%3C/text%3E%3C/svg%3E";
                          }}
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {video.title}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                          {video.artist || "Artista desconhecido"}
                        </p>
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {video.duration != null
                          ? `${Math.floor((video.duration || 0) / 60)}:${(((video.duration || 0) % 60).toString()).padStart(2, "0")}`
                          : "--:--"}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6 bg-gray-50 dark:bg-gray-750 rounded-lg">
                  <Music className="w-10 h-10 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-500 dark:text-gray-400 text-sm">
                    Nenhum vídeo disponível
                  </p>
                </div>
              )}

              {filteredVideos.length > 0 && (
                <div className="mt-2 flex items-center justify-between text-xs text-gray-600 dark:text-gray-300">
                  <button
                    type="button"
                    onClick={() => setPage(p => Math.max(1, p - 1))}
                    disabled={currentPage <= 1}
                    className="px-3 py-1 rounded bg-gray-100 dark:bg-gray-700 disabled:opacity-50 hover:bg-gray-200 dark:hover:bg-gray-600"
                  >
                    Anterior
                  </button>
                  <span>
                    Página {currentPage} de {totalPages}
                  </span>
                  <button
                    type="button"
                    onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                    disabled={currentPage >= totalPages}
                    className="px-3 py-1 rounded bg-gray-100 dark:bg-gray-700 disabled:opacity-50 hover:bg-gray-200 dark:hover:bg-gray-600"
                  >
                    Próxima
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};

// Componente de Card em Lista
const ListPlaylistCard: React.FC<PlaylistCardProps> = ({
  playlist,
  onDelete,
  onSync,
  onToggle,
  onEdit,
  onOpenDetails,
  onSchedule,
  formatDate,
  getStatusColor,
  getCategoryColor,
  formatDuration,
}) => {
  const copyText = useCallback((text: string, label: string) => {
    if (!text) return;
    try {
      navigator.clipboard?.writeText(text);
      toast.success(`${label} copiado`);
    } catch {
      toast.error(`Falha ao copiar ${label}`);
    }
  }, []);
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden"
    >
      <div className="p-4 flex items-center gap-4">
        {/* Thumbnail */}
        <div
          className="w-12 h-12 flex-shrink-0 cursor-pointer"
          onClick={() => onOpenDetails(playlist)}
        >
          {playlist.thumbnail ? (
            <img
              src={playlist.thumbnail}
              alt={playlist.name}
              className="w-full h-full object-cover rounded"
            />
          ) : (
            <div className="w-full h-full bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center">
              <Music className="w-5 h-5 text-gray-400" />
            </div>
          )}
        </div>

        {/* Informações principais */}
        <div
          className="flex-1 min-w-0 cursor-pointer"
          onClick={() => onOpenDetails(playlist)}
        >
          <div className="flex items-center space-x-2">
            {playlist.order && (
              <span className="w-6 h-6 bg-blue-600 text-white text-xs font-bold rounded-full flex items-center justify-center flex-shrink-0">
                {playlist.order}
              </span>
            )}
            <h3 className="font-semibold text-gray-900 dark:text-white truncate">
              {playlist.name}
            </h3>
            {playlist.isDefault && (
              <span className="px-2 py-0.5 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 text-xs rounded-full">
                Padrão
              </span>
            )}
            <span
              className={`px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                playlist.isActive ?? true
              )}`}
            >
              {playlist.isActive ?? true ? "Ativa" : "Inativa"}
            </span>
            {((playlist as any).__activeBySchedule) && (
              <span className="px-2 py-0.5 rounded-full text-xs bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-300">
                Ativa pelo agendamento agora
              </span>
            )}
            {playlist.category && (
              <span
                className={`px-2 py-0.5 rounded text-xs ${getCategoryColor(
                  playlist.category
                )}`}
              >
                {playlist.category.charAt(0).toUpperCase() +
                  playlist.category.slice(1)}
              </span>
            )}
          </div>

          <div className="text-sm text-gray-600 dark:text-gray-400 truncate">
            {playlist.description}
          </div>

          {/* IDs para identificação rápida */}
          <div className="mt-1 text-[11px] text-gray-500 dark:text-gray-400 flex flex-wrap gap-2">
            <button
              type="button"
              onClick={() => copyText(playlist.id, 'ID da playlist')}
              className="px-1.5 py-0.5 bg-gray-100 dark:bg-gray-700 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors font-mono"
              title="Copiar ID interno"
            >
              ID: {playlist.id?.slice(0, 8)}…
            </button>
            {playlist.youtubePlaylistId && (
              <button
                type="button"
                onClick={() => copyText(playlist.youtubePlaylistId!, 'YouTube Playlist ID')}
                className="px-1.5 py-0.5 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 rounded hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors font-mono"
                title="Copiar ID do YouTube"
              >
                YT: {playlist.youtubePlaylistId.slice(0, 8)}…
              </button>
            )}
          </div>

          <div className="flex items-center space-x-3 mt-1 text-xs text-gray-500 dark:text-gray-400">
            <span>{playlist.videoCount || 0} vídeos</span>
            {playlist.totalDuration && (
              <span>{formatDuration(playlist.totalDuration)}</span>
            )}
            {playlist.lastSync && (
              <span>
                Sincronizado: {formatDate(playlist.lastSync).split(" ")[0]}
              </span>
            )}
            {playlist.schedule?.enabled && (
              <span className="flex items-center">
                <Calendar className="w-3 h-3 mr-1" />
                {playlist.schedule.startTime} - {playlist.schedule.endTime}
              </span>
            )}
            {playlist.isActive && (
              <span className="px-2 py-0.5 bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-400 rounded-full">
                Colaborativa ativa
              </span>
            )}
          </div>
        </div>

  {/* Ações */}
  <div className="flex items-center gap-1 flex-shrink-0 flex-wrap max-w-full justify-end">
          <button
            onClick={() => onToggle(playlist)}
            className={`p-1.5 rounded-lg ${
              playlist.isActive ?? true
                ? "text-green-600 hover:bg-green-100 dark:hover:bg-green-900/20"
                : "text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"
            }`}
            title={playlist.isActive ?? true ? "Desativar" : "Ativar"}
          >
            <Power className="w-4 h-4" />
          </button>

          <button
            onClick={() => onSync(playlist.id)}
            className="p-1.5 text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900/20 rounded-lg"
            title="Sincronizar com YouTube"
          >
            <RefreshCw className="w-4 h-4" />
          </button>

          <button
            onClick={() => onSchedule(playlist)}
            className="p-1.5 text-purple-600 hover:bg-purple-100 dark:hover:bg-purple-900/20 rounded-lg"
            title="Programar horários"
          >
            <Calendar className="w-4 h-4" />
          </button>

          {playlist.youtubeUrl && (
            <a
              href={playlist.youtubeUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="p-1.5 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg"
              title="Ver no YouTube"
            >
              <Youtube className="w-4 h-4" />
            </a>
          )}

          <button
            onClick={onEdit}
            className="p-1.5 text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
            title="Editar"
          >
            <Edit className="w-4 h-4" />
          </button>

          {!playlist.isDefault ? (
            <button
              onClick={() => onDelete(playlist.id)}
              className="p-1.5 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded-lg"
              title="Excluir"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          ) : (
            <div className="w-[26px]">
              {/* Espaçador para manter alinhamento */}
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};

// Componente de detalhes da playlist
const PlaylistDetails: React.FC<{
  playlist: Playlist;
  onClose: () => void;
  onEdit: () => void;
  onSync: (id: string) => void;
  formatDate: (date: string) => string;
  formatDuration: (seconds: number) => string;
}> = ({ playlist, onClose, onEdit, onSync, formatDate, formatDuration }) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    >
      <motion.div
        initial={{ scale: 0.9, y: 20 }}
        animate={{ scale: 1, y: 0 }}
        exit={{ scale: 0.9, y: 20 }}
        className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-3xl relative overflow-hidden max-h-[90vh] flex flex-col"
      >
        <div className="p-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
            Detalhes da Playlist
          </h3>
          <div className="flex items-center gap-2">
            <button
              onClick={() => onSync(playlist.id)}
              className="p-2 text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900/20 rounded-lg"
              title="Sincronizar com YouTube"
            >
              <RefreshCw className="w-5 h-5" />
            </button>
            <button
              onClick={onEdit}
              className="p-2 text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
              title="Editar playlist"
            >
              <Edit className="w-5 h-5" />
            </button>
            <button
              onClick={onClose}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        <div className="overflow-y-auto flex-1 p-6">
          {/* Cabeçalho da playlist */}
          <div className="flex flex-col md:flex-row gap-6 mb-6">
            <div className="flex-shrink-0 w-full md:w-48">
              {playlist.thumbnail ? (
                <img
                  src={playlist.thumbnail}
                  alt={playlist.name}
                  className="w-full h-auto object-cover rounded-lg"
                />
              ) : (
                <div className="w-full aspect-video bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                  <Music className="w-12 h-12 text-gray-400" />
                </div>
              )}
            </div>

            <div className="flex-1">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                {playlist.name}
              </h2>
              <p className="text-gray-700 dark:text-gray-300 mb-3">
                {playlist.description || "Sem descrição"}
              </p>

              <div className="flex flex-wrap gap-2 mb-3">
                {playlist.isDefault && (
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 text-xs rounded-full">
                    Padrão
                  </span>
                )}
                <span
                  className={`px-2 py-1 rounded-full text-xs font-medium ${
                    playlist.isActive
                      ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                      : "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
                  }`}
                >
                  {playlist.isActive ? "Ativa" : "Inativa"}
                </span>
                {playlist.category && (
                  <span className="px-2 py-1 bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400 text-xs rounded">
                    {playlist.category.charAt(0).toUpperCase() +
                      playlist.category.slice(1)}
                  </span>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500 dark:text-gray-400">
                    ID YouTube:
                  </span>
                  <span className="ml-2 text-gray-700 dark:text-gray-300">
                    {playlist.youtubePlaylistId || "N/A"}
                  </span>
                </div>
                <div>
                  <span className="text-gray-500 dark:text-gray-400">
                    Criada em:
                  </span>
                  <span className="ml-2 text-gray-700 dark:text-gray-300">
                    {formatDate(playlist.createdAt)}
                  </span>
                </div>
                <div>
                  <span className="text-gray-500 dark:text-gray-400">
                    Última sincronização:
                  </span>
                  <span className="ml-2 text-gray-700 dark:text-gray-300">
                    {playlist.lastSync
                      ? formatDate(playlist.lastSync)
                      : "Nunca"}
                  </span>
                </div>
                <div>
                  <span className="text-gray-500 dark:text-gray-400">
                    Volume:
                  </span>
                  <span className="ml-2 text-gray-700 dark:text-gray-300">
                    {playlist.volume || 80}%
                  </span>
                </div>
                <div>
                  <span className="text-gray-500 dark:text-gray-400">
                    Duração total:
                  </span>
                  <span className="ml-2 text-gray-700 dark:text-gray-300">
                    {playlist.totalDuration
                      ? formatDuration(playlist.totalDuration)
                      : "Desconhecida"}
                  </span>
                </div>
                <div>
                  <span className="text-gray-500 dark:text-gray-400">
                    Número de vídeos:
                  </span>
                  <span className="ml-2 text-gray-700 dark:text-gray-300">
                    {playlist.videoCount || 0}
                  </span>
                </div>
              </div>

              {/* Tags */}
              {playlist.tags && playlist.tags.length > 0 && (
                <div className="mt-4">
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Tags:
                  </h4>
                  <div className="flex flex-wrap gap-1">
                    {playlist.tags.map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Programação */}
              {playlist.schedule?.enabled && (
                <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <h4 className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-2 flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    Programação
                  </h4>
                  <div className="text-sm text-blue-700 dark:text-blue-400">
                    <p>
                      Horário: {playlist.schedule.startTime} -{" "}
                      {playlist.schedule.endTime}
                    </p>
                    <p>
                      Dias:{" "}
                      {playlist.schedule.days.length === 7
                        ? "Todos os dias"
                        : playlist.schedule.days
                            .map((day) => {
                              const dayMap: Record<string, string> = {
                                monday: "Segunda",
                                tuesday: "Terça",
                                wednesday: "Quarta",
                                thursday: "Quinta",
                                friday: "Sexta",
                                saturday: "Sábado",
                                sunday: "Domingo",
                              };
                              return dayMap[day] || day;
                            })
                            .join(", ")}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

      {/* Lista de vídeos + filtro de gênero
        Removido deste escopo: esta seção pertence ao GridPlaylistCard, onde as
        variáveis de filtro (genres, genreFilter, filteredVideos) estão declaradas. */}

          {/* Stats e analytics */}
          {playlist.playCount !== undefined && (
            <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-750 rounded-lg">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                <BarChart className="w-5 h-5 mr-2" />
                Estatísticas de Reprodução
              </h3>

              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Total de Reproduções
                  </p>
                  <p className="text-xl font-bold text-gray-900 dark:text-white">
                    {playlist.playCount}
                  </p>
                </div>

                {playlist.lastPlayed && (
                  <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Última Reprodução
                    </p>
                    <p className="text-xl font-bold text-gray-900 dark:text-white">
                      {formatDate(playlist.lastPlayed)}
                    </p>
                  </div>
                )}

                {playlist.suggestionsCount !== undefined && (
                  <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm">
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Sugestões dos Clientes
                    </p>
                    <p className="text-xl font-bold text-gray-900 dark:text-white">
                      {playlist.suggestionsCount}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        <div className="border-t border-gray-200 dark:border-gray-700 p-4 flex justify-between">
          <div>
            {playlist.youtubeUrl && (
              <a
                href={playlist.youtubeUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                <Youtube className="w-4 h-4" />
                <span>Abrir no YouTube</span>
              </a>
            )}
          </div>
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
          >
            Fechar
          </button>
        </div>
      </motion.div>
    </motion.div>
  );
};

// Componente principal
const PlaylistManager: React.FC = () => {
  // Obter o restaurantId do contexto
  const { restaurantId } = useRestaurantContext();

  // Estados principais
  const [playlists, setPlaylists] = useState<Playlist[]>([]);
  const [loading, setLoading] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingPlaylist, setEditingPlaylist] = useState<Playlist | null>(null);
  const [selectedPlaylist, setSelectedPlaylist] = useState<Playlist | null>(
    null
  );
  const [error, setError] = useState<string | null>(null);
  const [view, setView] = useState<"grid" | "list">("grid");
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Estados para filtragem e ordenação
  const [searchQuery, setSearchQuery] = useState("");
  const [filterActive, setFilterActive] = useState<boolean | null>(null);
  const [sortBy, setSortBy] = useState<"name" | "date" | "count" | "lastSync" | "order">(
    "order"
  );
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [currentCategory, setCurrentCategory] = useState<string | null>(null);

  // Estado para nova playlist
  const [newPlaylist, setNewPlaylist] = useState({
    name: "",
    description: "",
    youtubeUrl: "",
    tags: "",
    isActive: true,
    category: "",
    volume: 80,
    schedule: {
      enabled: false,
      startTime: "08:00",
      endTime: "22:00",
      days: [
        "monday",
        "tuesday",
        "wednesday",
        "thursday",
        "friday",
        "saturday",
        "sunday",
      ],
    },
  });

  // Estados para formulário de schedule
  const [showScheduleForm, setShowScheduleForm] = useState(false);
  const [schedulingPlaylist, setSchedulingPlaylist] = useState<Playlist | null>(
    null
  );
  const [scheduleSettings, setScheduleSettings] = useState<Schedule>({
    enabled: false,
    startTime: "08:00",
    endTime: "22:00",
    days: [
      "monday",
      "tuesday",
      "wednesday",
      "thursday",
      "friday",
      "saturday",
      "sunday",
    ],
  });

  // ID do restaurante obtido do contexto

  // Categorias de playlists
  const categories: Category[] = [
    {
      id: "ambient",
      name: "Ambiente",
      color: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400",
    },
    {
      id: "upbeat",
      name: "Animadas",
      color: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400",
    },
    {
      id: "relaxing",
      name: "Relaxantes",
      color:
        "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400",
    },
    {
      id: "jazz",
      name: "Jazz",
      color:
        "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400",
    },
    {
      id: "international",
      name: "Internacional",
      color:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400",
    },
    {
      id: "seasonal",
      name: "Sazonal",
      color:
        "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400",
    },
  ];

  // Dias da semana para o agendamento
  const weekDays = [
    { id: "monday", label: "Segunda" },
    { id: "tuesday", label: "Terça" },
    { id: "wednesday", label: "Quarta" },
    { id: "thursday", label: "Quinta" },
    { id: "friday", label: "Sexta" },
    { id: "saturday", label: "Sábado" },
    { id: "sunday", label: "Domingo" },
  ];

  // Função helper para criar headers autenticados
  const getAuthHeaders = (contentType: string = "application/json"): Record<string, string> => {
    const headers: Record<string, string> = {};
    
    if (contentType) {
      headers["Content-Type"] = contentType;
    }
    
    const authToken = localStorage.getItem("authToken");
    console.log(`🔑 Auth token exists: ${!!authToken}, value: ${authToken?.substring(0, 20)}...`);
    
    if (authToken) {
      headers.Authorization = `Bearer ${authToken}`;
    }
    
    console.log(`📋 Headers criados:`, headers);
    return headers;
  };

  // Carregar playlists
  const loadPlaylists = useCallback(async () => {
    console.log(`🔍 DEBUG: loadPlaylists called with restaurantId: ${restaurantId}`);

    if (!restaurantId) {
      console.log(
        "❌ RestaurantId não encontrado, não é possível carregar playlists"
      );
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Buscar playlists reais do backend usando a API configurada
      const url = buildApiUrl(`/playlists/restaurant/${restaurantId}`);
      console.log(
        `🔄 Carregando playlists do restaurante: ${restaurantId} via ${url}`
      );

      const response = await fetch(url, {
        method: "GET",
        headers: getAuthHeaders(),
      });

      console.log(`📡 Response status: ${response.status}`);

      if (!response.ok) {
        throw new Error(`Erro ao carregar playlists: ${response.status}`);
      }

      const data = await response.json();
      console.log(`✅ Resposta da API:`, data);

      let list: Playlist[] = [];
      if (data.playlists && Array.isArray(data.playlists)) {
        console.log(`✅ Encontradas ${data.playlists.length} playlists`);
        list = data.playlists;
      } else {
        console.log(
          "⚠️ API não retornou playlists válidas, carregando dados de exemplo"
        );
        // Carregar dados de exemplo se a API não retornar playlists
        loadExamplePlaylists();
        return;
      }

      // Consultar agendamento atual para marcar "Ativa pelo agendamento agora"
      try {
        const schedRes = await fetch(buildApiUrl(`/playlist-schedules/${restaurantId}/current`), {
          method: "GET",
          headers: getAuthHeaders(),
        });
        if (schedRes.ok) {
          const schedJson = await schedRes.json();
          const currentId = schedJson?.current?.playlist?.playlistId || schedJson?.current?.playlist?.playlist?.playlistId || schedJson?.current?.playlist?.id;
          if (currentId) {
            list = list.map((p) => (p.id === currentId ? { ...p, __activeBySchedule: true as any } : { ...p, __activeBySchedule: false as any }));
          }
        }
      } catch (e) {
        console.warn("Não foi possível obter agendamento atual:", e);
      }

      setPlaylists(list);
    } catch (error: any) {
      console.error("Erro ao carregar playlists:", error);
      setError(error.message || "Erro ao carregar playlists");
      toast.error("Erro ao carregar playlists. Carregando dados de exemplo.");

      // Carregar dados de exemplo em caso de erro
      loadExamplePlaylists();
    } finally {
      setLoading(false);
    }
  }, [restaurantId]);

  // Carregar playlists de exemplo para demonstração
  const loadExamplePlaylists = () => {
    const examplePlaylists: Playlist[] = [
      {
        id: "pl-1",
        name: "Música Ambiente Almoço",
        description: "Playlist suave para o horário de almoço",
        youtubePlaylistId: "PLR7XO54Pktt8EfB-fKT9y8jEzQjnUniom",
        youtubeUrl:
          "https://www.youtube.com/playlist?list=PLR7XO54Pktt8EfB-fKT9y8jEzQjnUniom",
        videoCount: 42,
        isActive: true,
        isDefault: true,
        createdAt: "2023-05-15T10:23:45Z",
        lastSync: "2024-07-28T14:30:12Z",
        thumbnail: "https://i.ytimg.com/vi/5qap5aO4i9A/mqdefault.jpg",
        tags: ["ambiente", "jazz", "relaxante"],
        suggestionsCount: 12,
        schedule: {
          enabled: true,
          startTime: "11:00",
          endTime: "15:00",
          days: [
            "monday",
            "tuesday",
            "wednesday",
            "thursday",
            "friday",
            "saturday",
            "sunday",
          ],
        },
        volume: 75,
        category: "ambient",
        order: 1,
        totalDuration: 9540, // 2h39m em segundos
        playCount: 128,
        lastPlayed: "2024-08-02T15:45:23Z",
        videos: [
          {
            id: "v-1",
            title: "Autumn Jazz Mix",
            artist: "Coffee Shop Vibes",
            thumbnail: "https://i.ytimg.com/vi/5qap5aO4i9A/mqdefault.jpg",
            duration: 3600,
            isAvailable: true,
          },
          {
            id: "v-2",
            title: "Smooth Bossa Nova",
            artist: "Jazz Café",
            thumbnail: "https://i.ytimg.com/vi/2ccaHpy5Ewo/mqdefault.jpg",
            duration: 2400,
            isAvailable: true,
          },
        ],
      },
      {
        id: "pl-2",
        name: "Happy Hour",
        description: "Música animada para o final da tarde",
        youtubePlaylistId: "PLgzTt0k8mXzEk586ze4BjvDXR7c-TUSnx",
        youtubeUrl:
          "https://www.youtube.com/playlist?list=PLgzTt0k8mXzEk586ze4BjvDXR7c-TUSnx",
        videoCount: 28,
        isActive: true,
        isDefault: false,
        createdAt: "2023-06-20T16:45:23Z",
        lastSync: "2024-07-20T18:15:45Z",
        thumbnail: "https://i.ytimg.com/vi/36YnV9STBqc/mqdefault.jpg",
        tags: ["pop", "animada", "contemporânea"],
        suggestionsCount: 8,
        schedule: {
          enabled: true,
          startTime: "17:00",
          endTime: "20:00",
          days: ["thursday", "friday", "saturday"],
        },
        volume: 85,
        category: "upbeat",
        order: 2,
        totalDuration: 5460, // 1h31m em segundos
        playCount: 84,
        lastPlayed: "2024-08-01T19:12:56Z",
      },
      {
        id: "pl-3",
        name: "Jantar Romântico",
        description: "Músicas suaves para jantares especiais",
        youtubePlaylistId: "PLQog_FHUHAFUDDQPOTeAWSHwzFV1Zz5PZ",
        youtubeUrl:
          "https://www.youtube.com/playlist?list=PLQog_FHUHAFUDDQPOTeAWSHwzFV1Zz5PZ",
        videoCount: 35,
        isActive: false,
        isDefault: false,
        createdAt: "2023-07-10T20:10:30Z",
        lastSync: "2024-06-15T22:40:18Z",
        thumbnail: "https://i.ytimg.com/vi/gdLLRj1Ge7g/mqdefault.jpg",
        tags: ["romântica", "clássica", "instrumental"],
        schedule: {
          enabled: false,
          startTime: "19:00",
          endTime: "23:00",
          days: ["friday", "saturday", "sunday"],
        },
        volume: 65,
        category: "relaxing",
        order: 3,
        totalDuration: 7200, // 2h em segundos
        playCount: 32,
        lastPlayed: "2024-07-15T21:08:47Z",
      },
      {
        id: "pl-4",
        name: "Brunch de Domingo",
        description: "Playlist leve para o brunch de domingo",
        youtubePlaylistId: "PLQdw4_EElB3OeIE9Gqu-2kBwN2SQbM6P7",
        youtubeUrl:
          "https://www.youtube.com/playlist?list=PLQdw4_EElB3OeIE9Gqu-2kBwN2SQbM6P7",
        videoCount: 22,
        isActive: true,
        isDefault: false,
        createdAt: "2023-08-05T09:30:15Z",
        lastSync: "2024-07-25T10:20:33Z",
        thumbnail: "https://i.ytimg.com/vi/MYPVQccHhAQ/mqdefault.jpg",
        tags: ["acoustic", "folk", "indie"],
        schedule: {
          enabled: true,
          startTime: "10:00",
          endTime: "14:00",
          days: ["sunday"],
        },
        volume: 70,
        category: "ambient",
        order: 4,
        totalDuration: 4500, // 1h15m em segundos
        playCount: 28,
        lastPlayed: "2024-07-28T11:35:22Z",
      },
    ];

    setPlaylists(examplePlaylists);
  };

  // Carregar playlists ao iniciar
  useEffect(() => {
    console.log(`📋 PlaylistManager: restaurantId = ${restaurantId}`);
    if (restaurantId) {
      console.log(
        `📋 PlaylistManager: Iniciando carregamento de playlists para ${restaurantId}`
      );
      loadPlaylists();
    } else {
      console.log(`📋 PlaylistManager: restaurantId não disponível ainda`);
    }
  }, [restaurantId, loadPlaylists]);

  // Extrair ID da playlist do YouTube da URL
  const extractPlaylistId = (url: string): string | null => {
    const regex = /[?&]list=([^#\&\?]*)/;
    const match = url.match(regex);
    return match ? match[1] : null;
  };

  // Adicionar nova playlist
  const addPlaylist = async () => {
    // Validação
    if (!newPlaylist.name.trim()) {
      toast.error("Nome da playlist é obrigatório");
      return;
    }

    if (!newPlaylist.youtubeUrl.trim()) {
      toast.error("URL da playlist do YouTube é obrigatória");
      return;
    }

    const playlistId = extractPlaylistId(newPlaylist.youtubeUrl);
    if (!playlistId) {
      toast.error("URL da playlist do YouTube inválida");
      return;
    }

    setLoading(true);
    try {
      // Preparar os dados
      const playlistData = {
        restaurantId,
        name: newPlaylist.name,
        description: newPlaylist.description,
        youtubeUrl: newPlaylist.youtubeUrl,
        youtubePlaylistId: playlistId,
        tags: newPlaylist.tags
          .split(",")
          .map((tag) => tag.trim())
          .filter(Boolean),
        isActive: newPlaylist.isActive,
        category: newPlaylist.category || undefined,
        volume: newPlaylist.volume || 80,
        schedule: newPlaylist.schedule.enabled
          ? newPlaylist.schedule
          : undefined,
      };

      // Criar playlist via API
      const url = buildApiUrl("/playlists");

      const response = await fetch(url, {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify(playlistData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `Erro ao criar playlist: ${response.status}`
        );
      }

      // Recarregar playlists
      await loadPlaylists();

      // Resetar formulário
      setNewPlaylist({
        name: "",
        description: "",
        youtubeUrl: "",
        tags: "",
        isActive: true,
        category: "",
        volume: 80,
        schedule: {
          enabled: false,
          startTime: "08:00",
          endTime: "22:00",
          days: [
            "monday",
            "tuesday",
            "wednesday",
            "thursday",
            "friday",
            "saturday",
            "sunday",
          ],
        },
      });

      setShowAddForm(false);
      toast.success("Playlist adicionada com sucesso!");
    } catch (error: any) {
      console.error("Erro ao adicionar playlist:", error);
      toast.error(error.message || "Erro ao adicionar playlist");
    } finally {
      setLoading(false);
    }
  };

  // Sincronizar playlist com YouTube
  const syncPlaylist = async (playlistId: string) => {
    setLoading(true);
    try {
      // Chamar API de sincronização
      const url = buildApiUrl(`/playlists/${playlistId}/sync`);

      const response = await fetch(url, {
        method: "POST",
        headers: getAuthHeaders(""),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `Erro ao sincronizar: ${response.status}`
        );
      }

      const result = await response.json();
      
      // Atualizar com os dados reais da API
      setPlaylists((prev) =>
        prev.map((playlist) =>
          playlist.id === playlistId
            ? {
                ...playlist,
                lastSync: new Date().toISOString(),
                videoCount: result.playlist.trackCount,
              }
            : playlist
        )
      );

      toast.success("Playlist sincronizada com sucesso!");
    } catch (error: any) {
      console.error("Erro ao sincronizar playlist:", error);
      toast.error(error.message || "Erro ao sincronizar playlist");
    } finally {
      setLoading(false);
    }
  };

  // Excluir playlist
  const deletePlaylist = async (playlistId: string) => {
    const playlist = playlists.find((p) => p.id === playlistId);

    if (!playlist) {
      toast.error("Playlist não encontrada");
      return;
    }

    if (playlist.isDefault) {
      toast.error("Não é possível excluir a playlist padrão");
      return;
    }

    if (
      !window.confirm(
        "Tem certeza que deseja excluir esta playlist? Esta ação não pode ser desfeita."
      )
    ) {
      return;
    }

    try {
      setLoading(true);

      const url = buildApiUrl(`/playlists/${playlistId}`);

      const response = await fetch(url, {
        method: "DELETE",
        headers: getAuthHeaders(""),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `Erro ao excluir: ${response.status}`
        );
      }

      // Recarregar playlists
      await loadPlaylists();
      toast.success("Playlist excluída com sucesso!");
    } catch (error: any) {
      console.error("Erro ao excluir playlist:", error);
      toast.error(error.message || "Erro ao excluir playlist");
    } finally {
      setLoading(false);
    }
  };

  // Atualizar playlist
  const updatePlaylist = async (playlist: Playlist) => {
    try {
      setLoading(true);

      // Validar dados
      if (!playlist.name.trim()) {
        throw new Error("Nome da playlist é obrigatório");
      }

      if (playlist.youtubeUrl && !extractPlaylistId(playlist.youtubeUrl)) {
        throw new Error("URL da playlist do YouTube inválida");
      }

      const url = buildApiUrl(`/playlists/${playlist.id}`);

      const response = await fetch(url, {
        method: "PUT",
        headers: getAuthHeaders(),
        body: JSON.stringify(playlist),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `Erro ao atualizar: ${response.status}`
        );
      }

      // Fechar modal e recarregar playlists
      setEditingPlaylist(null);
      await loadPlaylists();
      toast.success("Playlist atualizada com sucesso!");
    } catch (error: any) {
      console.error("Erro ao atualizar playlist:", error);
      toast.error(error.message || "Erro ao atualizar playlist");
    } finally {
      setLoading(false);
    }
  };

  // Alternar status de ativação da playlist
  const togglePlaylistStatus = async (playlist: Playlist) => {
    try {
      setLoading(true);

      const newStatus = !playlist.isActive;
      const url = buildApiUrl(`/playlists/${playlist.id}/status`);

      const response = await fetch(url, {
        method: "PATCH",
        headers: getAuthHeaders(),
        body: JSON.stringify({ isActive: newStatus }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `Erro ao alterar status: ${response.status}`
        );
      }

      // Exclusividade: se ativar esta, desativar as outras
      if (newStatus) {
        const others = playlists.filter((p) => p.id !== playlist.id && (p.isActive ?? false));
        await Promise.all(
          others.map((p) =>
            fetch(buildApiUrl(`/playlists/${p.id}/status`), {
              method: "PATCH",
              headers: getAuthHeaders(),
              body: JSON.stringify({ isActive: false }),
            })
          )
        );
      }

      // Atualizar estado local
      setPlaylists((prev) =>
        prev.map((p) =>
          p.id === playlist.id ? { ...p, isActive: newStatus } : newStatus ? { ...p, isActive: false } : p
        )
      );

      toast.success(
        `Playlist ${newStatus ? "ativada" : "desativada"} com sucesso!`
      );
    } catch (error: any) {
      console.error("Erro ao alterar status da playlist:", error);
      toast.error(error.message || "Erro ao alterar status da playlist");
    } finally {
      setLoading(false);
    }
  };

  // Definir Playlist como Colaborativa (ativa)
  const setCollaborative = async (playlist: Playlist) => {
    try {
      setLoading(true);
      // 1) Ativar esta playlist
      const statusUrl = buildApiUrl(`/playlists/${playlist.id}/status`);
      const statusRes = await fetch(statusUrl, {
        method: "PATCH",
        headers: getAuthHeaders(),
        body: JSON.stringify({ isActive: true }),
      });
      if (!statusRes.ok) {
        const errorData = await statusRes.json().catch(() => ({}));
        throw new Error(errorData.message || "Falha ao ativar playlist como colaborativa");
      }

      // 2) Desativar todas as outras playlists ativas (exclusividade)
      const others = playlists.filter((p) => p.id !== playlist.id && (p.isActive ?? false));
      await Promise.all(
        others.map((p) =>
          fetch(buildApiUrl(`/playlists/${p.id}/status`), {
            method: "PATCH",
            headers: getAuthHeaders(),
            body: JSON.stringify({ isActive: false }),
          })
        )
      );

      // 3) Persistir seleção local (usado no ws e no painel)
      localStorage.setItem("currentRestaurantId", restaurantId);
      localStorage.setItem("currentPlaylistId", playlist.id);

      // 4) Feedback e reload
      toast.success(`Playlist "${playlist.name}" definida como colaborativa ativa`);
      await loadPlaylists();
    } catch (e: any) {
      console.error("Erro ao definir como colaborativa:", e);
      toast.error(e.message || "Erro ao definir como colaborativa");
    } finally {
      setLoading(false);
    }
  };

  // Reordenar por Votos (QA)
  const reorderByVotes = async (playlist: Playlist) => {
    try {
      setLoading(true);
      const url = buildApiUrl(`/collaborative-playlist/${restaurantId}/reorder`);
      const res = await fetch(url, { method: "POST", headers: getAuthHeaders() });
      const data = await res.json().catch(() => ({}));
      if (!res.ok || data?.success === false) {
        throw new Error(data?.message || `Falha ao reordenar: ${res.status}`);
      }
      toast.success(data?.message || "Playlist reordenada com sucesso (QA)"
      );
    } catch (e: any) {
      console.error("Erro ao reordenar por votos:", e);
      toast.error(e.message || "Erro ao reordenar");
    } finally {
      setLoading(false);
    }
  };

  // Definir playlist como padrão
  const setAsDefault = async (playlistId: string) => {
    try {
      setLoading(true);

      const url = buildApiUrl(`/playlists/${playlistId}/default`);

      const response = await fetch(url, {
        method: "PATCH",
        headers: getAuthHeaders(),
        body: JSON.stringify({ isDefault: true }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message ||
            `Erro ao definir playlist padrão: ${response.status}`
        );
      }

      // Atualizar estado local
      setPlaylists((prev) =>
        prev.map((playlist) => ({
          ...playlist,
          isDefault: playlist.id === playlistId,
        }))
      );

      toast.success("Playlist definida como padrão");
    } catch (error: any) {
      console.error("Erro ao definir playlist padrão:", error);
      toast.error(error.message || "Erro ao definir playlist padrão");
    } finally {
      setLoading(false);
    }
  };

  // Atualizar programação de playlist
  const updateSchedule = async (playlistId: string, schedule: Schedule) => {
    try {
      setLoading(true);

      const url = buildApiUrl(`/playlists/${playlistId}/schedule`);

      const response = await fetch(url, {
        method: "PATCH",
        headers: {
          ...getAuthHeaders(),
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ schedule }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message ||
            `Erro ao atualizar programação: ${response.status}`
        );
      }

      // Atualizar estado local
      setPlaylists((prev) =>
        prev.map((p) => (p.id === playlistId ? { ...p, schedule } : p))
      );

      setSchedulingPlaylist(null);
      setShowScheduleForm(false);
      toast.success("Programação atualizada com sucesso!");
    } catch (error: any) {
      console.error("Erro ao atualizar programação:", error);
      toast.error(error.message || "Erro ao atualizar programação");
    } finally {
      setLoading(false);
    }
  };

  // Importar múltiplas playlists
  const handleImportPlaylists = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const content = e.target?.result as string;
        const importedPlaylists = JSON.parse(content);

        if (!Array.isArray(importedPlaylists)) {
          throw new Error(
            "Formato de arquivo inválido. Esperava-se uma array de playlists."
          );
        }

        setLoading(true);

        // Em um ambiente real, enviaríamos para o backend
        // Aqui vamos simular o sucesso
        setTimeout(() => {
          const newPlaylists = importedPlaylists.map(
            (p: any, index: number) => ({
              ...p,
              id: `imported-${Date.now()}-${index}`,
              createdAt: new Date().toISOString(),
              isDefault: false,
            })
          );

          setPlaylists((prev) => [...prev, ...newPlaylists]);
          setLoading(false);
          toast.success(
            `${newPlaylists.length} playlists importadas com sucesso!`
          );
        }, 1500);
      } catch (error: any) {
        console.error("Erro ao importar playlists:", error);
        toast.error(error.message || "Erro ao importar playlists");
      }

      // Limpar input
      if (event.target) {
        event.target.value = "";
      }
    };

    reader.readAsText(file);
  };

  // Exportar playlists
  const exportPlaylists = () => {
    try {
      const dataStr = JSON.stringify(playlists, null, 2);
      const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(
        dataStr
      )}`;

      const exportFileDefaultName = `playlists-${restaurantId}-${new Date()
        .toISOString()
        .slice(0, 10)}.json`;

      const linkElement = document.createElement("a");
      linkElement.setAttribute("href", dataUri);
      linkElement.setAttribute("download", exportFileDefaultName);
      linkElement.click();

      toast.success("Playlists exportadas com sucesso!");
    } catch (error: any) {
      console.error("Erro ao exportar playlists:", error);
      toast.error("Erro ao exportar playlists");
    }
  };

  // Reordenar playlists (drag and drop)
  const handleDragEnd = async (result: any) => {
    if (!result.destination) return;

    const items = Array.from(playlists);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Atualizar ordem
    const updatedPlaylists = items.map((item, index) => ({
      ...item,
      order: index + 1,
    }));

    setPlaylists(updatedPlaylists);

    // Em produção, enviaríamos a nova ordem para o backend
    toast.success("Ordem das playlists atualizada");
  };

  // Copiar link da playlist
  const copyPlaylistLink = (playlistId: string) => {
    const url = `https://yourapp.com/playlists/${playlistId}`;
    navigator.clipboard
      .writeText(url)
      .then(() => toast.success("Link copiado para a área de transferência"))
      .catch(() => toast.error("Erro ao copiar link"));
  };

  // Formatação de data
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Formatação de duração (segundos para formato legível)
  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (hours > 0) {
      return `${hours}h ${minutes}min`;
    }
    return `${minutes} minutos`;
  };

  // Obter cor de status
  const getStatusColor = (isActive: boolean) => {
    return isActive
      ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
      : "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400";
  };

  // Obter cor da categoria
  const getCategoryColor = (categoryId: string | undefined) => {
    if (!categoryId)
      return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400";
    const category = categories.find((c) => c.id === categoryId);
    return (
      category?.color ||
      "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
    );
  };

  // Filtragem de playlists
  const filteredPlaylists = playlists
    .filter((playlist) => {
      // Filtro por texto de busca
      const matchesSearch =
        searchQuery === "" ||
        playlist.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        playlist.description
          .toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        playlist.tags?.some((tag) =>
          tag.toLowerCase().includes(searchQuery.toLowerCase())
        );

      // Filtro por status de ativação
      const matchesActive =
        filterActive === null || playlist.isActive === filterActive;

      // Filtro por categoria
      const matchesCategory =
        currentCategory === null || playlist.category === currentCategory;

      return matchesSearch && matchesActive && matchesCategory;
    })
    .sort((a, b) => {
      // Ordenação
      let comparison = 0;

      switch (sortBy) {
        case "name":
          comparison = a.name.localeCompare(b.name);
          break;
        case "date":
          comparison =
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
          break;
        case "count":
          comparison = (a.videoCount || 0) - (b.videoCount || 0);
          break;
        case "lastSync":
          const aSync = a.lastSync ? new Date(a.lastSync).getTime() : 0;
          const bSync = b.lastSync ? new Date(b.lastSync).getTime() : 0;
          comparison = aSync - bSync;
          break;
        case "order":
          comparison = (a.order || 999) - (b.order || 999);
          break;
        default:
          comparison = 0;
      }

      return sortDirection === "asc" ? comparison : -comparison;
    });

  // Handlers para formulários
  const handleSchedulePlaylist = (playlist: Playlist) => {
    setSchedulingPlaylist(playlist);
    setScheduleSettings(
      playlist.schedule || {
        enabled: false,
        startTime: "08:00",
        endTime: "22:00",
        days: [
          "monday",
          "tuesday",
          "wednesday",
          "thursday",
          "friday",
          "saturday",
          "sunday",
        ],
      }
    );
    setShowScheduleForm(true);
  };

  const handleSaveSchedule = () => {
    if (schedulingPlaylist) {
      updateSchedule(schedulingPlaylist.id, scheduleSettings);
    }
  };

  const handleEditPlaylist = (playlist: Playlist) => {
    setEditingPlaylist(playlist);
  };

  const handleSaveEdit = () => {
    if (editingPlaylist) {
      updatePlaylist(editingPlaylist);
    }
  };

  // Render do componente principal
  return (
    <div className="space-y-6">
      {/* Cabeçalho */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Gerenciar Playlists
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Configure e gerencie as playlists do seu restaurante
          </p>
        </div>

        <div className="flex items-center gap-2">
          {/* Botão de exportar */}
          <button
            onClick={exportPlaylists}
            className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            title="Exportar playlists"
          >
            <Download className="w-4 h-4" />
            <span className="hidden sm:inline">Exportar</span>
          </button>

          {/* Botão de importar */}
          <button
            onClick={() => fileInputRef.current?.click()}
            className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            title="Importar playlists"
          >
            <Upload className="w-4 h-4" />
            <span className="hidden sm:inline">Importar</span>
          </button>

          {/* Input oculto para importação */}
          <input
            ref={fileInputRef}
            type="file"
            accept=".json"
            onChange={handleImportPlaylists}
            className="hidden"
          />

          {/* Botão de adicionar */}
          <button
            onClick={() => setShowAddForm(true)}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="w-4 h-4" />
            <span>Nova Playlist</span>
          </button>
        </div>
      </div>

      {/* Barra de ferramentas */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Busca */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Buscar playlists..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
          </div>

          {/* Filtros */}
          <div className="flex flex-wrap items-center gap-2">
            {/* Filtro por status */}
            <select
              value={filterActive === null ? "all" : filterActive.toString()}
              onChange={(e) =>
                setFilterActive(
                  e.target.value === "all" ? null : e.target.value === "true"
                )
              }
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
            >
              <option value="all">Todos os status</option>
              <option value="true">Ativas</option>
              <option value="false">Inativas</option>
            </select>

            {/* Filtro por categoria */}
            <select
              value={currentCategory || "all"}
              onChange={(e) =>
                setCurrentCategory(
                  e.target.value === "all" ? null : e.target.value
                )
              }
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
            >
              <option value="all">Todas as categorias</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>

            {/* Ordenação */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
            >
              <option value="order">Ordem de execução</option>
              <option value="date">Data de criação</option>
              <option value="name">Nome</option>
              <option value="count">Número de vídeos</option>
              <option value="lastSync">Última sincronização</option>
            </select>

            {/* Direção da ordenação */}
            <button
              onClick={() =>
                setSortDirection((prev) => (prev === "asc" ? "desc" : "asc"))
              }
              className="p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-600"
              title={`Ordenar ${
                sortDirection === "asc" ? "decrescente" : "crescente"
              }`}
            >
              <ArrowUpDown className="w-4 h-4" />
            </button>

            {/* Alternar visualização */}
            <div className="flex border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
              <button
                onClick={() => setView("grid")}
                className={`p-2 ${
                  view === "grid"
                    ? "bg-blue-600 text-white"
                    : "bg-white dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-600"
                }`}
                title="Visualização em grade"
              >
                <Grid className="w-4 h-4" />
              </button>
              <button
                onClick={() => setView("list")}
                className={`p-2 ${
                  view === "list"
                    ? "bg-blue-600 text-white"
                    : "bg-white dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-600"
                }`}
                title="Visualização em lista"
              >
                <List className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Estatísticas rápidas */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <Music className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Total de Playlists
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {playlists.length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
              <Power className="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Playlists Ativas
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {playlists.filter((p) => p.isActive).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
              <Youtube className="w-5 h-5 text-purple-600 dark:text-purple-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Total de Vídeos
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {playlists.reduce((sum, p) => sum + (p.videoCount || 0), 0)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg">
              <Calendar className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Com Programação
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {playlists.filter((p) => p.schedule?.enabled).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Lista de playlists */}
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center space-x-2">
            <RefreshCw className="w-5 h-5 animate-spin text-blue-600" />
            <span className="text-gray-600 dark:text-gray-400">
              Carregando playlists...
            </span>
          </div>
        </div>
      ) : error ? (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400 mr-2" />
            <span className="text-red-800 dark:text-red-200">{error}</span>
          </div>
        </div>
      ) : filteredPlaylists.length === 0 ? (
        <div className="text-center py-12">
          <Music className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {searchQuery || filterActive !== null || currentCategory
              ? "Nenhuma playlist encontrada"
              : "Nenhuma playlist cadastrada"}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {searchQuery || filterActive !== null || currentCategory
              ? "Tente ajustar os filtros de busca"
              : "Comece adicionando sua primeira playlist do YouTube"}
          </p>
          {!searchQuery && filterActive === null && !currentCategory && (
            <button
              onClick={() => setShowAddForm(true)}
              className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus className="w-4 h-4" />
              <span>Adicionar Primeira Playlist</span>
            </button>
          )}
        </div>
      ) : (
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="playlists">
            {(provided) => (
              <div
                {...provided.droppableProps}
                ref={provided.innerRef}
                className={
                  view === "grid"
                    ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                    : "space-y-4"
                }
              >
                {filteredPlaylists.map((playlist, index) => (
                  <Draggable
                    key={playlist.id}
                    draggableId={playlist.id}
                    index={index}
                  >
                    {(provided, snapshot) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                        className={snapshot.isDragging ? "opacity-75" : ""}
                      >
                        {view === "grid" ? (
                          <GridPlaylistCard
                            playlist={playlist}
                            onDelete={deletePlaylist}
                            onSync={syncPlaylist}
                            onToggle={togglePlaylistStatus}
                            onEdit={() => handleEditPlaylist(playlist)}
                            onSetDefault={setAsDefault}
                            onOpenDetails={setSelectedPlaylist}
                            onSchedule={handleSchedulePlaylist}
                            onCopyLink={copyPlaylistLink}
                            onSetCollaborative={setCollaborative}
                            onReorderByVotes={reorderByVotes}
                            formatDate={formatDate}
                            getStatusColor={getStatusColor}
                            getCategoryColor={getCategoryColor}
                            formatDuration={formatDuration}
                          />
                        ) : (
                          <ListPlaylistCard
                            playlist={playlist}
                            onDelete={deletePlaylist}
                            onSync={syncPlaylist}
                            onToggle={togglePlaylistStatus}
                            onEdit={() => handleEditPlaylist(playlist)}
                            onSetDefault={setAsDefault}
                            onOpenDetails={setSelectedPlaylist}
                            onSchedule={handleSchedulePlaylist}
                            onCopyLink={copyPlaylistLink}
                            onSetCollaborative={setCollaborative}
                            onReorderByVotes={reorderByVotes}
                            formatDate={formatDate}
                            getStatusColor={getStatusColor}
                            getCategoryColor={getCategoryColor}
                            formatDuration={formatDuration}
                          />
                        )}
                      </div>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      )}

      {/* Modal de adicionar playlist */}
      <AnimatePresence>
        {showAddForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          >
            <motion.div
              initial={{ scale: 0.9, y: 20 }}
              animate={{ scale: 1, y: 0 }}
              exit={{ scale: 0.9, y: 20 }}
              className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto"
            >
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex justify-between items-center">
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                    Nova Playlist
                  </h3>
                  <button
                    onClick={() => setShowAddForm(false)}
                    className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
              </div>

              <div className="p-6 space-y-4">
                {/* Nome */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Nome da Playlist *
                  </label>
                  <input
                    type="text"
                    value={newPlaylist.name}
                    onChange={(e) =>
                      setNewPlaylist((prev) => ({
                        ...prev,
                        name: e.target.value,
                      }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="Ex: Música Ambiente Almoço"
                  />
                </div>

                {/* Descrição */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Descrição
                  </label>
                  <textarea
                    value={newPlaylist.description}
                    onChange={(e) =>
                      setNewPlaylist((prev) => ({
                        ...prev,
                        description: e.target.value,
                      }))
                    }
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="Descreva o estilo e propósito desta playlist"
                  />
                </div>

                {/* URL do YouTube */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    URL da Playlist do YouTube *
                  </label>
                  <input
                    type="url"
                    value={newPlaylist.youtubeUrl}
                    onChange={(e) =>
                      setNewPlaylist((prev) => ({
                        ...prev,
                        youtubeUrl: e.target.value,
                      }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="https://www.youtube.com/playlist?list=..."
                  />
                </div>

                {/* Categoria e Volume */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Categoria
                    </label>
                    <select
                      value={newPlaylist.category}
                      onChange={(e) =>
                        setNewPlaylist((prev) => ({
                          ...prev,
                          category: e.target.value,
                        }))
                      }
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="">Selecione uma categoria</option>
                      {categories.map((category) => (
                        <option key={category.id} value={category.id}>
                          {category.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Volume ({newPlaylist.volume}%)
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={newPlaylist.volume}
                      onChange={(e) =>
                        setNewPlaylist((prev) => ({
                          ...prev,
                          volume: parseInt(e.target.value),
                        }))
                      }
                      className="w-full"
                    />
                  </div>
                </div>

                {/* Tags */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Tags (separadas por vírgula)
                  </label>
                  <input
                    type="text"
                    value={newPlaylist.tags}
                    onChange={(e) =>
                      setNewPlaylist((prev) => ({
                        ...prev,
                        tags: e.target.value,
                      }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="jazz, relaxante, ambiente"
                  />
                </div>

                {/* Status ativo */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isActive"
                    checked={newPlaylist.isActive}
                    onChange={(e) =>
                      setNewPlaylist((prev) => ({
                        ...prev,
                        isActive: e.target.checked,
                      }))
                    }
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                  <label
                    htmlFor="isActive"
                    className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Playlist ativa
                  </label>
                </div>

                {/* Programação */}
                <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                  <div className="flex items-center mb-3">
                    <input
                      type="checkbox"
                      id="scheduleEnabled"
                      checked={newPlaylist.schedule.enabled}
                      onChange={(e) =>
                        setNewPlaylist((prev) => ({
                          ...prev,
                          schedule: {
                            ...prev.schedule,
                            enabled: e.target.checked,
                          },
                        }))
                      }
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                    />
                    <label
                      htmlFor="scheduleEnabled"
                      className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300"
                    >
                      Programar horários de reprodução
                    </label>
                  </div>

                  {newPlaylist.schedule.enabled && (
                    <div className="space-y-3">
                      {/* Horários */}
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                            Início
                          </label>
                          <input
                            type="time"
                            value={newPlaylist.schedule.startTime}
                            onChange={(e) =>
                              setNewPlaylist((prev) => ({
                                ...prev,
                                schedule: {
                                  ...prev.schedule,
                                  startTime: e.target.value,
                                },
                              }))
                            }
                            className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                            Fim
                          </label>
                          <input
                            type="time"
                            value={newPlaylist.schedule.endTime}
                            onChange={(e) =>
                              setNewPlaylist((prev) => ({
                                ...prev,
                                schedule: {
                                  ...prev.schedule,
                                  endTime: e.target.value,
                                },
                              }))
                            }
                            className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          />
                        </div>
                      </div>

                      {/* Dias da semana */}
                      <div>
                        <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
                          Dias da semana
                        </label>
                        <div className="grid grid-cols-4 sm:grid-cols-7 gap-2">
                          {weekDays.map((day) => (
                            <label
                              key={day.id}
                              className="flex items-center text-xs"
                            >
                              <input
                                type="checkbox"
                                checked={newPlaylist.schedule.days.includes(
                                  day.id
                                )}
                                onChange={(e) => {
                                  const days = e.target.checked
                                    ? [...newPlaylist.schedule.days, day.id]
                                    : newPlaylist.schedule.days.filter(
                                        (d) => d !== day.id
                                      );
                                  setNewPlaylist((prev) => ({
                                    ...prev,
                                    schedule: { ...prev.schedule, days },
                                  }));
                                }}
                                className="w-3 h-3 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-1 dark:bg-gray-700 dark:border-gray-600 mr-1"
                              />
                              <span className="text-gray-700 dark:text-gray-300">
                                {day.label}
                              </span>
                            </label>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="p-6 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
                <button
                  onClick={() => setShowAddForm(false)}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                  Cancelar
                </button>
                <button
                  onClick={addPlaylist}
                  disabled={
                    loading ||
                    !newPlaylist.name.trim() ||
                    !newPlaylist.youtubeUrl.trim()
                  }
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
                >
                  {loading && <RefreshCw className="w-4 h-4 animate-spin" />}
                  <span>Adicionar Playlist</span>
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Modal de editar playlist */}
      <AnimatePresence>
        {editingPlaylist && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          >
            <motion.div
              initial={{ scale: 0.9, y: 20 }}
              animate={{ scale: 1, y: 0 }}
              exit={{ scale: 0.9, y: 20 }}
              className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto"
            >
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex justify-between items-center">
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                    Editar Playlist
                  </h3>
                  <button
                    onClick={() => setEditingPlaylist(null)}
                    className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
              </div>

              <div className="p-6 space-y-4">
                {/* Nome */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Nome da Playlist *
                  </label>
                  <input
                    type="text"
                    value={editingPlaylist.name}
                    onChange={(e) =>
                      setEditingPlaylist((prev) =>
                        prev ? { ...prev, name: e.target.value } : null
                      )
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>

                {/* Descrição */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Descrição
                  </label>
                  <textarea
                    value={editingPlaylist.description}
                    onChange={(e) =>
                      setEditingPlaylist((prev) =>
                        prev ? { ...prev, description: e.target.value } : null
                      )
                    }
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>

                {/* URL do YouTube */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    URL da Playlist do YouTube
                  </label>
                  <input
                    type="url"
                    value={editingPlaylist.youtubeUrl || ""}
                    onChange={(e) =>
                      setEditingPlaylist((prev) =>
                        prev ? { ...prev, youtubeUrl: e.target.value } : null
                      )
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>

                {/* Categoria e Volume */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Categoria
                    </label>
                    <select
                      value={editingPlaylist.category || ""}
                      onChange={(e) =>
                        setEditingPlaylist((prev) =>
                          prev ? { ...prev, category: e.target.value } : null
                        )
                      }
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="">Selecione uma categoria</option>
                      {categories.map((category) => (
                        <option key={category.id} value={category.id}>
                          {category.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Volume ({editingPlaylist.volume || 80}%)
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={editingPlaylist.volume || 80}
                      onChange={(e) =>
                        setEditingPlaylist((prev) =>
                          prev
                            ? { ...prev, volume: parseInt(e.target.value) }
                            : null
                        )
                      }
                      className="w-full"
                    />
                  </div>
                </div>

                {/* Tags */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Tags (separadas por vírgula)
                  </label>
                  <input
                    type="text"
                    value={editingPlaylist.tags?.join(", ") || ""}
                    onChange={(e) =>
                      setEditingPlaylist((prev) =>
                        prev
                          ? {
                              ...prev,
                              tags: e.target.value
                                .split(",")
                                .map((tag) => tag.trim())
                                .filter(Boolean),
                            }
                          : null
                      )
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>

                {/* Status ativo */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="editIsActive"
                    checked={editingPlaylist.isActive ?? true}
                    onChange={(e) =>
                      setEditingPlaylist((prev) =>
                        prev ? { ...prev, isActive: e.target.checked } : null
                      )
                    }
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                  <label
                    htmlFor="editIsActive"
                    className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Playlist ativa
                  </label>
                </div>
              </div>

              <div className="p-6 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
                <button
                  onClick={() => setEditingPlaylist(null)}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                  Cancelar
                </button>
                <button
                  onClick={handleSaveEdit}
                  disabled={loading || !editingPlaylist.name.trim()}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
                >
                  {loading && <RefreshCw className="w-4 h-4 animate-spin" />}
                  <span>Salvar Alterações</span>
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Modal de programação */}
      <AnimatePresence>
        {showScheduleForm && schedulingPlaylist && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          >
            <motion.div
              initial={{ scale: 0.9, y: 20 }}
              animate={{ scale: 1, y: 0 }}
              exit={{ scale: 0.9, y: 20 }}
              className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-md"
            >
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex justify-between items-center">
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                    Programar Playlist
                  </h3>
                  <button
                    onClick={() => setShowScheduleForm(false)}
                    className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {schedulingPlaylist.name}
                </p>
              </div>

              <div className="p-6 space-y-4">
                {/* Habilitar programação */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="scheduleEnabledEdit"
                    checked={scheduleSettings.enabled}
                    onChange={(e) =>
                      setScheduleSettings((prev) => ({
                        ...prev,
                        enabled: e.target.checked,
                      }))
                    }
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                  />
                  <label
                    htmlFor="scheduleEnabledEdit"
                    className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Habilitar programação automática
                  </label>
                </div>

                {scheduleSettings.enabled && (
                  <div className="space-y-4">
                    {/* Horários */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Horário de início
                        </label>
                        <input
                          type="time"
                          value={scheduleSettings.startTime}
                          onChange={(e) =>
                            setScheduleSettings((prev) => ({
                              ...prev,
                              startTime: e.target.value,
                            }))
                          }
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Horário de fim
                        </label>
                        <input
                          type="time"
                          value={scheduleSettings.endTime}
                          onChange={(e) =>
                            setScheduleSettings((prev) => ({
                              ...prev,
                              endTime: e.target.value,
                            }))
                          }
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        />
                      </div>
                    </div>

                    {/* Dias da semana */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Dias da semana
                      </label>
                      <div className="space-y-2">
                        {weekDays.map((day) => (
                          <label key={day.id} className="flex items-center">
                            <input
                              type="checkbox"
                              checked={scheduleSettings.days.includes(day.id)}
                              onChange={(e) => {
                                const days = e.target.checked
                                  ? [...scheduleSettings.days, day.id]
                                  : scheduleSettings.days.filter(
                                      (d) => d !== day.id
                                    );
                                setScheduleSettings((prev) => ({
                                  ...prev,
                                  days,
                                }));
                              }}
                              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                            />
                            <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                              {day.label}
                            </span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className="p-6 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
                <button
                  onClick={() => setShowScheduleForm(false)}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                  Cancelar
                </button>
                <button
                  onClick={handleSaveSchedule}
                  disabled={loading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
                >
                  {loading && <RefreshCw className="w-4 h-4 animate-spin" />}
                  <span>Salvar Programação</span>
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Modal de detalhes da playlist */}
      <AnimatePresence>
        {selectedPlaylist && (
          <PlaylistDetails
            playlist={selectedPlaylist}
            onClose={() => setSelectedPlaylist(null)}
            onEdit={() => {
              handleEditPlaylist(selectedPlaylist);
              setSelectedPlaylist(null);
            }}
            onSync={syncPlaylist}
            formatDate={formatDate}
            formatDuration={formatDuration}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default PlaylistManager;
