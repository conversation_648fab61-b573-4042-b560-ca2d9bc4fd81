import { redisClient } from "../config/redis";

/**
 * VOTE RATE LIMIT
 * Bloqueia votos normais consecutivos por sessão/cliente dentro de uma janela.
 *
 * Configuração por env:
 * - NORMAL_VOTE_RATE_LIMIT (default: 1)
 * - NORMAL_VOTE_RATE_WINDOW_SECONDS (default: 180)
 */
export interface VoteRateResult {
  allowed: boolean;
  remaining: number;
  retryAfter?: number; // segundos
}

function getWindowSeconds(): number {
  const s = Number(process.env.NORMAL_VOTE_RATE_WINDOW_SECONDS || 180);
  return Number.isFinite(s) && s > 0 ? s : 180;
}

function getLimit(): number {
  const n = Number(process.env.NORMAL_VOTE_RATE_LIMIT || 1);
  return Number.isFinite(n) && n > 0 ? n : 1;
}

/**
 * Aplica rate limit por (restaurantId + clientSessionId OU ip) para votos normais.
 * Retorna allowed=false se exceder o limite.
 */
export async function checkNormalVoteRateLimit(
  restaurantId: string,
  clientSessionId?: string,
  clientIp?: string
): Promise<VoteRateResult> {
  const limit = getLimit();
  const windowSec = getWindowSeconds();

  const identity = clientSessionId || clientIp || "anonymous";
  const key = `rate:vote:normal:${restaurantId}:${identity}`;

  const { allowed, remaining } = await redisClient.checkRateLimit(
    key,
    limit,
    windowSec
  );

  if (allowed) {
    return { allowed, remaining };
  }

  // Calcular retryAfter aproximado pelo TTL restante
  try {
    const ttl = await redisClient.getClient().ttl(key);
    return { allowed, remaining, retryAfter: ttl > 0 ? ttl : windowSec };
  } catch {
    return { allowed, remaining, retryAfter: windowSec };
  }
}

export default {
  checkNormalVoteRateLimit,
};
