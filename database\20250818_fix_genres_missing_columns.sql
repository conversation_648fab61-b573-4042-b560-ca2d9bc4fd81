-- Idempotent fix: ensure genres table has all columns expected by backend entity

-- Ensure table exists minimally
CREATE TABLE IF NOT EXISTS genres (
	id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
	name VARCHAR(100) UNIQUE NOT NULL,
	display_name VARCHAR(100),
	created_at TIMESTAMP DEFAULT now(),
	updated_at TIMESTAMP DEFAULT now()
);

-- Add missing columns safely (PostgreSQL style guards)
DO $$
BEGIN
	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns
		WHERE table_name='genres' AND column_name='description'
	) THEN
		ALTER TABLE genres ADD COLUMN description VARCHAR(200) NULL;
	END IF;

	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns
		WHERE table_name='genres' AND column_name='category'
	) THEN
		ALTER TABLE genres ADD COLUMN category VARCHAR(50) DEFAULT 'music';
	END IF;

	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns
		WHERE table_name='genres' AND column_name='color'
	) THEN
		ALTER TABLE genres ADD COLUMN color VARCHAR(7) DEFAULT '#3B82F6';
	END IF;

	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns
		WHERE table_name='genres' AND column_name='icon'
	) THEN
		ALTER TABLE genres ADD COLUMN icon VARCHAR(50) NULL;
	END IF;

	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns
		WHERE table_name='genres' AND column_name='metadata'
	) THEN
		ALTER TABLE genres ADD COLUMN metadata JSONB NULL;
	END IF;

	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns
		WHERE table_name='genres' AND column_name='priority'
	) THEN
		ALTER TABLE genres ADD COLUMN priority INTEGER DEFAULT 0;
	END IF;

	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns
		WHERE table_name='genres' AND column_name='is_active'
	) THEN
		ALTER TABLE genres ADD COLUMN is_active BOOLEAN DEFAULT true;
	END IF;

	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns
		WHERE table_name='genres' AND column_name='is_default'
	) THEN
		ALTER TABLE genres ADD COLUMN is_default BOOLEAN DEFAULT false;
	END IF;

	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns
		WHERE table_name='genres' AND column_name='usage_count'
	) THEN
		ALTER TABLE genres ADD COLUMN usage_count INTEGER DEFAULT 0;
	END IF;

	IF NOT EXISTS (
		SELECT 1 FROM information_schema.columns
		WHERE table_name='genres' AND column_name='last_used_at'
	) THEN
		ALTER TABLE genres ADD COLUMN last_used_at TIMESTAMP NULL;
	END IF;
END $$;

-- Indexes
CREATE INDEX IF NOT EXISTS idx_genres_name ON genres(name);
CREATE INDEX IF NOT EXISTS idx_genres_category ON genres(category);
CREATE INDEX IF NOT EXISTS idx_genres_is_active ON genres(is_active);
