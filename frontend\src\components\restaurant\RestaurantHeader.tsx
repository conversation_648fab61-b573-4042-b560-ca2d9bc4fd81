import React from 'react';
import { motion } from 'framer-motion';
import { 
  MapPin, 
  Clock, 
  Phone, 
  Globe, 
  Instagram, 
  Facebook,
  Music,
  Users,
  Star
} from 'lucide-react';
import { Restaurant } from '@/types';

interface RestaurantHeaderProps {
  restaurant: Restaurant;
}

const RestaurantHeader: React.FC<RestaurantHeaderProps> = ({ restaurant }) => {
  const formatAddress = () => {
    const addr = restaurant.address;
    if (!addr) return null;
    
    const parts = [
      addr.street && addr.number ? `${addr.street}, ${addr.number}` : addr.street,
      addr.neighborhood,
      addr.city,
      addr.state
    ].filter(Boolean);
    
    return parts.join(', ');
  };

  const getSocialIcon = (platform: string) => {
    switch (platform) {
      case 'instagram':
        return Instagram;
      case 'facebook':
        return Facebook;
      default:
        return Globe;
    }
  };

  return (
    <motion.header
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700"
    >
      <div className="max-w-4xl mx-auto px-4 py-6">
        <div className="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-6">
          {/* Logo/Avatar */}
          <div className="flex-shrink-0">
            <div className="w-16 h-16 md:w-20 md:h-20 rounded-full bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center shadow-lg">
              {restaurant.logo ? (
                <img
                  src={restaurant.logo}
                  alt={restaurant.name}
                  className="w-full h-full rounded-full object-cover"
                />
              ) : (
                <Music className="w-8 h-8 md:w-10 md:h-10 text-white" />
              )}
            </div>
          </div>

          {/* Informações principais */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-3 mb-2">
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white truncate">
                {restaurant.name}
              </h1>
              
              {/* Status de funcionamento */}
              <div className={`
                inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                ${restaurant.isOpen 
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
                  : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                }
              `}>
                <div className={`
                  w-2 h-2 rounded-full mr-1.5
                  ${restaurant.isOpen ? 'bg-green-500' : 'bg-red-500'}
                `} />
                {restaurant.isOpen ? 'Aberto' : 'Fechado'}
              </div>
            </div>

            {/* Descrição */}
            {restaurant.description && (
              <p className="text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                {restaurant.description}
              </p>
            )}

            {/* Informações de contato e localização */}
            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
              {/* Endereço */}
              {restaurant.address && (
                <div className="flex items-center space-x-1">
                  <MapPin className="w-4 h-4" />
                  <span className="truncate max-w-xs">{formatAddress()}</span>
                </div>
              )}

              {/* Horário (placeholder - seria implementado com dados reais) */}
              <div className="flex items-center space-x-1">
                <Clock className="w-4 h-4" />
                <span>Seg-Dom: 11h-23h</span>
              </div>
            </div>
          </div>

          {/* Ações e redes sociais */}
          <div className="flex items-center space-x-3">
            {/* Redes sociais */}
            {restaurant.socialMedia && (
              <div className="flex items-center space-x-2">
                {Object.entries(restaurant.socialMedia).map(([platform, url]) => {
                  if (!url) return null;
                  
                  const Icon = getSocialIcon(platform);
                  
                  return (
                    <a
                      key={platform}
                      href={url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                    >
                      <Icon className="w-4 h-4" />
                    </a>
                  );
                })}
              </div>
            )}

            {/* Estatísticas rápidas */}
            <div className="hidden md:flex items-center space-x-4 text-sm">
              <div className="text-center">
                <div className="flex items-center space-x-1 text-gray-600 dark:text-gray-400">
                  <Users className="w-4 h-4" />
                  <span>Ativo</span>
                </div>
              </div>
              
              <div className="text-center">
                <div className="flex items-center space-x-1 text-gray-600 dark:text-gray-400">
                  <Music className="w-4 h-4" />
                  <span>Playlist</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Configurações de interface (se disponíveis) */}
        {restaurant.settings?.interface && (
          <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-center space-x-2 text-sm text-blue-700 dark:text-blue-300">
              <Music className="w-4 h-4" />
              <span>
                Sistema de playlist interativa ativo - Sugestões e votações em tempo real
              </span>
            </div>
          </div>
        )}
      </div>
    </motion.header>
  );
};

export default RestaurantHeader;
