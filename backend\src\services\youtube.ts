import axios from "axios";

interface YouTubeVideo {
  id: string;
  title: string;
  channelTitle: string;
  duration: string;
  thumbnailUrl: string;
  publishedAt: string;
}

interface YouTubePlaylistItem {
  id: string;
  title: string;
  channelTitle: string;
  duration: string;
  thumbnailUrl: string;
  position: number;
}

interface YouTubeSearchResult {
  videos: YouTubeVideo[];
  nextPageToken?: string;
  totalResults: number;
}

interface YouTubePlaylistResult {
  items: YouTubePlaylistItem[];
  nextPageToken?: string;
  totalResults: number;
}

class YouTubeService {
  private apiKey: string;
  private baseUrl = "https://www.googleapis.com/youtube/v3";

  constructor() {
    this.apiKey = process.env.YOUTUBE_API_KEY || "";
    if (
      !this.apiKey ||
      this.apiKey.includes("Dummy") ||
      this.apiKey.includes("dummy") ||
      this.apiKey.length < 30
    ) {
      console.warn(
        "⚠️  YouTube API Key não configurada ou inválida. Usando dados mock."
      );
      console.warn("📝  Para usar a API real do YouTube:");
      console.warn("   1. Acesse: https://console.cloud.google.com/");
      console.warn("   2. Ative a YouTube Data API v3");
      console.warn("   3. Crie uma API Key");
      console.warn(
        "   4. Configure no arquivo .env: YOUTUBE_API_KEY=sua_chave_aqui"
      );
    } else {
      console.log(
        "✅ YouTube API Key configurada. Usando API real do YouTube."
      );
    }
  }

  /**
   * Busca vídeos no YouTube
   */
  async searchVideos(
    query: string,
    maxResults: number = 10,
    pageToken?: string
  ): Promise<YouTubeSearchResult> {
    if (!this.isApiKeyValid()) {
      return this.getMockSearchResults(query, maxResults);
    }

    try {
      const response = await axios.get(`${this.baseUrl}/search`, {
        params: {
          key: this.apiKey,
          q: query,
          part: "snippet",
          type: "video",
          maxResults,
          pageToken,
          videoCategoryId: "10", // Música
          order: "relevance",
        },
      });

      const videoIds = response.data.items
        .map((item: any) => item.id.videoId)
        .join(",");

      // Buscar detalhes dos vídeos (duração, etc.)
      const detailsResponse = await axios.get(`${this.baseUrl}/videos`, {
        params: {
          key: this.apiKey,
          id: videoIds,
          part: "contentDetails,snippet",
        },
      });

      const videos: YouTubeVideo[] = detailsResponse.data.items.map(
        (item: any) => ({
          id: item.id,
          title: item.snippet.title,
          channelTitle: item.snippet.channelTitle,
          duration: this.parseDuration(item.contentDetails.duration),
          thumbnailUrl:
            item.snippet.thumbnails.medium?.url ||
            item.snippet.thumbnails.default.url,
          publishedAt: item.snippet.publishedAt,
        })
      );

      return {
        videos,
        nextPageToken: response.data.nextPageToken,
        totalResults: response.data.pageInfo.totalResults,
      };
    } catch (error) {
      console.error("Erro ao buscar vídeos no YouTube:", error);
      return this.getMockSearchResults(query, maxResults);
    }
  }

  /**
   * Busca itens de uma playlist do YouTube
   */
  async getPlaylistItems(
    playlistId: string,
    maxResults: number = 50,
    pageToken?: string
  ): Promise<YouTubePlaylistResult> {
    if (!this.isApiKeyValid()) {
      return this.getMockPlaylistResults(playlistId, maxResults);
    }

    try {
      const response = await axios.get(`${this.baseUrl}/playlistItems`, {
        params: {
          key: this.apiKey,
          playlistId,
          part: "snippet,contentDetails",
          maxResults,
          pageToken,
        },
      });

      const videoIds = response.data.items
        .map((item: any) => item.contentDetails.videoId)
        .join(",");

      // Buscar detalhes dos vídeos
      const detailsResponse = await axios.get(`${this.baseUrl}/videos`, {
        params: {
          key: this.apiKey,
          id: videoIds,
          part: "contentDetails,snippet",
        },
      });

      const items: YouTubePlaylistItem[] = response.data.items.map(
        (item: any, index: number) => {
          const videoDetails = detailsResponse.data.items.find(
            (video: any) => video.id === item.contentDetails.videoId
          );

          const thumbnailUrl =
            item.snippet.thumbnails.medium?.url ||
            item.snippet.thumbnails.default.url;

          console.log(
            `🖼️ Thumbnail para ${item.snippet.title}: ${thumbnailUrl}`
          );

          return {
            id: item.contentDetails.videoId,
            title: item.snippet.title,
            channelTitle: item.snippet.channelTitle,
            duration: videoDetails
              ? this.parseDuration(videoDetails.contentDetails.duration)
              : "0:00",
            thumbnailUrl: thumbnailUrl,
            position: item.snippet.position,
          };
        }
      );

      return {
        items,
        nextPageToken: response.data.nextPageToken,
        totalResults: response.data.pageInfo.totalResults,
      };
    } catch (error) {
      console.error("Erro ao buscar playlist do YouTube:", error);
      return this.getMockPlaylistResults(playlistId, maxResults);
    }
  }

  /**
   * Busca uma playlist completa do YouTube
   */
  async getPlaylist(playlistId: string): Promise<{
    videos: YouTubeVideo[];
    videoCount: number;
    totalDuration?: number;
  } | null> {
    console.log(`🚀 getPlaylist chamado para: ${playlistId}`);
    console.log(`🔑 API Key válida: ${this.isApiKeyValid()}`);

    if (!this.isApiKeyValid()) {
      console.log("⚠️ API Key inválida, usando dados mock para playlist");
      const mockResult = this.getMockPlaylistResults(playlistId, 50);
      return {
        videos: mockResult.items.map((item) => ({
          id: item.id,
          title: item.title,
          channelTitle: item.channelTitle,
          duration: item.duration,
          thumbnailUrl: item.thumbnailUrl,
          publishedAt: new Date().toISOString(),
        })),
        videoCount: mockResult.totalResults,
        totalDuration: mockResult.items.reduce((total, item) => {
          const [minutes, seconds] = item.duration.split(":").map(Number);
          return total + minutes * 60 + seconds;
        }, 0),
      };
    }

    try {
      console.log(`🔄 Buscando playlist do YouTube: ${playlistId}`);

      // Primeiro, buscar informações básicas da playlist
      const playlistResponse = await axios.get(`${this.baseUrl}/playlists`, {
        params: {
          key: this.apiKey,
          id: playlistId,
          part: "snippet,contentDetails",
        },
      });

      if (
        !playlistResponse.data.items ||
        playlistResponse.data.items.length === 0
      ) {
        console.error(`❌ Playlist não encontrada: ${playlistId}`);
        return null;
      }

      const playlistInfo = playlistResponse.data.items[0];
      console.log(
        `✅ Playlist encontrada: ${playlistInfo.snippet.title} (${playlistInfo.contentDetails.itemCount} itens)`
      );

      // Buscar itens da playlist
      console.log(`🔍 Chamando getPlaylistItems para ${playlistId}`);
      const playlistItemsResult = await this.getPlaylistItems(playlistId, 50);

      if (
        !playlistItemsResult.items ||
        playlistItemsResult.items.length === 0
      ) {
        console.log(`⚠️ Playlist encontrada mas sem itens válidos`);
        return {
          videos: [],
          videoCount: 0,
          totalDuration: 0,
        };
      }

      console.log(
        `📋 Processando ${playlistItemsResult.items.length} itens da playlist`
      );

      // Converter itens para formato de vídeos
      const videos: YouTubeVideo[] = playlistItemsResult.items.map(
        (item, index) => {
          console.log(
            `🎵 Item ${index + 1}: ${item.title} - Thumbnail: ${
              item.thumbnailUrl
            }`
          );
          return {
            id: item.id,
            title: item.title,
            channelTitle: item.channelTitle,
            duration: item.duration,
            thumbnailUrl: item.thumbnailUrl,
            publishedAt: new Date().toISOString(),
          };
        }
      );

      // Calcular duração total
      const totalDuration = playlistItemsResult.items.reduce((total, item) => {
        const [minutes, seconds] = item.duration.split(":").map(Number);
        return total + minutes * 60 + seconds;
      }, 0);

      console.log(
        `✅ Playlist processada: ${
          videos.length
        } vídeos, duração total: ${Math.floor(totalDuration / 60)}:${(
          totalDuration % 60
        )
          .toString()
          .padStart(2, "0")}`
      );

      return {
        videos,
        videoCount: videos.length,
        totalDuration,
      };
    } catch (error) {
      console.error("❌ Erro ao buscar playlist do YouTube:", error);

      // Em caso de erro, retornar dados mock
      console.log("🔄 Usando dados mock como fallback");
      const mockResult = this.getMockPlaylistResults(playlistId, 50);
      return {
        videos: mockResult.items.map((item) => ({
          id: item.id,
          title: item.title,
          channelTitle: item.channelTitle,
          duration: item.duration,
          thumbnailUrl: item.thumbnailUrl,
          publishedAt: new Date().toISOString(),
        })),
        videoCount: mockResult.totalResults,
        totalDuration: mockResult.items.reduce((total, item) => {
          const [minutes, seconds] = item.duration.split(":").map(Number);
          return total + minutes * 60 + seconds;
        }, 0),
      };
    }
  }

  /**
   * Valida uma URL de playlist do YouTube
   */
  validatePlaylistUrl(url: string): { isValid: boolean; playlistId?: string } {
    const playlistRegex = /[?&]list=([a-zA-Z0-9_-]+)/;
    const match = url.match(playlistRegex);

    if (match && match[1]) {
      return {
        isValid: true,
        playlistId: match[1],
      };
    }

    return { isValid: false };
  }

  /**
   * Converte duração ISO 8601 para formato legível
   */
  private parseDuration(duration: string): string {
    const match = duration.match(/PT(\d+H)?(\d+M)?(\d+S)?/);
    if (!match) return "0:00";

    const hours = parseInt(match[1]?.replace("H", "") || "0");
    const minutes = parseInt(match[2]?.replace("M", "") || "0");
    const seconds = parseInt(match[3]?.replace("S", "") || "0");

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, "0")}:${seconds
        .toString()
        .padStart(2, "0")}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  }

  /**
   * Verifica se a API key é válida
   */
  private isApiKeyValid(): boolean {
    return !!(
      this.apiKey &&
      !this.apiKey.includes("Dummy") &&
      !this.apiKey.includes("dummy") &&
      this.apiKey.length >= 30 &&
      this.apiKey.startsWith("AIza")
    );
  }

  /**
   * Retorna resultados mock para desenvolvimento
   */
  private getMockSearchResults(
    query: string,
    maxResults: number
  ): YouTubeSearchResult {
    const mockVideos: YouTubeVideo[] = [
      {
        id: "dQw4w9WgXcQ",
        title: "Rick Astley - Never Gonna Give You Up",
        channelTitle: "Rick Astley",
        duration: "3:33",
        thumbnailUrl: "https://i.ytimg.com/vi/dQw4w9WgXcQ/mqdefault.jpg",
        publishedAt: "2009-10-25T06:57:33Z",
      },
      {
        id: "fJ9rUzIMcZQ",
        title: "Queen - Bohemian Rhapsody",
        channelTitle: "Queen Official",
        duration: "5:55",
        thumbnailUrl: "https://i.ytimg.com/vi/fJ9rUzIMcZQ/mqdefault.jpg",
        publishedAt: "2008-08-01T15:53:05Z",
      },
      {
        id: "kJQP7kiw5Fk",
        title: "Luis Fonsi - Despacito ft. Daddy Yankee",
        channelTitle: "Luis Fonsi",
        duration: "4:41",
        thumbnailUrl: "https://i.ytimg.com/vi/kJQP7kiw5Fk/mqdefault.jpg",
        publishedAt: "2017-01-12T19:06:32Z",
      },
    ]
      .filter(
        (video) =>
          video.title.toLowerCase().includes(query.toLowerCase()) ||
          video.channelTitle.toLowerCase().includes(query.toLowerCase())
      )
      .slice(0, maxResults);

    return {
      videos: mockVideos,
      totalResults: mockVideos.length,
    };
  }

  /**
   * Retorna resultados mock de playlist para desenvolvimento
   */
  private getMockPlaylistResults(
    playlistId: string,
    maxResults: number
  ): YouTubePlaylistResult {
    const mockItems: YouTubePlaylistItem[] = [
      {
        id: "dQw4w9WgXcQ",
        title: "Never Gonna Give You Up",
        channelTitle: "Rick Astley",
        duration: "3:33",
        thumbnailUrl: "https://i.ytimg.com/vi/dQw4w9WgXcQ/mqdefault.jpg",
        position: 0,
      },
      {
        id: "fJ9rUzIMcZQ",
        title: "Bohemian Rhapsody",
        channelTitle: "Queen",
        duration: "5:55",
        thumbnailUrl: "https://i.ytimg.com/vi/fJ9rUzIMcZQ/mqdefault.jpg",
        position: 1,
      },
      {
        id: "kJQP7kiw5Fk",
        title: "Despacito",
        channelTitle: "Luis Fonsi",
        duration: "4:41",
        thumbnailUrl: "https://i.ytimg.com/vi/kJQP7kiw5Fk/mqdefault.jpg",
        position: 2,
      },
    ].slice(0, maxResults);

    return {
      items: mockItems,
      totalResults: mockItems.length,
    };
  }
}

export const youtubeService = new YouTubeService();
export type {
  YouTubeVideo,
  YouTubePlaylistItem,
  YouTubeSearchResult,
  YouTubePlaylistResult,
};
