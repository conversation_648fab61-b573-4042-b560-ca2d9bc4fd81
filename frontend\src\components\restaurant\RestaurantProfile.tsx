import React, { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useParams } from "react-router-dom";
import {
  User,
  Mail,
  Phone,
  MapPin,
  Building2,
  Clock,
  Save,
  Edit,
  X,
  Check,
  AlertCircle,
  RefreshCw,
  Settings,
  FileImage,
  ExternalLink,
  Eye,
  Shield,
  Bell,
} from "lucide-react";
import { YouTubeAuthManager } from "@/components/restaurant/YouTubeAuthManager";
import { toast } from "react-hot-toast";
import { buildApiUrl, API_CONFIG } from "../../config/api";

// Tipos e Interfaces
type TabType = "basic" | "hours" | "settings" | "appearance" | "integrations";

interface BusinessHours {
  open: string;
  close: string;
  isOpen: boolean;
}

interface RestaurantSettings {
  // Já existentes
  allowSuggestions: boolean;
  maxSuggestionsPerUser: number;
  autoPlayEnabled: boolean;
  autoSkipDisliked?: boolean;
  // Da tela Settings antiga (fundidas)
  allowVoting?: boolean;
  showQueue?: boolean;
  showVoteCounts?: boolean;
  // Interface
  darkMode?: boolean;
  primaryColor?: string;
  // Moderação
  moderation?: {
    autoApprove: boolean;
    requireModeration: boolean;
    bannedWords: string[];
    maxVotesForAutoApproval: number;
    minVotesForAutoRejection: number;
  };
  // Agenda (horário de abertura/fechamento geral)
  schedule?: {
    enabled: boolean;
    openTime: string;
    closeTime: string;
    timezone: string;
    closedMessage: string;
  };
  // Notificações
  notifications?: {
    emailNotifications: boolean;
    newSuggestionAlert: boolean;
    highVoteAlert: boolean;
    moderationAlert: boolean;
  };
  // Áudio
  audio?: {
    volume: number; // 0-100
    fadeInDuration: number; // s
    fadeOutDuration: number; // s
    crossfade: boolean;
  };
  // Integração YouTube (UI no Profile)
  youtube?: {
    enabled?: boolean;
  };
}

type AddressObj = {
  street?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
};

interface RestaurantProfile {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string | AddressObj;
  description: string;
  businessHours: {
    [key: string]: BusinessHours;
  };
  settings: RestaurantSettings;
  logoUrl?: string;
  bannerUrl?: string;
  appearance?: {
    darkMode: boolean;
    primaryColor: string;
    accentColor: string;
    fontFamily: string;
  };
  integrations?: {
    youtubeApiEnabled: boolean;
    spotifyConnected: boolean;
    googleAnalytics: boolean;
  };
}

interface TabProps {
  icon: React.ReactNode;
  label: string;
  id: TabType;
  active: boolean;
  onClick: (id: TabType) => void;
}

// Componente de Tab
const Tab: React.FC<TabProps> = ({ icon, label, id, active, onClick }) => (
  <button
    onClick={() => onClick(id)}
    className={`flex items-center space-x-2 px-4 py-3 rounded-lg transition-colors ${
      active
        ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 font-medium"
        : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800"
    }`}
    aria-selected={active}
    role="tab"
    id={`tab-${id}`}
    aria-controls={`panel-${id}`}
  >
    {React.cloneElement(icon as React.ReactElement, {
      className: `w-5 h-5 ${
        active
          ? "text-blue-600 dark:text-blue-400"
          : "text-gray-500 dark:text-gray-400"
      }`,
    })}
    <span>{label}</span>
  </button>
);

// Componente Principal
const RestaurantProfile: React.FC = () => {
  const { restaurantId } = useParams<{ restaurantId: string }>();

  // Mapeamento entre frontend e backend schemas
  const mapBackendToFrontend = (backendSettings: any): Partial<RestaurantSettings> => {
    return {
      // Configurações básicas
      allowSuggestions: backendSettings?.interface?.allowAnonymousSuggestions ?? true,
      maxSuggestionsPerUser: backendSettings?.moderation?.maxSuggestionsPerUser ?? 3,
      autoPlayEnabled: true, // Não existe no backend, sempre true
      autoSkipDisliked: false, // Não existe no backend, sempre false

      // Interface do cliente (mapeamento do backend.interface)
      allowVoting: backendSettings?.interface?.showVoteCount ?? true,
      showQueue: backendSettings?.interface?.showQueuePosition ?? true,
      showVoteCounts: backendSettings?.interface?.showVoteCount ?? true,

      // Aparência (mapeamento do backend.interface)
      darkMode: backendSettings?.interface?.theme === "dark",
      primaryColor: backendSettings?.interface?.primaryColor ?? "#3B82F6",

      // Moderação (mapeamento do backend.moderation)
      moderation: {
        autoApprove: backendSettings?.moderation?.autoApprove ?? false,
        requireModeration: backendSettings?.moderation?.requireApproval ?? true,
        bannedWords: [], // Não implementado no backend ainda
        maxVotesForAutoApproval: 10, // Não existe no backend
        minVotesForAutoRejection: -5, // Não existe no backend
      },

      // Notificações (mapeamento do backend.notifications.email)
      notifications: {
        emailNotifications: backendSettings?.notifications?.email?.enabled ?? true,
        newSuggestionAlert: backendSettings?.notifications?.email?.newSuggestions ?? true,
        highVoteAlert: true, // Não existe no backend
        moderationAlert: backendSettings?.notifications?.email?.moderationRequired ?? true,
      },

      // Áudio (mapeamento do backend.playlist)
      audio: {
        volume: backendSettings?.playlist?.defaultVolume ?? 75,
        fadeInDuration: backendSettings?.playlist?.crossfadeDuration ?? 3,
        fadeOutDuration: backendSettings?.playlist?.crossfadeDuration ?? 3,
        crossfade: backendSettings?.playlist?.crossfadeDuration > 0,
      },

      // Schedule (mapeamento do backend.schedule)
      schedule: {
        enabled: true,
        openTime: "11:00",
        closeTime: "23:00",
        timezone: backendSettings?.schedule?.timezone ?? "America/Sao_Paulo",
        closedMessage: "Estamos fechados. Volte durante nosso horário de funcionamento!",
      },

      // YouTube
      youtube: { enabled: true },
    };
  };

  const mapFrontendToBackend = (frontendSettings: RestaurantSettings): any => {
    return {
      moderation: {
        autoApprove: frontendSettings.moderation?.autoApprove ?? false,
        requireApproval: frontendSettings.moderation?.requireModeration ?? true,
        maxSuggestionsPerUser: frontendSettings.maxSuggestionsPerUser ?? 3,
        maxSuggestionsPerHour: 10, // Valor fixo
        allowExplicitContent: false, // Valor fixo
        allowLiveVideos: true, // Valor fixo
        minVideoDuration: 30, // Valor fixo
        maxVideoDuration: 600, // Valor fixo
      },
      playlist: {
        maxQueueSize: 50, // Valor fixo
        allowDuplicates: false, // Valor fixo
        shuffleMode: false, // Valor fixo
        repeatMode: "none", // Valor fixo
        crossfadeDuration: frontendSettings.audio?.crossfade ? (frontendSettings.audio?.fadeInDuration ?? 3) : 0,
        defaultVolume: frontendSettings.audio?.volume ?? 75,
      },
      interface: {
        theme: frontendSettings.darkMode ? "dark" : "light",
        primaryColor: frontendSettings.primaryColor ?? "#3B82F6",
        secondaryColor: "#10B981", // Valor fixo
        showVoteCount: frontendSettings.allowVoting ?? true,
        showQueuePosition: frontendSettings.showQueue ?? true,
        allowAnonymousSuggestions: frontendSettings.allowSuggestions ?? true,
        requireSessionId: true, // Valor fixo
      },
      notifications: {
        email: {
          enabled: frontendSettings.notifications?.emailNotifications ?? true,
          newSuggestions: frontendSettings.notifications?.newSuggestionAlert ?? true,
          moderationRequired: frontendSettings.notifications?.moderationAlert ?? true,
          dailyReport: false, // Valor fixo
        },
        webhook: {
          enabled: false, // Valor fixo
          url: "", // Valor fixo
          events: [], // Valor fixo
        },
      },
      schedule: {
        timezone: frontendSettings.schedule?.timezone ?? "America/Sao_Paulo",
        // operatingHours será gerenciado pela aba de horários
      },
    };
  };

  // Valores padrão usando o mapeamento
  const defaultMergedSettings = mapBackendToFrontend({});

  // Validar que restaurantId existe
  if (!restaurantId) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600">Erro de Rota</h1>
          <p className="text-gray-600 mt-2">
            ID do restaurante não fornecido na URL
          </p>
        </div>
      </div>
    );
  }

  const [profile, setProfile] = useState<RestaurantProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const mergeSettings = (incoming: Partial<RestaurantSettings>): RestaurantSettings => {
    return {
      ...defaultMergedSettings,
      ...incoming,
      moderation: { ...defaultMergedSettings.moderation!, ...incoming.moderation },
      schedule: { ...defaultMergedSettings.schedule!, ...incoming.schedule },
      notifications: { ...defaultMergedSettings.notifications!, ...incoming.notifications },
      audio: { ...defaultMergedSettings.audio!, ...incoming.audio },
      youtube: { ...defaultMergedSettings.youtube!, ...incoming.youtube },
    } as RestaurantSettings;
  };

  const [editing, setEditing] = useState(false);
  const [saving, setSaving] = useState(false);
  const [editedProfile, setEditedProfile] = useState<RestaurantProfile | null>(
    null
  );
  const [activeTab, setActiveTab] = useState<TabType>("basic");
  const [error, setError] = useState<string | null>(null);

  // Função para carregar o perfil
  const loadProfile = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      console.log("🏪 Carregando perfil do restaurante:", restaurantId);

      const url = buildApiUrl(
        `${API_CONFIG.ENDPOINTS.RESTAURANTS}/${restaurantId}/profile`
      );
      console.log("🏪 URL:", url);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const response = await fetch(url, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const data = await response.json();
        console.log("🏪 Profile loaded:", data);

        if (data.success && data.profile) {
          // Mapear configurações do backend para o frontend
          const backendSettings = data.profile.settings || {};
          const mappedSettings = mapBackendToFrontend(backendSettings);
          const mergedSettings = mergeSettings(mappedSettings);

          const profileData: RestaurantProfile = {
            id: data.profile.id,
            name: data.profile.name || "",
            email: data.profile.email || "",
            phone: data.profile.phone || "",
            address: {
              street: data.profile.address?.street || "",
              city: data.profile.address?.city || "",
              state: data.profile.address?.state || "",
              zipCode: data.profile.address?.zipCode || "",
              country: data.profile.address?.country || "Brasil",
            },
            description: data.profile.description || "",
            logoUrl: data.profile.logo || "",
            bannerUrl: data.profile.banner || "",
            businessHours: data.profile.businessHours || {},
            settings: mergedSettings,
            appearance: data.profile.appearance || {
              darkMode: false,
              primaryColor: "#3b82f6",
              accentColor: "#10b981",
              fontFamily: "Inter",
            },
            integrations: data.profile.integrations || {
              youtubeApiEnabled: false,
              spotifyConnected: false,
              googleAnalytics: false,
            },
          };

          setProfile(profileData);
          setEditedProfile(profileData);
        } else {
          throw new Error(data.message || "Dados do perfil inválidos");
        }
      } else {
        console.error(
          "🏪 Erro ao carregar perfil:",
          response.status,
          response.statusText
        );
        const errorText = await response.text().catch(() => "");
        console.error("🏪 Error details:", errorText);
        throw new Error(
          `Erro ${response.status}: ${response.statusText || "Erro desconhecido"}`
        );
      }
    } catch (error: any) {
      console.error("🏪 Erro ao carregar perfil:", error);
      if (error.name === "AbortError") {
        setError("Timeout: A requisição demorou muito para responder");
      } else if (error.message?.includes("Failed to fetch")) {
        setError("Erro de conexão: Verifique sua internet");
      } else {
        setError(error.message || "Erro desconhecido ao carregar perfil");
      }
    } finally {
      setLoading(false);
    }
  }, [restaurantId]);

  // Garante que o carregamento do perfil ocorra sempre, mesmo quando loading === true no primeiro render
  useEffect(() => {
    loadProfile();
  }, [loadProfile]);

  // Estados de loading e error - retorno antecipado
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <RefreshCw
          className="w-8 h-8 animate-spin text-blue-600"
          aria-hidden="true"
        />
        <span className="sr-only">Carregando perfil do restaurante</span>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="text-center py-12 bg-red-50 dark:bg-red-900/20 rounded-lg">
        <AlertCircle
          className="w-12 h-12 text-red-400 mx-auto mb-4"
          aria-hidden="true"
        />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Erro ao carregar perfil
        </h3>
        <p className="text-red-500 mb-4">
          {error || "Não foi possível carregar os dados do perfil"}
        </p>
        <button
          onClick={loadProfile}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
        >
          Tentar novamente
        </button>
      </div>
    );
  }

  const daysOfWeek = [
    { key: "monday", label: "Segunda-feira" },
    { key: "tuesday", label: "Terça-feira" },
    { key: "wednesday", label: "Quarta-feira" },
    { key: "thursday", label: "Quinta-feira" },
    { key: "friday", label: "Sexta-feira" },
    { key: "saturday", label: "Sábado" },
    { key: "sunday", label: "Domingo" },
  ];

  const tabs = [
    {
      id: "basic" as TabType,
      label: "Informações Básicas",
      icon: <Building2 />,
    },
    {
      id: "hours" as TabType,
      label: "Horário de Funcionamento",
      icon: <Clock />,
    },
    { id: "settings" as TabType, label: "Configurações", icon: <Settings /> },
    { id: "appearance" as TabType, label: "Aparência", icon: <FileImage /> },
    {
      id: "integrations" as TabType,
      label: "Integrações",
      icon: <ExternalLink />,
    },
  ];

  const handleSave = async () => {
    if (!editedProfile) return;

    try {
      setSaving(true);
      console.log("🏪 Salvando perfil do restaurante:", restaurantId);

      const url = buildApiUrl(
        `${API_CONFIG.ENDPOINTS.RESTAURANTS}/${restaurantId}/profile`
      );
      console.log("🏪 Salvando para:", url);

      // Mapear configurações do frontend para o formato do backend
      const backendSettings = mapFrontendToBackend(editedProfile.settings);
      const profileToSave = {
        ...editedProfile,
        settings: backendSettings,
      };

      console.log("🔄 Frontend settings:", editedProfile.settings);
      console.log("🔄 Backend settings:", backendSettings);
      console.log("🔄 Profile to save:", profileToSave);

      const response = await fetch(url, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(profileToSave),
      });

      if (response.ok) {
        const data = await response.json();
        console.log("🏪 Profile saved:", data);
        setProfile(data.profile || editedProfile);
        setEditing(false);
        toast.success("Perfil atualizado com sucesso!");
      } else {
        console.error(
          "🏪 Erro ao salvar:",
          response.status,
          response.statusText
        );
        const errorText = await response.text().catch(() => "");
        console.error("🏪 Error details:", errorText);
        throw new Error(`API returned status ${response.status}`);
      }
    } catch (error: any) {
      console.error("🏪 Erro ao salvar perfil:", error);

      // Mesmo em caso de erro de conectividade, salvar localmente
      setProfile(editedProfile);
      setEditing(false);

      if (error?.response?.status) {
        toast.error(
          `Erro ${error.response.status}: ${
            error.response.data?.message || "Erro ao salvar perfil"
          }`
        );
      } else {
        toast("Perfil salvo localmente (sem conexão com servidor)", { icon: "⚠️" });
      }
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setEditedProfile(profile);
    setEditing(false);
  };

  const updateField = (field: string, value: any) => {
    if (!editedProfile) return;
    setEditedProfile({
      ...editedProfile,
      [field]: value,
    });
  };

  const updateBusinessHours = (day: string, field: string, value: any) => {
    if (!editedProfile) return;
    setEditedProfile({
      ...editedProfile,
      businessHours: {
        ...editedProfile.businessHours,
        [day]: {
          ...editedProfile.businessHours[day],
          [field]: value,
        },
      },
    });
  };

  // Atualiza settings garantindo objeto completo para tipos aninhados
  const updateSettingsSafe = <K extends keyof RestaurantSettings>(field: K, value: any) => {
    if (!editedProfile) return;
    const current = editedProfile.settings as RestaurantSettings;
    let next: RestaurantSettings = current;
    if (field === 'moderation') {
      next = mergeSettings({ ...current, moderation: { ...current.moderation!, ...(value as any) } } as RestaurantSettings);
    } else if (field === 'notifications') {
      next = mergeSettings({ ...current, notifications: { ...current.notifications!, ...(value as any) } } as RestaurantSettings);
    } else if (field === 'audio') {
      next = mergeSettings({ ...current, audio: { ...current.audio!, ...(value as any) } } as RestaurantSettings);
    } else {
      next = mergeSettings({ ...current, [field]: value } as RestaurantSettings);
    }
    setEditedProfile({ ...editedProfile, settings: next });
  };

  const updateSettings = <K extends keyof RestaurantSettings>(field: K, value: any) => {
    updateSettingsSafe(field, value);
  };

  const updateAppearance = (field: string, value: any) => {
    if (!editedProfile || !editedProfile.appearance) return;
    setEditedProfile({
      ...editedProfile,
      appearance: {
        ...editedProfile.appearance,
        [field]: value,
      },
    });
  };

  const updateIntegrations = (field: string, value: any) => {
    if (!editedProfile || !editedProfile.integrations) return;
    setEditedProfile({
      ...editedProfile,
      integrations: {
        ...editedProfile.integrations,
        [field]: value,
      },
    });
  };

  // Renderizadores de abas
  const renderBasicInfo = () => (
    <div
      className="space-y-6"
      role="tabpanel"
      id="panel-basic"
      aria-labelledby="tab-basic"
    >
      {/* Grid de Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

        {/* Card 1: Informações de Contato */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center space-x-2 mb-4">
            <Building2 className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Informações de Contato</h3>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
            Dados básicos de identificação e contato do restaurante
          </p>

          <div className="space-y-4">
            {/* Nome do Restaurante */}
            <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <label
                htmlFor="restaurant-name"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
              >
                Nome do Restaurante
              </label>
              {editing ? (
                <input
                  id="restaurant-name"
                  type="text"
                  value={editedProfile?.name || ""}
                  onChange={(e) => updateField("name", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              ) : (
                <div className="flex items-center space-x-2 text-gray-900 dark:text-white">
                  <Building2 className="w-4 h-4 text-gray-500" aria-hidden="true" />
                  <span className="font-medium">{profile?.name}</span>
                </div>
              )}
            </div>

            {/* Email */}
            <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
              >
                Email
              </label>
              {editing ? (
                <input
                  id="email"
                  type="email"
                  value={editedProfile?.email || ""}
                  onChange={(e) => updateField("email", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              ) : (
                <div className="flex items-center space-x-2 text-gray-900 dark:text-white">
                  <Mail className="w-4 h-4 text-gray-500" aria-hidden="true" />
                  <span className="font-medium">{profile?.email}</span>
                </div>
              )}
            </div>

            {/* Telefone */}
            <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <label
                htmlFor="phone"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
              >
                Telefone
              </label>
              {editing ? (
                <input
                  id="phone"
                  type="tel"
                  value={editedProfile?.phone || ""}
                  onChange={(e) => updateField("phone", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              ) : (
                <div className="flex items-center space-x-2 text-gray-900 dark:text-white">
                  <Phone className="w-4 h-4 text-gray-500" aria-hidden="true" />
                  <span className="font-medium">{profile?.phone}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Card 2: Localização */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center space-x-2 mb-4">
            <MapPin className="w-5 h-5 text-green-600 dark:text-green-400" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Localização</h3>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
            Endereço e informações de localização do restaurante
          </p>

          <div className="space-y-4">
            {/* Endereço */}
            <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <label
                htmlFor="address"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
              >
                Endereço Completo
              </label>
              {editing ? (
                <textarea
                  id="address"
                  rows={3}
                  value={
                    typeof editedProfile?.address === "string"
                      ? editedProfile?.address
                      : [
                          (editedProfile?.address as AddressObj)?.street,
                          (editedProfile?.address as AddressObj)?.city,
                          (editedProfile?.address as AddressObj)?.state,
                          (editedProfile?.address as AddressObj)?.zipCode,
                          (editedProfile?.address as AddressObj)?.country,
                        ]
                          .filter(Boolean)
                          .join(", ")
                  }
                  onChange={(e) => updateField("address", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                />
              ) : (
                <div className="flex items-start space-x-2 text-gray-900 dark:text-white">
                  <MapPin className="w-4 h-4 text-gray-500 mt-0.5" aria-hidden="true" />
                  <span className="font-medium">
                    {typeof profile?.address === "string"
                      ? profile?.address
                      : [
                          (profile?.address as AddressObj)?.street,
                          (profile?.address as AddressObj)?.city,
                          (profile?.address as AddressObj)?.state,
                          (profile?.address as AddressObj)?.zipCode,
                          (profile?.address as AddressObj)?.country,
                        ]
                          .filter(Boolean)
                          .join(", ")}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Segunda linha - Card de Descrição */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center space-x-2 mb-4">
          <FileImage className="w-5 h-5 text-purple-600 dark:text-purple-400" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Descrição</h3>
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
          Descrição detalhada do restaurante para os clientes
        </p>

        <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
          <label
            htmlFor="description"
            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >
            Descrição do Restaurante
          </label>
          {editing ? (
            <textarea
              id="description"
              value={editedProfile?.description || ""}
              onChange={(e) => updateField("description", e.target.value)}
              rows={4}
              placeholder="Descreva seu restaurante, especialidades, ambiente..."
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
            />
          ) : (
            <div className="text-gray-900 dark:text-white">
              <span className="font-medium">{profile?.description || "Nenhuma descrição fornecida"}</span>
            </div>
          )}
        </div>
      </div>

      {/* Terceira linha - Card de Imagens */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center space-x-2 mb-4">
          <FileImage className="w-5 h-5 text-orange-600 dark:text-orange-400" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Imagens do Restaurante</h3>
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
          Logo e imagens que representam seu restaurante
        </p>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Logo */}
          <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Logo do Restaurante
            </label>
            {editing ? (
              <div className="space-y-3">
                <div className="flex items-center justify-center h-40 w-40 mx-auto border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 overflow-hidden">
                  {editedProfile?.logoUrl ? (
                    <img
                      src={editedProfile.logoUrl}
                      alt="Logo do Restaurante"
                      className="max-h-full max-w-full object-contain"
                    />
                  ) : (
                    <div className="text-center p-4">
                      <FileImage
                        className="w-8 h-8 text-gray-400 mx-auto mb-2"
                        aria-hidden="true"
                      />
                      <p className="text-xs text-gray-500">
                        Logo do restaurante
                      </p>
                    </div>
                  )}
                </div>
                <input
                  type="text"
                  placeholder="URL da imagem do logo"
                  value={editedProfile?.logoUrl || ""}
                  onChange={(e) => updateField("logoUrl", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            ) : (
              <div className="flex items-center justify-center h-40 w-40 mx-auto bg-white dark:bg-gray-800 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-600">
                {profile?.logoUrl ? (
                  <img
                    src={profile.logoUrl}
                    alt="Logo do Restaurante"
                    className="max-h-full max-w-full object-contain"
                  />
                ) : (
                  <Building2
                    className="w-12 h-12 text-gray-400"
                    aria-hidden="true"
                  />
                )}
              </div>
            )}
          </div>

          {/* Banner */}
          <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Banner do Restaurante
            </label>
            {editing ? (
              <div className="space-y-3">
                <div className="flex items-center justify-center h-32 w-full border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 overflow-hidden">
                  {editedProfile?.bannerUrl ? (
                    <img
                      src={editedProfile.bannerUrl}
                      alt="Banner do Restaurante"
                      className="max-h-full max-w-full object-cover w-full h-full"
                    />
                  ) : (
                    <div className="text-center p-4">
                      <FileImage
                        className="w-8 h-8 text-gray-400 mx-auto mb-2"
                        aria-hidden="true"
                      />
                      <p className="text-xs text-gray-500">
                        Banner do restaurante
                      </p>
                    </div>
                  )}
                </div>
                <input
                  type="text"
                  placeholder="URL da imagem do banner"
                  value={editedProfile?.bannerUrl || ""}
                  onChange={(e) => updateField("bannerUrl", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            ) : (
              <div className="flex items-center justify-center h-32 w-full bg-white dark:bg-gray-800 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-600">
                {profile?.bannerUrl ? (
                  <img
                    src={profile.bannerUrl}
                    alt="Banner do Restaurante"
                    className="max-h-full max-w-full object-cover w-full h-full"
                  />
                ) : (
                  <Building2
                    className="w-12 h-12 text-gray-400"
                    aria-hidden="true"
                  />
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  const renderBusinessHours = () => (
    <div
      className="space-y-6"
      role="tabpanel"
      id="panel-hours"
      aria-labelledby="tab-hours"
    >
      {/* Card de Horários */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center space-x-2 mb-4">
          <Clock className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Horário de Funcionamento</h3>
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
          Configure os horários de funcionamento para cada dia da semana
        </p>

        <div className="space-y-3">
          {daysOfWeek.map((day) => (
            <div
              key={day.key}
              className="flex flex-wrap md:flex-nowrap items-center gap-4 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-100 dark:border-gray-600"
            >
              <div className="w-full md:w-32">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {day.label}
                </span>
              </div>

              {editing ? (
                <>
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={
                        editedProfile?.businessHours[day.key]?.isOpen || false
                      }
                      onChange={(e) =>
                        updateBusinessHours(day.key, "isOpen", e.target.checked)
                      }
                      className="rounded text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm">Aberto</span>
                  </label>

                  {editedProfile?.businessHours[day.key]?.isOpen && (
                    <div className="flex flex-wrap md:flex-nowrap items-center gap-2">
                      <label
                        className="text-sm sr-only"
                        htmlFor={`open-${day.key}`}
                      >
                        Horário de abertura
                      </label>
                      <input
                        id={`open-${day.key}`}
                        type="time"
                        value={editedProfile.businessHours[day.key]?.open || ""}
                        onChange={(e) =>
                          updateBusinessHours(day.key, "open", e.target.value)
                        }
                        className="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                      <span className="text-gray-500">às</span>
                      <label
                        className="text-sm sr-only"
                        htmlFor={`close-${day.key}`}
                      >
                        Horário de fechamento
                      </label>
                      <input
                        id={`close-${day.key}`}
                        type="time"
                        value={
                          editedProfile.businessHours[day.key]?.close || ""
                        }
                        onChange={(e) =>
                          updateBusinessHours(day.key, "close", e.target.value)
                        }
                        className="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  )}
                </>
              ) : (
                <div className="flex items-center space-x-2">
                  {profile?.businessHours[day.key]?.isOpen ? (
                    <>
                      <Check
                        className="w-5 h-5 text-green-600"
                        aria-hidden="true"
                      />
                      <span className="text-sm text-gray-700 dark:text-gray-300">
                        {profile.businessHours[day.key]?.open} às{" "}
                        {profile.businessHours[day.key]?.close}
                      </span>
                    </>
                  ) : (
                    <>
                      <X className="w-5 h-5 text-red-600" aria-hidden="true" />
                      <span className="text-sm text-gray-500">Fechado</span>
                    </>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderSettings = () => (
    <div
      className="space-y-6"
      role="tabpanel"
      id="panel-settings"
      aria-labelledby="tab-settings"
    >
      {/* Grid de Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

        {/* Card 1: Configurações Básicas */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center space-x-2 mb-4">
            <Settings className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Configurações Básicas</h3>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
            Controle fundamental da playlist e sugestões dos clientes
          </p>

          <div className="space-y-4">
            {/* Permitir Sugestões */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Permitir Sugestões dos Clientes
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Clientes podem sugerir músicas via QR code
                </p>
              </div>
              {editing ? (
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={editedProfile?.settings.allowSuggestions || false}
                    onChange={(e) => updateSettings("allowSuggestions", e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
              ) : (
                <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                  profile?.settings.allowSuggestions
                    ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                    : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                }`}>
                  {profile?.settings.allowSuggestions ? "Ativo" : "Inativo"}
                </div>
              )}
            </div>

            {/* Reprodução Automática */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Reprodução Automática
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Próxima música inicia automaticamente
                </p>
              </div>
              {editing ? (
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={editedProfile?.settings.autoPlayEnabled || false}
                    onChange={(e) => updateSettings("autoPlayEnabled", e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
              ) : (
                <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                  profile?.settings.autoPlayEnabled
                    ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                    : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                }`}>
                  {profile?.settings.autoPlayEnabled ? "Ativo" : "Inativo"}
                </div>
              )}
            </div>

            {/* Auto Skip */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Pular Músicas com Muitos Votos Negativos
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Músicas com muitos dislikes serão puladas automaticamente
                </p>
              </div>
              {editing ? (
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={editedProfile?.settings.autoSkipDisliked || false}
                    onChange={(e) => updateSettings("autoSkipDisliked", e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
              ) : (
                <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                  profile?.settings.autoSkipDisliked
                    ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                    : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                }`}>
                  {profile?.settings.autoSkipDisliked ? "Ativo" : "Inativo"}
                </div>
              )}
            </div>

            {/* Máximo de Sugestões */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Máximo de Sugestões por Cliente
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Limite de sugestões por cliente por sessão
                </p>
              </div>
              {editing ? (
                <input
                  type="number"
                  min="1"
                  max="10"
                  value={editedProfile?.settings.maxSuggestionsPerUser || 3}
                  onChange={(e) => updateSettings("maxSuggestionsPerUser", parseInt(e.target.value))}
                  className="w-20 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-center focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              ) : (
                <div className="px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 rounded-full text-xs font-medium">
                  {profile?.settings.maxSuggestionsPerUser}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Card 2: Interface do Cliente */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center space-x-2 mb-4">
            <Eye className="w-5 h-5 text-green-600 dark:text-green-400" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Interface do Cliente</h3>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
            Controles visuais que aparecem para os clientes
          </p>

          <div className="space-y-4">
            {/* Votação */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Permitir Votação
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Clientes podem votar nas músicas
                </p>
              </div>
              {editing ? (
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={!!editedProfile?.settings.allowVoting}
                    onChange={(e) => updateSettings("allowVoting", e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
              ) : (
                <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                  profile?.settings.allowVoting
                    ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                    : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                }`}>
                  {profile?.settings.allowVoting ? "Ativo" : "Inativo"}
                </div>
              )}
            </div>

            {/* Mostrar Fila */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Mostrar Fila
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Exibir fila de reprodução para clientes
                </p>
              </div>
              {editing ? (
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={!!editedProfile?.settings.showQueue}
                    onChange={(e) => updateSettings("showQueue", e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
              ) : (
                <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                  profile?.settings.showQueue
                    ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                    : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                }`}>
                  {profile?.settings.showQueue ? "Ativo" : "Inativo"}
                </div>
              )}
            </div>

            {/* Contagem de Votos */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Mostrar Contagem de Votos
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Exibir número de votos para clientes
                </p>
              </div>
              {editing ? (
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={!!editedProfile?.settings.showVoteCounts}
                    onChange={(e) => updateSettings("showVoteCounts", e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
              ) : (
                <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                  profile?.settings.showVoteCounts
                    ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                    : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                }`}>
                  {profile?.settings.showVoteCounts ? "Ativo" : "Inativo"}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Segunda linha de cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

        {/* Card 3: Moderação */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center space-x-2 mb-4">
            <Shield className="w-5 h-5 text-orange-600 dark:text-orange-400" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Moderação</h3>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
            Controles de aprovação e moderação de conteúdo
          </p>

          <div className="space-y-4">
            {/* Auto Aprovar */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Aprovação Automática
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Aprovar automaticamente com base em votos
                </p>
              </div>
              {editing ? (
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={!!editedProfile?.settings.moderation?.autoApprove}
                    onChange={(e) => updateSettings("moderation", { ...editedProfile?.settings.moderation, autoApprove: e.target.checked })}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
              ) : (
                <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                  profile?.settings.moderation?.autoApprove
                    ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                    : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                }`}>
                  {profile?.settings.moderation?.autoApprove ? "Ativo" : "Inativo"}
                </div>
              )}
            </div>

            {/* Moderação Manual */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Moderação Manual
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Exigir aprovação manual para sugestões
                </p>
              </div>
              {editing ? (
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={!!editedProfile?.settings.moderation?.requireModeration}
                    onChange={(e) => updateSettings("moderation", { ...editedProfile?.settings.moderation, requireModeration: e.target.checked })}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
              ) : (
                <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                  profile?.settings.moderation?.requireModeration
                    ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                    : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                }`}>
                  {profile?.settings.moderation?.requireModeration ? "Ativo" : "Inativo"}
                </div>
              )}
            </div>

            {/* Limites de Votos */}
            {editing && (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Máx. votos para auto-aprovar
                  </label>
                  <input
                    type="number"
                    value={editedProfile?.settings.moderation?.maxVotesForAutoApproval ?? 10}
                    onChange={(e) => updateSettings("moderation", { ...editedProfile?.settings.moderation, maxVotesForAutoApproval: parseInt(e.target.value || '0', 10) })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Mín. votos para auto-rejeitar
                  </label>
                  <input
                    type="number"
                    value={editedProfile?.settings.moderation?.minVotesForAutoRejection ?? -5}
                    onChange={(e) => updateSettings("moderation", { ...editedProfile?.settings.moderation, minVotesForAutoRejection: parseInt(e.target.value || '0', 10) })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            )}

            {/* Palavras Banidas */}
            <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Palavras Banidas
                  </label>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Lista de palavras que serão automaticamente rejeitadas
                  </p>
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {editedProfile?.settings.moderation?.bannedWords?.length || 0} palavras
                </div>
              </div>

              {editing ? (
                <div className="space-y-2">
                  <div className="flex gap-2">
                    <input
                      type="text"
                      placeholder="Adicionar palavra banida..."
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          const input = e.target as HTMLInputElement;
                          const word = input.value.trim().toLowerCase();
                          if (word && !editedProfile?.settings.moderation?.bannedWords?.includes(word)) {
                            updateSettings("moderation", {
                              ...editedProfile?.settings.moderation,
                              bannedWords: [...(editedProfile?.settings.moderation?.bannedWords || []), word]
                            });
                            input.value = '';
                          }
                        }
                      }}
                    />
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {editedProfile?.settings.moderation?.bannedWords?.map((word, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center gap-1 px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 rounded text-xs"
                      >
                        {word}
                        <button
                          type="button"
                          onClick={() => {
                            const newWords = editedProfile?.settings.moderation?.bannedWords?.filter((_, i) => i !== index) || [];
                            updateSettings("moderation", {
                              ...editedProfile?.settings.moderation,
                              bannedWords: newWords
                            });
                          }}
                          className="ml-1 hover:text-red-600 dark:hover:text-red-300"
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="flex flex-wrap gap-1">
                  {profile?.settings.moderation?.bannedWords?.length ? (
                    profile.settings.moderation.bannedWords.map((word, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 rounded text-xs"
                      >
                        {word}
                      </span>
                    ))
                  ) : (
                    <span className="text-xs text-gray-500 dark:text-gray-400">Nenhuma palavra banida</span>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Card 4: Notificações e Áudio */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center space-x-2 mb-4">
            <Bell className="w-5 h-5 text-purple-600 dark:text-purple-400" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Notificações & Áudio</h3>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
            Configurações de alertas e controles de áudio
          </p>

          <div className="space-y-6">
            {/* Notificações */}
            <div>
              <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">Notificações</h4>
              <div className="grid grid-cols-1 gap-3">
                {[
                  { key: "emailNotifications", label: "Email", desc: "Receber notificações por email" },
                  { key: "newSuggestionAlert", label: "Novas Sugestões", desc: "Alertas de novas sugestões" },
                  { key: "highVoteAlert", label: "Votos Altos", desc: "Alertas de músicas com muitos votos" },
                  { key: "moderationAlert", label: "Moderação", desc: "Alertas de moderação necessária" }
                ].map(({ key, label, desc }) => (
                  <div key={key} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                    <div>
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">{label}</label>
                      <p className="text-xs text-gray-500 dark:text-gray-400">{desc}</p>
                    </div>
                    {editing ? (
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={!!editedProfile?.settings.notifications?.[key as keyof typeof editedProfile.settings.notifications]}
                          onChange={(e) => updateSettings("notifications", { ...editedProfile?.settings.notifications, [key]: e.target.checked })}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                      </label>
                    ) : (
                      <div className={`px-2 py-1 rounded text-xs font-medium ${
                        profile?.settings.notifications?.[key as keyof typeof profile.settings.notifications]
                          ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                          : "bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300"
                      }`}>
                        {profile?.settings.notifications?.[key as keyof typeof profile.settings.notifications] ? "On" : "Off"}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Áudio */}
            <div>
              <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">Configurações de Áudio</h4>
              <div className="space-y-4">
                {/* Volume */}
                <div className="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Volume</label>
                    <span className="text-sm text-gray-600 dark:text-gray-400">{editedProfile?.settings.audio?.volume ?? 75}%</span>
                  </div>
                  {editing && (
                    <input
                      type="range"
                      min={0}
                      max={100}
                      value={editedProfile?.settings.audio?.volume ?? 75}
                      onChange={(e) => updateSettings("audio", { ...editedProfile?.settings.audio, volume: parseInt(e.target.value, 10) })}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                    />
                  )}
                </div>

                {/* Fade In/Out e Crossfade */}
                {editing && (
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Fade-in (s)</label>
                      <input
                        type="number"
                        value={editedProfile?.settings.audio?.fadeInDuration ?? 3}
                        onChange={(e) => updateSettings("audio", { ...editedProfile?.settings.audio, fadeInDuration: parseInt(e.target.value || '0', 10) })}
                        className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Fade-out (s)</label>
                      <input
                        type="number"
                        value={editedProfile?.settings.audio?.fadeOutDuration ?? 3}
                        onChange={(e) => updateSettings("audio", { ...editedProfile?.settings.audio, fadeOutDuration: parseInt(e.target.value || '0', 10) })}
                        className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                      />
                    </div>
                  </div>
                )}

                {/* Crossfade */}
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <div>
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Crossfade</label>
                    <p className="text-xs text-gray-500 dark:text-gray-400">Transição suave entre músicas</p>
                  </div>
                  {editing ? (
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={!!editedProfile?.settings.audio?.crossfade}
                        onChange={(e) => updateSettings("audio", { ...editedProfile?.settings.audio, crossfade: e.target.checked })}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                  ) : (
                    <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                      profile?.settings.audio?.crossfade
                        ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                        : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                    }`}>
                      {profile?.settings.audio?.crossfade ? "Ativo" : "Inativo"}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Terceira linha de cards - Configurações Avançadas */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

        {/* Card 5: Configurações Avançadas de Playlist */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center space-x-2 mb-4">
            <Settings className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Playlist Avançada</h3>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
            Configurações avançadas de comportamento da playlist
          </p>

          <div className="space-y-4">
            {/* Tamanho Máximo da Fila */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Tamanho Máximo da Fila
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Número máximo de músicas na fila
                </p>
              </div>
              {editing ? (
                <input
                  type="number"
                  min="10"
                  max="200"
                  value={50} // Valor fixo do backend
                  disabled
                  className="w-20 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-100 dark:bg-gray-600 text-gray-500 dark:text-gray-400 text-center text-sm"
                />
              ) : (
                <div className="px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 rounded-full text-xs font-medium">
                  50
                </div>
              )}
            </div>

            {/* Permitir Duplicatas */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Permitir Duplicatas
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Permitir a mesma música múltiplas vezes na fila
                </p>
              </div>
              <div className="px-3 py-1 bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 rounded-full text-xs font-medium">
                Desabilitado
              </div>
            </div>

            {/* Modo Shuffle */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Modo Aleatório
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Reproduzir músicas em ordem aleatória
                </p>
              </div>
              <div className="px-3 py-1 bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 rounded-full text-xs font-medium">
                Desabilitado
              </div>
            </div>

            {/* Modo Repetição */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Modo Repetição
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Como repetir a playlist
                </p>
              </div>
              <div className="px-3 py-1 bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 rounded-full text-xs font-medium">
                Nenhum
              </div>
            </div>
          </div>
        </div>

        {/* Card 6: Interface Avançada */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center space-x-2 mb-4">
            <Eye className="w-5 h-5 text-teal-600 dark:text-teal-400" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Interface Avançada</h3>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
            Configurações avançadas da interface do cliente
          </p>

          <div className="space-y-4">
            {/* Mostrar Posição na Fila */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Mostrar Posição na Fila
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Exibir posição da música na fila para clientes
                </p>
              </div>
              <div className="px-3 py-1 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 rounded-full text-xs font-medium">
                Ativo
              </div>
            </div>

            {/* Sugestões Anônimas */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Sugestões Anônimas
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Permitir sugestões sem identificação do cliente
                </p>
              </div>
              <div className="px-3 py-1 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 rounded-full text-xs font-medium">
                Ativo
              </div>
            </div>

            {/* Exigir ID de Sessão */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Exigir ID de Sessão
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Rastrear clientes por sessão única
                </p>
              </div>
              <div className="px-3 py-1 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 rounded-full text-xs font-medium">
                Ativo
              </div>
            </div>

            {/* Tema da Interface */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Tema da Interface
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Tema padrão para clientes
                </p>
              </div>
              <div className="px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 rounded-full text-xs font-medium">
                Claro
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderAppearance = () => (
    <div
      className="space-y-6"
      role="tabpanel"
      id="panel-appearance"
      aria-labelledby="tab-appearance"
    >
      {/* Grid de Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

        {/* Card 1: Tema */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center space-x-2 mb-4">
            <FileImage className="w-5 h-5 text-purple-600 dark:text-purple-400" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Tema da Interface</h3>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
            Configurações de aparência e tema do player de música
          </p>

          <div className="space-y-4">
            {/* Modo Escuro */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Modo Escuro
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Ativar interface com tema escuro para o player
                </p>
              </div>
              {editing ? (
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={editedProfile?.appearance?.darkMode || false}
                    onChange={(e) => updateAppearance("darkMode", e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
              ) : (
                <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                  profile?.appearance?.darkMode
                    ? "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400"
                    : "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400"
                }`}>
                  {profile?.appearance?.darkMode ? "Escuro" : "Claro"}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Card 2: Cores */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center space-x-2 mb-4">
            <Settings className="w-5 h-5 text-orange-600 dark:text-orange-400" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Cores do Tema</h3>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
            Personalize as cores da interface do player
          </p>

          <div className="space-y-4">
            {/* Cor Primária */}
            <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <label
                htmlFor="primary-color"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3"
              >
                Cor Primária
              </label>
              <div className="flex items-center space-x-3">
                {editing ? (
                  <input
                    id="primary-color"
                    type="color"
                    value={editedProfile?.appearance?.primaryColor || "#3b82f6"}
                    onChange={(e) => updateAppearance("primaryColor", e.target.value)}
                    className="h-10 w-20 border-0 p-0 rounded cursor-pointer"
                  />
                ) : (
                  <div
                    className="h-8 w-8 rounded-full border border-gray-300 dark:border-gray-600 shadow-sm"
                    style={{
                      backgroundColor: profile?.appearance?.primaryColor || "#3b82f6",
                    }}
                  ></div>
                )}
                <span className="text-sm font-mono text-gray-700 dark:text-gray-300">
                  {editing ? editedProfile?.appearance?.primaryColor || "#3b82f6" : profile?.appearance?.primaryColor || "#3b82f6"}
                </span>
              </div>
            </div>

            {/* Cor de Destaque */}
            <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <label
                htmlFor="accent-color"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3"
              >
                Cor de Destaque
              </label>
              <div className="flex items-center space-x-3">
                {editing ? (
                  <input
                    id="accent-color"
                    type="color"
                    value={editedProfile?.appearance?.accentColor || "#10b981"}
                    onChange={(e) => updateAppearance("accentColor", e.target.value)}
                    className="h-10 w-20 border-0 p-0 rounded cursor-pointer"
                  />
                ) : (
                  <div
                    className="h-8 w-8 rounded-full border border-gray-300 dark:border-gray-600 shadow-sm"
                    style={{
                      backgroundColor: profile?.appearance?.accentColor || "#10b981",
                    }}
                  ></div>
                )}
                <span className="text-sm font-mono text-gray-700 dark:text-gray-300">
                {editing ? editedProfile?.appearance?.accentColor || "#10b981" : profile?.appearance?.accentColor || "#10b981"}
                </span>
              </div>
            </div>

            {/* Fonte */}
            <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <label
                htmlFor="font-family"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3"
              >
                Família da Fonte
              </label>
              {editing ? (
                <select
                  id="font-family"
                  value={editedProfile?.appearance?.fontFamily || "Inter"}
                  onChange={(e) => updateAppearance("fontFamily", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="Inter">Inter</option>
                  <option value="Roboto">Roboto</option>
                  <option value="Poppins">Poppins</option>
                  <option value="Montserrat">Montserrat</option>
                  <option value="Open Sans">Open Sans</option>
                </select>
              ) : (
                <div className="text-gray-900 dark:text-white">
                  <span
                    className="font-medium"
                    style={{
                      fontFamily: profile?.appearance?.fontFamily || "Inter",
                    }}
                  >
                    {profile?.appearance?.fontFamily || "Inter"}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Card de Visualização */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center space-x-2 mb-4">
          <Eye className="w-5 h-5 text-teal-600 dark:text-teal-400" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Visualização</h3>
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
          Preview de como o player aparecerá para os clientes
        </p>

        <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
          <div
            className="h-36 rounded-lg border border-gray-200 dark:border-gray-600 p-4 flex flex-col"
            style={{
              backgroundColor: editedProfile?.appearance?.darkMode ? "#1f2937" : "#ffffff",
              color: editedProfile?.appearance?.darkMode ? "#f3f4f6" : "#111827",
              fontFamily: editedProfile?.appearance?.fontFamily || "Inter",
            }}
          >
            <div className="text-center mb-3">
              <div
                className="font-bold text-lg"
                style={{ color: editedProfile?.appearance?.primaryColor || "#3b82f6" }}
              >
                Agora Tocando
              </div>
              <div className="text-sm opacity-75">Nome da Música - Artista</div>
            </div>
            <div className="flex-1 flex items-center justify-center">
              <div className="h-2 w-full rounded-full bg-gray-200 dark:bg-gray-600">
                <div
                  className="h-2 rounded-full transition-all duration-300"
                  style={{
                    width: "40%",
                    backgroundColor: editedProfile?.appearance?.accentColor || "#10b981",
                  }}
                ></div>
              </div>
            </div>
            <div className="flex justify-between text-xs mt-2 opacity-75">
              <span>1:30</span>
              <span>3:45</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderIntegrations = () => (
    <div
      className="space-y-6"
      role="tabpanel"
      id="panel-integrations"
      aria-labelledby="tab-integrations"
    >
      {/* Card de Integrações */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center space-x-2 mb-4">
          <ExternalLink className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Integrações</h3>
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
          Configure as integrações com serviços externos
        </p>

        <div className="space-y-4">
          {/* YouTube API */}
          <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                API do YouTube
              </label>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Usar a API oficial do YouTube para buscar músicas
              </p>
            </div>
            {editing ? (
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={editedProfile?.integrations?.youtubeApiEnabled || false}
                  onChange={(e) => updateIntegrations("youtubeApiEnabled", e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            ) : (
              <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                profile?.integrations?.youtubeApiEnabled
                  ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                  : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
              }`}>
                {profile?.integrations?.youtubeApiEnabled ? "Ativo" : "Inativo"}
              </div>
            )}
          </div>

          {/* YouTube Auth Manager */}
          <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
            <div className="mb-3">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Autenticação YouTube
              </label>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Configure a autenticação com sua conta YouTube
              </p>
            </div>
            {restaurantId && (
              <YouTubeAuthManager
                restaurantId={restaurantId}
                onAuthStatusChange={(authed) => {
                  console.log("YouTube auth:", authed);
                }}
              />
            )}
          </div>

          {/* Spotify */}
          <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Spotify
              </label>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Integrar com sua conta Spotify para playlists
              </p>
            </div>
            {editing ? (
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={editedProfile?.integrations?.spotifyConnected || false}
                  onChange={(e) => updateIntegrations("spotifyConnected", e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            ) : (
              <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                profile?.integrations?.spotifyConnected
                  ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                  : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
              }`}>
                {profile?.integrations?.spotifyConnected ? "Conectado" : "Desconectado"}
              </div>
            )}
          </div>

          {/* Google Analytics */}
          <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Google Analytics
              </label>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Monitorar estatísticas de uso do player
              </p>
            </div>
            {editing ? (
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={editedProfile?.integrations?.googleAnalytics || false}
                  onChange={(e) => updateIntegrations("googleAnalytics", e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </label>
            ) : (
              <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                profile?.integrations?.googleAnalytics
                  ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                  : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
              }`}>
                {profile?.integrations?.googleAnalytics ? "Ativo" : "Inativo"}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  // Renderização principal do componente
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Perfil do Restaurante
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Gerencie as informações e configurações do seu restaurante
          </p>
        </div>

        <div className="flex space-x-2">
          {editing ? (
            <>
              <button
                onClick={handleCancel}
                disabled={saving}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-50"
              >
                <X className="w-4 h-4 mr-2 inline" />
                Cancelar
              </button>
              <button
                onClick={handleSave}
                disabled={saving}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-50 flex items-center"
              >
                {saving ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Save className="w-4 h-4 mr-2" />
                )}
                {saving ? "Salvando..." : "Salvar"}
              </button>
            </>
          ) : (
            <button
              onClick={() => setEditing(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 flex items-center"
            >
              <Edit className="w-4 h-4 mr-2" />
              Editar
            </button>
          )}
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8" aria-label="Tabs">
          {tabs.map((tab) => (
            <Tab
              key={tab.id}
              icon={tab.icon}
              label={tab.label}
              id={tab.id}
              active={activeTab === tab.id}
              onClick={setActiveTab}
            />
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            {activeTab === "basic" && renderBasicInfo()}
            {activeTab === "hours" && renderBusinessHours()}
            {activeTab === "settings" && renderSettings()}
            {activeTab === "appearance" && renderAppearance()}
            {activeTab === "integrations" && renderIntegrations()}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default RestaurantProfile;