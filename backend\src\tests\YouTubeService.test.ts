import { YouTubeService, YouTubeError, SearchVideosDto, IProcessedVideoInfo, ISearchResult } from '../services/YouTubeService';
import { redisClient } from '../config/redis';
import axios from 'axios';

// Mock das dependências
jest.mock('axios');
jest.mock('../config/redis');
jest.mock('../utils/logger');

const mockedAxios = axios as jest.Mocked<typeof axios>;
const mockedRedisClient = redisClient as jest.Mocked<typeof redisClient>;

describe('YouTubeService', () => {
  let youtubeService: YouTubeService;

  const mockVideoResponse = {
    data: {
      items: [
        {
          id: 'test-video-id',
          snippet: {
            title: 'Test Song - Test Artist',
            description: 'Test description',
            channelTitle: 'Test Channel',
            channelId: 'test-channel-id',
            publishedAt: '2024-01-01T00:00:00Z',
            thumbnails: {
              high: { url: 'https://test.com/thumb.jpg', width: 480, height: 360 }
            },
            tags: ['rock', 'music'],
            categoryId: '10'
          },
          contentDetails: {
            duration: 'PT4M13S',
            definition: 'hd',
            caption: 'false',
            licensedContent: true
          },
          statistics: {
            viewCount: '1000000',
            likeCount: '50000',
            favoriteCount: '0'
          },
          status: {
            uploadStatus: 'processed',
            privacyStatus: 'public',
            license: 'youtube',
            embeddable: true,
            publicStatsViewable: true,
            madeForKids: false
          }
        }
      ],
      nextPageToken: 'next-token',
      pageInfo: {
        totalResults: 100,
        resultsPerPage: 10
      }
    }
  };

  const mockSearchResponse = {
    data: {
      items: [
        {
          id: { videoId: 'test-video-id' },
          snippet: {
            title: 'Test Song - Test Artist',
            description: 'Test description',
            channelTitle: 'Test Channel',
            publishedAt: '2024-01-01T00:00:00Z',
            thumbnails: {
              high: { url: 'https://test.com/thumb.jpg', width: 480, height: 360 }
            }
          }
        }
      ],
      nextPageToken: 'next-token',
      pageInfo: {
        totalResults: 100,
        resultsPerPage: 10
      }
    }
  };

  beforeEach(() => {
    // Setup mocks
    (mockedRedisClient as any).isReady = true;
    mockedRedisClient.getClient.mockReturnValue({
      get: jest.fn(),
      setEx: jest.fn(),
      del: jest.fn()
    } as any);

    // Mock environment variables
    process.env.YOUTUBE_API_KEY = 'test-api-key';
    process.env.YOUTUBE_API_QUOTA_LIMIT = '10000';

    youtubeService = new YouTubeService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Configuração', () => {
    it('deve inicializar com configurações padrão', () => {
      expect(youtubeService).toBeInstanceOf(YouTubeService);
    });

    it('deve lançar erro sem API key', () => {
      delete process.env.YOUTUBE_API_KEY;
      
      expect(() => new YouTubeService()).toThrow(YouTubeError);
      expect(() => new YouTubeService()).toThrow('YOUTUBE_API_KEY não configurada');
    });

    it('deve aceitar configuração personalizada', () => {
      const customConfig = {
        apiKey: 'custom-key',
        quotaLimit: 5000,
        cacheEnabled: false,
        cacheTTL: 1800
      };

      const service = new YouTubeService(customConfig);
      expect(service).toBeInstanceOf(YouTubeService);
    });
  });

  describe('searchVideos', () => {
    beforeEach(() => {
      mockedAxios.get
        .mockResolvedValueOnce(mockSearchResponse) // Para busca
        .mockResolvedValueOnce(mockVideoResponse); // Para detalhes
    });

    it('deve buscar vídeos com sucesso', async () => {
      const result = await youtubeService.searchVideos('rock music', 10);

      expect(result).toEqual({
        videos: expect.arrayContaining([
          expect.objectContaining({
            youtubeVideoId: 'test-video-id',
            title: 'Test Song',
            artist: 'Test Artist',
            duration: 253 // 4m13s em segundos
          })
        ]),
        nextPageToken: 'next-token',
        totalResults: 100
      });

      expect(mockedAxios.get).toHaveBeenCalledTimes(2);
    });

    it('deve validar parâmetros de entrada', async () => {
      await expect(
        youtubeService.searchVideos('', 10)
      ).rejects.toThrow(YouTubeError);

      await expect(
        youtubeService.searchVideos('test', 0)
      ).rejects.toThrow(YouTubeError);

      await expect(
        youtubeService.searchVideos('test', 100)
      ).rejects.toThrow(YouTubeError);
    });

    it('deve usar cache quando disponível', async () => {
      const cachedResult = {
        videos: [{ youtubeVideoId: 'cached-video' }],
        totalResults: 1
      };

      mockedRedisClient.getClient().get = jest.fn().mockResolvedValue(JSON.stringify(cachedResult));

      const result = await youtubeService.searchVideos('rock music', 10);

      expect(result).toEqual(cachedResult);
      expect(mockedAxios.get).not.toHaveBeenCalled();
    });

    it('deve lidar com erro de quota excedida', async () => {
      // Simular quota excedida
      jest.spyOn(youtubeService as any, 'hasQuotaFor').mockReturnValue(false);

      await expect(
        youtubeService.searchVideos('rock music', 10)
      ).rejects.toThrow(YouTubeError);

      await expect(
        youtubeService.searchVideos('rock music', 10)
      ).rejects.toMatchObject({
        code: 'QUOTA_EXCEEDED',
        statusCode: 429
      });
    });
  });

  describe('getVideoInfo', () => {
    beforeEach(() => {
      mockedAxios.get.mockResolvedValue(mockVideoResponse);
    });

    it('deve obter informações de vídeo específico', async () => {
      const result = await youtubeService.getVideoInfo('test-video-id');

      expect(result).toEqual(
        expect.objectContaining({
          youtubeVideoId: 'test-video-id',
          title: 'Test Song',
          artist: 'Test Artist',
          duration: 253
        })
      );
    });

    it('deve retornar null para vídeo não encontrado', async () => {
      mockedAxios.get.mockResolvedValue({ data: { items: [] } });

      const result = await youtubeService.getVideoInfo('invalid-video-id');

      expect(result).toBeNull();
    });

    it('deve validar ID do vídeo', async () => {
      await expect(
        youtubeService.getVideoInfo('')
      ).rejects.toThrow(YouTubeError);
    });
  });

  describe('Gerenciamento de Quota', () => {
    it('deve rastrear uso de quota', async () => {
      mockedAxios.get
        .mockResolvedValueOnce(mockSearchResponse)
        .mockResolvedValueOnce(mockVideoResponse);

      await youtubeService.searchVideos('test', 10);

      const quotaInfo = youtubeService.getQuotaInfo();
      expect(quotaInfo.used).toBeGreaterThan(0);
      expect(quotaInfo.remaining).toBeLessThan(quotaInfo.limit);
    });

    it('deve resetar quota diariamente', () => {
      youtubeService.resetQuota();
      
      const quotaInfo = youtubeService.getQuotaInfo();
      expect(quotaInfo.used).toBe(0);
      expect(quotaInfo.remaining).toBe(quotaInfo.limit);
    });

    it('deve verificar disponibilidade de quota', () => {
      const hasQuota = youtubeService.hasQuotaFor('search');
      expect(typeof hasQuota).toBe('boolean');
    });
  });

  describe('Cache', () => {
    it('deve obter estatísticas de cache', () => {
      const stats = youtubeService.getCacheStats();
      
      expect(stats).toEqual(
        expect.objectContaining({
          hits: expect.any(Number),
          misses: expect.any(Number),
          hitRate: expect.any(Number),
          totalKeys: expect.any(Number)
        })
      );
    });

    it('deve limpar cache quando solicitado', async () => {
      await expect(youtubeService.clearCache()).resolves.not.toThrow();
    });
  });

  describe('Tratamento de Erros', () => {
    it('deve lançar YouTubeError para erro de API', async () => {
  mockedAxios.get.mockReset();
  mockedAxios.get.mockRejectedValue(new Error('API Error'));

      await expect(
        youtubeService.searchVideos('test', 10)
      ).rejects.toThrow(YouTubeError);
    });

    it('deve incluir informações de quota no erro', async () => {
      jest.spyOn(youtubeService as any, 'hasQuotaFor').mockReturnValue(false);

      try {
        await youtubeService.searchVideos('test', 10);
      } catch (error) {
        expect(error).toBeInstanceOf(YouTubeError);
        expect((error as YouTubeError).quotaUsed).toBeDefined();
      }
    });
  });

  describe('Validação de Vídeos', () => {
    it('deve filtrar vídeos não embeddable', async () => {
      const nonEmbeddableVideo = {
        ...mockVideoResponse.data.items[0],
        status: { ...mockVideoResponse.data.items[0].status, embeddable: false }
      };

      mockedAxios.get
        .mockResolvedValueOnce(mockSearchResponse)
        .mockResolvedValueOnce({ data: { items: [nonEmbeddableVideo] } });

      const result = await youtubeService.searchVideos('test', 10);

      expect(result.videos).toHaveLength(0);
    });

    it('deve filtrar vídeos para crianças', async () => {
      const kidsVideo = {
        ...mockVideoResponse.data.items[0],
        status: { ...mockVideoResponse.data.items[0].status, madeForKids: true }
      };

      mockedAxios.get
        .mockResolvedValueOnce(mockSearchResponse)
        .mockResolvedValueOnce({ data: { items: [kidsVideo] } });

      const result = await youtubeService.searchVideos('test', 10);

      expect(result.videos).toHaveLength(0);
    });
  });
});
