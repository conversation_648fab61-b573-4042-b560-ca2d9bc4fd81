import { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-hot-toast';

interface Song {
  id: string;
  videoId: string;
  title: string;
  artist: string;
  duration?: number;
  upvotes: number;
  downvotes: number;
  isPaid?: boolean;
  paymentId?: string;
  addedAt: Date;
  status: 'pending' | 'approved' | 'rejected' | 'playing' | 'played';
}

interface QueueManagerState {
  priorityQueue: Song[];
  normalQueue: Song[];
  currentSong: Song | null;
  isLoading: boolean;
  error: string | null;
}

interface QueueManagerActions {
  addToPriorityQueue: (song: Omit<Song, 'isPaid' | 'addedAt'>) => void;
  addToNormalQueue: (song: Omit<Song, 'isPaid' | 'addedAt'>) => void;
  removeFromQueue: (songId: string) => void;
  moveToNext: () => Song | null;
  updateSongStatus: (songId: string, status: Song['status']) => void;
  refreshQueues: () => Promise<void>;
  clearQueues: () => void;
  reorderQueue: (queueType: 'priority' | 'normal', fromIndex: number, toIndex: number) => void;
}

export const useQueueManager = (restaurantId: string): QueueManagerState & QueueManagerActions => {
  const [state, setState] = useState<QueueManagerState>({
    priorityQueue: [],
    normalQueue: [],
    currentSong: null,
    isLoading: false,
    error: null,
  });

  // Carregar filas do backend
  const refreshQueues = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const response = await fetch(`/api/restaurants/${restaurantId}/queue`);
      if (!response.ok) {
        throw new Error('Falha ao carregar filas');
      }
      
      const data = await response.json();
      
      setState(prev => ({
        ...prev,
        priorityQueue: data.priorityQueue || [],
        normalQueue: data.normalQueue || [],
        currentSong: data.currentSong || null,
        isLoading: false,
      }));
      
      console.log('🔄 Filas atualizadas do backend');
    } catch (error) {
      console.error('❌ Erro ao carregar filas:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Erro desconhecido',
        isLoading: false,
      }));
      toast.error('Erro ao carregar filas de música');
    }
  }, [restaurantId]);

  // Carregar filas na inicialização
  useEffect(() => {
    refreshQueues();
  }, [refreshQueues]);

  // Polling para atualizações em tempo real
  useEffect(() => {
    const interval = setInterval(() => {
      refreshQueues();
    }, 10000); // Atualizar a cada 10 segundos

    return () => clearInterval(interval);
  }, [refreshQueues]);

  // Adicionar música à fila prioritária (paga)
  const addToPriorityQueue = useCallback(async (song: Omit<Song, 'isPaid' | 'addedAt'>) => {
    try {
      const response = await fetch(`/api/restaurants/${restaurantId}/queue/priority`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...song,
          isPaid: true,
          addedAt: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error('Falha ao adicionar à fila prioritária');
      }

      const newSong = await response.json();
      
      setState(prev => ({
        ...prev,
        priorityQueue: [...prev.priorityQueue, { ...newSong, isPaid: true, addedAt: new Date() }],
      }));

      console.log(`➕ Música adicionada à fila prioritária: ${song.title}`);
      toast.success(`${song.title} adicionada à fila prioritária!`);
      
    } catch (error) {
      console.error('❌ Erro ao adicionar à fila prioritária:', error);
      toast.error('Erro ao adicionar música à fila prioritária');
    }
  }, [restaurantId]);

  // Adicionar música à fila normal (gratuita)
  const addToNormalQueue = useCallback(async (song: Omit<Song, 'isPaid' | 'addedAt'>) => {
    try {
      const response = await fetch(`/api/restaurants/${restaurantId}/queue/normal`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...song,
          isPaid: false,
          addedAt: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error('Falha ao adicionar à fila normal');
      }

      const newSong = await response.json();
      
      setState(prev => ({
        ...prev,
        normalQueue: [...prev.normalQueue, { ...newSong, isPaid: false, addedAt: new Date() }],
      }));

      console.log(`➕ Música adicionada à fila normal: ${song.title}`);
      toast.success(`${song.title} adicionada à fila!`);
      
    } catch (error) {
      console.error('❌ Erro ao adicionar à fila normal:', error);
      toast.error('Erro ao adicionar música à fila');
    }
  }, [restaurantId]);

  // Remover música da fila
  const removeFromQueue = useCallback(async (songId: string) => {
    try {
      const response = await fetch(`/api/restaurants/${restaurantId}/queue/${songId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Falha ao remover da fila');
      }

      setState(prev => ({
        ...prev,
        priorityQueue: prev.priorityQueue.filter(song => song.id !== songId),
        normalQueue: prev.normalQueue.filter(song => song.id !== songId),
      }));

      console.log(`➖ Música removida da fila: ${songId}`);
      
    } catch (error) {
      console.error('❌ Erro ao remover da fila:', error);
      toast.error('Erro ao remover música da fila');
    }
  }, [restaurantId]);

  // Mover para próxima música (lógica de prioridade)
  const moveToNext = useCallback((): Song | null => {
    let nextSong: Song | null = null;
    
    // Prioridade: fila paga > fila normal
    if (state.priorityQueue.length > 0) {
      nextSong = state.priorityQueue[0];
      setState(prev => ({
        ...prev,
        priorityQueue: prev.priorityQueue.slice(1),
        currentSong: nextSong,
      }));
    } else if (state.normalQueue.length > 0) {
      // Ordenar fila normal por votos (upvotes - downvotes)
      const sortedNormalQueue = [...state.normalQueue].sort(
        (a, b) => (b.upvotes - b.downvotes) - (a.upvotes - a.downvotes)
      );
      
      nextSong = sortedNormalQueue[0];
      setState(prev => ({
        ...prev,
        normalQueue: prev.normalQueue.filter(song => song.id !== nextSong!.id),
        currentSong: nextSong,
      }));
    }

    if (nextSong) {
      updateSongStatus(nextSong.id, 'playing');
      console.log(`🎵 Próxima música: ${nextSong.title} - ${nextSong.artist}`);
    }

    return nextSong;
  }, [state.priorityQueue, state.normalQueue]);

  // Atualizar status da música
  const updateSongStatus = useCallback(async (songId: string, status: Song['status']) => {
    try {
      const response = await fetch(`/api/restaurants/${restaurantId}/queue/${songId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        throw new Error('Falha ao atualizar status da música');
      }

      // Atualizar estado local se necessário
      setState(prev => ({
        ...prev,
        currentSong: prev.currentSong?.id === songId 
          ? { ...prev.currentSong, status }
          : prev.currentSong,
      }));

      console.log(`🔄 Status atualizado para ${songId}: ${status}`);
      
    } catch (error) {
      console.error('❌ Erro ao atualizar status:', error);
    }
  }, [restaurantId]);

  // Limpar todas as filas
  const clearQueues = useCallback(async () => {
    try {
      const response = await fetch(`/api/restaurants/${restaurantId}/queue/clear`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Falha ao limpar filas');
      }

      setState(prev => ({
        ...prev,
        priorityQueue: [],
        normalQueue: [],
        currentSong: null,
      }));

      console.log('🗑️ Filas limpas');
      toast.success('Filas limpas com sucesso');
      
    } catch (error) {
      console.error('❌ Erro ao limpar filas:', error);
      toast.error('Erro ao limpar filas');
    }
  }, [restaurantId]);

  // Reordenar fila
  const reorderQueue = useCallback(async (
    queueType: 'priority' | 'normal',
    fromIndex: number,
    toIndex: number
  ) => {
    const queue = queueType === 'priority' ? state.priorityQueue : state.normalQueue;
    const newQueue = [...queue];
    const [movedItem] = newQueue.splice(fromIndex, 1);
    newQueue.splice(toIndex, 0, movedItem);

    // Atualizar estado local imediatamente para UX responsiva
    setState(prev => ({
      ...prev,
      [queueType === 'priority' ? 'priorityQueue' : 'normalQueue']: newQueue,
    }));

    try {
      const response = await fetch(`/api/restaurants/${restaurantId}/queue/reorder`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          queueType,
          newOrder: newQueue.map(song => song.id),
        }),
      });

      if (!response.ok) {
        throw new Error('Falha ao reordenar fila');
        // Reverter mudança local em caso de erro
        refreshQueues();
      }

      console.log(`🔄 Fila ${queueType} reordenada`);
      
    } catch (error) {
      console.error('❌ Erro ao reordenar fila:', error);
      toast.error('Erro ao reordenar fila');
    }
  }, [restaurantId, state.priorityQueue, state.normalQueue, refreshQueues]);

  return {
    // Estado
    priorityQueue: state.priorityQueue,
    normalQueue: state.normalQueue,
    currentSong: state.currentSong,
    isLoading: state.isLoading,
    error: state.error,
    
    // Ações
    addToPriorityQueue,
    addToNormalQueue,
    removeFromQueue,
    moveToNext,
    updateSongStatus,
    refreshQueues,
    clearQueues,
    reorderQueue,
  };
};
