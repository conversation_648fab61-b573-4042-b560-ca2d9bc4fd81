// Script para adicionar músicas de teste via sugestões
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

const songs = [
  {
    youtubeVideoId: "dQw4w9WgXcQ",
    title: "Never Gonna Give You Up",
    artist: "<PERSON>",
    clientName: "Cliente Teste",
    tableNumber: 1
  },
  {
    youtubeVideoId: "9bZkp7q19f0",
    title: "Gangnam Style",
    artist: "PSY",
    clientName: "Cliente Teste 2",
    tableNumber: 2
  },
  {
    youtubeVideoId: "kJQP7kiw5Fk",
    title: "<PERSON><PERSON><PERSON><PERSON>",
    artist: "<PERSON>i ft. Daddy Yankee",
    clientName: "Cliente Teste 3",
    tableNumber: 3
  }
];

async function addSongs() {
  for (const song of songs) {
    try {
      const response = await fetch('http://localhost:8001/api/v1/suggestions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Session-ID': generateUUID()
        },
        body: JSON.stringify({
          youtubeVideoId: song.youtubeVideoId,
          restaurantId: 'demo-restaurant',
          title: song.title,
          artist: song.artist
        })
      });

      const result = await response.text();
      console.log(`Added ${song.title}:`, result);

      // Aguardar um pouco entre as sugestões
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error(`Error adding ${song.title}:`, error);
    }
  }
}

addSongs();
