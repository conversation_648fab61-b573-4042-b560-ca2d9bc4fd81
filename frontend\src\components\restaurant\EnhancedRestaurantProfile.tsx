import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  User,
  Mail,
  Phone,
  MapPin,
  Building2,
  Clock,
  Save,
  Edit,
  X,
  Check,
  AlertCircle,
  RefreshCw,
  Settings,
  Globe,
  Shield,
  Music,
  Users,
  Camera,
  Upload,
  Eye,
  EyeOff,
} from "lucide-react";
import { toast } from "react-hot-toast";
import BusinessHoursManager from "./BusinessHoursManager";

interface RestaurantProfile {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  description: string;
  businessHours: {
    [key: string]: {
      open: string;
      close: string;
      isOpen: boolean;
    };
  };
  settings: {
    allowSuggestions: boolean;
    maxSuggestionsPerUser: number;
    autoPlayEnabled: boolean;
    playlist?: any;
  };
  createdAt?: string;
  updatedAt?: string;
}

interface EnhancedRestaurantProfileProps {
  restaurantId?: string;
}

const EnhancedRestaurantProfile: React.FC<EnhancedRestaurantProfileProps> = ({
  restaurantId = "demo-restaurant",
}) => {
  const [profile, setProfile] = useState<RestaurantProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState("basic");
  const [editingField, setEditingField] = useState<string | null>(null);
  const [tempValues, setTempValues] = useState<Record<string, any>>({});

  const tabs = [
    { id: "basic", label: "Informações Básicas", icon: Building2 },
    { id: "hours", label: "Horários", icon: Clock },
    { id: "settings", label: "Configurações", icon: Settings },
    { id: "advanced", label: "Avançado", icon: Shield },
  ];

  useEffect(() => {
    loadProfile();
  }, [restaurantId]);

  const loadProfile = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        `http://localhost:8001/api/v1/restaurants/${restaurantId}/profile`
      );

      if (response.ok) {
        const data = await response.json();
        console.log("Profile loaded:", data);
        setProfile(data.profile);
      } else {
        console.error("Failed to load profile, status:", response.status);
        const errorData = await response.json().catch(() => ({}));
        console.error("Error details:", errorData);

        // Fallback para dados mock
        const mockProfile: RestaurantProfile = {
          id: restaurantId,
          name: "Restaurante Demo",
          email: "<EMAIL>",
          phone: "(11) 99999-9999",
          address: "Rua das Flores, 123 - São Paulo, SP",
          description:
            "Um restaurante aconchegante com música ambiente personalizada pelos clientes.",
          businessHours: {
            monday: { open: "11:00", close: "23:00", isOpen: true },
            tuesday: { open: "11:00", close: "23:00", isOpen: true },
            wednesday: { open: "11:00", close: "23:00", isOpen: true },
            thursday: { open: "11:00", close: "23:00", isOpen: true },
            friday: { open: "11:00", close: "24:00", isOpen: true },
            saturday: { open: "11:00", close: "24:00", isOpen: true },
            sunday: { open: "12:00", close: "22:00", isOpen: true },
          },
          settings: {
            allowSuggestions: true,
            maxSuggestionsPerUser: 3,
            autoPlayEnabled: true,
          },
        };
        setProfile(mockProfile);
        toast.error("Usando dados de demonstração");
      }
    } catch (error) {
      console.error("Erro ao carregar perfil:", error);
      toast.error("Erro de conexão ao carregar perfil");
    } finally {
      setLoading(false);
    }
  };

  const startEdit = (field: string, currentValue: any) => {
    setEditingField(field);
    setTempValues({ [field]: currentValue });
  };

  const cancelEdit = () => {
    setEditingField(null);
    setTempValues({});
  };

  const saveField = async (field: string) => {
    if (!profile) return;

    try {
      setSaving(true);
      const updateData = { [field]: tempValues[field] };

      const response = await fetch(
        `http://localhost:8001/api/v1/restaurants/${restaurantId}/profile`,
        {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(updateData),
        }
      );

      if (response.ok) {
        const data = await response.json();
        setProfile(data.profile || { ...profile, [field]: tempValues[field] });
        setEditingField(null);
        setTempValues({});
        toast.success("Campo atualizado com sucesso!");
      } else {
        const errorData = await response.json().catch(() => ({}));
        toast.error(errorData.message || "Erro ao salvar");
      }
    } catch (error) {
      console.error("Erro ao salvar campo:", error);
      toast.error("Erro de conexão");
    } finally {
      setSaving(false);
    }
  };

  const saveSettings = async (newSettings: any) => {
    if (!profile) return;

    try {
      setSaving(true);
      const response = await fetch(
        `http://localhost:8001/api/v1/restaurants/${restaurantId}/profile`,
        {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ settings: newSettings }),
        }
      );

      if (response.ok) {
        const data = await response.json();
        setProfile(data.profile || { ...profile, settings: newSettings });
        toast.success("Configurações salvas!");
      } else {
        const errorData = await response.json().catch(() => ({}));
        toast.error(errorData.message || "Erro ao salvar configurações");
      }
    } catch (error) {
      console.error("Erro ao salvar configurações:", error);
      toast.error("Erro de conexão");
    } finally {
      setSaving(false);
    }
  };

  const EditableField: React.FC<{
    field: string;
    label: string;
    value: string;
    icon: React.ComponentType<any>;
    type?: string;
    multiline?: boolean;
  }> = ({
    field,
    label,
    value,
    icon: Icon,
    type = "text",
    multiline = false,
  }) => {
    const isEditing = editingField === field;

    return (
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          <Icon className="w-4 h-4 inline mr-2" />
          {label}
        </label>

        <div className="flex items-center space-x-2">
          {isEditing ? (
            <>
              {multiline ? (
                <textarea
                  value={tempValues[field] || ""}
                  onChange={(e) =>
                    setTempValues({ ...tempValues, [field]: e.target.value })
                  }
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={3}
                />
              ) : (
                <input
                  type={type}
                  value={tempValues[field] || ""}
                  onChange={(e) =>
                    setTempValues({ ...tempValues, [field]: e.target.value })
                  }
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              )}

              <button
                onClick={() => saveField(field)}
                disabled={saving}
                className="p-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors"
              >
                <Check className="w-4 h-4" />
              </button>

              <button
                onClick={cancelEdit}
                className="p-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </>
          ) : (
            <>
              <div className="flex-1 px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded-lg text-gray-900 dark:text-white">
                {value || "Não informado"}
              </div>

              <button
                onClick={() => startEdit(field, value)}
                className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Edit className="w-4 h-4" />
              </button>
            </>
          )}
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="w-6 h-6 animate-spin text-blue-600 mr-2" />
        <span className="text-gray-600 dark:text-gray-400">
          Carregando perfil...
        </span>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="text-center p-8">
        <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Erro ao carregar perfil
        </h3>
        <button
          onClick={loadProfile}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Tentar novamente
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <Building2 className="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Perfil do Restaurante
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Gerencie as informações do seu estabelecimento
            </p>
          </div>
        </div>

        <button
          onClick={loadProfile}
          disabled={loading}
          className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50"
        >
          <RefreshCw className={`w-4 h-4 ${loading ? "animate-spin" : ""}`} />
          <span>Atualizar</span>
        </button>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? "border-blue-500 text-blue-600 dark:text-blue-400"
                  : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <AnimatePresence mode="wait">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.2 }}
        >
          {activeTab === "basic" && (
            <div className="space-y-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Informações Básicas
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <EditableField
                    field="name"
                    label="Nome do Restaurante"
                    value={profile.name}
                    icon={Building2}
                  />

                  <EditableField
                    field="email"
                    label="Email"
                    value={profile.email}
                    icon={Mail}
                    type="email"
                  />

                  <EditableField
                    field="phone"
                    label="Telefone"
                    value={profile.phone}
                    icon={Phone}
                    type="tel"
                  />

                  <EditableField
                    field="address"
                    label="Endereço"
                    value={profile.address}
                    icon={MapPin}
                  />
                </div>

                <div className="mt-6">
                  <EditableField
                    field="description"
                    label="Descrição"
                    value={profile.description}
                    icon={User}
                    multiline
                  />
                </div>
              </div>
            </div>
          )}

          {activeTab === "hours" && (
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
              <BusinessHoursManager
                restaurantId={restaurantId}
                initialHours={profile.businessHours}
                onSave={(hours) => {
                  setProfile({ ...profile, businessHours: hours });
                  toast.success("Horários atualizados!");
                }}
              />
            </div>
          )}

          {activeTab === "settings" && (
            <div className="space-y-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Configurações da Playlist
                </h3>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        Permitir Sugestões
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Clientes podem sugerir músicas
                      </p>
                    </div>
                    <button
                      onClick={() =>
                        saveSettings({
                          ...profile.settings,
                          allowSuggestions: !profile.settings.allowSuggestions,
                        })
                      }
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        profile.settings.allowSuggestions
                          ? "bg-blue-600"
                          : "bg-gray-200 dark:bg-gray-700"
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          profile.settings.allowSuggestions
                            ? "translate-x-6"
                            : "translate-x-1"
                        }`}
                      />
                    </button>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        Reprodução Automática
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Tocar próxima música automaticamente
                      </p>
                    </div>
                    <button
                      onClick={() =>
                        saveSettings({
                          ...profile.settings,
                          autoPlayEnabled: !profile.settings.autoPlayEnabled,
                        })
                      }
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        profile.settings.autoPlayEnabled
                          ? "bg-blue-600"
                          : "bg-gray-200 dark:bg-gray-700"
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          profile.settings.autoPlayEnabled
                            ? "translate-x-6"
                            : "translate-x-1"
                        }`}
                      />
                    </button>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Máximo de Sugestões por Cliente
                    </label>
                    <select
                      value={profile.settings.maxSuggestionsPerUser}
                      onChange={(e) =>
                        saveSettings({
                          ...profile.settings,
                          maxSuggestionsPerUser: parseInt(e.target.value),
                        })
                      }
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value={1}>1 sugestão</option>
                      <option value={2}>2 sugestões</option>
                      <option value={3}>3 sugestões</option>
                      <option value={5}>5 sugestões</option>
                      <option value={10}>10 sugestões</option>
                      <option value={-1}>Ilimitado</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === "advanced" && (
            <div className="space-y-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Configurações Avançadas
                </h3>

                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                      Informações do Sistema
                    </h4>
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          ID:
                        </span>
                        <span className="text-sm font-mono text-gray-900 dark:text-white">
                          {profile.id}
                        </span>
                      </div>
                      {profile.createdAt && (
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            Criado em:
                          </span>
                          <span className="text-sm text-gray-900 dark:text-white">
                            {new Date(profile.createdAt).toLocaleDateString(
                              "pt-BR"
                            )}
                          </span>
                        </div>
                      )}
                      {profile.updatedAt && (
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            Atualizado em:
                          </span>
                          <span className="text-sm text-gray-900 dark:text-white">
                            {new Date(profile.updatedAt).toLocaleDateString(
                              "pt-BR"
                            )}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

export default EnhancedRestaurantProfile;
