import React from "react";
import { motion } from "framer-motion";
import { LucideIcon } from "lucide-react";
import { clsx } from "clsx";

interface Tab {
  id: string;
  label: string;
  icon: LucideIcon;
  count?: number;
}

interface TabNavigationProps {
  tabs: Tab[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  className?: string;
}

const TabNavigation: React.FC<TabNavigationProps> = ({
  tabs,
  activeTab,
  onTabChange,
  className,
}) => {
  return (
    <nav
      className={clsx(
        "flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg",
        className
      )}
    >
      {tabs.map((tab) => {
        const isActive = activeTab === tab.id;
        const Icon = tab.icon;

        return (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={clsx(
              "relative flex items-center space-x-2 px-3 py-2 text-sm font-medium rounded-md transition-all duration-200",
              "focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",
              isActive
                ? "text-primary-700 dark:text-primary-300"
                : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
            )}
          >
            {/* Background animado */}
            {isActive && (
              <motion.div
                layoutId="activeTab"
                className="absolute inset-0 bg-white dark:bg-gray-700 rounded-md shadow-sm"
                initial={false}
                transition={{
                  type: "spring",
                  stiffness: 500,
                  damping: 30,
                }}
              />
            )}

            {/* Conteúdo da tab */}
            <div className="relative flex items-center justify-center space-x-2">
              <Icon className="w-4 h-4 flex-shrink-0" />
              <span className="hidden sm:inline whitespace-nowrap">
                {tab.label}
              </span>

              {/* Badge de contagem */}
              {tab.count !== undefined && tab.count > 0 && (
                <span
                  className={clsx(
                    "inline-flex items-center justify-center px-2 py-0.5 text-xs font-bold rounded-full flex-shrink-0",
                    isActive
                      ? "bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200"
                      : "bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300"
                  )}
                >
                  {tab.count > 99 ? "99+" : tab.count}
                </span>
              )}
            </div>
          </button>
        );
      })}
    </nav>
  );
};

export default TabNavigation;
