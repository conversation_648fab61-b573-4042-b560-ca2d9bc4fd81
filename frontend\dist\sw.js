if(!self.define){let e,s={};const i=(i,n)=>(i=new URL(i+".js",n).href,s[i]||new Promise(s=>{if("document"in self){const e=document.createElement("script");e.src=i,e.onload=s,document.head.appendChild(e)}else e=i,importScripts(i),s()}).then(()=>{let e=s[i];if(!e)throw new Error(`Module ${i} didn’t register its module`);return e}));self.define=(n,r)=>{const l=e||("document"in self?document.currentScript.src:"")||location.href;if(s[l])return;let t={};const a=e=>i(e,l),u={module:{uri:l},exports:t,require:a};s[l]=Promise.all(n.map(e=>u[e]||a(e))).then(e=>(r(...e),t))}}define(["./workbox-e20531c6"],function(e){"use strict";self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"assets/EnhancedRestaurantProfile-39002e11.js",revision:null},{url:"assets/GenreManager-afd8429c.js",revision:null},{url:"assets/index-3b37f57e.js",revision:null},{url:"assets/index-521e0fa8.css",revision:null},{url:"assets/MusicPlayer-bc235a29.js",revision:null},{url:"assets/PlaylistManager-87d19449.js",revision:null},{url:"assets/ProblematicTracksAlert-71e8e181.js",revision:null},{url:"assets/QRCodeManager-2f65a890.js",revision:null},{url:"assets/RestaurantProfile-d1c07026.js",revision:null},{url:"assets/RestaurantSettings-dec5b9cc.js",revision:null},{url:"assets/router-f729e475.js",revision:null},{url:"assets/ui-1cb796d3.js",revision:null},{url:"assets/UnifiedAnalytics-a45d3510.js",revision:null},{url:"assets/utils-08f61814.js",revision:null},{url:"assets/vendor-66b0ef43.js",revision:null},{url:"assets/YouTubeAuthManager-d052a128.js",revision:null},{url:"favicon.svg",revision:"c96e9c7ecec474350501d8a935b938d5"},{url:"icons/icon-144x144.png",revision:"c9df239901a3fed8c62afd267b59f293"},{url:"icons/icon-144x144.svg",revision:"81c9cbf42df0146e58c7a51443275f69"},{url:"index.html",revision:"9ff87c88885a197b7ba5392541218421"},{url:"registerSW.js",revision:"1872c500de691dce40960bb85481de07"},{url:"manifest.webmanifest",revision:"8a04d6eb7deb84e16ad45a8c4051427d"}],{}),e.cleanupOutdatedCaches(),e.registerRoute(new e.NavigationRoute(e.createHandlerBoundToURL("/index.html"))),e.registerRoute(/^https:\/\/api\.*/i,new e.NetworkFirst({cacheName:"api-cache",plugins:[new e.ExpirationPlugin({maxEntries:100,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/^https:\/\/i\.ytimg\.com\/.*/i,new e.CacheFirst({cacheName:"youtube-thumbnails",plugins:[new e.ExpirationPlugin({maxEntries:200,maxAgeSeconds:604800})]}),"GET")});
//# sourceMappingURL=sw.js.map
