import React, { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  SkipBack,
  SkipForward,
  X,
  ExternalLink,
  Share2,
  Heart,
  Plus,
  Maximize2,
} from "lucide-react";
import { toast } from "react-hot-toast";

interface Song {
  id: string;
  title: string;
  artist: string;
  duration: number;
  formattedDuration: string;
  thumbnailUrl: string;
  channelName?: string;
  viewCount?: number;
  publishedAt?: string;
  genre?: string;
  mood?: string;
}

interface MusicPreviewProps {
  song: Song | null;
  isOpen: boolean;
  onClose: () => void;
  onSuggest?: (song: Song) => void;
  onNext?: () => void;
  onPrevious?: () => void;
  hasNext?: boolean;
  hasPrevious?: boolean;
}

const MusicPreview: React.FC<MusicPreviewProps> = ({
  song,
  isOpen,
  onClose,
  onSuggest,
  onNext,
  onPrevious,
  hasNext = false,
  hasPrevious = false,
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [volume, setVolume] = useState(0.7);
  const [isMuted, setIsMuted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isLiked, setIsLiked] = useState(false);

  const audioRef = useRef<HTMLAudioElement>(null);
  const progressRef = useRef<HTMLDivElement>(null);

  // Simular URL de preview (normalmente viria da API)
  const previewUrl = song ? `https://www.youtube.com/watch?v=${song.id}` : null;

  useEffect(() => {
    if (song) {
      // Verificar se a música está nos favoritos
      const favorites = JSON.parse(localStorage.getItem("favoriteSongs") || "[]");
      setIsLiked(favorites.some((fav: any) => fav.id === song.id));
    }
  }, [song]);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateTime = () => setCurrentTime(audio.currentTime);
    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
      if (hasNext && onNext) {
        onNext();
      }
    };

    audio.addEventListener("timeupdate", updateTime);
    audio.addEventListener("ended", handleEnded);

    return () => {
      audio.removeEventListener("timeupdate", updateTime);
      audio.removeEventListener("ended", handleEnded);
    };
  }, [hasNext, onNext]);

  const togglePlay = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
    } else {
      // Simular reprodução (em produção, usaria URL real de preview)
  toast("Preview em desenvolvimento - Simulando reprodução", { icon: "ℹ️" });
      // audio.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleSeek = (e: React.MouseEvent<HTMLDivElement>) => {
    const audio = audioRef.current;
    const progress = progressRef.current;
    if (!audio || !progress || !song) return;

    const rect = progress.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const newTime = (clickX / rect.width) * song.duration;
    
    audio.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const toggleMute = () => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.muted = !isMuted;
    setIsMuted(!isMuted);
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    const audio = audioRef.current;
    
    setVolume(newVolume);
    if (audio) {
      audio.volume = newVolume;
    }
    
    if (newVolume === 0) {
      setIsMuted(true);
    } else if (isMuted) {
      setIsMuted(false);
    }
  };

  const toggleLike = () => {
    if (!song) return;

    const favorites = JSON.parse(localStorage.getItem("favoriteSongs") || "[]");
    
    if (isLiked) {
      const newFavorites = favorites.filter((fav: any) => fav.id !== song.id);
      localStorage.setItem("favoriteSongs", JSON.stringify(newFavorites));
      toast.success("Removido dos favoritos");
    } else {
      favorites.push(song);
      localStorage.setItem("favoriteSongs", JSON.stringify(favorites));
      toast.success("Adicionado aos favoritos");
    }
    
    setIsLiked(!isLiked);
  };

  const shareMusic = async () => {
    if (!song) return;

    const shareData = {
      title: song.title,
      text: `Confira "${song.title}" de ${song.artist}`,
      url: `https://youtube.com/watch?v=${song.id}`,
    };

    if (navigator.share) {
      try {
        await navigator.share(shareData);
        toast.success("Música compartilhada!");
      } catch (error) {
        console.error("Erro ao compartilhar:", error);
      }
    } else {
      navigator.clipboard.writeText(shareData.url);
      toast.success("Link copiado!");
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const getProgressPercentage = () => {
    if (!song || song.duration === 0) return 0;
    return (currentTime / song.duration) * 100;
  };

  if (!isOpen || !song) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className={`fixed inset-0 bg-black/80 flex items-center justify-center z-50 ${
          isFullscreen ? "p-0" : "p-4"
        }`}
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
          className={`bg-white dark:bg-gray-900 rounded-2xl shadow-2xl overflow-hidden ${
            isFullscreen ? "w-full h-full rounded-none" : "w-full max-w-2xl"
          }`}
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-purple-600 to-pink-600 p-6 text-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <img
                  src={song.thumbnailUrl}
                  alt={song.title}
                  className="w-16 h-16 rounded-lg object-cover shadow-lg"
                />
                <div>
                  <h2 className="text-xl font-bold truncate">{song.title}</h2>
                  <p className="text-purple-100 truncate">{song.artist}</p>
                  <div className="flex items-center space-x-2 mt-1">
                    {song.genre && (
                      <span className="px-2 py-0.5 bg-white/20 rounded-full text-xs">
                        {song.genre}
                      </span>
                    )}
                    {song.mood && (
                      <span className="px-2 py-0.5 bg-white/20 rounded-full text-xs">
                        {song.mood}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setIsFullscreen(!isFullscreen)}
                  className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors"
                  title={isFullscreen ? "Sair da tela cheia" : "Tela cheia"}
                >
                  <Maximize2 className="w-5 h-5" />
                </button>
                
                <button
                  onClick={onClose}
                  className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>

          {/* Player Controls */}
          <div className="p-6 space-y-6">
            {/* Progress Bar */}
            <div className="space-y-2">
              <div
                ref={progressRef}
                onClick={handleSeek}
                className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full cursor-pointer"
              >
                <div
                  className="h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full transition-all duration-100"
                  style={{ width: `${getProgressPercentage()}%` }}
                />
              </div>
              
              <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                <span>{formatTime(currentTime)}</span>
                <span>{song.formattedDuration}</span>
              </div>
            </div>

            {/* Main Controls */}
            <div className="flex items-center justify-center space-x-6">
              <button
                onClick={onPrevious}
                disabled={!hasPrevious}
                className="p-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <SkipBack className="w-6 h-6" />
              </button>
              
              <button
                onClick={togglePlay}
                className="p-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-lg"
              >
                {isPlaying ? (
                  <Pause className="w-8 h-8" />
                ) : (
                  <Play className="w-8 h-8 ml-1" />
                )}
              </button>
              
              <button
                onClick={onNext}
                disabled={!hasNext}
                className="p-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <SkipForward className="w-6 h-6" />
              </button>
            </div>

            {/* Volume Control */}
            <div className="flex items-center space-x-3">
              <button
                onClick={toggleMute}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              >
                {isMuted || volume === 0 ? (
                  <VolumeX className="w-5 h-5" />
                ) : (
                  <Volume2 className="w-5 h-5" />
                )}
              </button>
              
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={isMuted ? 0 : volume}
                onChange={handleVolumeChange}
                className="flex-1 h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
              
              <span className="text-sm text-gray-600 dark:text-gray-400 w-8">
                {Math.round((isMuted ? 0 : volume) * 100)}
              </span>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3">
                <button
                  onClick={toggleLike}
                  className={`p-2 rounded-lg transition-colors ${
                    isLiked
                      ? "text-red-500 bg-red-50 dark:bg-red-900/20"
                      : "text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20"
                  }`}
                  title={isLiked ? "Remover dos favoritos" : "Adicionar aos favoritos"}
                >
                  <Heart className={`w-5 h-5 ${isLiked ? "fill-current" : ""}`} />
                </button>
                
                <button
                  onClick={shareMusic}
                  className="p-2 text-gray-400 hover:text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                  title="Compartilhar"
                >
                  <Share2 className="w-5 h-5" />
                </button>
                
                <a
                  href={previewUrl || "#"}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 text-gray-400 hover:text-green-500 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg transition-colors"
                  title="Abrir no YouTube"
                >
                  <ExternalLink className="w-5 h-5" />
                </a>
              </div>
              
              {onSuggest && (
                <button
                  onClick={() => onSuggest(song)}
                  className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-lg"
                >
                  <Plus className="w-4 h-4" />
                  <span>Sugerir Música</span>
                </button>
              )}
            </div>

            {/* Song Info */}
            {(song.viewCount || song.publishedAt) && (
              <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                  {song.viewCount && (
                    <span>{song.viewCount.toLocaleString()} visualizações</span>
                  )}
                  {song.publishedAt && (
                    <span>Publicado em {new Date(song.publishedAt).toLocaleDateString()}</span>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Hidden Audio Element */}
          <audio ref={audioRef} preload="metadata" muted={isMuted} />
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default MusicPreview;
