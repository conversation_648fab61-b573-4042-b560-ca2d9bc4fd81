-- Idempotent fixes para playlist_tracks de acordo com o modelo PlaylistTrack do backend

DO $$
BEGIN
	IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name='playlist_tracks') THEN
		-- FK e coluna playlist_id
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_tracks' AND column_name='playlist_id') THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD COLUMN playlist_id UUID';
		END IF;
		IF NOT EXISTS (
			SELECT 1 FROM information_schema.table_constraints 
			WHERE table_name='playlist_tracks' AND constraint_name='fk_playlist_tracks_playlist'
		) THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD CONSTRAINT fk_playlist_tracks_playlist FOREIGN KEY (playlist_id) REFERENCES playlists(id) ON DELETE CASCADE';
		END IF;

		-- Campos principais
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_tracks' AND column_name='title') THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD COLUMN title VARCHAR';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_tracks' AND column_name='artist') THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD COLUMN artist VARCHAR';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_tracks' AND column_name='youtube_video_id') THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD COLUMN youtube_video_id VARCHAR';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_tracks' AND column_name='thumbnail_url') THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD COLUMN thumbnail_url VARCHAR';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_tracks' AND column_name='duration') THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD COLUMN duration INT DEFAULT 0';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_tracks' AND column_name='position') THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD COLUMN position INT DEFAULT 0';
		END IF;

		-- Flags e metadados
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_tracks' AND column_name='is_active') THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD COLUMN is_active BOOLEAN DEFAULT TRUE';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_tracks' AND column_name='is_explicit') THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD COLUMN is_explicit BOOLEAN DEFAULT FALSE';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_tracks' AND column_name='genre') THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD COLUMN genre VARCHAR';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_tracks' AND column_name='mood') THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD COLUMN mood VARCHAR';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_tracks' AND column_name='bpm') THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD COLUMN bpm INT';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_tracks' AND column_name='key') THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD COLUMN key VARCHAR';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_tracks' AND column_name='energy') THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD COLUMN energy FLOAT DEFAULT 0';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_tracks' AND column_name='valence') THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD COLUMN valence FLOAT DEFAULT 0.5';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_tracks' AND column_name='danceability') THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD COLUMN danceability FLOAT DEFAULT 0.5';
		END IF;

		-- Estatísticas
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_tracks' AND column_name='play_count') THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD COLUMN play_count INT DEFAULT 0';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_tracks' AND column_name='skip_count') THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD COLUMN skip_count INT DEFAULT 0';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_tracks' AND column_name='upvotes') THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD COLUMN upvotes INT DEFAULT 0';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_tracks' AND column_name='downvotes') THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD COLUMN downvotes INT DEFAULT 0';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_tracks' AND column_name='average_play_duration') THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD COLUMN average_play_duration FLOAT DEFAULT 0';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_tracks' AND column_name='completion_rate') THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD COLUMN completion_rate FLOAT DEFAULT 0';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_tracks' AND column_name='last_played_at') THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD COLUMN last_played_at TIMESTAMP';
		END IF;

		-- Analytics JSON
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_tracks' AND column_name='analytics') THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD COLUMN analytics JSON';
		END IF;

		-- Timestamps
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_tracks' AND column_name='created_at') THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD COLUMN created_at TIMESTAMP DEFAULT NOW()';
		END IF;
		IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='playlist_tracks' AND column_name='updated_at') THEN
			EXECUTE 'ALTER TABLE playlist_tracks ADD COLUMN updated_at TIMESTAMP DEFAULT NOW()';
		END IF;
	END IF;
END $$;

-- Índices comuns
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_playlist ON playlist_tracks(playlist_id);
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_position ON playlist_tracks(playlist_id, position);
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_youtube ON playlist_tracks(youtube_video_id);
