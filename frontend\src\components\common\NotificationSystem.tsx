import React, { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Bell,
  X,
  Check,
  AlertCircle,
  Info,
  CheckCircle,
  XCircle,
  Music,
  Users,
  Heart,
  MessageSquare,
  Settings,
  Volume2,
  Star,
  Trophy,
  Gift,
  Zap,
} from "lucide-react";
import { toast } from "react-hot-toast";

export interface Notification {
  id: string;
  type:
    | "success"
    | "error"
    | "warning"
    | "info"
    | "music"
    | "vote"
    | "badge"
    | "system";
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actions?: NotificationAction[];
  data?: any;
  priority?: "low" | "normal" | "high" | "urgent";
  category?: string;
  icon?: React.ComponentType<any>;
  color?: string;
  autoClose?: boolean;
  duration?: number;
}

export interface NotificationAction {
  id: string;
  label: string;
  action: () => void;
  style?: "primary" | "secondary" | "danger";
}

interface NotificationSystemProps {
  maxNotifications?: number;
  position?: "top-right" | "top-left" | "bottom-right" | "bottom-left";
  enableSound?: boolean;
  enablePersistence?: boolean;
}

const NotificationSystem: React.FC<NotificationSystemProps> = ({
  maxNotifications = 5,
  position = "top-right",
  enableSound = true,
  enablePersistence = true,
}) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const [connectionStatus, setConnectionStatus] = useState<
    "connected" | "disconnected" | "error" | "connecting"
  >("disconnected");
  const audioRef = useRef<HTMLAudioElement | null>(null);
  // WebSocket é gerenciado por wsService

  // Ícones por tipo
  const typeIcons = {
    success: CheckCircle,
    error: XCircle,
    warning: AlertCircle,
    info: Info,
    music: Music,
    vote: Heart,
    badge: Trophy,
    system: Settings,
  };

  // Cores por tipo
  const typeColors = {
    success: "text-green-600 bg-green-100 dark:bg-green-900/20",
    error: "text-red-600 bg-red-100 dark:bg-red-900/20",
    warning: "text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20",
    info: "text-blue-600 bg-blue-100 dark:bg-blue-900/20",
    music: "text-purple-600 bg-purple-100 dark:bg-purple-900/20",
    vote: "text-pink-600 bg-pink-100 dark:bg-pink-900/20",
    badge: "text-orange-600 bg-orange-100 dark:bg-orange-900/20",
    system: "text-gray-600 bg-gray-100 dark:bg-gray-900/20",
  };

  useEffect(() => {
    // Conectar WebSocket para notificações em tempo real
    connectWebSocket();

    // Carregar notificações persistidas
    if (enablePersistence) {
      loadPersistedNotifications();
    }

    // Configurar áudio
    if (enableSound) {
      audioRef.current = new Audio("/notification-sound.mp3");
    }

    return () => {
      // Cleanup será feito pelo serviço Socket.IO
    };
  }, []);

  useEffect(() => {
    // Atualizar contador de não lidas
    const unread = notifications.filter((n) => !n.read).length;
    setUnreadCount(unread);

    // Persistir notificações
    if (enablePersistence) {
      localStorage.setItem("notifications", JSON.stringify(notifications));
    }
  }, [notifications, enablePersistence]);

  const connectWebSocket = () => {
    try {
      // Usar o serviço Socket.IO existente para notificações
      console.log("🔌 NotificationSystem conectando via Socket.IO");

      // Importar o serviço WebSocket
      import("@/services/websocket").then(({ wsService }) => {
        // Configurar listeners para notificações
        wsService.on("notification", (data: any) => {
          const notification = data.notification || data;
          console.log("📨 Notificação recebida no sistema:", notification);
          addNotification(notification);
        });

        // Configurar listeners de conexão
        wsService.onConnectionStatusChange((status) => {
          setConnectionStatus(status);
        });

        // Verificar status inicial
        setConnectionStatus(wsService.getConnectionStatus());

        // Entrar na sala do restaurante se disponível
        const pathname = window.location.pathname;
        let restaurantId = "demo-restaurant";
        if (pathname.includes("/client/")) {
          restaurantId = pathname.split("/client/")[1]?.split("?")[0] || restaurantId;
        } else if (pathname.includes("/restaurant/public/")) {
          restaurantId = pathname.split("/restaurant/public/")[1]?.split("/")[0] || restaurantId;
        }

        if (restaurantId) {
          wsService.joinRestaurant(restaurantId);
        }
      });
    } catch (error) {
      console.error("❌ Erro ao conectar NotificationSystem:", error);
      setConnectionStatus("error");
    }
  };

  const loadPersistedNotifications = () => {
    try {
      const saved = localStorage.getItem("notifications");
      if (saved) {
        const parsed = JSON.parse(saved);
        const notifications = parsed.map((n: any) => ({
          ...n,
          timestamp: new Date(n.timestamp),
        }));
        setNotifications(notifications);
      }
    } catch (error) {
      console.error("Erro ao carregar notificações:", error);
    }
  };

  const addNotification = (
    notification: Omit<Notification, "id" | "timestamp" | "read">
  ) => {
    const newNotification: Notification = {
      id: crypto.randomUUID(),
      timestamp: new Date(),
      read: false,
      priority: "normal",
      autoClose: true,
      duration: 5000,
      ...notification,
    };

    setNotifications((prev) => {
      const updated = [newNotification, ...prev];

      // Limitar número máximo
      if (updated.length > maxNotifications) {
        return updated.slice(0, maxNotifications);
      }

      return updated;
    });

    // Tocar som
    if (enableSound && audioRef.current) {
      audioRef.current.play().catch(() => {
        // Ignorar erro se não conseguir tocar
      });
    }

    // Auto-close
    if (newNotification.autoClose) {
      setTimeout(() => {
        removeNotification(newNotification.id);
      }, newNotification.duration);
    }

    // Toast para notificações importantes
    if (
      newNotification.priority === "high" ||
      newNotification.priority === "urgent"
    ) {
      toast(newNotification.message, {
        icon: React.createElement(
          newNotification.icon || typeIcons[newNotification.type],
          {
            className: "w-5 h-5",
          }
        ),
      });
    }
  };

  const removeNotification = (id: string) => {
    setNotifications((prev) => prev.filter((n) => n.id !== id));
  };

  const markAsRead = (id: string) => {
    setNotifications((prev) =>
      prev.map((n) => (n.id === id ? { ...n, read: true } : n))
    );
  };

  const markAllAsRead = () => {
    setNotifications((prev) => prev.map((n) => ({ ...n, read: true })));
  };

  const clearAll = () => {
    setNotifications([]);
  };

  const executeAction = (
    action: NotificationAction,
    notificationId: string
  ) => {
    action.action();
    markAsRead(notificationId);
  };

  const getPositionClasses = () => {
    switch (position) {
      case "top-left":
        return "top-4 left-4";
      case "top-right":
        return "top-4 right-4";
      case "bottom-left":
        return "bottom-4 left-4";
      case "bottom-right":
        return "bottom-4 right-4";
      default:
        return "top-4 right-4";
    }
  };

  // API pública para outros componentes
  const notificationAPI = {
    success: (
      title: string,
      message: string,
      options?: Partial<Notification>
    ) => addNotification({ type: "success", title, message, ...options }),

    error: (title: string, message: string, options?: Partial<Notification>) =>
      addNotification({ type: "error", title, message, ...options }),

    warning: (
      title: string,
      message: string,
      options?: Partial<Notification>
    ) => addNotification({ type: "warning", title, message, ...options }),

    info: (title: string, message: string, options?: Partial<Notification>) =>
      addNotification({ type: "info", title, message, ...options }),

    music: (title: string, message: string, options?: Partial<Notification>) =>
      addNotification({ type: "music", title, message, ...options }),

    vote: (title: string, message: string, options?: Partial<Notification>) =>
      addNotification({ type: "vote", title, message, ...options }),

    badge: (title: string, message: string, options?: Partial<Notification>) =>
      addNotification({ type: "badge", title, message, ...options }),

    custom: (notification: Omit<Notification, "id" | "timestamp" | "read">) =>
      addNotification(notification),
  };

  // Expor API globalmente
  useEffect(() => {
    (window as any).notifications = notificationAPI;
  }, []);

  return (
    <>
      {/* Botão de notificações */}
      <div className="relative">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="relative p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
        >
          <Bell className="w-6 h-6" />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
              {unreadCount > 9 ? "9+" : unreadCount}
            </span>
          )}
          {/* Indicador de status da conexão WebSocket */}
          <span
            className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-gray-800 ${
              connectionStatus === "connected"
                ? "bg-green-500"
                : connectionStatus === "error"
                ? "bg-red-500"
                : "bg-yellow-500"
            }`}
            title={
              connectionStatus === "connected"
                ? "WebSocket conectado"
                : connectionStatus === "error"
                ? "Erro na conexão"
                : "Desconectado"
            }
          />
        </button>

        {/* Dropdown de notificações */}
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: -10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -10 }}
              className="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50"
            >
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                <h3 className="font-medium text-gray-900 dark:text-white">
                  Notificações
                </h3>
                <div className="flex items-center space-x-2">
                  {unreadCount > 0 && (
                    <button
                      onClick={markAllAsRead}
                      className="text-xs text-blue-600 hover:text-blue-700 transition-colors"
                    >
                      Marcar todas como lidas
                    </button>
                  )}
                  <button
                    onClick={clearAll}
                    className="text-xs text-gray-500 hover:text-gray-700 transition-colors"
                  >
                    Limpar
                  </button>
                </div>
              </div>

              {/* Lista de notificações */}
              <div className="max-h-96 overflow-y-auto">
                {notifications.length === 0 ? (
                  <div className="p-8 text-center text-gray-500 dark:text-gray-400">
                    <Bell className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>Nenhuma notificação</p>
                  </div>
                ) : (
                  <div className="space-y-1 p-2">
                    {notifications.map((notification) => {
                      const Icon =
                        notification.icon || typeIcons[notification.type];
                      const colorClass = typeColors[notification.type];

                      return (
                        <motion.div
                          key={notification.id}
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -20 }}
                          className={`p-3 rounded-lg border transition-all cursor-pointer ${
                            notification.read
                              ? "bg-gray-50 dark:bg-gray-700/50 border-gray-200 dark:border-gray-600"
                              : "bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-500 shadow-sm"
                          }`}
                          onClick={() => markAsRead(notification.id)}
                        >
                          <div className="flex items-start space-x-3">
                            <div className={`p-2 rounded-lg ${colorClass}`}>
                              <Icon className="w-4 h-4" />
                            </div>

                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between">
                                <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                                  {notification.title}
                                </h4>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    removeNotification(notification.id);
                                  }}
                                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                                >
                                  <X className="w-4 h-4" />
                                </button>
                              </div>

                              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                {notification.message}
                              </p>

                              <div className="flex items-center justify-between mt-2">
                                <span className="text-xs text-gray-500">
                                  {notification.timestamp.toLocaleTimeString(
                                    "pt-BR",
                                    {
                                      hour: "2-digit",
                                      minute: "2-digit",
                                    }
                                  )}
                                </span>

                                {notification.actions && (
                                  <div className="flex items-center space-x-2">
                                    {notification.actions.map((action) => (
                                      <button
                                        key={action.id}
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          executeAction(
                                            action,
                                            notification.id
                                          );
                                        }}
                                        className={`px-2 py-1 text-xs rounded transition-colors ${
                                          action.style === "primary"
                                            ? "bg-blue-600 text-white hover:bg-blue-700"
                                            : action.style === "danger"
                                            ? "bg-red-600 text-white hover:bg-red-700"
                                            : "bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-500"
                                        }`}
                                      >
                                        {action.label}
                                      </button>
                                    ))}
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      );
                    })}
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Notificações flutuantes */}
      <div className={`fixed ${getPositionClasses()} z-50 space-y-2`}>
        <AnimatePresence>
          {notifications
            .filter((n) => !n.read && n.priority === "urgent")
            .slice(0, 3)
            .map((notification) => {
              const Icon = notification.icon || typeIcons[notification.type];
              const colorClass = typeColors[notification.type];

              return (
                <motion.div
                  key={notification.id}
                  initial={{
                    opacity: 0,
                    x: position.includes("right") ? 300 : -300,
                  }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{
                    opacity: 0,
                    x: position.includes("right") ? 300 : -300,
                  }}
                  className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4 w-80"
                >
                  <div className="flex items-start space-x-3">
                    <div className={`p-2 rounded-lg ${colorClass}`}>
                      <Icon className="w-5 h-5" />
                    </div>

                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {notification.title}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        {notification.message}
                      </p>

                      {notification.actions && (
                        <div className="flex items-center space-x-2 mt-3">
                          {notification.actions.map((action) => (
                            <button
                              key={action.id}
                              onClick={() =>
                                executeAction(action, notification.id)
                              }
                              className={`px-3 py-1 text-sm rounded transition-colors ${
                                action.style === "primary"
                                  ? "bg-blue-600 text-white hover:bg-blue-700"
                                  : action.style === "danger"
                                  ? "bg-red-600 text-white hover:bg-red-700"
                                  : "bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-500"
                              }`}
                            >
                              {action.label}
                            </button>
                          ))}
                        </div>
                      )}
                    </div>

                    <button
                      onClick={() => removeNotification(notification.id)}
                      className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    >
                      <X className="w-5 h-5" />
                    </button>
                  </div>
                </motion.div>
              );
            })}
        </AnimatePresence>
      </div>
    </>
  );
};

// Hook para usar notificações
export const useNotifications = () => {
  const notify = {
    success: (
      title: string,
      message: string,
      options?: Partial<Notification>
    ) => {
      if ((window as any).notifications) {
        (window as any).notifications.success(title, message, options);
      }
    },

    error: (
      title: string,
      message: string,
      options?: Partial<Notification>
    ) => {
      if ((window as any).notifications) {
        (window as any).notifications.error(title, message, options);
      }
    },

    warning: (
      title: string,
      message: string,
      options?: Partial<Notification>
    ) => {
      if ((window as any).notifications) {
        (window as any).notifications.warning(title, message, options);
      }
    },

    info: (title: string, message: string, options?: Partial<Notification>) => {
      if ((window as any).notifications) {
        (window as any).notifications.info(title, message, options);
      }
    },

    music: (
      title: string,
      message: string,
      options?: Partial<Notification>
    ) => {
      if ((window as any).notifications) {
        (window as any).notifications.music(title, message, options);
      }
    },

    vote: (title: string, message: string, options?: Partial<Notification>) => {
      if ((window as any).notifications) {
        (window as any).notifications.vote(title, message, options);
      }
    },

    badge: (
      title: string,
      message: string,
      options?: Partial<Notification>
    ) => {
      if ((window as any).notifications) {
        (window as any).notifications.badge(title, message, options);
      }
    },

    custom: (notification: Omit<Notification, "id" | "timestamp" | "read">) => {
      if ((window as any).notifications) {
        (window as any).notifications.custom(notification);
      }
    },
  };

  return notify;
};

export default NotificationSystem;
