import React, { useState, useEffect, useRef, useCallback, memo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Play,
  Pause,
  SkipForward,
  SkipBack,
  Volume2,
  VolumeX,
  Repeat,
  Shuffle,
  Music,
  Clock,
  Users,
  Heart,
  Share2,
  Maximize2,
  Minimize2,
} from "lucide-react";
import { toast } from "react-hot-toast";

enum RepeatMode {
  Off = "off",
  One = "one",
  All = "all",
}

interface Track {
  id: string;
  title: string;
  artist: string;
  duration: number;
  thumbnailUrl: string;
  youtubeVideoId: string;
  suggestedBy: string;
  upvotes: number;
  downvotes: number;
}

interface AdvancedMusicPlayerProps {
  currentTrack: Track | null;
  queue: Track[];
  isPlaying: boolean;
  volume: number;
  currentTime: number;
  onPlay: () => void;
  onPause: () => void;
  onNext: () => void;
  onPrevious: () => void;
  onVolumeChange: (volume: number) => void;
  onSeek: (time: number) => void;
  onShuffle: () => void;
  onRepeat: () => void;
  restaurantId: string;
}

const AdvancedMusicPlayer: React.FC<AdvancedMusicPlayerProps> = memo(
  ({
    currentTrack,
    queue,
    isPlaying,
    volume,
    currentTime,
    onPlay,
    onPause,
    onNext,
    onPrevious,
    onVolumeChange,
    onSeek,
    onShuffle,
    onRepeat,
    restaurantId,
  }) => {
    const [isExpanded, setIsExpanded] = useState(false);
    const [isMuted, setIsMuted] = useState(volume === 0);
    const [previousVolume, setPreviousVolume] = useState(volume);
    const [repeatMode, setRepeatMode] = useState<RepeatMode>(RepeatMode.Off);
    const [shuffleMode, setShuffleMode] = useState(false);
    const [showVolumeTooltip, setShowVolumeTooltip] = useState(false);
    const [isDraggingProgress, setIsDraggingProgress] = useState(false);

    const progressRef = useRef<HTMLDivElement>(null);
    const volumeRef = useRef<HTMLDivElement>(null);

    const formatTime = useCallback((seconds: number): string => {
      if (!isFinite(seconds) || seconds < 0) return "0:00";
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins}:${secs.toString().padStart(2, "0")}`;
    }, []);

    const handleProgressInteraction = useCallback(
      (clientX: number) => {
        if (!progressRef.current || !currentTrack) return;
        const rect = progressRef.current.getBoundingClientRect();
        const clickX = Math.max(0, Math.min(rect.width, clientX - rect.left));
        const percentage = clickX / rect.width;
        const newTime = Math.max(0, Math.min(currentTrack.duration, percentage * currentTrack.duration));
        onSeek(newTime);
      },
      [currentTrack, onSeek]
    );

    const handleProgressMouseDown = useCallback(
      (e: React.MouseEvent<HTMLDivElement>) => {
        setIsDraggingProgress(true);
        handleProgressInteraction(e.clientX);
      },
      [handleProgressInteraction]
    );

    const handleProgressMouseMove = useCallback(
      (e: MouseEvent) => {
        if (isDraggingProgress) {
          handleProgressInteraction(e.clientX);
        }
      },
      [isDraggingProgress, handleProgressInteraction]
    );

    const handleProgressMouseUp = useCallback(() => {
      setIsDraggingProgress(false);
    }, []);

    useEffect(() => {
      if (isDraggingProgress) {
        window.addEventListener("mousemove", handleProgressMouseMove);
        window.addEventListener("mouseup", handleProgressMouseUp);
      }
      return () => {
        window.removeEventListener("mousemove", handleProgressMouseMove);
        window.removeEventListener("mouseup", handleProgressMouseUp);
      };
    }, [isDraggingProgress, handleProgressMouseMove, handleProgressMouseUp]);

    const handleVolumeClick = useCallback(
      (e: React.MouseEvent<HTMLDivElement>) => {
        if (!volumeRef.current) return;
        const rect = volumeRef.current.getBoundingClientRect();
        const clickX = Math.max(0, Math.min(rect.width, e.clientX - rect.left));
        const newVolume = Math.round((clickX / rect.width) * 100);
        onVolumeChange(newVolume);
        setIsMuted(newVolume === 0);
        if (newVolume > 0) setPreviousVolume(newVolume);
        setShowVolumeTooltip(true);
        setTimeout(() => setShowVolumeTooltip(false), 1000);
      },
      [onVolumeChange]
    );

    const toggleMute = useCallback(() => {
      if (isMuted) {
        const restoreVolume = previousVolume || 50;
        onVolumeChange(restoreVolume);
        setIsMuted(false);
      } else {
        setPreviousVolume(volume);
        onVolumeChange(0);
        setIsMuted(true);
      }
    }, [isMuted, volume, previousVolume, onVolumeChange]);

    const handleRepeat = useCallback(() => {
      const modes = [RepeatMode.Off, RepeatMode.One, RepeatMode.All];
      const currentIndex = modes.indexOf(repeatMode);
      const nextMode = modes[(currentIndex + 1) % modes.length];
      setRepeatMode(nextMode);
      onRepeat();
      toast.success(
        {
          [RepeatMode.Off]: "Repetição desativada",
          [RepeatMode.One]: "Repetindo música atual",
          [RepeatMode.All]: "Repetindo fila",
        }[nextMode]
      );
    }, [repeatMode, onRepeat]);

    const handleShuffle = useCallback(() => {
      setShuffleMode((prev) => {
        const newMode = !prev;
        onShuffle();
        toast.success(newMode ? "Modo aleatório ativado" : "Modo aleatório desativado");
        return newMode;
      });
    }, [onShuffle]);

    const shareCurrentTrack = useCallback(async () => {
      if (!currentTrack) return;
      const shareData = {
        title: `${currentTrack.title} - ${currentTrack.artist}`,
        text: `Escutando agora no restaurante ${restaurantId}`,
        url: `https://youtube.com/watch?v=${currentTrack.youtubeVideoId}`,
      };
      try {
        await navigator.share(shareData);
      } catch (error) {
        await navigator.clipboard.writeText(
          `${shareData.title}\n${shareData.text}\n${shareData.url}`
        );
        toast.success("Link copiado para a área de transferência!");
      }
    }, [currentTrack, restaurantId]);

    if (!currentTrack) {
      return (
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-center text-gray-500 dark:text-gray-400">
            <Music className="w-8 h-8 mr-3" />
            <span>Nenhuma música tocando</span>
          </div>
        </div>
      );
    }

    const progressPercentage = currentTrack.duration > 0 ? Math.min(100, (currentTime / currentTrack.duration) * 100) : 0;

    return (
      <motion.div
        layout
        className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg transition-all duration-300 ${
          isExpanded ? "fixed inset-4 z-50 max-w-5xl mx-auto" : "relative max-w-3xl mx-auto"
        }`}
      >
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3 flex-1 min-w-0">
            <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-200 dark:bg-gray-700 flex-shrink-0">
              <img
                src={currentTrack.thumbnailUrl}
                alt={currentTrack.title}
                className="w-full h-full object-cover"
                loading="lazy"
              />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">{currentTrack.title}</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 truncate">{currentTrack.artist}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={shareCurrentTrack}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              aria-label="Compartilhar música"
            >
              <Share2 className="w-5 h-5" />
            </button>
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              aria-label={isExpanded ? "Minimizar player" : "Expandir player"}
            >
              {isExpanded ? <Minimize2 className="w-5 h-5" /> : <Maximize2 className="w-5 h-5" />}
            </button>
          </div>
        </div>

        <div className="px-4 py-3">
          <div className="flex items-center space-x-3 text-sm text-gray-600 dark:text-gray-400">
            <span>{formatTime(currentTime)}</span>
            <div
              ref={progressRef}
              className="flex-1 h-2 bg-gray-200 dark:bg-gray-700 rounded-full cursor-pointer relative"
              onMouseDown={handleProgressMouseDown}
              role="slider"
              aria-valuemin={0}
              aria-valuemax={currentTrack.duration}
              aria-valuenow={currentTime}
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === "ArrowRight") onSeek(Math.min(currentTrack.duration, currentTime + 5));
                if (e.key === "ArrowLeft") onSeek(Math.max(0, currentTime - 5));
              }}
            >
              <div
                className="h-full bg-blue-600 rounded-full relative"
                style={{ width: `${progressPercentage}%` }}
              >
                <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-3 h-3 bg-blue-600 rounded-full shadow-sm transition-opacity" />
              </div>
            </div>
            <span>{formatTime(currentTrack.duration)}</span>
          </div>
        </div>

        <div className="flex items-center justify-center space-x-4 sm:space-x-6 p-4">
          <button
            onClick={handleShuffle}
            className={`p-2 rounded-full transition-all ${
              shuffleMode
                ? "text-blue-600 bg-blue-100 dark:bg-blue-900/20"
                : "text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            }`}
            aria-label={shuffleMode ? "Desativar modo aleatório" : "Ativar modo aleatório"}
          >
            <Shuffle className="w-5 h-5" />
          </button>
          <button
            onClick={onPrevious}
            className="p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
            aria-label="Música anterior"
          >
            <SkipBack className="w-5 h-5" />
          </button>
          <button
            onClick={isPlaying ? onPause : onPlay}
            className="p-3 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors shadow-md"
            aria-label={isPlaying ? "Pausar" : "Tocar"}
          >
            {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6 ml-0.5" />}
          </button>
          <button
            onClick={onNext}
            className="p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
            aria-label="Próxima música"
          >
            <SkipForward className="w-5 h-5" />
          </button>
          <button
            onClick={handleRepeat}
            className={`p-2 rounded-full transition-all relative ${
              repeatMode !== RepeatMode.Off
                ? "text-blue-600 bg-blue-100 dark:bg-blue-900/20"
                : "text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            }`}
            aria-label={`Modo de repetição: ${repeatMode}`}
          >
            <Repeat className="w-5 h-5" />
            {repeatMode === RepeatMode.One && (
              <span className="absolute -top-1 -right-1 w-3 h-3 bg-blue-600 text-white text-[10px] rounded-full flex items-center justify-center">
                1
              </span>
            )}
          </button>
        </div>

        <div className="flex flex-col sm:flex-row items-center justify-between px-4 pb-4 gap-4">
          <div className="flex items-center space-x-3 relative">
            <button
              onClick={toggleMute}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              aria-label={isMuted ? "Ativar som" : "Silenciar"}
            >
              {isMuted || volume === 0 ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
            </button>
            <div
              ref={volumeRef}
              className="w-20 sm:w-24 h-2 bg-gray-200 dark:bg-gray-700 rounded-full cursor-pointer relative"
              onClick={handleVolumeClick}
              onMouseEnter={() => setShowVolumeTooltip(true)}
              onMouseLeave={() => setShowVolumeTooltip(false)}
              role="slider"
              aria-valuemin={0}
              aria-valuemax={100}
              aria-valuenow={isMuted ? 0 : volume}
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === "ArrowRight") onVolumeChange(Math.min(100, volume + 5));
                if (e.key === "ArrowLeft") onVolumeChange(Math.max(0, volume - 5));
              }}
            >
              <div
                className="h-full bg-blue-600 rounded-full"
                style={{ width: `${isMuted ? 0 : volume}%` }}
              />
              {showVolumeTooltip && (
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded">
                  {Math.round(isMuted ? 0 : volume)}%
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-center space-x-1">
              <Users className="w-4 h-4" />
              <span className="truncate max-w-[100px] sm:max-w-[150px]">{currentTrack.suggestedBy}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Heart className="w-4 h-4" />
              <span>{currentTrack.upvotes - currentTrack.downvotes}</span>
            </div>
          </div>
        </div>

        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="border-t border-gray-200 dark:border-gray-700 p-4"
            >
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Próximas na Fila ({queue.length})
              </h4>
              <div className="space-y-3 max-h-[300px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600">
                {queue.length === 0 ? (
                  <p className="text-sm text-gray-500 dark:text-gray-400 text-center">Nenhuma música na fila</p>
                ) : (
                  queue.slice(0, 15).map((track, index) => (
                    <div
                      key={track.id}
                      className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    >
                      <span className="text-sm text-gray-400 w-6">{index + 1}</span>
                      <img
                        src={track.thumbnailUrl}
                        alt={track.title}
                        className="w-10 h-10 rounded object-cover flex-shrink-0"
                        loading="lazy"
                      />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">{track.title}</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                          {track.artist} • {track.suggestedBy}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                        <Clock className="w-4 h-4" />
                        <span>{formatTime(track.duration)}</span>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    );
  }
);

AdvancedMusicPlayer.displayName = "AdvancedMusicPlayer";

export default AdvancedMusicPlayer;