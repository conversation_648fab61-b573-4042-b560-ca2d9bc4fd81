import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  <PERSON>in<PERSON><PERSON><PERSON>n,
  Index,
} from "typeorm";
import { Restaurant } from "./Restaurant";
import { Suggestion } from "./Suggestion";
import { User } from "./User";

export enum PlayStatus {
  PLAYING = "playing",
  COMPLETED = "completed",
  SKIPPED = "skipped",
  INTERRUPTED = "interrupted",
  ERROR = "error",
}

@Entity("play_history")

export class PlayHistory {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar" })
  youtubeVideoId: string;

  @Column({ type: "varchar" })
  title: string;

  @Column({ type: "varchar", nullable: true })
  artist: string;

  @Column({ type: "integer" })
  duration: number; // duração total em segundos

  @Column({ type: "integer" })
  playDuration: number; // tempo efetivamente tocado em segundos

  @Column({
    type: "enum",
    enum: PlayStatus,
  })
  status: PlayStatus;

  @Column({ type: "text", nullable: true })
  skipReason?: string;

  @Column({ type: "timestamp" })
  playedAt: Date;

  @Column({ type: "timestamp", nullable: true })
  endedAt: Date;

  @Column({ type: "integer", nullable: true })
  queuePosition: number; // posição que estava na fila quando tocou

  @Column({ type: "integer", default: 0 })
  voteCount: number; // votos que tinha quando tocou

  @Column({ type: "json", nullable: true })
  metadata: {
    source?: "playlist" | "suggestion" | "manual";
    playlistId?: string;
    suggestionId?: string;
    volume?: number;
    crossfadeUsed?: boolean;
    deviceInfo?: {
      type?: string;
      name?: string;
    };
  };

  @ManyToOne(() => Restaurant, (restaurant) => restaurant.playHistory, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "restaurant_id" })
  restaurant: Restaurant;

  @ManyToOne(() => Suggestion, {
    nullable: true,
    onDelete: "SET NULL",
  })
  @JoinColumn({ name: "suggestion_id" })
  suggestion: Suggestion;

  @ManyToOne(() => User, {
    nullable: true,
    onDelete: "SET NULL",
  })
  @JoinColumn({ name: "played_by" })
  playedBy: User;

  @CreateDateColumn()
  createdAt: Date;

  // Métodos da instância

  // Calcular porcentagem tocada
  getPlayPercentage(): number {
    if (this.duration === 0) return 0;
    return Math.round((this.playDuration / this.duration) * 100);
  }

  // Verificar se foi tocada completamente
  wasPlayedCompletely(): boolean {
    return (
      this.status === PlayStatus.COMPLETED && this.getPlayPercentage() >= 90
    );
  }

  // Verificar se foi pulada rapidamente
  wasSkippedQuickly(): boolean {
    return this.status === PlayStatus.SKIPPED && this.getPlayPercentage() < 10;
  }

  // Obter duração formatada
  getFormattedDuration(): string {
    const minutes = Math.floor(this.duration / 60);
    const seconds = this.duration % 60;
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  }

  // Obter duração tocada formatada
  getFormattedPlayDuration(): string {
    const minutes = Math.floor(this.playDuration / 60);
    const seconds = this.playDuration % 60;
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  }

  // Obter tempo total de reprodução
  getTotalPlayTime(): number {
    if (!this.endedAt) return 0;
    return Math.floor(
      (this.endedAt.getTime() - this.playedAt.getTime()) / 1000
    );
  }

  // Marcar como finalizada
  markAsCompleted(): void {
    this.status = PlayStatus.COMPLETED;
    this.endedAt = new Date();
    this.playDuration = this.duration;
  }

  // Marcar como pulada
  markAsSkipped(reason?: string): void {
    this.status = PlayStatus.SKIPPED;
    this.endedAt = new Date();
    this.skipReason = reason;
    this.playDuration = this.getTotalPlayTime();
  }

  // Marcar como interrompida
  markAsInterrupted(reason?: string): void {
    this.status = PlayStatus.INTERRUPTED;
    this.endedAt = new Date();
    this.skipReason = reason;
    this.playDuration = this.getTotalPlayTime();
  }

  // Marcar como erro
  markAsError(reason?: string): void {
    this.status = PlayStatus.ERROR;
    this.endedAt = new Date();
    this.skipReason = reason;
    this.playDuration = this.getTotalPlayTime();
  }

  // Serialização para JSON
  toJSON() {
    return {
      id: this.id,
      youtubeVideoId: this.youtubeVideoId,
      title: this.title,
      artist: this.artist,
      duration: this.duration,
      playDuration: this.playDuration,
      formattedDuration: this.getFormattedDuration(),
      formattedPlayDuration: this.getFormattedPlayDuration(),
      playPercentage: this.getPlayPercentage(),
      status: this.status,
      skipReason: this.skipReason,
      playedAt: this.playedAt,
      endedAt: this.endedAt,
      queuePosition: this.queuePosition,
      voteCount: this.voteCount,
      wasPlayedCompletely: this.wasPlayedCompletely(),
      wasSkippedQuickly: this.wasSkippedQuickly(),
      metadata: this.metadata,
      playedBy: this.playedBy
        ? {
            id: this.playedBy.id,
            name: this.playedBy.name,
          }
        : null,
      suggestion: this.suggestion
        ? {
            id: this.suggestion.id,
            source: this.suggestion.source,
          }
        : null,
    };
  }

  // Serialização para relatórios ECAD
  toECADReport() {
    return {
      titulo: this.title,
      artista: this.artist || "Desconhecido",
      duracao: this.getFormattedDuration(),
      dataHora: this.playedAt.toISOString(),
      percentualTocado: this.getPlayPercentage(),
      status: this.status,
      youtubeId: this.youtubeVideoId,
    };
  }
}
