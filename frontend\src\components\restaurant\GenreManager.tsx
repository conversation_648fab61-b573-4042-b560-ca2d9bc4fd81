import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Music,
  Plus,
  Edit3,
  Trash2,
  Save,
  X,
  Search,
  Filter,
  Palette,
  Tag,
  <PERSON><PERSON>hart3,
  <PERSON><PERSON><PERSON>,
  <PERSON>freshC<PERSON>,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import { toast } from "react-hot-toast";
import { useRestaurantContext } from "./RestaurantDashboard";
import { buildApiUrl, API_CONFIG } from "../../config/api";

interface Genre {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  category: "music" | "mood" | "energy" | "time" | "custom";
  color: string;
  icon?: string;
  priority: number;
  isActive: boolean;
  isDefault: boolean;
  usageCount: number;
  lastUsedAt?: string;
}

interface GenreManagerProps {
  onGenreSelect?: (genre: Genre) => void;
  selectedGenres?: string[];
  mode?: "select" | "manage";
}

const GenreManager: React.FC<GenreManagerProps> = ({
  onGenreSelect,
  selectedGenres = [],
  mode = "manage",
}) => {
  const { restaurantId } = useRestaurantContext();

  const [genres, setGenres] = useState<Record<string, Genre[]>>({});
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingGenre, setEditingGenre] = useState<Genre | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    displayName: "",
    description: "",
    category: "music" as const,
    color: "#3B82F6",
    icon: "",
    priority: 0,
  });

  const categories = {
    all: "Todos",
    music: "Gêneros Musicais",
    mood: "Humor",
    energy: "Energia",
    time: "Horário",
    custom: "Personalizado",
  };

  const iconOptions = [
    "Music",
    "Music2",
    "Music3",
    "Music4",
    "Mic",
    "Heart",
    "Zap",
    "Volume2",
    "Headphones",
    "Sun",
    "Moon",
    "Coffee",
    "Smile",
    "CloudRain",
    "Waves",
    "Activity",
    "Star",
    "Disc",
    "Radio",
    "Speaker",
  ];

  useEffect(() => {
    loadGenres();
  }, []);

  const loadGenres = async () => {
    try {
      setLoading(true);
      console.log("🎵 Carregando gêneros para restaurante:", restaurantId);

      // Primeiro, tentar carregar gêneros baseados na playlist do restaurante
      await loadPlaylistBasedGenres();

      const url = buildApiUrl(`${API_CONFIG.ENDPOINTS.GENRES}`);
      console.log("🎵 URL da API:", url);

      // Obter token de autenticação
      const authToken = localStorage.getItem("authToken");
      const headers: Record<string, string> = {
        "Content-Type": "application/json",
      };

      if (authToken) {
        headers.Authorization = `Bearer ${authToken}`;
      }

      const response = await fetch(url, { headers });
      if (response.ok) {
        const data = await response.json();
        console.log("🎵 Gêneros do sistema carregados:", data);

        // Mesclar gêneros do sistema com os da playlist
        setGenres((prevGenres) => ({
          ...data.genres,
          ...prevGenres, // Priorizar gêneros da playlist
        }));

        toast.success("Gêneros carregados com sucesso!");
      } else {
        const errorText = await response.text().catch(() => "");
        console.error(
          "🎵 Erro na resposta:",
          response.status,
          response.statusText,
          errorText
        );

        // Se for erro 500, carregar dados de exemplo
        if (response.status === 500) {
          console.log("🎵 Carregando gêneros de exemplo devido ao erro 500");
          loadExampleGenres();
          toast("Carregados gêneros de exemplo (erro no servidor)", {
            icon: "⚠️",
            duration: 4000,
          });
        } else {
          toast.error("Erro ao carregar gêneros");
        }
      }
    } catch (error) {
      console.error("🎵 Erro ao carregar gêneros:", error);
      loadExampleGenres();
      toast.error("Erro ao carregar gêneros. Carregando dados de exemplo.");
    } finally {
      setLoading(false);
    }
  };

  // Nova função para carregar gêneros baseados na playlist do restaurante
  const loadPlaylistBasedGenres = async () => {
    try {
      console.log("🎵 Analisando playlist para extrair gêneros...");

      // Carregar playlists do restaurante
      const playlistsResponse = await fetch(
        buildApiUrl(`/playlists/restaurant/${restaurantId}`)
      );

      if (!playlistsResponse.ok) {
        console.log(
          "🎵 Não foi possível carregar playlists para análise de gêneros"
        );
        return;
      }

      const playlistsData = await playlistsResponse.json();
      const playlists = playlistsData.playlists || [];

      if (playlists.length === 0) {
        console.log("🎵 Nenhuma playlist encontrada para análise");
        return;
      }

      // Analisar as músicas das playlists para extrair gêneros
  const extractedGenres: Record<string, Genre[]> = { } as any;
      const genreMap = new Map<string, number>();

      for (const playlist of playlists) {
        if (playlist.isActive) {
          // Carregar detalhes da playlist com tracks
          const playlistDetailResponse = await fetch(
            buildApiUrl(`/playlists/${playlist.id}`)
          );

          if (playlistDetailResponse.ok) {
            const playlistDetail = await playlistDetailResponse.json();
            const tracks = playlistDetail.tracks || [];

            console.log(
              `🎵 Analisando ${tracks.length} músicas da playlist "${playlist.name}"`
            );

            for (const track of tracks) {
              // Analisar título para identificar possíveis gêneros
              const genres = analyzeTrackGenres(track.title, "");
              genres.forEach((genre) => {
                const count = genreMap.get(genre) || 0;
                genreMap.set(genre, count + 1);
              });
            }
          }
        }
      }

      // Converter para formato de gêneros
      let priority = 1;
      genreMap.forEach((count, genreName) => {
        if (count >= 2) {
          // Só incluir gêneros que aparecem pelo menos 2 vezes
          (extractedGenres as any).music = (extractedGenres as any).music || [];
          (extractedGenres as any).music.push({
            id: `playlist-${genreName}`,
            name: genreName.toLowerCase(),
            displayName: genreName,
            color: getGenreColor(genreName),
            isActive: true,
            category: "music",
            priority: priority++,
            description: `Gênero identificado na playlist (${count} músicas)`,
            isDefault: false,
            usageCount: count,
          });
        }
      });

      if ((extractedGenres as any).music && (extractedGenres as any).music.length > 0) {
        console.log("🎵 Gêneros extraídos da playlist:", extractedGenres);
        setGenres((prevGenres) => ({
          ...prevGenres,
          music: [
            ...((prevGenres.music as any) || []),
            ...((extractedGenres as any).music as Genre[]),
          ],
        }));
        toast.success(
          `${(extractedGenres as any).music.length} gêneros identificados na playlist!`
        );
      }
    } catch (error) {
      console.error("🎵 Erro ao analisar playlist para gêneros:", error);
    }
  };

  // Carregar gêneros de exemplo
  const loadExampleGenres = () => {
  const exampleGenres: Record<string, Genre[]> = {
      music: [
        {
          id: "1",
          name: "rock",
          displayName: "Rock",
          color: "#e74c3c",
          isActive: true,
          category: "music",
          priority: 1,
      isDefault: true,
      usageCount: 0,
        },
        {
          id: "2",
          name: "pop",
          displayName: "Pop",
          color: "#3498db",
          isActive: true,
          category: "music",
          priority: 2,
      isDefault: true,
      usageCount: 0,
        },
        {
          id: "3",
          name: "jazz",
          displayName: "Jazz",
          color: "#f39c12",
          isActive: true,
          category: "music",
          priority: 3,
      isDefault: false,
      usageCount: 0,
        },
        {
          id: "4",
          name: "classical",
          displayName: "Clássica",
          color: "#9b59b6",
          isActive: true,
          category: "music",
          priority: 4,
      isDefault: false,
      usageCount: 0,
        },
        {
          id: "5",
          name: "electronic",
          displayName: "Eletrônica",
          color: "#1abc9c",
          isActive: true,
          category: "music",
          priority: 5,
      isDefault: false,
      usageCount: 0,
        },
        {
          id: "6",
          name: "reggae",
          displayName: "Reggae",
          color: "#27ae60",
          isActive: true,
          category: "music",
          priority: 6,
      isDefault: false,
      usageCount: 0,
        },
        {
          id: "7",
          name: "blues",
          displayName: "Blues",
          color: "#34495e",
          isActive: true,
          category: "music",
          priority: 7,
      isDefault: false,
      usageCount: 0,
        },
        {
          id: "8",
          name: "country",
          displayName: "Country",
          color: "#d35400",
          isActive: true,
          category: "music",
          priority: 8,
      isDefault: false,
      usageCount: 0,
        },
      ],
    };
    setGenres(exampleGenres);
  };

  const seedDefaultGenres = async () => {
    try {
      console.log("🎵 Criando gêneros padrão para restaurante:", restaurantId);

      const url = buildApiUrl(`${API_CONFIG.ENDPOINTS.GENRES}/seed`);
      const response = await fetch(url, {
        method: "POST",
      });
      if (response.ok) {
        const data = await response.json();
        toast.success(data.message || "Gêneros padrão criados com sucesso!");
        loadGenres();
      } else {
        const errorData = await response.json().catch(() => ({}));
        toast.error(errorData.message || "Erro ao criar gêneros padrão");
      }
    } catch (error) {
      console.error("🎵 Erro ao criar gêneros padrão:", error);
      toast.error("Erro ao criar gêneros padrão");
    }
  };

  // Função para analisar uma música e identificar possíveis gêneros
  const analyzeTrackGenres = (title: string, artist: string): string[] => {
    const genres: string[] = [];
    const text = `${title} ${artist}`.toLowerCase();

    // Mapeamento de palavras-chave para gêneros (sem acentos para evitar problemas de encoding)
    const genreKeywords = {
      Rock: [
        "rock",
        "metal",
        "punk",
        "grunge",
        "alternative",
        "hard rock",
        "soft rock",
      ],
      Pop: ["pop", "mainstream", "hit", "chart", "top", "billboard"],
      Jazz: ["jazz", "swing", "blues", "smooth", "bossa nova"],
      Eletronica: [
        "electronic",
        "edm",
        "techno",
        "house",
        "dance",
        "remix",
        "mix",
        "dj",
      ],
      "Hip Hop": ["hip hop", "rap", "trap", "beats", "freestyle", "mc"],
      Reggae: ["reggae", "ska", "dub", "bob marley"],
      Country: ["country", "folk", "acoustic", "bluegrass"],
      Classica: [
        "classical",
        "orchestra",
        "symphony",
        "piano",
        "violin",
        "opera",
      ],
      Latina: [
        "latin",
        "salsa",
        "bachata",
        "reggaeton",
        "spanish",
        "latino",
        "merengue",
      ],
      "R&B": ["r&b", "soul", "funk", "motown", "rhythm"],
      Indie: ["indie", "independent", "alternative", "underground"],
      Sertanejo: ["sertanejo", "caipira", "modao", "universitario", "raiz"],
      MPB: ["mpb", "bossa nova", "brazilian", "brasil", "caetano", "gilberto"],
      Forro: ["forro", "nordestino", "sanfona", "zabumba", "triangulo"],
      Samba: ["samba", "pagode", "carnaval", "escola de samba", "batucada"],
      Funk: ["funk", "baile funk", "mc", "beat", "pancadao"],
      Gospel: ["gospel", "religioso", "igreja", "louvor", "adoracao", "jesus"],
      Internacional: ["english", "international", "worldwide", "global"],
    };

    Object.entries(genreKeywords).forEach(([genre, keywords]) => {
      if (keywords.some((keyword) => text.includes(keyword))) {
        genres.push(genre);
      }
    });

    return genres;
  };

  // Função para obter cor do gênero
  const getGenreColor = (genreName: string): string => {
    const colors: Record<string, string> = {
      Rock: "#DC2626",
      Pop: "#EC4899",
      Jazz: "#F59E0B",
      Eletronica: "#10B981",
      "Hip Hop": "#8B5CF6",
      Reggae: "#059669",
      Country: "#D97706",
      Classica: "#7C3AED",
      Latina: "#EF4444",
      "R&B": "#F97316",
      Indie: "#6366F1",
      Sertanejo: "#84CC16",
      MPB: "#06B6D4",
      Forro: "#F59E0B",
      Samba: "#EF4444",
      Funk: "#9333EA",
      Gospel: "#0EA5E9",
      Internacional: "#64748B",
    };

    return colors[genreName] || "#6B7280";
  };

  const handleCreateGenre = async () => {
    try {
      console.log("🎵 Criando novo gênero:", formData);

      const url = buildApiUrl(`${API_CONFIG.ENDPOINTS.GENRES}`);
      const response = await fetch(url, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const data = await response.json();
        console.log("🎵 Gênero criado:", data);
        toast.success("Gênero criado com sucesso!");
        setShowCreateForm(false);
        resetForm();
        loadGenres();
      } else {
        const error = await response.json().catch(() => ({}));
        console.error("🎵 Erro ao criar gênero:", error);
        toast.error(error.message || "Erro ao criar gênero");
      }
    } catch (error) {
      console.error("🎵 Erro ao criar gênero:", error);
      toast.error("Erro ao criar gênero");
    }
  };

  const handleUpdateGenre = async () => {
    if (!editingGenre) return;

    try {
      console.log("🎵 Atualizando gênero:", editingGenre.id, formData);

      const url = buildApiUrl(
        `${API_CONFIG.ENDPOINTS.GENRES}/${editingGenre.id}`
      );
      const response = await fetch(url, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const data = await response.json();
        console.log("🎵 Gênero atualizado:", data);
        toast.success("Gênero atualizado com sucesso!");
        setEditingGenre(null);
        resetForm();
        loadGenres();
      } else {
        const error = await response.json().catch(() => ({}));
        console.error("🎵 Erro ao atualizar gênero:", error);
        toast.error(error.message || "Erro ao atualizar gênero");
      }
    } catch (error) {
      console.error("🎵 Erro ao atualizar gênero:", error);
      toast.error("Erro ao atualizar gênero");
    }
  };

  const handleDeleteGenre = async (genre: Genre) => {
    if (genre.isDefault) {
      toast.error("Não é possível deletar gêneros padrão do sistema");
      return;
    }

    if (
      !confirm(
        `Tem certeza que deseja deletar o gênero "${genre.displayName}"?`
      )
    ) {
      return;
    }

    try {
      console.log("🎵 Deletando gênero:", genre.id);

      const url = buildApiUrl(`${API_CONFIG.ENDPOINTS.GENRES}/${genre.id}`);
      const response = await fetch(url, {
        method: "DELETE",
      });

      if (response.ok) {
        console.log("🎵 Gênero deletado com sucesso");
        toast.success("Gênero deletado com sucesso!");
        loadGenres();
      } else {
        const error = await response
          .json()
          .catch(() => ({ message: "Erro desconhecido" }));
        console.error("🎵 Erro ao deletar gênero:", error);

        // Tratar diferentes tipos de erro
        if (response.status === 400) {
          toast.error(error.message || "Não é possível deletar este gênero");
        } else if (response.status === 404) {
          toast.error("Gênero não encontrado");
        } else {
          toast.error(error.message || "Erro ao deletar gênero");
        }
      }
    } catch (error) {
      console.error("🎵 Erro ao deletar gênero:", error);
      toast.error("Erro ao deletar gênero");
    }
  };

  const startEdit = (genre: Genre) => {
    setEditingGenre(genre);
    setFormData({
      name: genre.name,
      displayName: genre.displayName,
      description: genre.description || "",
  category: (genre.category as any),
      color: genre.color,
      icon: genre.icon || "",
      priority: genre.priority,
    });
    setShowCreateForm(true);
  };

  const resetForm = () => {
    setFormData({
      name: "",
      displayName: "",
      description: "",
      category: "music",
      color: "#3B82F6",
      icon: "",
      priority: 0,
    });
    setEditingGenre(null);
  };

  const filteredGenres = Object.entries(genres).reduce(
    (acc, [category, categoryGenres]) => {
      if (selectedCategory !== "all" && category !== selectedCategory) {
        return acc;
      }

      // Garantir que categoryGenres seja um array
      const genresArray = Array.isArray(categoryGenres) ? categoryGenres : [];

      const filtered = genresArray.filter(
        (genre) =>
          genre.displayName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          genre.name.toLowerCase().includes(searchQuery.toLowerCase())
      );

      if (filtered.length > 0) {
        acc[category] = filtered;
      }

      return acc;
    },
    {} as Record<string, Genre[]>
  );

  const totalGenres = Object.values(genres).flat().length;
  const activeGenres = Object.values(genres)
    .flat()
    .filter((g) => g.isActive).length;

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="w-6 h-6 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Carregando gêneros...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
            <Tag className="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Gerenciamento de Gêneros
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {totalGenres} gêneros • {activeGenres} ativos
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={seedDefaultGenres}
            className="flex items-center space-x-2 px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Gêneros Padrão</span>
          </button>

          <button
            onClick={() => setShowCreateForm(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            <Plus className="w-4 h-4" />
            <span>Novo Gênero</span>
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Buscar gêneros..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>
        </div>

        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
        >
          {Object.entries(categories).map(([key, label]) => (
            <option key={key} value={key}>
              {label}
            </option>
          ))}
        </select>
      </div>

      {/* Genres Grid */}
      <div className="space-y-6">
        {Object.entries(filteredGenres).map(([category, categoryGenres]) => (
          <div
            key={category}
            className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700"
          >
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center space-x-2">
              <span>{categories[category as keyof typeof categories]}</span>
              <span className="text-sm text-gray-500 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full">
                {categoryGenres.length}
              </span>
            </h3>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {categoryGenres.map((genre) => (
                <motion.div
                  key={genre.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`p-4 rounded-lg border-2 transition-all cursor-pointer ${
                    selectedGenres.includes(genre.id)
                      ? "border-purple-500 bg-purple-50 dark:bg-purple-900/20"
                      : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                  }`}
                  onClick={() => mode === "select" && onGenreSelect?.(genre)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div
                      className="w-8 h-8 rounded-lg flex items-center justify-center text-white text-sm font-medium"
                      style={{ backgroundColor: genre.color }}
                    >
                      {genre.icon ? (
                        <Music className="w-4 h-4" />
                      ) : (
                        genre.displayName.charAt(0).toUpperCase()
                      )}
                    </div>

                    {mode === "manage" && (
                      <div className="flex items-center space-x-1">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            startEdit(genre);
                          }}
                          className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                        >
                          <Edit3 className="w-3 h-3" />
                        </button>

                        {!genre.isDefault && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteGenre(genre);
                            }}
                            className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                          >
                            <Trash2 className="w-3 h-3" />
                          </button>
                        )}
                      </div>
                    )}
                  </div>

                  <h4 className="font-medium text-gray-900 dark:text-white text-sm mb-1">
                    {genre.displayName}
                  </h4>

                  {genre.description && (
                    <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                      {genre.description}
                    </p>
                  )}

                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span className="flex items-center space-x-1">
                      <BarChart3 className="w-3 h-3" />
                      <span>{genre.usageCount}</span>
                    </span>

                    <div className="flex items-center space-x-1">
                      {genre.isDefault && (
                        <CheckCircle className="w-3 h-3 text-green-500" />
                      )}
                      {!genre.isActive && (
                        <AlertCircle className="w-3 h-3 text-red-500" />
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {Object.keys(filteredGenres).length === 0 && (
        <div className="text-center py-12">
          <Tag className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Nenhum gênero encontrado
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {searchQuery
              ? "Tente uma busca diferente"
              : "Crie seu primeiro gênero"}
          </p>
          {!searchQuery && (
            <button
              onClick={() => setShowCreateForm(true)}
              className="inline-flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              <Plus className="w-4 h-4" />
              <span>Criar Gênero</span>
            </button>
          )}
        </div>
      )}

      {/* Create/Edit Form Modal */}
      <AnimatePresence>
        {showCreateForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
            onClick={() => {
              setShowCreateForm(false);
              resetForm();
            }}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {editingGenre ? "Editar Gênero" : "Novo Gênero"}
                </h3>
                <button
                  onClick={() => {
                    setShowCreateForm(false);
                    resetForm();
                  }}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Nome (ID)
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) =>
                      setFormData({ ...formData, name: e.target.value })
                    }
                    disabled={!!editingGenre}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent disabled:opacity-50"
                    placeholder="rock, pop, sertanejo..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Nome de Exibição
                  </label>
                  <input
                    type="text"
                    value={formData.displayName}
                    onChange={(e) =>
                      setFormData({ ...formData, displayName: e.target.value })
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    placeholder="Rock, Pop, Sertanejo..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Descrição
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) =>
                      setFormData({ ...formData, description: e.target.value })
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    rows={2}
                    placeholder="Descrição do gênero..."
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Categoria
                    </label>
                    <select
                      value={formData.category}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          category: e.target.value as any,
                        })
                      }
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    >
                      <option value="music">Gênero Musical</option>
                      <option value="mood">Humor</option>
                      <option value="energy">Energia</option>
                      <option value="time">Horário</option>
                      <option value="custom">Personalizado</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Cor
                    </label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="color"
                        value={formData.color}
                        onChange={(e) =>
                          setFormData({ ...formData, color: e.target.value })
                        }
                        className="w-10 h-10 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer"
                      />
                      <input
                        type="text"
                        value={formData.color}
                        onChange={(e) =>
                          setFormData({ ...formData, color: e.target.value })
                        }
                        className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        placeholder="#3B82F6"
                      />
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Ícone
                    </label>
                    <select
                      value={formData.icon}
                      onChange={(e) =>
                        setFormData({ ...formData, icon: e.target.value })
                      }
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    >
                      <option value="">Sem ícone</option>
                      {iconOptions.map((icon) => (
                        <option key={icon} value={icon}>
                          {icon}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Prioridade
                    </label>
                    <input
                      type="number"
                      value={formData.priority}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          priority: parseInt(e.target.value) || 0,
                        })
                      }
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      min="0"
                    />
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowCreateForm(false);
                    resetForm();
                  }}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  Cancelar
                </button>
                <button
                  onClick={editingGenre ? handleUpdateGenre : handleCreateGenre}
                  className="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  <Save className="w-4 h-4" />
                  <span>{editingGenre ? "Atualizar" : "Criar"}</span>
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default GenreManager;
