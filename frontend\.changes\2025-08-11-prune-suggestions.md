Remoções e ajustes relacionados ao fluxo legado de sugestões:

- Removido `src/components/admin/SuggestionsManagement.tsx` (painel de moderação legado, não usado no SaaS atual).
- `src/pages/RestaurantPage.tsx`: removidos a aba "Sugestões" e o uso de `SuggestionsList`; listeners de `new-suggestion` e `vote-update` também foram retirados. Página ficou focada em Buscar + Fila.
- Sem alterações de API neste commit; endpoints continuam disponíveis para compat, mas UI não os consome nesta página.

Motivação: simplificar o escopo para PlayQueue + Votação e reduzir erros de tipagem/uso de módulos não necessários no MVP.

Próximos passos sugeridos:
- Remover referências restantes a sugestões nos componentes do cliente (já excluídos do tsconfig) e em dashboards/admin que não compõem o MVP.
- Consolidar tipos e eventos de fila e votos e revalidar o build com tsc.
