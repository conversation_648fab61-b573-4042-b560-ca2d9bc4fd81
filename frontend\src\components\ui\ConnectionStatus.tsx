import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { WifiOff, AlertCircle, CheckCircle } from "lucide-react";
import { ConnectionStatus as ConnectionStatusType } from "@/types";

interface ConnectionStatusProps {
  isOnline: boolean;
  connectionStatus: ConnectionStatusType;
}

const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  isOnline,
  connectionStatus,
}) => {
  const [showStatus, setShowStatus] = useState(false);
  const [lastStatus, setLastStatus] =
    useState<ConnectionStatusType>(connectionStatus);

  useEffect(() => {
    // Mostrar status quando mudar
    if (connectionStatus !== lastStatus) {
      setLastStatus(connectionStatus);

      // Só mostrar se não estiver conectado
      if (connectionStatus !== "connected") {
        setShowStatus(true);
      } else {
        // Se conectou, esconder imediatamente
        setShowStatus(false);
      }
    }

    // Sempre mostrar se não estiver online ou conectado
    if (!isOnline || connectionStatus !== "connected") {
      setShowStatus(true);
    } else {
      // Se estiver online e conectado, esconder
      setShowStatus(false);
    }
  }, [connectionStatus, lastStatus, isOnline]);

  const getStatusConfig = () => {
    if (!isOnline) {
      return {
        icon: WifiOff,
        text: "Sem conexão com a internet",
        bgColor: "bg-red-500",
        textColor: "text-white",
      };
    }

    switch (connectionStatus) {
      case "connected":
        return {
          icon: CheckCircle,
          text: "Conectado",
          bgColor: "bg-green-500",
          textColor: "text-white",
        };
      case "connecting":
        return {
          icon: AlertCircle,
          text: "Conectando...",
          bgColor: "bg-yellow-500",
          textColor: "text-white",
        };
      case "disconnected":
        return {
          icon: WifiOff,
          text: "Desconectado do servidor",
          bgColor: "bg-red-500",
          textColor: "text-white",
        };
      case "error":
        return {
          icon: AlertCircle,
          text: "Erro de conexão",
          bgColor: "bg-red-500",
          textColor: "text-white",
        };
      default:
        return {
          icon: AlertCircle,
          text: "Status desconhecido",
          bgColor: "bg-gray-500",
          textColor: "text-white",
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  return (
    <AnimatePresence>
      {showStatus && (
        <motion.div
          initial={{ y: -100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -100, opacity: 0 }}
          transition={{ type: "spring", stiffness: 500, damping: 30 }}
          className={`fixed top-0 left-0 right-0 z-50 ${config.bgColor} ${config.textColor} shadow-lg`}
        >
          <div className="max-w-4xl mx-auto px-4 py-2">
            <div className="flex items-center justify-center space-x-2">
              <Icon className="w-4 h-4" />
              <span className="text-sm font-medium">{config.text}</span>

              {connectionStatus === "connecting" && (
                <div className="flex space-x-1">
                  {[0, 1, 2].map((i) => (
                    <motion.div
                      key={i}
                      animate={{
                        scale: [1, 1.2, 1],
                        opacity: [0.5, 1, 0.5],
                      }}
                      transition={{
                        duration: 1,
                        repeat: Infinity,
                        delay: i * 0.2,
                      }}
                      className="w-1 h-1 bg-white rounded-full"
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ConnectionStatus;
