-- Idempotent fix: ensure users.restaurant_id and FK to restaurants.id exist
BEGIN;

-- Columns
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS "restaurant_id" varchar;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS "phone" varchar;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS "avatar" varchar;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS "lastLoginIp" inet;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS "emailVerificationToken" varchar;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS "passwordResetToken" varchar;
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS "passwordResetExpiresAt" timestamp;

COMMIT;

-- Add FK only if missing
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints tc
    WHERE tc.table_schema='public' AND tc.table_name='users'
      AND tc.constraint_type='FOREIGN KEY'
      AND tc.constraint_name='users_restaurant_id_fkey'
  ) THEN
    ALTER TABLE public.users
      ADD CONSTRAINT users_restaurant_id_fkey
      FOREIGN KEY ("restaurant_id") REFERENCES public.restaurants(id)
      ON DELETE SET NULL;
  END IF;
END
$$;

-- Add index only if missing
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_class c
    JOIN pg_namespace n ON n.oid = c.relnamespace
    WHERE c.relkind='i' AND n.nspname='public' AND c.relname='idx_users_restaurant_id'
  ) THEN
    CREATE INDEX idx_users_restaurant_id ON public.users ("restaurant_id");
  END IF;
END
$$;
