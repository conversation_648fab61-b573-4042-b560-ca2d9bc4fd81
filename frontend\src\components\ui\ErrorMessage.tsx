import React from 'react';
import { motion } from 'framer-motion';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';
import Button from './Button';

interface ErrorMessageProps {
  title?: string;
  message?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  showHomeButton?: boolean;
  className?: string;
}

const ErrorMessage: React.FC<ErrorMessageProps> = ({
  title = 'Algo deu errado',
  message = 'Ocorreu um erro inesperado. Tente novamente.',
  action,
  showHomeButton = true,
  className = '',
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`text-center space-y-4 ${className}`}
    >
      {/* Ícone de erro */}
      <div className="w-16 h-16 mx-auto bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
        <AlertTriangle className="w-8 h-8 text-red-600 dark:text-red-400" />
      </div>

      {/* Título */}
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
        {title}
      </h2>

      {/* Mensagem */}
      <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
        {message}
      </p>

      {/* Ações */}
      <div className="flex flex-col sm:flex-row gap-3 justify-center">
        {action && (
          <Button
            onClick={action.onClick}
            variant="primary"
            icon={<RefreshCw className="w-4 h-4" />}
          >
            {action.label}
          </Button>
        )}
        
        {showHomeButton && (
          <Button
            onClick={() => window.location.href = '/'}
            variant="outline"
            icon={<Home className="w-4 h-4" />}
          >
            Voltar ao início
          </Button>
        )}
      </div>
    </motion.div>
  );
};

export default ErrorMessage;
