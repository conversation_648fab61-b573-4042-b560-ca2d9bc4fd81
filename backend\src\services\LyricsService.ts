import axios from "axios";
import { AppDataSource } from "../config/database";
import { Lyrics } from "../models/Lyrics";
import { redisClient } from "../config/redis";

export interface LyricsLine {
  time: number; // Tempo em segundos
  text: string;
  duration?: number;
}

export interface LyricsData {
  id: string;
  title: string;
  artist: string;
  album?: string;
  duration: number;
  language: string;
  lines: LyricsLine[];
  source: "musixmatch" | "manual" | "genius" | "azlyrics" | "liriks";
  copyright?: string;
  isExplicit: boolean;
  hasTimestamps: boolean;
}

export interface MusixmatchResponse {
  message: {
    header: {
      status_code: number;
      execute_time: number;
    };
    body: {
      lyrics?: {
        lyrics_id: number;
        lyrics_body: string;
        lyrics_language: string;
        lyrics_copyright: string;
      };
      subtitle?: {
        subtitle_id: number;
        subtitle_body: string;
        subtitle_language: string;
      };
    };
  };
}

class LyricsService {
  private lyricsRepository = AppDataSource.getRepository(Lyrics);
  private musixmatchApiKey =
    process.env.MUSIXMATCH_API_KEY || "YOUR_MUSIXMATCH_API_KEY";
  private baseURL = "https://api.musixmatch.com/ws/1.1";

  // Buscar letras por título e artista
  async getLyrics(
    title: string,
    artist: string,
    youtubeVideoId?: string
  ): Promise<LyricsData | null> {
    try {
      // Verificar cache primeiro
      const cacheKey = `lyrics:${title}:${artist}`
        .toLowerCase()
        .replace(/[^a-z0-9:]/g, "");
      const cached = await redisClient.getClient().get(cacheKey);

      if (cached) {
        return JSON.parse(cached);
      }

      // Verificar banco de dados
      const existingLyrics = await this.lyricsRepository.findOne({
        where: {
          title: title.toLowerCase(),
          artist: artist.toLowerCase(),
        },
      });

      if (existingLyrics) {
        const lyricsData = this.formatLyricsData(existingLyrics);
        await redisClient
          .getClient()
          .setEx(cacheKey, 3600, JSON.stringify(lyricsData)); // Cache por 1 hora
        return lyricsData;
      }

      // Buscar na API do Musixmatch
      const musixmatchData = await this.fetchFromMusixmatch(title, artist);

      if (musixmatchData) {
        // Salvar no banco
        await this.saveLyrics(musixmatchData, youtubeVideoId);

        // Cache
        await redisClient.setex(cacheKey, 3600, JSON.stringify(musixmatchData));

        return musixmatchData;
      }

      // Fallback: tentar Liriks
      const liriksData = await this.fetchFromLiriks(title, artist);

      if (liriksData) {
        // Salvar no banco
        await this.saveLyrics(liriksData, youtubeVideoId);

        // Cache por menos tempo (Liriks pode ter qualidade variável)
        await redisClient.setex(cacheKey, 1800, JSON.stringify(liriksData));

        return liriksData;
      }

      // Se não encontrou, tentar buscar letras básicas (mock para desenvolvimento)
      const basicLyrics = await this.fetchBasicLyrics(title, artist);

      if (basicLyrics) {
        await redisClient.setex(cacheKey, 900, JSON.stringify(basicLyrics)); // Cache por 15 min
        return basicLyrics;
      }

      return null;
    } catch (error) {
      console.error("Erro ao buscar letras:", error);
      return null;
    }
  }

  // Buscar letras sincronizadas do Musixmatch
  private async fetchFromMusixmatch(
    title: string,
    artist: string
  ): Promise<LyricsData | null> {
    try {
      // Primeiro, buscar a música
      const searchResponse = await axios.get(`${this.baseURL}/track.search`, {
        params: {
          apikey: this.musixmatchApiKey,
          q_track: title,
          q_artist: artist,
          page_size: 1,
          s_track_rating: "desc",
        },
      });

      const tracks = searchResponse.data?.message?.body?.track_list;
      if (!tracks || tracks.length === 0) {
        return null;
      }

      const track = tracks[0].track;
      const trackId = track.track_id;

      // Buscar letras sincronizadas (subtitle)
      const subtitleResponse = await axios.get(
        `${this.baseURL}/track.subtitle.get`,
        {
          params: {
            apikey: this.musixmatchApiKey,
            track_id: trackId,
          },
        }
      );

      const subtitle = subtitleResponse.data?.message?.body?.subtitle;

      if (subtitle && subtitle.subtitle_body) {
        // Processar letras sincronizadas
        const lines = this.parseSubtitleBody(subtitle.subtitle_body);

        return {
          id: `musixmatch_${trackId}`,
          title: track.track_name,
          artist: track.artist_name,
          album: track.album_name,
          duration: track.track_length,
          language: subtitle.subtitle_language || "pt",
          lines,
          source: "musixmatch",
          copyright: track.lyrics_copyright,
          isExplicit: track.explicit === 1,
          hasTimestamps: true,
        };
      }

      // Se não tem letras sincronizadas, buscar letras normais
      const lyricsResponse = await axios.get(
        `${this.baseURL}/track.lyrics.get`,
        {
          params: {
            apikey: this.musixmatchApiKey,
            track_id: trackId,
          },
        }
      );

      const lyrics = lyricsResponse.data?.message?.body?.lyrics;

      if (lyrics && lyrics.lyrics_body) {
        const lines = this.parseLyricsBody(
          lyrics.lyrics_body,
          track.track_length
        );

        return {
          id: `musixmatch_${trackId}`,
          title: track.track_name,
          artist: track.artist_name,
          album: track.album_name,
          duration: track.track_length,
          language: lyrics.lyrics_language || "pt",
          lines,
          source: "musixmatch",
          copyright: lyrics.lyrics_copyright,
          isExplicit: track.explicit === 1,
          hasTimestamps: false,
        };
      }

      return null;
    } catch (error) {
      console.error("Erro ao buscar no Musixmatch:", error);
      return null;
    }
  }

  // Buscar letras da API Liriks
  private async fetchFromLiriks(
    title: string,
    artist: string
  ): Promise<LyricsData | null> {
    try {
      // API Liriks - implementação básica
      const liriksApiKey = process.env.LIRIKS_API_KEY;
      if (!liriksApiKey) {
        console.warn("LIRIKS_API_KEY não configurada");
        return null;
      }

      const searchUrl = `https://api.liriks.com/v1/search`;
      const response = await axios.get(searchUrl, {
        params: {
          q: `${title} ${artist}`,
          api_key: liriksApiKey,
          format: 'json',
          limit: 1
        },
        timeout: 5000
      });

      if (response.data && response.data.results && response.data.results.length > 0) {
        const track = response.data.results[0];

        // Buscar letras detalhadas
        const lyricsUrl = `https://api.liriks.com/v1/lyrics/${track.id}`;
        const lyricsResponse = await axios.get(lyricsUrl, {
          params: {
            api_key: liriksApiKey,
            format: 'json',
            sync: true // Tentar obter letras sincronizadas
          },
          timeout: 5000
        });

        if (lyricsResponse.data && lyricsResponse.data.lyrics) {
          const lyricsData = lyricsResponse.data.lyrics;

          // Processar letras sincronizadas se disponíveis
          let lines: LyricsLine[] = [];

          if (lyricsData.synced && lyricsData.lines) {
            lines = lyricsData.lines.map((line: any) => ({
              time: line.time || 0,
              text: line.text || '',
              duration: line.duration || 5
            }));
          } else if (lyricsData.text) {
            // Letras não sincronizadas - criar timestamps estimados
            const textLines = lyricsData.text.split('\n').filter((line: string) => line.trim());
            const estimatedDuration = track.duration || 180;
            const timePerLine = estimatedDuration / textLines.length;

            lines = textLines.map((text: string, index: number) => ({
              time: index * timePerLine,
              text: text.trim(),
              duration: timePerLine
            }));
          }

          return {
            id: `liriks_${track.id}`,
            title: track.title || title,
            artist: track.artist || artist,
            album: track.album,
            duration: track.duration || 180,
            language: lyricsData.language || 'pt',
            lines,
            source: 'liriks',
            copyright: lyricsData.copyright,
            isExplicit: track.explicit || false,
            hasTimestamps: !!(lyricsData.synced && lyricsData.lines),
          };
        }
      }

      return null;
    } catch (error) {
      console.error("Erro ao buscar letras do Liriks:", error);
      return null;
    }
  }

  // Buscar letras básicas (fallback)
  private async fetchBasicLyrics(
    title: string,
    artist: string
  ): Promise<LyricsData | null> {
    try {
      // Tentar Liriks primeiro
      const liriksResult = await this.fetchFromLiriks(title, artist);
      if (liriksResult) {
        return liriksResult;
      }

      // Implementar busca em outras fontes (Genius, AZLyrics, etc.)
      // Por enquanto, retornar letras mock para desenvolvimento
      if (process.env.NODE_ENV === "development") {
        return this.createMockLyrics(title, artist);
      }

      return null;
    } catch (error) {
      console.error("Erro ao buscar letras básicas:", error);
      return null;
    }
  }

  // Processar letras sincronizadas do Musixmatch
  private parseSubtitleBody(subtitleBody: string): LyricsLine[] {
    try {
      const subtitleData = JSON.parse(subtitleBody);
      const lines: LyricsLine[] = [];

      for (const item of subtitleData) {
        if (item.text && item.time) {
          lines.push({
            time: item.time.total,
            text: item.text.trim(),
            duration: item.time.minutes * 60 + item.time.seconds,
          });
        }
      }

      return lines.sort((a, b) => a.time - b.time);
    } catch (error) {
      console.error("Erro ao processar letras sincronizadas:", error);
      return [];
    }
  }

  // Processar letras normais (sem sincronização)
  private parseLyricsBody(lyricsBody: string, duration: number): LyricsLine[] {
    const lines = lyricsBody
      .split("\n")
      .filter((line) => line.trim() && !line.includes("****"))
      .map((line) => line.trim());

    // Distribuir linhas ao longo da duração da música
    const timePerLine = duration / lines.length;

    return lines.map((text, index) => ({
      time: index * timePerLine,
      text,
      duration: timePerLine,
    }));
  }

  // Criar letras mock para desenvolvimento
  private createMockLyrics(title: string, artist: string): LyricsData {
    const mockLines: LyricsLine[] = [
      { time: 0, text: "🎵 Música instrumental", duration: 10 },
      { time: 10, text: `♪ ${title} ♪`, duration: 5 },
      { time: 15, text: `Por ${artist}`, duration: 5 },
      { time: 20, text: "🎤 Cante junto!", duration: 10 },
      { time: 30, text: "La la la la la", duration: 10 },
      { time: 40, text: "Na na na na na", duration: 10 },
      { time: 50, text: "🎵 Música continua...", duration: 20 },
      { time: 70, text: "Refrão chegando!", duration: 10 },
      { time: 80, text: "Todos juntos agora!", duration: 15 },
      { time: 95, text: "🎶 Final da música", duration: 10 },
    ];

    return {
      id: `mock_${Date.now()}`,
      title,
      artist,
      duration: 105,
      language: "pt",
      lines: mockLines,
      source: "manual",
      isExplicit: false,
      hasTimestamps: true,
    };
  }

  // Salvar letras no banco
  private async saveLyrics(
    lyricsData: LyricsData,
    youtubeVideoId?: string
  ): Promise<void> {
    try {
      const lyrics = this.lyricsRepository.create({
        id: lyricsData.id,
        title: lyricsData.title.toLowerCase(),
        artist: lyricsData.artist.toLowerCase(),
        album: lyricsData.album,
        duration: lyricsData.duration,
        language: lyricsData.language,
        lines: lyricsData.lines,
        source: lyricsData.source,
        copyright: lyricsData.copyright,
        isExplicit: lyricsData.isExplicit,
        hasTimestamps: lyricsData.hasTimestamps,
        youtubeVideoId,
      });

      await this.lyricsRepository.save(lyrics);
    } catch (error) {
      console.error("Erro ao salvar letras:", error);
    }
  }

  // Formatar dados de letras
  private formatLyricsData(lyrics: Lyrics): LyricsData {
    return {
      id: lyrics.id,
      title: lyrics.title,
      artist: lyrics.artist,
      album: lyrics.album,
      duration: lyrics.duration,
      language: lyrics.language,
      lines: lyrics.lines,
      source: lyrics.source,
      copyright: lyrics.copyright,
      isExplicit: lyrics.isExplicit,
      hasTimestamps: lyrics.hasTimestamps,
    };
  }

  // Buscar letras por ID do YouTube
  async getLyricsByYouTubeId(
    youtubeVideoId: string
  ): Promise<LyricsData | null> {
    try {
      const lyrics = await this.lyricsRepository.findOne({
        where: { youtubeVideoId },
      });

      if (lyrics) {
        return this.formatLyricsData(lyrics);
      }

      return null;
    } catch (error) {
      console.error("Erro ao buscar letras por YouTube ID:", error);
      return null;
    }
  }

  // Adicionar letras manualmente
  async addManualLyrics(data: {
    title: string;
    artist: string;
    lines: LyricsLine[];
    youtubeVideoId?: string;
    album?: string;
    language?: string;
  }): Promise<LyricsData> {
    const lyricsData: LyricsData = {
      id: `manual_${Date.now()}`,
      title: data.title,
      artist: data.artist,
      album: data.album,
      duration: data.lines[data.lines.length - 1]?.time + 10 || 180,
      language: data.language || "pt",
      lines: data.lines,
      source: "manual",
      isExplicit: false,
      hasTimestamps: true,
    };

    await this.saveLyrics(lyricsData, data.youtubeVideoId);

    return lyricsData;
  }

  // Obter linha atual baseada no tempo
  getCurrentLine(
    lyrics: LyricsData,
    currentTime: number
  ): { current: LyricsLine | null; next: LyricsLine | null; index: number } {
    const lines = lyrics.lines;
    let currentIndex = -1;

    for (let i = 0; i < lines.length; i++) {
      if (currentTime >= lines[i].time) {
        currentIndex = i;
      } else {
        break;
      }
    }

    return {
      current: currentIndex >= 0 ? lines[currentIndex] : null,
      next: currentIndex + 1 < lines.length ? lines[currentIndex + 1] : null,
      index: currentIndex,
    };
  }

  // Limpar cache de letras
  async clearLyricsCache(title?: string, artist?: string): Promise<void> {
    if (title && artist) {
      const cacheKey = `lyrics:${title}:${artist}`
        .toLowerCase()
        .replace(/[^a-z0-9:]/g, "");
      await redisClient.del(cacheKey);
    } else {
      // Limpar todo o cache de letras (implementar pattern matching)
      console.log("Limpando todo o cache de letras...");
    }
  }
}

export const lyricsService = new LyricsService();
export default LyricsService;
