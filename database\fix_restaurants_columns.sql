-- Idempotent fix: align restaurants table columns to match TypeORM entity
BEGIN;

-- Simple add-if-missing columns
ALTER TABLE public.restaurants ADD COLUMN IF NOT EXISTS logo varchar;
ALTER TABLE public.restaurants ADD COLUMN IF NOT EXISTS email varchar;
ALTER TABLE public.restaurants ADD COLUMN IF NOT EXISTS website varchar;
ALTER TABLE public.restaurants ADD COLUMN IF NOT EXISTS youtubeChannelId varchar;
ALTER TABLE public.restaurants ADD COLUMN IF NOT EXISTS youtubePremiumToken text;
ALTER TABLE public.restaurants ADD COLUMN IF NOT EXISTS youtubeCredentials json;
ALTER TABLE public.restaurants ADD COLUMN IF NOT EXISTS socialMedia json;
ALTER TABLE public.restaurants ADD COLUMN IF NOT EXISTS businessHours json;
ALTER TABLE public.restaurants ADD COLUMN IF NOT EXISTS trialExpiresAt timestamp;
ALTER TABLE public.restaurants ADD COLUMN IF NOT EXISTS lastActivityAt timestamp;

COMMIT;

-- Address: convert TEXT to JSON if needed
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_schema='public' AND table_name='restaurants' 
      AND column_name='address' AND data_type <> 'json'
  ) THEN
    ALTER TABLE public.restaurants 
      ALTER COLUMN address TYPE json 
      USING CASE 
        WHEN address IS NULL OR address = '' THEN '{}'::json 
        ELSE json_build_object('text', address)::json 
      END;
  END IF;
END
$$;
