import { Router } from "express";
import { body, param, validationResult } from "../utils/validation";
import { AppDataSource } from "../config/database";
import { PlaylistSchedule } from "../models/PlaylistSchedule";
import { Restaurant } from "../models/Restaurant";
import { Playlist } from "../models/Playlist";
import asyncHandler from "../middleware/asyncHandler";
import { optionalAuth } from "../middleware/auth";
import { ValidationError, NotFoundError } from "../utils/errors";

const router = Router();

/**
 * @swagger
 * /api/v1/playlist-schedules/{restaurantId}:
 *   get:
 *     summary: Obter agendamentos de playlist
 *     tags: [Playlist Schedules]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Lista de agendamentos
 */
router.get(
  "/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    const scheduleRepository = AppDataSource.getRepository(PlaylistSchedule);
    const schedules = await scheduleRepository.find({
      where: { restaurantId },
      order: { createdAt: "DESC" },
    });

    res.json({
      success: true,
      schedules,
    });
  })
);

/**
 * @swagger
 * /api/v1/playlist-schedules:
 *   post:
 *     summary: Criar agendamento de playlist
 *     tags: [Playlist Schedules]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - restaurantId
 *               - name
 *               - timeSlots
 *             properties:
 *               restaurantId:
 *                 type: string
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               timeSlots:
 *                 type: array
 *               isActive:
 *                 type: boolean
 *     responses:
 *       201:
 *         description: Agendamento criado com sucesso
 */
router.post(
  "/",
  [
    body("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    body("name").notEmpty().withMessage("Nome é obrigatório"),
    body("timeSlots").isArray().withMessage("Time slots devem ser um array"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Dados inválidos", errors.array());
    }

    const { restaurantId, name, description, timeSlots, isActive = true } = req.body;

    // Verificar se restaurante existe
    const restaurantRepository = AppDataSource.getRepository(Restaurant);
    const restaurant = await restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      throw new NotFoundError("Restaurante não encontrado");
    }

    // Criar agendamento
    const scheduleRepository = AppDataSource.getRepository(PlaylistSchedule);
    const schedule = scheduleRepository.create({
      restaurantId,
      name,
      description,
      timeSlots,
      isActive,
    });

    // Validar time slots
    const validation = schedule.validateTimeSlots();
    if (!validation.isValid) {
      throw new ValidationError(
        "Conflitos de horário encontrados: " + validation.conflicts.join(", ")
      );
    }

    const savedSchedule = await scheduleRepository.save(schedule);

    res.status(201).json({
      success: true,
      schedule: savedSchedule,
      message: "Agendamento criado com sucesso",
    });
  })
);

/**
 * @swagger
 * /api/v1/playlist-schedules/{id}:
 *   put:
 *     summary: Atualizar agendamento de playlist
 *     tags: [Playlist Schedules]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Agendamento atualizado com sucesso
 */
router.put(
  "/:id",
  [param("id").notEmpty().withMessage("ID é obrigatório")],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { id } = req.params;
    const updateData = req.body;

    const scheduleRepository = AppDataSource.getRepository(PlaylistSchedule);
    const schedule = await scheduleRepository.findOne({ where: { id } });

    if (!schedule) {
      throw new NotFoundError("Agendamento não encontrado");
    }

    // Atualizar dados
    Object.assign(schedule, updateData);

    // Validar time slots se foram alterados
    if (updateData.timeSlots) {
      const validation = schedule.validateTimeSlots();
      if (!validation.isValid) {
        throw new ValidationError(
          "Conflitos de horário encontrados: " + validation.conflicts.join(", ")
        );
      }
    }

    const updatedSchedule = await scheduleRepository.save(schedule);

    res.json({
      success: true,
      schedule: updatedSchedule,
      message: "Agendamento atualizado com sucesso",
    });
  })
);

/**
 * @swagger
 * /api/v1/playlist-schedules/{id}:
 *   delete:
 *     summary: Deletar agendamento de playlist
 *     tags: [Playlist Schedules]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Agendamento deletado com sucesso
 */
router.delete(
  "/:id",
  [param("id").notEmpty().withMessage("ID é obrigatório")],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { id } = req.params;

    const scheduleRepository = AppDataSource.getRepository(PlaylistSchedule);
    const schedule = await scheduleRepository.findOne({ where: { id } });

    if (!schedule) {
      throw new NotFoundError("Agendamento não encontrado");
    }

    await scheduleRepository.remove(schedule);

    res.json({
      success: true,
      message: "Agendamento deletado com sucesso",
    });
  })
);

/**
 * @swagger
 * /api/v1/playlist-schedules/{restaurantId}/current:
 *   get:
 *     summary: Obter playlist ativa no momento
 *     tags: [Playlist Schedules]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Playlist ativa atual
 */
router.get(
  "/:restaurantId/current",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { restaurantId } = req.params;

    const scheduleRepository = AppDataSource.getRepository(PlaylistSchedule);
    const schedules = await scheduleRepository.find({
      where: { restaurantId, isActive: true },
    });

    let currentPlaylist = null;
    let nextPlaylist = null;

    for (const schedule of schedules) {
      const current = schedule.getCurrentActivePlaylist();
      if (current) {
        currentPlaylist = {
          schedule: schedule.name,
          playlist: current,
        };
        break;
      }

      const next = schedule.getNextScheduledPlaylist();
      if (next && (!nextPlaylist || next.startsIn < nextPlaylist.startsIn)) {
        nextPlaylist = {
          schedule: schedule.name,
          playlist: next.slot,
          startsIn: next.startsIn,
        };
      }
    }

    res.json({
      success: true,
      current: currentPlaylist,
      next: nextPlaylist,
    });
  })
);

export default router;
