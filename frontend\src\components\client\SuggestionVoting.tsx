import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ThumbsUp, 
  ThumbsDown, 
  Music, 
  Users, 
  Clock, 
  Star,
  TrendingUp,
  Play
} from 'lucide-react';
import { toast } from 'react-hot-toast';

interface Suggestion {
  id: string;
  title: string;
  artist: string;
  thumbnailUrl: string;
  duration: number;
  formattedDuration: string;
  upvotes: number;
  downvotes: number;
  voteCount: number;
  isPaid: boolean;
  clientName?: string;
  tableNumber?: string;
  queuePosition: number;
  status: string;
  youtubeVideoId: string;
  createdAt: string;
}

interface SuggestionVotingProps {
  restaurantId: string;
  sessionId: string;
  onVote?: (suggestionId: string, voteType: 'up' | 'down') => void;
}

export const SuggestionVoting: React.FC<SuggestionVotingProps> = ({
  restaurantId,
  sessionId,
  onVote
}) => {
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [loading, setLoading] = useState(false);
  const [votingStates, setVotingStates] = useState<Record<string, boolean>>({});
  const [userVotes, setUserVotes] = useState<Record<string, 'up' | 'down'>>({});
  const [showAll, setShowAll] = useState(false);

  // Carregar sugestões da fila
  useEffect(() => {
    loadSuggestions();
    const interval = setInterval(loadSuggestions, 10000); // Atualizar a cada 10s
    return () => clearInterval(interval);
  }, [restaurantId]);

  const loadSuggestions = async () => {
    setLoading(true);
    try {
      const response = await fetch(`http://localhost:8001/api/v1/suggestions/${restaurantId}`);
      
      if (response.ok) {
        const data = await response.json();
        const approvedSuggestions = data.suggestions
          .filter((s: any) => s.status === 'approved')
          .sort((a: any, b: any) => a.queuePosition - b.queuePosition)
          .map((s: any) => ({
            ...s,
            thumbnailUrl: s.thumbnailUrl || `https://img.youtube.com/vi/${s.youtubeVideoId}/mqdefault.jpg`,
            formattedDuration: formatDuration(s.duration)
          }));
        
        setSuggestions(approvedSuggestions);
      }
    } catch (error) {
      console.error('Erro ao carregar sugestões:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleVote = async (suggestionId: string, voteType: 'up' | 'down') => {
    if (votingStates[suggestionId] || userVotes[suggestionId]) {
      return;
    }

    setVotingStates(prev => ({ ...prev, [suggestionId]: true }));

    try {
      const response = await fetch(`http://localhost:8001/api/v1/suggestions/${suggestionId}/vote`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Session-ID': sessionId,
        },
        body: JSON.stringify({ voteType })
      });

      if (response.ok) {
        const data = await response.json();
        
        // Atualizar sugestão local
        setSuggestions(prev => 
          prev.map(s => 
            s.id === suggestionId 
              ? {
                  ...s,
                  upvotes: data.suggestion.upvotes,
                  downvotes: data.suggestion.downvotes,
                  voteCount: data.suggestion.voteCount
                }
              : s
          )
        );

        setUserVotes(prev => ({ ...prev, [suggestionId]: voteType }));
        
        toast.success(
          `Voto ${voteType === 'up' ? 'positivo' : 'negativo'} registrado! +10 pontos!`,
          { icon: voteType === 'up' ? '👍' : '👎' }
        );

        // Chamar callback se fornecido
        onVote?.(suggestionId, voteType);
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || 'Erro ao votar');
      }
    } catch (error) {
      console.error('Erro ao votar:', error);
      toast.error('Erro ao votar');
    } finally {
      setVotingStates(prev => ({ ...prev, [suggestionId]: false }));
    }
  };

  const displayedSuggestions = showAll ? suggestions : suggestions.slice(0, 3);

  if (suggestions.length === 0) {
    return (
      <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
        <div className="text-center">
          <Music className="w-12 h-12 text-purple-400 mx-auto mb-3" />
          <h3 className="text-white font-semibold mb-2">Nenhuma música na fila</h3>
          <p className="text-purple-200 text-sm">
            Aguarde novas sugestões para votar!
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
            <TrendingUp className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-white font-bold text-lg">Vote nas Músicas</h3>
            <p className="text-purple-200 text-sm">
              {suggestions.length} música{suggestions.length !== 1 ? 's' : ''} na fila
            </p>
          </div>
        </div>
        
        {loading && (
          <div className="w-5 h-5 border-2 border-purple-400 border-t-transparent rounded-full animate-spin"></div>
        )}
      </div>

      <div className="space-y-4">
        <AnimatePresence>
          {displayedSuggestions.map((suggestion, index) => (
            <motion.div
              key={suggestion.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className={`p-4 rounded-lg border transition-all ${
                suggestion.queuePosition === 1
                  ? 'bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-green-400/50'
                  : 'bg-white/5 border-white/10 hover:bg-white/10'
              }`}
            >
              <div className="flex items-center space-x-4">
                {/* Posição na fila */}
                <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                  suggestion.queuePosition === 1
                    ? 'bg-green-500 text-white'
                    : 'bg-purple-500 text-white'
                }`}>
                  {suggestion.queuePosition === 1 ? <Play className="w-4 h-4" /> : suggestion.queuePosition}
                </div>

                {/* Thumbnail */}
                <img
                  src={suggestion.thumbnailUrl}
                  alt={suggestion.title}
                  className="w-12 h-12 rounded-lg object-cover"
                />

                {/* Informações da música */}
                <div className="flex-1 min-w-0">
                  <h4 className="text-white font-semibold text-sm truncate">
                    {suggestion.title}
                  </h4>
                  <p className="text-purple-200 text-xs truncate">
                    {suggestion.artist}
                  </p>
                  <div className="flex items-center space-x-3 mt-1">
                    {suggestion.tableNumber && (
                      <div className="flex items-center space-x-1 text-xs text-purple-300">
                        <Users className="w-3 h-3" />
                        <span>Mesa {suggestion.tableNumber}</span>
                      </div>
                    )}
                    <div className="flex items-center space-x-1 text-xs text-purple-300">
                      <Clock className="w-3 h-3" />
                      <span>{suggestion.formattedDuration}</span>
                    </div>
                    {suggestion.isPaid && (
                      <div className="px-2 py-1 bg-yellow-500 text-black text-xs rounded-full font-bold">
                        PAGO
                      </div>
                    )}
                  </div>
                </div>

                {/* Estatísticas de votos */}
                <div className="flex items-center space-x-3 text-sm">
                  <div className="flex items-center space-x-1 text-green-400">
                    <ThumbsUp className="w-4 h-4" />
                    <span>{suggestion.upvotes}</span>
                  </div>
                  <div className="flex items-center space-x-1 text-red-400">
                    <ThumbsDown className="w-4 h-4" />
                    <span>{suggestion.downvotes}</span>
                  </div>
                  <div className="flex items-center space-x-1 text-yellow-400">
                    <Star className="w-4 h-4" />
                    <span>{suggestion.voteCount}</span>
                  </div>
                </div>

                {/* Botões de votação */}
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleVote(suggestion.id, 'up')}
                    disabled={votingStates[suggestion.id] || userVotes[suggestion.id] === 'up'}
                    className={`p-2 rounded-lg transition-colors ${
                      userVotes[suggestion.id] === 'up'
                        ? 'bg-green-600 text-white'
                        : 'bg-white/20 hover:bg-green-600 hover:text-white text-white'
                    } disabled:opacity-50 disabled:cursor-not-allowed`}
                  >
                    <ThumbsUp className="w-4 h-4" />
                  </button>
                  
                  <button
                    onClick={() => handleVote(suggestion.id, 'down')}
                    disabled={votingStates[suggestion.id] || userVotes[suggestion.id] === 'down'}
                    className={`p-2 rounded-lg transition-colors ${
                      userVotes[suggestion.id] === 'down'
                        ? 'bg-red-600 text-white'
                        : 'bg-white/20 hover:bg-red-600 hover:text-white text-white'
                    } disabled:opacity-50 disabled:cursor-not-allowed`}
                  >
                    <ThumbsDown className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* Indicador de loading */}
              {votingStates[suggestion.id] && (
                <div className="flex items-center justify-center mt-3">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span className="ml-2 text-xs text-white">Enviando voto...</span>
                </div>
              )}
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Botão para mostrar mais */}
      {suggestions.length > 3 && (
        <div className="mt-4 text-center">
          <button
            onClick={() => setShowAll(!showAll)}
            className="px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors text-sm"
          >
            {showAll ? 'Mostrar menos' : `Ver todas (${suggestions.length})`}
          </button>
        </div>
      )}

      {/* Dica */}
      <div className="mt-4 p-3 bg-blue-500/20 border border-blue-400/30 rounded-lg">
        <p className="text-blue-200 text-xs text-center">
          💡 Vote nas músicas para influenciar a ordem da playlist! Cada voto vale +10 pontos.
        </p>
      </div>
    </div>
  );
};
