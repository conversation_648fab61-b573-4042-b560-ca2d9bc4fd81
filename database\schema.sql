-- Schema do Sistema de Playlist Interativa para Restaurantes
-- Versão: 1.0
-- Data: 2025-01-30

-- ==================== TABELAS PRINCIPAIS ====================

-- Tabela de Restaurantes
CREATE TABLE restaurants (
    id VARCHAR(255) PRIMARY KEY,
    name VA<PERSON>HAR(255) NOT NULL,
    description TEXT,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    address TEXT,
    logo_url VARCHAR(500),
    is_active BOOLEAN DEFAULT true,
    is_open BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Configurações JSON
    settings JSON,
    
    -- Social Media
    social_media JSON,
    
    -- Hor<PERSON>rios de funcionamento
    business_hours JSON
);

-- <PERSON><PERSON><PERSON> de Usuários/Clientes
CREATE TABLE users (
    id VARCHAR(255) PRIMARY KEY,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    restaurant_id VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    table_number VARCHAR(10),
    is_active BOOLEAN DEFAULT true,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Stats de gamificação
    points INT DEFAULT 0,
    level INT DEFAULT 1,
    badges JSON,
    suggestions_count INT DEFAULT 0,
    votes_count INT DEFAULT 0,
    streak INT DEFAULT 0
);

-- Tabela de Playlists
CREATE TABLE playlists (
    id VARCHAR(255) PRIMARY KEY,
    restaurant_id VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    youtube_url VARCHAR(500),
    youtube_playlist_id VARCHAR(255),
    tags JSON,
    is_active BOOLEAN DEFAULT true,
    video_count INT DEFAULT 0,
    total_duration INT DEFAULT 0,
    thumbnail_url VARCHAR(500),
    last_sync TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (restaurant_id) REFERENCES restaurants(id) ON DELETE CASCADE,
    INDEX idx_playlist_restaurant (restaurant_id),
    INDEX idx_playlist_active (is_active)
);

-- Tabela de Músicas
CREATE TABLE songs (
    id VARCHAR(255) PRIMARY KEY,
    youtube_video_id VARCHAR(255) UNIQUE NOT NULL,
    title VARCHAR(500) NOT NULL,
    artist VARCHAR(255),
    duration INT,
    thumbnail_url VARCHAR(500),
    channel_name VARCHAR(255),
    view_count BIGINT DEFAULT 0,
    published_at DATE,
    genre VARCHAR(100),
    language VARCHAR(10),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_song_youtube_id (youtube_video_id),
    INDEX idx_song_artist (artist),
    INDEX idx_song_genre (genre)
);

-- Tabela de Sugestões
CREATE TABLE suggestions (
    id VARCHAR(255) PRIMARY KEY,
    restaurant_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255),
    song_id VARCHAR(255) NOT NULL,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    upvotes INT DEFAULT 0,
    downvotes INT DEFAULT 0,
    score INT DEFAULT 0,
    priority INT DEFAULT 0,
    suggested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    moderated_at TIMESTAMP NULL,
    moderated_by VARCHAR(255),
    
    FOREIGN KEY (restaurant_id) REFERENCES restaurants(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (song_id) REFERENCES songs(id) ON DELETE CASCADE,
    INDEX idx_suggestion_restaurant (restaurant_id),
    INDEX idx_suggestion_status (status),
    INDEX idx_suggestion_score (score DESC),
    INDEX idx_suggestion_date (suggested_at DESC)
);

-- Tabela de Votos
CREATE TABLE votes (
    id VARCHAR(255) PRIMARY KEY,
    suggestion_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255),
    vote_type ENUM('up', 'down') NOT NULL,
    ip_address VARCHAR(45),
    voted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (suggestion_id) REFERENCES suggestions(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_user_suggestion_vote (user_id, suggestion_id),
    INDEX idx_vote_suggestion (suggestion_id),
    INDEX idx_vote_type (vote_type)
);

-- Tabela de Fila de Reprodução
CREATE TABLE playback_queue (
    id VARCHAR(255) PRIMARY KEY,
    restaurant_id VARCHAR(255) NOT NULL,
    suggestion_id VARCHAR(255) NOT NULL,
    position INT NOT NULL,
    is_playing BOOLEAN DEFAULT false,
    played_at TIMESTAMP NULL,
    added_to_queue_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (restaurant_id) REFERENCES restaurants(id) ON DELETE CASCADE,
    FOREIGN KEY (suggestion_id) REFERENCES suggestions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_restaurant_position (restaurant_id, position),
    INDEX idx_queue_restaurant (restaurant_id),
    INDEX idx_queue_position (position)
);

-- Tabela de Analytics
CREATE TABLE analytics_events (
    id VARCHAR(255) PRIMARY KEY,
    restaurant_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255),
    event_type ENUM('suggestion', 'vote', 'play', 'skip', 'search') NOT NULL,
    event_data JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (restaurant_id) REFERENCES restaurants(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_analytics_restaurant (restaurant_id),
    INDEX idx_analytics_type (event_type),
    INDEX idx_analytics_date (created_at DESC)
);

-- Tabela de Estatísticas Diárias
CREATE TABLE daily_stats (
    id VARCHAR(255) PRIMARY KEY,
    restaurant_id VARCHAR(255) NOT NULL,
    date DATE NOT NULL,
    suggestions_count INT DEFAULT 0,
    votes_count INT DEFAULT 0,
    plays_count INT DEFAULT 0,
    unique_users INT DEFAULT 0,
    total_session_time INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (restaurant_id) REFERENCES restaurants(id) ON DELETE CASCADE,
    UNIQUE KEY unique_restaurant_date (restaurant_id, date),
    INDEX idx_daily_stats_restaurant (restaurant_id),
    INDEX idx_daily_stats_date (date DESC)
);

-- ==================== DADOS INICIAIS ====================

-- Inserir restaurante demo
INSERT INTO restaurants (
    id, name, description, email, password_hash, is_active, is_open,
    settings, business_hours, social_media
) VALUES (
    'demo-restaurant',
    'Restaurante Demo',
    'Restaurante de demonstração do sistema de playlist interativa',
    '<EMAIL>',
    '$2b$10$hash_demo_password',
    true,
    true,
    JSON_OBJECT(
        'theme', 'dark',
        'primaryColor', '#8B5CF6',
        'allowSuggestions', true,
        'allowVoting', true,
        'maxSuggestionsPerUser', 5,
        'autoApprove', false
    ),
    JSON_OBJECT(
        'monday', JSON_OBJECT('open', '11:00', 'close', '23:00', 'isOpen', true),
        'tuesday', JSON_OBJECT('open', '11:00', 'close', '23:00', 'isOpen', true),
        'wednesday', JSON_OBJECT('open', '11:00', 'close', '23:00', 'isOpen', true),
        'thursday', JSON_OBJECT('open', '11:00', 'close', '23:00', 'isOpen', true),
        'friday', JSON_OBJECT('open', '11:00', 'close', '24:00', 'isOpen', true),
        'saturday', JSON_OBJECT('open', '11:00', 'close', '24:00', 'isOpen', true),
        'sunday', JSON_OBJECT('open', '11:00', 'close', '22:00', 'isOpen', true)
    ),
    JSON_OBJECT(
        'instagram', '@restaurante_demo',
        'facebook', 'RestauranteDemo'
    )
);

-- Inserir músicas demo
INSERT INTO songs (id, youtube_video_id, title, artist, duration, thumbnail_url, channel_name, genre) VALUES
('song-1', 'fJ9rUzIMcZQ', 'Bohemian Rhapsody', 'Queen', 355, 'https://img.youtube.com/vi/fJ9rUzIMcZQ/mqdefault.jpg', 'Queen Official', 'Rock'),
('song-2', 'BciS5krYL80', 'Hotel California', 'Eagles', 390, 'https://img.youtube.com/vi/BciS5krYL80/mqdefault.jpg', 'Eagles', 'Rock'),
('song-3', 'YkgkThdzX-8', 'Imagine', 'John Lennon', 187, 'https://img.youtube.com/vi/YkgkThdzX-8/mqdefault.jpg', 'John Lennon', 'Pop'),
('song-4', '1w7OgIMMRc4', 'Sweet Child O Mine', 'Guns N Roses', 303, 'https://img.youtube.com/vi/1w7OgIMMRc4/mqdefault.jpg', 'Guns N Roses', 'Rock'),
('song-5', 'QkF3oxziUI4', 'Stairway to Heaven', 'Led Zeppelin', 482, 'https://img.youtube.com/vi/QkF3oxziUI4/mqdefault.jpg', 'Led Zeppelin', 'Rock'),
('song-6', '1k8craCGpgs', 'Don\'t Stop Believin\'', 'Journey', 250, 'https://img.youtube.com/vi/1k8craCGpgs/mqdefault.jpg', 'Journey', 'Rock');

-- Playlists will be created when restaurants start creating content
