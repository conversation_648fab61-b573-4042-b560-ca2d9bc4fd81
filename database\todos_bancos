-- Audita a existência de tabelas, colunas-chave, enums e gatilhos essenciais
\pset tuples_only on
\pset format aligned

-- Tabelas esperadas
WITH expected(table_name) AS (
  VALUES
    ('restaurants'),('users'),('playlists'),('playlist_tracks'),('playlist_schedules'),
    ('suggestions'),('votes'),('client_sessions'),('play_history'),('analytics_daily'),
    ('moderation_rules'),('competitive_votes'),('payments'),('qr_codes'),('lyrics'),
    ('genres'),('rewards')
)
SELECT 'MISSING_TABLE' AS issue, e.table_name
FROM expected e
LEFT JOIN information_schema.tables t ON t.table_schema='public' AND t.table_name=e.table_name
WHERE t.table_name IS NULL
ORDER BY e.table_name;

-- Colunas-chave por tabela (parcial)
WITH expected_cols AS (
  SELECT * FROM (VALUES
    ('restaurants','id'),('restaurants','name'),('restaurants','isActive'),
    ('users','id'),('users','email'),('users','password'),('users','restaurant_id'),
    ('playlists','id'),('playlists','restaurant_id'),('playlist_tracks','playlist_id'),
    ('suggestions','id'),('suggestions','youtube_video_id'),('suggestions','status'),('suggestions','restaurant_id'),
    ('votes','id'),('votes','vote_type'),('votes','suggestion_id'),
    ('client_sessions','id'),('client_sessions','sessionToken'),('client_sessions','restaurant_id'),
    ('play_history','id'),('play_history','status'),('play_history','restaurant_id'),
    ('analytics_daily','id'),('analytics_daily','restaurant_id'),
    ('moderation_rules','id'),('moderation_rules','ruleType'),('moderation_rules','restaurant_id'),
    ('payments','id'),('payments','status'),('payments','suggestion_id')
  ) AS t(table_name,column_name)
)
SELECT 'MISSING_COLUMN' AS issue, ec.table_name, ec.column_name
FROM expected_cols ec
LEFT JOIN information_schema.columns c ON c.table_schema='public' AND c.table_name=ec.table_name AND c.column_name=ec.column_name
WHERE c.column_name IS NULL
ORDER BY ec.table_name, ec.column_name;

-- Enums esperados e valores
WITH expected_enums AS (
  SELECT * FROM (VALUES
    ('restaurant_status', ARRAY['active','inactive','suspended','trial']::text[]),
    ('user_role', ARRAY['super_admin','admin','moderator','staff']::text[]),
    ('playlist_type', ARRAY['custom','youtube','youtube_import','auto_generated','suggestions']::text[]),
    ('playlist_status', ARRAY['active','inactive','archived','deleted']::text[]),
    ('suggestion_status', ARRAY['pending','approved','rejected','playing','played','skipped','completed','expired']::text[]),
    ('suggestion_source', ARRAY['client','admin','auto','import']::text[]),
    ('vote_type', ARRAY['up','down']::text[]),
    ('play_status', ARRAY['playing','completed','skipped','interrupted','error']::text[]),
    ('rule_type', ARRAY['blacklist_word','blacklist_artist','blacklist_channel','genre_restriction','duration_limit','content_rating','language_filter','time_restriction']::text[]),
    ('rule_action', ARRAY['auto_reject','flag_for_review','require_approval','auto_approve']::text[])
  ) AS t(enum_name, values)
)
SELECT 'MISSING_ENUM' AS issue, e.enum_name
FROM expected_enums e
LEFT JOIN pg_type t ON t.typname=e.enum_name
WHERE t.oid IS NULL
UNION ALL
SELECT 'MISSING_ENUM_LABEL' AS issue, e.enum_name||':'||v.value
FROM expected_enums e
JOIN pg_type t ON t.typname=e.enum_name
JOIN pg_enum en ON en.enumtypid=t.oid
LEFT JOIN LATERAL unnest(e.values) v(value) ON TRUE
WHERE NOT EXISTS (
  SELECT 1 FROM pg_enum en2 WHERE en2.enumtypid=t.oid AND en2.enumlabel=v.value
)
ORDER BY 1,2;

-- Índices essenciais
WITH expected_idx AS (
  SELECT * FROM (VALUES
    ('users','idx_users_email'),('users','idx_users_role'),('users','idx_users_active'),('users','idx_users_restaurant_role'),
    ('playlists','idx_playlists_restaurant'),('playlists','idx_playlists_type'),('playlists','idx_playlists_status'),('playlists','idx_playlists_default'),
    ('suggestions','idx_suggestions_restaurant'),('suggestions','idx_suggestions_youtube'),('suggestions','idx_suggestions_status'),('suggestions','idx_suggestions_created'),
    ('votes','uq_vote_suggestion_client_session'),('votes','idx_votes_suggestion'),('votes','idx_votes_type')
  ) AS t(table_name,indexname)
)
SELECT 'MISSING_INDEX' AS issue, ei.table_name, ei.indexname
FROM expected_idx ei
LEFT JOIN pg_indexes pi ON pi.schemaname='public' AND pi.indexname=ei.indexname
WHERE pi.indexname IS NULL
ORDER BY ei.table_name, ei.indexname;

-- Gatilhos de manutenção esperados
SELECT 'MISSING_TRIGGER' AS issue, 'votes' AS table_name, 'trg_update_vote_counts' AS trigger
WHERE NOT EXISTS (
  SELECT 1 FROM information_schema.triggers WHERE event_object_table='votes' AND trigger_name='trg_update_vote_counts'
);

\pset tuples_only off
\echo '--- Fim da auditoria ---'
-- Função e trigger para manter contadores de votos em suggestions
-- Idempotente

DO $$ BEGIN
  CREATE OR REPLACE FUNCTION update_suggestion_vote_counts()
  RETURNS TRIGGER AS $$
  DECLARE
    tgt UUID;
  BEGIN
    -- Determina o suggestion alvo
    IF (TG_OP = 'INSERT') THEN
      tgt := NEW.suggestion_id;
    ELSIF (TG_OP = 'UPDATE') THEN
      -- Se mudou de suggestion, atualiza os dois lados
      IF NEW.suggestion_id IS DISTINCT FROM OLD.suggestion_id THEN
        IF OLD.suggestion_id IS NOT NULL THEN
          UPDATE suggestions s SET
            upvotes    = (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = OLD.suggestion_id AND v.vote_type = 'up'),
            downvotes  = (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = OLD.suggestion_id AND v.vote_type = 'down'),
            vote_count = (
              (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = OLD.suggestion_id AND v.vote_type = 'up') -
              (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = OLD.suggestion_id AND v.vote_type = 'down')
            ),
            updated_at = NOW()
          WHERE s.id = OLD.suggestion_id;
        END IF;
        tgt := NEW.suggestion_id;
      ELSE
        tgt := NEW.suggestion_id;
      END IF;
    ELSIF (TG_OP = 'DELETE') THEN
      tgt := OLD.suggestion_id;
    END IF;

    IF tgt IS NOT NULL THEN
      UPDATE suggestions s SET
        upvotes    = (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = tgt AND v.vote_type = 'up'),
        downvotes  = (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = tgt AND v.vote_type = 'down'),
        vote_count = (
          (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = tgt AND v.vote_type = 'up') -
          (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = tgt AND v.vote_type = 'down')
        ),
        updated_at = NOW()
      WHERE s.id = tgt;
    END IF;

    RETURN NULL;
  END;
  $$ LANGUAGE plpgsql;
END $$;

-- Drop/recreate trigger de forma segura
DO $$ BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.triggers
    WHERE event_object_table = 'votes' AND trigger_name = 'trg_update_vote_counts'
  ) THEN
    DROP TRIGGER trg_update_vote_counts ON votes;
  END IF;
END $$;

CREATE TRIGGER trg_update_vote_counts
AFTER INSERT OR UPDATE OR DELETE ON votes
FOR EACH ROW EXECUTE FUNCTION update_suggestion_vote_counts();

-- Backfill inicial (idempotente)
UPDATE suggestions s SET
  upvotes    = COALESCE(uv.ups, 0),
  downvotes  = COALESCE(dv.downs, 0),
  vote_count = COALESCE(uv.ups, 0) - COALESCE(dv.downs, 0),
  updated_at = NOW()
FROM (
  SELECT suggestion_id, COUNT(*) AS ups
  FROM votes WHERE vote_type = 'up' AND suggestion_id IS NOT NULL
  GROUP BY suggestion_id
) uv
FULL JOIN (
  SELECT suggestion_id, COUNT(*) AS downs
  FROM votes WHERE vote_type = 'down' AND suggestion_id IS NOT NULL
  GROUP BY suggestion_id
) dv ON dv.suggestion_id = uv.suggestion_id
WHERE s.id = COALESCE(uv.suggestion_id, dv.suggestion_id);
-- Função e trigger para manter contadores de votos em suggestions
-- Idempotente

DO $$ BEGIN
  CREATE OR REPLACE FUNCTION update_suggestion_vote_counts()
  RETURNS TRIGGER AS $$
  DECLARE
    tgt UUID;
  BEGIN
    -- Determina o suggestion alvo
    IF (TG_OP = 'INSERT') THEN
      tgt := NEW.suggestion_id;
    ELSIF (TG_OP = 'UPDATE') THEN
      -- Se mudou de suggestion, atualiza os dois lados
      IF NEW.suggestion_id IS DISTINCT FROM OLD.suggestion_id THEN
        IF OLD.suggestion_id IS NOT NULL THEN
          UPDATE suggestions s SET
            upvotes    = (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = OLD.suggestion_id AND v.vote_type = 'up'),
            downvotes  = (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = OLD.suggestion_id AND v.vote_type = 'down'),
            vote_count = (
              (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = OLD.suggestion_id AND v.vote_type = 'up') -
              (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = OLD.suggestion_id AND v.vote_type = 'down')
            ),
            updated_at = NOW()
          WHERE s.id = OLD.suggestion_id;
        END IF;
        tgt := NEW.suggestion_id;
      ELSE
        tgt := NEW.suggestion_id;
      END IF;
    ELSIF (TG_OP = 'DELETE') THEN
      tgt := OLD.suggestion_id;
    END IF;

    IF tgt IS NOT NULL THEN
      UPDATE suggestions s SET
        upvotes    = (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = tgt AND v.vote_type = 'up'),
        downvotes  = (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = tgt AND v.vote_type = 'down'),
        vote_count = (
          (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = tgt AND v.vote_type = 'up') -
          (SELECT COUNT(*) FROM votes v WHERE v.suggestion_id = tgt AND v.vote_type = 'down')
        ),
        updated_at = NOW()
      WHERE s.id = tgt;
    END IF;

    RETURN NULL;
  END;
  $$ LANGUAGE plpgsql;
END $$;

-- Drop/recreate trigger de forma segura
DO $$ BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.triggers
    WHERE event_object_table = 'votes' AND trigger_name = 'trg_update_vote_counts'
  ) THEN
    DROP TRIGGER trg_update_vote_counts ON votes;
  END IF;
END $$;

CREATE TRIGGER trg_update_vote_counts
AFTER INSERT OR UPDATE OR DELETE ON votes
FOR EACH ROW EXECUTE FUNCTION update_suggestion_vote_counts();

-- Backfill inicial (idempotente)
UPDATE suggestions s SET
  upvotes    = COALESCE(uv.ups, 0),
  downvotes  = COALESCE(dv.downs, 0),
  vote_count = COALESCE(uv.ups, 0) - COALESCE(dv.downs, 0),
  updated_at = NOW()
FROM (
  SELECT suggestion_id, COUNT(*) AS ups
  FROM votes WHERE vote_type = 'up' AND suggestion_id IS NOT NULL
  GROUP BY suggestion_id
) uv
FULL JOIN (
  SELECT suggestion_id, COUNT(*) AS downs
  FROM votes WHERE vote_type = 'down' AND suggestion_id IS NOT NULL
  GROUP BY suggestion_id
) dv ON dv.suggestion_id = uv.suggestion_id
WHERE s.id = COALESCE(uv.suggestion_id, dv.suggestion_id);
-- Audita a existência de tabelas, colunas-chave, enums e gatilhos essenciais
\pset tuples_only on
\pset format aligned

-- Tabelas esperadas
WITH expected(table_name) AS (
  VALUES
    ('restaurants'),('users'),('playlists'),('playlist_tracks'),('playlist_schedules'),
    ('suggestions'),('votes'),('client_sessions'),('play_history'),('analytics_daily'),
    ('moderation_rules'),('competitive_votes'),('payments'),('qr_codes'),('lyrics'),
    ('genres'),('rewards')
)
SELECT 'MISSING_TABLE' AS issue, e.table_name
FROM expected e
LEFT JOIN information_schema.tables t ON t.table_schema='public' AND t.table_name=e.table_name
WHERE t.table_name IS NULL
ORDER BY e.table_name;

-- Colunas-chave por tabela (parcial)
WITH expected_cols AS (
  SELECT * FROM (VALUES
    ('restaurants','id'),('restaurants','name'),('restaurants','isActive'),
    ('users','id'),('users','email'),('users','password'),('users','restaurant_id'),
    ('playlists','id'),('playlists','restaurant_id'),('playlist_tracks','playlist_id'),
    ('suggestions','id'),('suggestions','youtube_video_id'),('suggestions','status'),('suggestions','restaurant_id'),
    ('votes','id'),('votes','vote_type'),('votes','suggestion_id'),
    ('client_sessions','id'),('client_sessions','sessionToken'),('client_sessions','restaurant_id'),
    ('play_history','id'),('play_history','status'),('play_history','restaurant_id'),
    ('analytics_daily','id'),('analytics_daily','restaurant_id'),
    ('moderation_rules','id'),('moderation_rules','ruleType'),('moderation_rules','restaurant_id'),
    ('payments','id'),('payments','status'),('payments','suggestion_id')
  ) AS t(table_name,column_name)
)
SELECT 'MISSING_COLUMN' AS issue, ec.table_name, ec.column_name
FROM expected_cols ec
LEFT JOIN information_schema.columns c ON c.table_schema='public' AND c.table_name=ec.table_name AND c.column_name=ec.column_name
WHERE c.column_name IS NULL
ORDER BY ec.table_name, ec.column_name;

-- Enums esperados e valores
WITH expected_enums AS (
  SELECT * FROM (VALUES
    ('restaurant_status', ARRAY['active','inactive','suspended','trial']::text[]),
    ('user_role', ARRAY['super_admin','admin','moderator','staff']::text[]),
    ('playlist_type', ARRAY['custom','youtube','youtube_import','auto_generated','suggestions']::text[]),
    ('playlist_status', ARRAY['active','inactive','archived','deleted']::text[]),
    ('suggestion_status', ARRAY['pending','approved','rejected','playing','played','skipped','completed','expired']::text[]),
    ('suggestion_source', ARRAY['client','admin','auto','import']::text[]),
    ('vote_type', ARRAY['up','down']::text[]),
    ('play_status', ARRAY['playing','completed','skipped','interrupted','error']::text[]),
    ('rule_type', ARRAY['blacklist_word','blacklist_artist','blacklist_channel','genre_restriction','duration_limit','content_rating','language_filter','time_restriction']::text[]),
    ('rule_action', ARRAY['auto_reject','flag_for_review','require_approval','auto_approve']::text[])
  ) AS t(enum_name, values)
)
SELECT 'MISSING_ENUM' AS issue, e.enum_name
FROM expected_enums e
LEFT JOIN pg_type t ON t.typname=e.enum_name
WHERE t.oid IS NULL
UNION ALL
SELECT 'MISSING_ENUM_LABEL' AS issue, e.enum_name||':'||v.value
FROM expected_enums e
JOIN pg_type t ON t.typname=e.enum_name
JOIN pg_enum en ON en.enumtypid=t.oid
LEFT JOIN LATERAL unnest(e.values) v(value) ON TRUE
WHERE NOT EXISTS (
  SELECT 1 FROM pg_enum en2 WHERE en2.enumtypid=t.oid AND en2.enumlabel=v.value
)
ORDER BY 1,2;

-- Índices essenciais
WITH expected_idx AS (
  SELECT * FROM (VALUES
    ('users','idx_users_email'),('users','idx_users_role'),('users','idx_users_active'),('users','idx_users_restaurant_role'),
    ('playlists','idx_playlists_restaurant'),('playlists','idx_playlists_type'),('playlists','idx_playlists_status'),('playlists','idx_playlists_default'),
    ('suggestions','idx_suggestions_restaurant'),('suggestions','idx_suggestions_youtube'),('suggestions','idx_suggestions_status'),('suggestions','idx_suggestions_created'),
    ('votes','uq_vote_suggestion_client_session'),('votes','idx_votes_suggestion'),('votes','idx_votes_type')
  ) AS t(table_name,indexname)
)
SELECT 'MISSING_INDEX' AS issue, ei.table_name, ei.indexname
FROM expected_idx ei
LEFT JOIN pg_indexes pi ON pi.schemaname='public' AND pi.indexname=ei.indexname
WHERE pi.indexname IS NULL
ORDER BY ei.table_name, ei.indexname;

-- Gatilhos de manutenção esperados
SELECT 'MISSING_TRIGGER' AS issue, 'votes' AS table_name, 'trg_update_vote_counts' AS trigger
WHERE NOT EXISTS (
  SELECT 1 FROM information_schema.triggers WHERE event_object_table='votes' AND trigger_name='trg_update_vote_counts'
);

\pset tuples_only off
\echo '--- Fim da auditoria ---'
-- Configurar playlist "teste" como padrão para demo-restaurant
UPDATE playlists 
SET "isDefault" = true 
WHERE id = '06881d7d-4e13-41d6-ad47-e81daea362a0';

-- Verificar se foi configurada
SELECT id, name, status, "isDefault", "youtubePlaylistId" 
FROM playlists 
WHERE restaurant_id = 'demo-restaurant' AND status = 'active';
-- Criar tabela de gêneros musicais
-- Sistema de Playlist Interativa para Restaurantes

-- Tabela principal de gêneros
CREATE TABLE IF NOT EXISTS genres (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    "displayName" VARCHAR(100) NOT NULL,
    description VARCHAR(200),
    category VARCHAR(50) DEFAULT 'music' CHECK (category IN ('music', 'mood', 'energy', 'time', 'custom')),
    color VARCHAR(7) DEFAULT '#3B82F6',
    icon VARCHAR(50),
    metadata JSON,
    priority INTEGER DEFAULT 0,
    "isActive" BOOLEAN DEFAULT true,
    "isDefault" BOOLEAN DEFAULT false,
    "usageCount" INTEGER DEFAULT 0,
    "lastUsedAt" TIMESTAMP WITHOUT TIME ZONE,
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_genres_name ON genres(name);
CREATE INDEX IF NOT EXISTS idx_genres_category ON genres(category);
CREATE INDEX IF NOT EXISTS idx_genres_active ON genres("isActive");
CREATE INDEX IF NOT EXISTS idx_genres_priority ON genres(priority);
CREATE INDEX IF NOT EXISTS idx_genres_usage ON genres("usageCount" DESC);

-- Tabela de relacionamento entre sugestões e gêneros
CREATE TABLE IF NOT EXISTS suggestion_genres (
    suggestion_id UUID NOT NULL REFERENCES suggestions(id) ON DELETE CASCADE,
    genre_id UUID NOT NULL REFERENCES genres(id) ON DELETE CASCADE,
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (suggestion_id, genre_id)
);

-- Índices para a tabela de relacionamento
CREATE INDEX IF NOT EXISTS idx_suggestion_genres_suggestion ON suggestion_genres(suggestion_id);
CREATE INDEX IF NOT EXISTS idx_suggestion_genres_genre ON suggestion_genres(genre_id);

-- Trigger para atualizar updatedAt automaticamente
CREATE OR REPLACE FUNCTION update_genres_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updatedAt" = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_genres_updated_at
    BEFORE UPDATE ON genres
    FOR EACH ROW
    EXECUTE FUNCTION update_genres_updated_at();

-- Inserir gêneros padrão do sistema
INSERT INTO genres (name, "displayName", description, category, color, icon, "isDefault", priority) VALUES
-- Gêneros Musicais
('rock', 'Rock', 'Rock clássico e moderno', 'music', '#DC2626', 'Music2', true, 1),
('pop', 'Pop', 'Musica popular contemporanea', 'music', '#EC4899', 'Mic', true, 2),
('sertanejo', 'Sertanejo', 'Musica sertaneja brasileira', 'music', '#D97706', 'Music', true, 3),
('mpb', 'MPB', 'Musica Popular Brasileira', 'music', '#059669', 'Heart', true, 4),
('funk', 'Funk', 'Funk brasileiro e internacional', 'music', '#EA580C', 'Volume2', true, 5),
('eletronica', 'Eletronica', 'Musica eletronica e EDM', 'music', '#7C3AED', 'Zap', true, 6),
('jazz', 'Jazz', 'Jazz classico e contemporaneo', 'music', '#1F2937', 'Music3', true, 7),
('reggae', 'Reggae', 'Reggae e musica jamaicana', 'music', '#16A34A', 'Music4', true, 8),
('hip-hop', 'Hip Hop', 'Hip hop e rap', 'music', '#374151', 'Mic', true, 9),
('country', 'Country', 'Musica country', 'music', '#92400E', 'Music', true, 10),
('blues', 'Blues', 'Blues tradicional e moderno', 'music', '#1E40AF', 'Music2', true, 11),
('classical', 'Classica', 'Musica classica', 'music', '#7C2D12', 'Music3', true, 12),

-- Humores
('happy', 'Alegre', 'Musicas alegres e animadas', 'mood', '#FCD34D', 'Smile', true, 10),
('sad', 'Melancolico', 'Musicas tristes e melancolicas', 'mood', '#6B7280', 'CloudRain', true, 11),
('romantic', 'Romantico', 'Musicas romanticas', 'mood', '#F472B6', 'Heart', true, 12),
('energetic', 'Energetico', 'Musicas energeticas', 'mood', '#EF4444', 'Zap', true, 13),
('calm', 'Calmo', 'Musicas calmas e relaxantes', 'mood', '#06B6D4', 'Waves', true, 14),
('nostalgic', 'Nostalgico', 'Musicas nostalgicas', 'mood', '#8B5CF6', 'Heart', true, 15),
('motivational', 'Motivacional', 'Musicas motivacionais', 'mood', '#10B981', 'Zap', true, 16),

-- Energia
('high_energy', 'Alta Energia', 'Músicas de alta energia', 'energy', '#DC2626', 'Zap', true, 20),
('medium_energy', 'Média Energia', 'Músicas de energia moderada', 'energy', '#F59E0B', 'Activity', true, 21),
('low_energy', 'Baixa Energia', 'Músicas de baixa energia', 'energy', '#3B82F6', 'Headphones', true, 22),

-- Horário
('morning', 'Manhã', 'Músicas para manhã', 'time', '#FCD34D', 'Sun', true, 30),
('afternoon', 'Tarde', 'Músicas para tarde', 'time', '#F97316', 'Coffee', true, 31),
('evening', 'Noite', 'Músicas para noite', 'time', '#6366F1', 'Moon', true, 32),
('late_night', 'Madrugada', 'Músicas para madrugada', 'time', '#4C1D95', 'Moon', true, 33)

ON CONFLICT (name) DO NOTHING;

-- Função para incrementar uso de gênero
CREATE OR REPLACE FUNCTION increment_genre_usage(genre_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE genres 
    SET "usageCount" = "usageCount" + 1,
        "lastUsedAt" = NOW()
    WHERE id = genre_id;
END;
$$ LANGUAGE plpgsql;

-- Função para obter gêneros por categoria
CREATE OR REPLACE FUNCTION get_genres_by_category(category_filter VARCHAR DEFAULT NULL)
RETURNS TABLE (
    id UUID,
    name VARCHAR,
    "displayName" VARCHAR,
    description VARCHAR,
    category VARCHAR,
    color VARCHAR,
    icon VARCHAR,
    priority INTEGER,
    "isActive" BOOLEAN,
    "usageCount" INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        g.id,
        g.name,
        g."displayName",
        g.description,
        g.category,
        g.color,
        g.icon,
        g.priority,
        g."isActive",
        g."usageCount"
    FROM genres g
    WHERE (category_filter IS NULL OR g.category = category_filter)
      AND g."isActive" = true
    ORDER BY g.priority ASC, g."displayName" ASC;
END;
$$ LANGUAGE plpgsql;

-- Função para buscar gêneros por texto
CREATE OR REPLACE FUNCTION search_genres(search_text VARCHAR)
RETURNS TABLE (
    id UUID,
    name VARCHAR,
    "displayName" VARCHAR,
    description VARCHAR,
    category VARCHAR,
    color VARCHAR,
    icon VARCHAR,
    priority INTEGER,
    "isActive" BOOLEAN,
    "usageCount" INTEGER,
    relevance FLOAT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        g.id,
        g.name,
        g."displayName",
        g.description,
        g.category,
        g.color,
        g.icon,
        g.priority,
        g."isActive",
        g."usageCount",
        (
            CASE 
                WHEN LOWER(g."displayName") = LOWER(search_text) THEN 1.0
                WHEN LOWER(g.name) = LOWER(search_text) THEN 0.9
                WHEN LOWER(g."displayName") LIKE LOWER(search_text || '%') THEN 0.8
                WHEN LOWER(g.name) LIKE LOWER(search_text || '%') THEN 0.7
                WHEN LOWER(g."displayName") LIKE LOWER('%' || search_text || '%') THEN 0.6
                WHEN LOWER(g.name) LIKE LOWER('%' || search_text || '%') THEN 0.5
                WHEN LOWER(g.description) LIKE LOWER('%' || search_text || '%') THEN 0.3
                ELSE 0.1
            END
        ) as relevance
    FROM genres g
    WHERE g."isActive" = true
      AND (
        LOWER(g."displayName") LIKE LOWER('%' || search_text || '%') OR
        LOWER(g.name) LIKE LOWER('%' || search_text || '%') OR
        LOWER(g.description) LIKE LOWER('%' || search_text || '%')
      )
    ORDER BY relevance DESC, g.priority ASC, g."displayName" ASC;
END;
$$ LANGUAGE plpgsql;

-- View para estatísticas de gêneros
CREATE OR REPLACE VIEW genre_stats AS
SELECT 
    g.id,
    g.name,
    g."displayName",
    g.category,
    g."usageCount",
    g."lastUsedAt",
    COUNT(sg.suggestion_id) as suggestions_count,
    COUNT(DISTINCT sg.suggestion_id) as unique_suggestions
FROM genres g
LEFT JOIN suggestion_genres sg ON g.id = sg.genre_id
GROUP BY g.id, g.name, g."displayName", g.category, g."usageCount", g."lastUsedAt"
ORDER BY g."usageCount" DESC, g."displayName" ASC;

-- Comentários para documentação
COMMENT ON TABLE genres IS 'Tabela de gêneros musicais e categorias';
COMMENT ON COLUMN genres.name IS 'Nome único do gênero (usado como ID)';
COMMENT ON COLUMN genres."displayName" IS 'Nome de exibição do gênero';
COMMENT ON COLUMN genres.category IS 'Categoria do gênero: music, mood, energy, time, custom';
COMMENT ON COLUMN genres.color IS 'Cor hexadecimal para exibição';
COMMENT ON COLUMN genres.icon IS 'Nome do ícone para exibição';
COMMENT ON COLUMN genres.metadata IS 'Metadados adicionais em JSON';
COMMENT ON COLUMN genres.priority IS 'Prioridade para ordenação (menor = maior prioridade)';
COMMENT ON COLUMN genres."isActive" IS 'Se o gênero está ativo para uso';
COMMENT ON COLUMN genres."isDefault" IS 'Se é um gênero padrão do sistema';
COMMENT ON COLUMN genres."usageCount" IS 'Número de vezes que foi usado';
COMMENT ON COLUMN genres."lastUsedAt" IS 'Última vez que foi usado';

COMMENT ON TABLE suggestion_genres IS 'Relacionamento many-to-many entre sugestões e gêneros';
COMMENT ON VIEW genre_stats IS 'Estatísticas de uso dos gêneros';

-- Conceder permissões (ajustar conforme necessário)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON genres TO playlist_user;
-- GRANT SELECT, INSERT, DELETE ON suggestion_genres TO playlist_user;
-- GRANT SELECT ON genre_stats TO playlist_user;
-- Criar tabelas faltantes para o sistema de playlist

-- Tabela de agendamento de playlists (horários de funcionamento)
CREATE TABLE IF NOT EXISTS playlist_schedules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    restaurant_id VARCHAR(255) NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    "timeSlots" JSON NOT NULL,
    "isActive" BOOLEAN DEFAULT true,
    mode VARCHAR(50) DEFAULT 'normal' CHECK (mode IN ('normal', 'shuffle', 'repeat', 'smart')),
    settings JSON,
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- Tabela de faixas/músicas das playlists
CREATE TABLE IF NOT EXISTS playlist_tracks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    playlist_id UUID NOT NULL REFERENCES playlists(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    artist VARCHAR(200) NOT NULL,
    "youtubeVideoId" VARCHAR(50) NOT NULL,
    "thumbnailUrl" VARCHAR(500),
    duration INTEGER DEFAULT 0,
    position INTEGER DEFAULT 0,
    "isActive" BOOLEAN DEFAULT true,
    "isExplicit" BOOLEAN DEFAULT false,
    "isLive" BOOLEAN DEFAULT false,
    genre VARCHAR(100),
    mood VARCHAR(100),
    energy INTEGER DEFAULT 5 CHECK (energy >= 1 AND energy <= 10),
    "bpmRange" VARCHAR(20),
    language VARCHAR(10),
    "releaseYear" INTEGER,
    "addedBy" UUID REFERENCES users(id) ON DELETE SET NULL,
    "addedAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    "lastPlayedAt" TIMESTAMP WITHOUT TIME ZONE,
    "playCount" INTEGER DEFAULT 0,
    "skipCount" INTEGER DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.00,
    "ratingCount" INTEGER DEFAULT 0,
    tags JSON,
    metadata JSON,
    "moderationStatus" VARCHAR(20) DEFAULT 'approved' CHECK ("moderationStatus" IN ('pending', 'approved', 'rejected')),
    "moderatedBy" UUID REFERENCES users(id) ON DELETE SET NULL,
    "moderatedAt" TIMESTAMP WITHOUT TIME ZONE,
    "moderationReason" TEXT,
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_playlist_schedules_restaurant ON playlist_schedules(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_playlist_schedules_active ON playlist_schedules("isActive");

CREATE INDEX IF NOT EXISTS idx_playlist_tracks_playlist ON playlist_tracks(playlist_id);
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_position ON playlist_tracks(playlist_id, position);
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_youtube ON playlist_tracks("youtubeVideoId");
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_active ON playlist_tracks("isActive");
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_genre ON playlist_tracks(genre);
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_mood ON playlist_tracks(mood);

-- Dados de exemplo serão inseridos pelo script seed-data.sql após a criação dos restaurantes

SELECT 'Tabelas criadas e dados inseridos com sucesso!' as status;
-- Corrige/alinia colunas de playlists com o modelo Playlist (TypeORM)
DO $$
BEGIN
    -- Adiciona coluna "youtubePlaylistId"
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'playlists' AND column_name = 'youtubePlaylistId'
    ) THEN
        ALTER TABLE playlists ADD COLUMN "youtubePlaylistId" VARCHAR NULL;
    END IF;

    -- Adiciona coluna "coverImage"
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'playlists' AND column_name = 'coverImage'
    ) THEN
        ALTER TABLE playlists ADD COLUMN "coverImage" VARCHAR NULL;
    END IF;

    -- Adiciona coluna "genreTags"
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'playlists' AND column_name = 'genreTags'
    ) THEN
        ALTER TABLE playlists ADD COLUMN "genreTags" JSON NULL;
    END IF;

    -- Adiciona coluna "moodTags"
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'playlists' AND column_name = 'moodTags'
    ) THEN
        ALTER TABLE playlists ADD COLUMN "moodTags" JSON NULL;
    END IF;

    -- Renomeia execution_order -> "executionOrder" se necessário
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'playlists' AND column_name = 'execution_order'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'playlists' AND column_name = 'executionOrder'
    ) THEN
        ALTER TABLE playlists RENAME COLUMN execution_order TO "executionOrder";
    END IF;

    -- Garante a coluna "executionOrder"
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'playlists' AND column_name = 'executionOrder'
    ) THEN
        ALTER TABLE playlists ADD COLUMN "executionOrder" INTEGER NULL;
    END IF;

    -- Adiciona coluna "averageRating"
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'playlists' AND column_name = 'averageRating'
    ) THEN
        ALTER TABLE playlists ADD COLUMN "averageRating" DOUBLE PRECISION DEFAULT 0;
    END IF;

    -- Adiciona coluna tracks (JSON)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'playlists' AND column_name = 'tracks'
    ) THEN
        ALTER TABLE playlists ADD COLUMN tracks JSON NULL;
    END IF;

    -- Adiciona coluna schedule (JSON)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'playlists' AND column_name = 'schedule'
    ) THEN
        ALTER TABLE playlists ADD COLUMN schedule JSON NULL;
    END IF;
END $$;

-- Índice em (restaurant_id, "executionOrder") para ordering
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_class c
        JOIN pg_namespace n ON n.oid = c.relnamespace
        WHERE c.relname = 'idx_playlists_execution_order_camel' AND n.nspname = 'public'
    ) THEN
        CREATE INDEX idx_playlists_execution_order_camel ON playlists(restaurant_id, "executionOrder");
    END IF;
END $$;
-- Criar tabelas faltantes para o sistema de playlist

-- Tabela de agendamento de playlists (horários de funcionamento)
CREATE TABLE IF NOT EXISTS playlist_schedules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    restaurant_id VARCHAR(255) NOT NULL REFERENCES restaurants(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    "timeSlots" JSON NOT NULL,
    "isActive" BOOLEAN DEFAULT true,
    mode VARCHAR(50) DEFAULT 'normal' CHECK (mode IN ('normal', 'shuffle', 'repeat', 'smart')),
    settings JSON,
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- Tabela de faixas/músicas das playlists
CREATE TABLE IF NOT EXISTS playlist_tracks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    playlist_id UUID NOT NULL REFERENCES playlists(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    artist VARCHAR(200) NOT NULL,
    "youtubeVideoId" VARCHAR(50) NOT NULL,
    "thumbnailUrl" VARCHAR(500),
    duration INTEGER DEFAULT 0,
    position INTEGER DEFAULT 0,
    "isActive" BOOLEAN DEFAULT true,
    "isExplicit" BOOLEAN DEFAULT false,
    "isLive" BOOLEAN DEFAULT false,
    genre VARCHAR(100),
    mood VARCHAR(100),
    energy INTEGER DEFAULT 5 CHECK (energy >= 1 AND energy <= 10),
    "bpmRange" VARCHAR(20),
    language VARCHAR(10),
    "releaseYear" INTEGER,
    "addedBy" UUID REFERENCES users(id) ON DELETE SET NULL,
    "addedAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    "lastPlayedAt" TIMESTAMP WITHOUT TIME ZONE,
    "playCount" INTEGER DEFAULT 0,
    "skipCount" INTEGER DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.00,
    "ratingCount" INTEGER DEFAULT 0,
    tags JSON,
    metadata JSON,
    "moderationStatus" VARCHAR(20) DEFAULT 'approved' CHECK ("moderationStatus" IN ('pending', 'approved', 'rejected')),
    "moderatedBy" UUID REFERENCES users(id) ON DELETE SET NULL,
    "moderatedAt" TIMESTAMP WITHOUT TIME ZONE,
    "moderationReason" TEXT,
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_playlist_schedules_restaurant ON playlist_schedules(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_playlist_schedules_active ON playlist_schedules("isActive");

CREATE INDEX IF NOT EXISTS idx_playlist_tracks_playlist ON playlist_tracks(playlist_id);
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_position ON playlist_tracks(playlist_id, position);
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_youtube ON playlist_tracks("youtubeVideoId");
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_active ON playlist_tracks("isActive");
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_genre ON playlist_tracks(genre);
CREATE INDEX IF NOT EXISTS idx_playlist_tracks_mood ON playlist_tracks(mood);

-- Dados de exemplo serão inseridos pelo script seed-data.sql após a criação dos restaurantes

SELECT 'Tabelas criadas e dados inseridos com sucesso!' as status;
