import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown,
  Building2,
  CreditCard,
  Calendar,
  Download,
  Filter,
  RefreshCw,
  Eye,
  <PERSON><PERSON>hart,
  BarChart3,
  Users,
  Percent
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import apiService from '@/services/api';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

interface RevenueData {
  totalRevenue: number;
  platformRevenue: number; // Nossa parte (ex: 10%)
  restaurantRevenue: number; // Parte dos restaurantes (90%)
  monthlyGrowth: number;
  totalTransactions: number;
  averageTransactionValue: number;
  revenueByRestaurant: Array<{
    restaurantId: string;
    restaurantName: string;
    totalRevenue: number;
    platformShare: number;
    transactions: number;
    averageValue: number;
  }>;
  revenueByMonth: Array<{
    month: string;
    totalRevenue: number;
    platformRevenue: number;
    transactions: number;
  }>;
  paymentMethods: Array<{
    method: string;
    count: number;
    revenue: number;
    percentage: number;
  }>;
}

const RevenueDashboard: React.FC = () => {
  const [revenueData, setRevenueData] = useState<RevenueData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [selectedView, setSelectedView] = useState<'overview' | 'restaurants' | 'trends'>('overview');

  // Configuração da taxa da plataforma (pode vir de configurações)
  const PLATFORM_FEE_PERCENTAGE = 10; // 10% para a plataforma

  useEffect(() => {
    loadRevenueData();
  }, [timeRange]);

  const loadRevenueData = async () => {
    try {
      setLoading(true);

      const response = await fetch(`/api/v1/admin/revenue?period=${timeRange}`);
      const data = await response.json();

      if (data.success) {
        setRevenueData(data.revenue);
      } else {
        throw new Error('Erro ao carregar dados de receita');
      }
    } catch (error) {
      console.error('Erro ao carregar dados de receita:', error);
      toast.error('Erro ao carregar dados de receita');

      // Fallback para dados mock em caso de erro
      setRevenueData({
        totalRevenue: 12450.75,
        platformRevenue: 1245.08, // 10%
        restaurantRevenue: 11205.67, // 90%
        monthlyGrowth: 15.3,
        totalTransactions: 623,
        averageTransactionValue: 2.00,
        revenueByRestaurant: [
          {
            restaurantId: '1',
            restaurantName: 'Restaurante A',
            totalRevenue: 3250.50,
            platformShare: 325.05,
            transactions: 162,
            averageValue: 2.01
          },
          {
            restaurantId: '2',
            restaurantName: 'Restaurante B',
            totalRevenue: 2890.30,
            platformShare: 289.03,
            transactions: 144,
            averageValue: 2.01
          },
          {
            restaurantId: '3',
            restaurantName: 'Restaurante C',
            totalRevenue: 2150.20,
            platformShare: 215.02,
            transactions: 107,
            averageValue: 2.01
          },
          {
            restaurantId: '4',
            restaurantName: 'Restaurante D',
            totalRevenue: 1980.15,
            platformShare: 198.02,
            transactions: 99,
            averageValue: 2.00
          },
          {
            restaurantId: '5',
            restaurantName: 'Restaurante E',
            totalRevenue: 1679.60,
            platformShare: 167.96,
            transactions: 84,
            averageValue: 1.99
          },
        ],
        revenueByMonth: [
          { month: 'Jan', totalRevenue: 8200, platformRevenue: 820, transactions: 410 },
          { month: 'Fev', totalRevenue: 9350, platformRevenue: 935, transactions: 467 },
          { month: 'Mar', totalRevenue: 10180, platformRevenue: 1018, transactions: 509 },
          { month: 'Abr', totalRevenue: 11420, platformRevenue: 1142, transactions: 571 },
          { month: 'Mai', totalRevenue: 12450, platformRevenue: 1245, transactions: 623 },
        ],
        paymentMethods: [
          { method: 'PIX', count: 589, revenue: 11780, percentage: 94.6 },
          { method: 'Cartão', count: 34, revenue: 670.75, percentage: 5.4 },
        ]
      });
    } finally {
      setLoading(false);
    }
  };

  const exportRevenueData = async () => {
    try {
  toast.success('Exportando relatório de receitas...');
  const resp = await fetch(`/api/v1/admin/revenue/export?period=${timeRange}`);
  if (!resp.ok) throw new Error('Falha ao exportar');
  const blob = await resp.blob();
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `revenue_${timeRange}_${new Date().toISOString().slice(0,10)}.json`;
  document.body.appendChild(a);
  a.click();
  a.remove();
  window.URL.revokeObjectURL(url);
    } catch (error) {
      toast.error('Erro ao exportar dados');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  if (!revenueData) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Nenhum dado de receita disponível</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Dashboard de Receitas
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Controle financeiro e receitas compartilhadas
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
          >
            <option value="7d">Últimos 7 dias</option>
            <option value="30d">Últimos 30 dias</option>
            <option value="90d">Últimos 90 dias</option>
            <option value="1y">Último ano</option>
          </select>
          
          <button
            onClick={exportRevenueData}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            title="Exportar dados de receita"
          >
            <Download className="w-4 h-4" />
            <span>Exportar</span>
          </button>
          
          <button
            onClick={loadRevenueData}
            disabled={loading}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Recarregar dados de receita"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span>Recarregar</span>
          </button>
          
          <button
            onClick={() => setSelectedView('overview')}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            <PieChart className="w-4 h-4" />
            <span>Relatório</span>
          </button>
        </div>
      </div>

      {/* View Selector */}
      <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
        {[
          { key: 'overview', label: 'Visão Geral', icon: BarChart3 },
          { key: 'restaurants', label: 'Por Restaurante', icon: Building2 },
          { key: 'trends', label: 'Tendências', icon: TrendingUp },
        ].map((view) => (
          <button
            key={view.key}
            onClick={() => setSelectedView(view.key as any)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              selectedView === view.key
                ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            <view.icon className="w-4 h-4" />
            <span>{view.label}</span>
          </button>
        ))}
      </div>

      {/* Overview Stats */}
      {selectedView === 'overview' && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              {
                title: 'Receita Total',
                value: `R$ ${revenueData.totalRevenue.toFixed(2)}`,
                subtitle: `${revenueData.totalTransactions} transações`,
                icon: DollarSign,
                color: 'text-green-600',
                bgColor: 'bg-green-100 dark:bg-green-900/20',
              },
              {
                title: 'Nossa Receita',
                value: `R$ ${revenueData.platformRevenue.toFixed(2)}`,
                subtitle: `${PLATFORM_FEE_PERCENTAGE}% da receita total`,
                icon: TrendingUp,
                color: 'text-blue-600',
                bgColor: 'bg-blue-100 dark:bg-blue-900/20',
              },
              {
                title: 'Receita Restaurantes',
                value: `R$ ${revenueData.restaurantRevenue.toFixed(2)}`,
                subtitle: `${100 - PLATFORM_FEE_PERCENTAGE}% da receita total`,
                icon: Building2,
                color: 'text-purple-600',
                bgColor: 'bg-purple-100 dark:bg-purple-900/20',
              },
              {
                title: 'Ticket Médio',
                value: `R$ ${revenueData.averageTransactionValue.toFixed(2)}`,
                subtitle: `+${revenueData.monthlyGrowth}% este mês`,
                icon: CreditCard,
                color: 'text-orange-600',
                bgColor: 'bg-orange-100 dark:bg-orange-900/20',
              },
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="card p-6"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                      {stat.title}
                    </p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">
                      {stat.value}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {stat.subtitle}
                    </p>
                  </div>
                  <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                    <stat.icon className={`w-6 h-6 ${stat.color}`} />
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Payment Methods */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Métodos de Pagamento
              </h3>
              <div className="space-y-4">
                {revenueData.paymentMethods.map((method) => (
                  <div key={method.method} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span className="text-gray-900 dark:text-white font-medium">
                        {method.method}
                      </span>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900 dark:text-white">
                        R$ {method.revenue.toFixed(2)}
                      </p>
                      <p className="text-sm text-gray-500">
                        {method.count} transações ({method.percentage}%)
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="card p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Distribuição de Receita
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-gray-900 dark:text-white font-medium">
                      Plataforma ({PLATFORM_FEE_PERCENTAGE}%)
                    </span>
                  </div>
                  <span className="font-semibold text-gray-900 dark:text-white">
                    R$ {revenueData.platformRevenue.toFixed(2)}
                  </span>
                </div>
                <div className="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span className="text-gray-900 dark:text-white font-medium">
                      Restaurantes ({100 - PLATFORM_FEE_PERCENTAGE}%)
                    </span>
                  </div>
                  <span className="font-semibold text-gray-900 dark:text-white">
                    R$ {revenueData.restaurantRevenue.toFixed(2)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </>
      )}

      {/* Revenue by Restaurant */}
      {selectedView === 'restaurants' && (
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Receita por Restaurante
          </h3>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                    Restaurante
                  </th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900 dark:text-white">
                    Receita Total
                  </th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900 dark:text-white">
                    Nossa Parte
                  </th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900 dark:text-white">
                    Transações
                  </th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900 dark:text-white">
                    Ticket Médio
                  </th>
                </tr>
              </thead>
              <tbody>
                {revenueData.revenueByRestaurant.map((restaurant) => (
                  <tr
                    key={restaurant.restaurantId}
                    className="border-b border-gray-100 dark:border-gray-800"
                  >
                    <td className="py-3 px-4">
                      <div className="font-medium text-gray-900 dark:text-white">
                        {restaurant.restaurantName}
                      </div>
                    </td>
                    <td className="py-3 px-4 text-right font-semibold text-gray-900 dark:text-white">
                      R$ {restaurant.totalRevenue.toFixed(2)}
                    </td>
                    <td className="py-3 px-4 text-right font-semibold text-green-600">
                      R$ {restaurant.platformShare.toFixed(2)}
                    </td>
                    <td className="py-3 px-4 text-right text-gray-600 dark:text-gray-400">
                      {restaurant.transactions}
                    </td>
                    <td className="py-3 px-4 text-right text-gray-600 dark:text-gray-400">
                      R$ {restaurant.averageValue.toFixed(2)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Trends */}
      {selectedView === 'trends' && (
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Evolução da Receita
          </h3>
          <div className="space-y-4">
            {revenueData.revenueByMonth.map((month) => (
              <div
                key={month.month}
                className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg"
              >
                <div className="flex items-center space-x-4">
                  <span className="font-medium text-gray-900 dark:text-white">
                    {month.month}
                  </span>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {month.transactions} transações
                  </span>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900 dark:text-white">
                    R$ {month.totalRevenue.toFixed(2)}
                  </p>
                  <p className="text-sm text-green-600">
                    +R$ {month.platformRevenue.toFixed(2)} nossa parte
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default RevenueDashboard;
