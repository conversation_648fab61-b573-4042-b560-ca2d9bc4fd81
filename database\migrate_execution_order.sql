-- ============================================
-- SCRIPT DE MIGRAÇÃO - EXECUTION ORDER
-- Atualiza playlists existentes com ordem de execução
-- ============================================

-- Função para atualizar execution_order das playlists existentes
DO $$
DECLARE
    restaurant_rec RECORD;
    playlist_rec RECORD;
    current_order INTEGER;
BEGIN
    -- Para cada restaurante
    FOR restaurant_rec IN 
        SELECT DISTINCT restaurant_id 
        FROM playlists 
        WHERE execution_order IS NULL
    LOOP
        current_order := 1;
        
        -- Para cada playlist do restaurante (ordenada por data de criação)
        FOR playlist_rec IN
            SELECT id FROM (
                SELECT id, COALESCE(created_at, "createdAt", now()) AS created_any
                FROM playlists
                WHERE restaurant_id = restaurant_rec.restaurant_id
                AND execution_order IS NULL
            ) t
            ORDER BY created_any ASC
        LOOP
            -- Atualizar execution_order
            UPDATE playlists 
            SET execution_order = current_order
            WHERE id = playlist_rec.id;
            
            current_order := current_order + 1;
        END LOOP;
        
        RAISE NOTICE 'Atualizado execution_order para restaurante: %', restaurant_rec.restaurant_id;
    END LOOP;
    
    RAISE NOTICE 'Migração de execution_order concluída com sucesso!';
END $$;
