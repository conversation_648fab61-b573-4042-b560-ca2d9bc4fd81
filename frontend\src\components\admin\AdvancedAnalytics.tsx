import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Music, 
  Clock, 
  Calendar,
  Download,
  Filter,
  RefreshCw,
  Eye,
  Heart,
  Star,
  Activity
} from 'lucide-react';

interface AnalyticsData {
  totalSuggestions: number;
  totalVotes: number;
  activeUsers: number;
  averageVotesPerSong: number;
  peakHour: string;
  topGenres: Array<{ name: string; count: number; percentage: number }>;
  hourlyActivity: Array<{ hour: string; suggestions: number; votes: number }>;
  topSongs: Array<{
    title: string;
    artist: string;
    votes: number;
    suggestions: number;
    lastPlayed: string;
  }>;
  weeklyTrends: Array<{
    day: string;
    suggestions: number;
    votes: number;
    users: number;
  }>;
}

const AdvancedAnalytics: React.FC = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState<'today' | 'week' | 'month'>('today');
  const [selectedMetric, setSelectedMetric] = useState<'suggestions' | 'votes' | 'users'>('suggestions');

  useEffect(() => {
    loadAnalyticsData();
  }, [timeRange]);

  const loadAnalyticsData = async () => {
    setLoading(true);
    try {
      // Mock data para demonstração
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockData: AnalyticsData = {
        totalSuggestions: 156,
        totalVotes: 1247,
        activeUsers: 89,
        averageVotesPerSong: 8.2,
        peakHour: '20:00',
        topGenres: [
          { name: 'Rock', count: 45, percentage: 28.8 },
          { name: 'Pop', count: 38, percentage: 24.4 },
          { name: 'Jazz', count: 25, percentage: 16.0 },
          { name: 'Blues', count: 22, percentage: 14.1 },
          { name: 'Clássica', count: 18, percentage: 11.5 },
          { name: 'Outros', count: 8, percentage: 5.1 },
        ],
        hourlyActivity: [
          { hour: '18:00', suggestions: 12, votes: 45 },
          { hour: '19:00', suggestions: 18, votes: 67 },
          { hour: '20:00', suggestions: 25, votes: 89 },
          { hour: '21:00', suggestions: 22, votes: 78 },
          { hour: '22:00', suggestions: 15, votes: 56 },
          { hour: '23:00', suggestions: 8, votes: 34 },
        ],
        topSongs: [
          {
            title: 'Bohemian Rhapsody',
            artist: 'Queen',
            votes: 45,
            suggestions: 8,
            lastPlayed: '2024-01-15T20:30:00Z',
          },
          {
            title: 'Hotel California',
            artist: 'Eagles',
            votes: 38,
            suggestions: 6,
            lastPlayed: '2024-01-15T19:45:00Z',
          },
          {
            title: 'Imagine',
            artist: 'John Lennon',
            votes: 32,
            suggestions: 5,
            lastPlayed: '2024-01-15T18:20:00Z',
          },
        ],
        weeklyTrends: [
          { day: 'Seg', suggestions: 45, votes: 234, users: 67 },
          { day: 'Ter', suggestions: 52, votes: 289, users: 78 },
          { day: 'Qua', suggestions: 38, votes: 198, users: 56 },
          { day: 'Qui', suggestions: 61, votes: 345, users: 89 },
          { day: 'Sex', suggestions: 78, votes: 456, users: 112 },
          { day: 'Sáb', suggestions: 89, votes: 567, users: 134 },
          { day: 'Dom', suggestions: 67, votes: 398, users: 98 },
        ],
      };
      
      setAnalyticsData(mockData);
    } catch (error) {
      console.error('Erro ao carregar analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const exportData = () => {
    if (!analyticsData) return;
    
    const dataStr = JSON.stringify(analyticsData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `analytics-${timeRange}-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="text-center py-12">
        <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-600 dark:text-gray-400">
          Erro ao carregar dados de analytics
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Analytics Avançados
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Métricas detalhadas e insights do restaurante
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
          >
            <option value="today">Hoje</option>
            <option value="week">Esta Semana</option>
            <option value="month">Este Mês</option>
          </select>
          
          <button
            onClick={exportData}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            title="Exportar dados de analytics avançados"
          >
            <Download className="w-4 h-4" />
            <span>Exportar</span>
          </button>
          
          <button
            onClick={loadAnalyticsData}
            disabled={loading}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Recarregar dados de analytics"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span>Recarregar</span>
          </button>
          
          <button
            onClick={() => setSelectedMetric('suggestions')}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            <Activity className="w-4 h-4" />
            <span>Relatório</span>
          </button>
        </div>
      </div>

      {/* Cards de métricas principais */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total de Sugestões
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {analyticsData.totalSuggestions}
              </p>
            </div>
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
              <Music className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
            <span className="text-sm text-green-600 dark:text-green-400">+12%</span>
            <span className="text-sm text-gray-600 dark:text-gray-400 ml-1">vs. período anterior</span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total de Votos
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {analyticsData.totalVotes}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
              <Heart className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
            <span className="text-sm text-green-600 dark:text-green-400">+8%</span>
            <span className="text-sm text-gray-600 dark:text-gray-400 ml-1">vs. período anterior</span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Usuários Ativos
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {analyticsData.activeUsers}
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
            <span className="text-sm text-green-600 dark:text-green-400">+15%</span>
            <span className="text-sm text-gray-600 dark:text-gray-400 ml-1">vs. período anterior</span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Horário de Pico
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {analyticsData.peakHour}
              </p>
            </div>
            <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center">
              <Clock className="w-6 h-6 text-orange-600 dark:text-orange-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <Activity className="w-4 h-4 text-blue-500 mr-1" />
            <span className="text-sm text-blue-600 dark:text-blue-400">Maior atividade</span>
          </div>
        </motion.div>
      </div>

      {/* Gráficos */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Atividade por hora */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-semibold">Atividade por Hora</h3>
            <select
              value={selectedMetric}
              onChange={(e) => setSelectedMetric(e.target.value as any)}
              className="text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700"
            >
              <option value="suggestions">Sugestões</option>
              <option value="votes">Votos</option>
            </select>
          </div>
          
          <div className="space-y-3">
            {analyticsData.hourlyActivity.map((item, index) => (
              <div key={item.hour} className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400 w-12">
                  {item.hour}
                </span>
                <div className="flex-1 mx-3">
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{
                        width: `${(selectedMetric === 'suggestions' ? item.suggestions : item.votes) / 
                          Math.max(...analyticsData.hourlyActivity.map(h => 
                            selectedMetric === 'suggestions' ? h.suggestions : h.votes
                          )) * 100}%`
                      }}
                    />
                  </div>
                </div>
                <span className="text-sm font-medium text-gray-900 dark:text-white w-8 text-right">
                  {selectedMetric === 'suggestions' ? item.suggestions : item.votes}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Top gêneros */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-6">Gêneros Mais Populares</h3>
          
          <div className="space-y-4">
            {analyticsData.topGenres.map((genre, index) => (
              <div key={genre.name} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xs font-bold">
                    {index + 1}
                  </div>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {genre.name}
                  </span>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${genre.percentage}%` }}
                    />
                  </div>
                  <span className="text-sm text-gray-600 dark:text-gray-400 w-12 text-right">
                    {genre.count}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Músicas mais populares */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold mb-6">Músicas Mais Populares</h3>
        
        <div className="space-y-4">
          {analyticsData.topSongs.map((song, index) => (
            <div key={`${song.title}-${song.artist}`} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="flex items-center space-x-4">
                <div className="w-10 h-10 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center text-white font-bold">
                  {index + 1}
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    {song.title}
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {song.artist}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-6 text-sm">
                <div className="text-center">
                  <p className="font-medium text-gray-900 dark:text-white">{song.votes}</p>
                  <p className="text-gray-600 dark:text-gray-400">Votos</p>
                </div>
                <div className="text-center">
                  <p className="font-medium text-gray-900 dark:text-white">{song.suggestions}</p>
                  <p className="text-gray-600 dark:text-gray-400">Sugestões</p>
                </div>
                <div className="text-center">
                  <p className="font-medium text-gray-900 dark:text-white">
                    {new Date(song.lastPlayed).toLocaleDateString('pt-BR')}
                  </p>
                  <p className="text-gray-600 dark:text-gray-400">Última vez</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Tendências semanais */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold mb-6">Tendências da Semana</h3>
        
        <div className="grid grid-cols-7 gap-4">
          {analyticsData.weeklyTrends.map((day) => (
            <div key={day.day} className="text-center">
              <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 mb-2">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                  {day.day}
                </p>
                <div className="space-y-2">
                  <div>
                    <p className="text-lg font-bold text-blue-600 dark:text-blue-400">
                      {day.suggestions}
                    </p>
                    <p className="text-xs text-gray-500">Sugestões</p>
                  </div>
                  <div>
                    <p className="text-lg font-bold text-green-600 dark:text-green-400">
                      {day.votes}
                    </p>
                    <p className="text-xs text-gray-500">Votos</p>
                  </div>
                  <div>
                    <p className="text-lg font-bold text-purple-600 dark:text-purple-400">
                      {day.users}
                    </p>
                    <p className="text-xs text-gray-500">Usuários</p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AdvancedAnalytics;
