import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Settings, 
  Save,
  RefreshCw,
  Shield,
  DollarSign,
  Bell,
  Database,
  Mail,
  Key,
  Globe,
  AlertTriangle,
  CheckCircle,
  Info
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import apiService from '@/services/api';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

interface SystemConfig {
  platform: {
    name: string;
    version: string;
    maintenanceMode: boolean;
    maxRestaurants: number;
    defaultLanguage: string;
  allowNewRegistrations?: boolean;
  defaultTrialDays?: number;
  };
  revenue: {
    platformFeePercentage: number;
    minimumPaymentAmount: number;
    paymentMethods: string[];
    autoPayoutEnabled: boolean;
    payoutFrequency: 'daily' | 'weekly' | 'monthly';
  defaultCurrency?: 'BRL' | 'USD' | 'EUR';
  };
  notifications: {
    emailEnabled: boolean;
    smsEnabled: boolean;
    webhookEnabled: boolean;
  webhookUrl?: string;
    adminNotifications: {
      newRestaurant: boolean;
      paymentIssues: boolean;
      systemErrors: boolean;
      maintenanceAlerts: boolean;
    };
  };
  security: {
    sessionTimeout: number;
    maxLoginAttempts: number;
    requireTwoFactor: boolean;
    allowedDomains: string[];
    rateLimitEnabled: boolean;
    rateLimitRequests: number;
  };
  integrations: {
    mercadoPago: {
      enabled: boolean;
      environment: 'sandbox' | 'production';
      webhookUrl: string;
    };
    youtube: {
      enabled: boolean;
      apiQuotaLimit: number;
      cacheEnabled: boolean;
    };
  };
}

const SystemSettings: React.FC = () => {
  const [config, setConfig] = useState<SystemConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState<'platform' | 'revenue' | 'notifications' | 'security' | 'integrations'>('platform');

  useEffect(() => {
    loadSystemConfig();
  }, []);

  const loadSystemConfig = async () => {
    try {
      setLoading(true);

      const { data } = await apiService.client.get('/admin/settings');

      if (data.success) {
        // Mapear os dados da API para o formato esperado pelo componente
        setConfig({
          platform: {
            name: 'Restaurant Playlist System',
            version: '2.1.0',
            maintenanceMode: data.settings.system.maintenanceMode,
            maxRestaurants: data.settings.system.maxRestaurantsPerPlan,
            defaultLanguage: 'pt-BR',
            allowNewRegistrations: !!data.settings.system.allowNewRegistrations,
            defaultTrialDays: Number(data.settings.system.defaultTrialDays || 30),
          },
          revenue: {
            platformFeePercentage: data.settings.payments.commissionRate,
            minimumPaymentAmount: 2.00,
            paymentMethods: ['pix', 'credit_card'],
            autoPayoutEnabled: true,
            payoutFrequency: 'weekly',
            defaultCurrency: (data.settings.payments.defaultCurrency || 'BRL'),
          },
          notifications: {
            emailEnabled: data.settings.notifications.emailNotifications,
            smsEnabled: data.settings.notifications.smsNotifications,
            webhookEnabled: !!data.settings.notifications.webhookUrl,
            webhookUrl: data.settings.notifications.webhookUrl || '',
            adminNotifications: {
              newRestaurant: true,
              paymentIssues: true,
              systemErrors: true,
              maintenanceAlerts: true,
            },
          },
          security: {
            sessionTimeout: 3600,
            maxLoginAttempts: 5,
            requireTwoFactor: false,
            allowedDomains: ['localhost', 'yourdomain.com'],
            rateLimitEnabled: true,
            rateLimitRequests: 100,
          },
          integrations: {
            mercadoPago: {
              enabled: data.settings.payments.pixEnabled,
              environment: 'sandbox',
              webhookUrl: data.settings.notifications.webhookUrl || '',
            },
            youtube: {
              enabled: data.settings.features.analyticsEnabled,
              apiQuotaLimit: 10000,
              cacheEnabled: true,
            },
          },
        });
      } else {
        throw new Error('Erro ao carregar configurações');
      }
    } catch (error) {
      console.error('Erro ao carregar configurações:', error);
      toast.error('Erro ao carregar configurações');

      // Fallback para dados mock em caso de erro
      setConfig({
        platform: {
          name: 'Restaurant Playlist System',
          version: '2.1.0',
          maintenanceMode: false,
          maxRestaurants: 100,
          defaultLanguage: 'pt-BR',
          allowNewRegistrations: true,
          defaultTrialDays: 30,
        },
        revenue: {
          platformFeePercentage: 10,
          minimumPaymentAmount: 2.00,
          paymentMethods: ['pix', 'credit_card'],
          autoPayoutEnabled: true,
          payoutFrequency: 'weekly',
          defaultCurrency: 'BRL',
        },
        notifications: {
          emailEnabled: true,
          smsEnabled: false,
          webhookEnabled: true,
          webhookUrl: 'https://yourdomain.com/api/v1/payments/webhook',
          adminNotifications: {
            newRestaurant: true,
            paymentIssues: true,
            systemErrors: true,
            maintenanceAlerts: true,
          },
        },
        security: {
          sessionTimeout: 3600, // 1 hora
          maxLoginAttempts: 5,
          requireTwoFactor: false,
          allowedDomains: ['localhost', 'yourdomain.com'],
          rateLimitEnabled: true,
          rateLimitRequests: 100,
        },
        integrations: {
          mercadoPago: {
            enabled: true,
            environment: 'sandbox',
            webhookUrl: 'https://yourdomain.com/api/v1/payments/webhook',
          },
          youtube: {
            enabled: true,
            apiQuotaLimit: 10000,
            cacheEnabled: true,
          },
        },
      });
    } finally {
      setLoading(false);
    }
  };

  const saveSystemConfig = async () => {
    if (!config) return;

    try {
      setSaving(true);
      // Transformar para o formato esperado pelo backend
      const payload = {
        settings: {
          system: {
            maintenanceMode: config.platform.maintenanceMode,
            allowNewRegistrations: !!config.platform.allowNewRegistrations,
            maxRestaurantsPerPlan: config.platform.maxRestaurants,
            defaultTrialDays: config.platform.defaultTrialDays ?? 30,
          },
          notifications: {
            emailNotifications: config.notifications.emailEnabled,
            smsNotifications: config.notifications.smsEnabled,
            webhookUrl: config.notifications.webhookEnabled ? (config.notifications.webhookUrl || '') : '',
          },
          payments: {
            stripeEnabled: config.revenue.paymentMethods.includes('credit_card'),
            pixEnabled: config.integrations.mercadoPago.enabled,
            defaultCurrency: config.revenue.defaultCurrency || 'BRL',
            commissionRate: config.revenue.platformFeePercentage,
          },
          features: {
            analyticsEnabled: config.integrations.youtube.enabled,
            competitiveVotingEnabled: true,
            playlistSchedulingEnabled: true,
            qrCodeGenerationEnabled: true,
          },
        },
      };

      await apiService.client.put('/admin/settings', payload);
      toast.success('Configurações salvas com sucesso!');
    } catch (error) {
      console.error('Erro ao salvar configurações:', error);
      toast.error('Erro ao salvar configurações');
    } finally {
      setSaving(false);
    }
  };

  const updateConfig = (section: keyof SystemConfig, field: string, value: any) => {
    if (!config) return;

    setConfig({
      ...config,
      [section]: {
        ...config[section],
        [field]: value,
      },
    });
  };

  const updateNestedConfig = (section: keyof SystemConfig, subsection: string, field: string, value: any) => {
    if (!config) return;

    setConfig({
      ...config,
      [section]: {
        ...config[section],
        [subsection]: {
          ...(config[section] as any)[subsection],
          [field]: value,
        },
      },
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  if (!config) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Erro ao carregar configurações</p>
      </div>
    );
  }

  const tabs = [
    { key: 'platform', label: 'Plataforma', icon: Globe },
    { key: 'revenue', label: 'Receitas', icon: DollarSign },
    { key: 'notifications', label: 'Notificações', icon: Bell },
    { key: 'security', label: 'Segurança', icon: Shield },
    { key: 'integrations', label: 'Integrações', icon: Settings },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Configurações do Sistema
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Configurações globais da plataforma
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={loadSystemConfig}
            disabled={loading}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Recarregar configurações"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span>Recarregar</span>
          </button>
          
          <button
            onClick={saveSystemConfig}
            disabled={saving || loading}
            className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            title="Salvar todas as configurações"
          >
            <Save className={`w-4 h-4 ${saving ? 'animate-pulse' : ''}`} />
            <span>{saving ? 'Salvando...' : 'Salvar'}</span>
          </button>
          
          <button
            onClick={() => window.location.href = '/admin/backup'}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            <Database className="w-4 h-4" />
            <span>Backup</span>
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
        {tabs.map((tab) => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key as any)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === tab.key
                ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            <tab.icon className="w-4 h-4" />
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Platform Settings */}
      {activeTab === 'platform' && (
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Configurações da Plataforma
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Nome da Plataforma
              </label>
              <input
                type="text"
                value={config.platform.name}
                onChange={(e) => updateConfig('platform', 'name', e.target.value)}
                className="input-field"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Versão
              </label>
              <input
                type="text"
                value={config.platform.version}
                onChange={(e) => updateConfig('platform', 'version', e.target.value)}
                className="input-field"
                readOnly
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Máximo de Restaurantes
              </label>
              <input
                type="number"
                value={config.platform.maxRestaurants}
                onChange={(e) => updateConfig('platform', 'maxRestaurants', parseInt(e.target.value))}
                className="input-field"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Idioma Padrão
              </label>
              <select
                value={config.platform.defaultLanguage}
                onChange={(e) => updateConfig('platform', 'defaultLanguage', e.target.value)}
                className="input-field"
              >
                <option value="pt-BR">Português (Brasil)</option>
                <option value="en-US">English (US)</option>
                <option value="es-ES">Español</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Dias de Teste (Trial)
              </label>
              <input
                type="number"
                min={0}
                value={config.platform.defaultTrialDays ?? 30}
                onChange={(e) => updateConfig('platform', 'defaultTrialDays', parseInt(e.target.value || '0'))}
                className="input-field"
              />
            </div>
          </div>
          
          <div className="mt-6">
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={config.platform.maintenanceMode}
                onChange={(e) => updateConfig('platform', 'maintenanceMode', e.target.checked)}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Modo de Manutenção
              </span>
            </label>
            <p className="text-sm text-gray-500 mt-1">
              Quando ativado, apenas administradores podem acessar o sistema
            </p>
            <div className="mt-4">
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={!!config.platform.allowNewRegistrations}
                  onChange={(e) => updateConfig('platform', 'allowNewRegistrations', e.target.checked)}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Permitir novos cadastros
                </span>
              </label>
              <p className="text-sm text-gray-500 mt-1">
                Controla se novos restaurantes podem se registrar na plataforma
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Revenue Settings */}
      {activeTab === 'revenue' && (
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Configurações de Receita
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Taxa da Plataforma (%)
              </label>
              <input
                type="number"
                min={0}
                max={100}
                step={0.1}
                value={config.revenue.platformFeePercentage}
                onChange={(e) => updateConfig('revenue', 'platformFeePercentage', parseFloat(e.target.value))}
                className="input-field"
              />
              <p className="text-sm text-gray-500 mt-1">
                Porcentagem que a plataforma recebe de cada transação
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Moeda Padrão
              </label>
              <select
                value={config.revenue.defaultCurrency || 'BRL'}
                onChange={(e) => updateConfig('revenue', 'defaultCurrency', e.target.value)}
                className="input-field"
              >
                <option value="BRL">BRL (R$)</option>
                <option value="USD">USD ($)</option>
                <option value="EUR">EUR (€)</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Valor Mínimo de Pagamento (R$)
              </label>
              <input
                type="number"
                min={0}
                step={0.01}
                value={config.revenue.minimumPaymentAmount}
                onChange={(e) => updateConfig('revenue', 'minimumPaymentAmount', parseFloat(e.target.value))}
                className="input-field"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Frequência de Repasse
              </label>
              <select
                value={config.revenue.payoutFrequency}
                onChange={(e) => updateConfig('revenue', 'payoutFrequency', e.target.value)}
                className="input-field"
              >
                <option value="daily">Diário</option>
                <option value="weekly">Semanal</option>
                <option value="monthly">Mensal</option>
              </select>
            </div>
          </div>
          
          <div className="mt-6">
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={config.revenue.autoPayoutEnabled}
                onChange={(e) => updateConfig('revenue', 'autoPayoutEnabled', e.target.checked)}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Repasse Automático
              </span>
            </label>
            <p className="text-sm text-gray-500 mt-1">
              Quando ativado, os repasses são feitos automaticamente
            </p>
          </div>
        </div>
      )}

      {/* Notifications Settings */}
      {activeTab === 'notifications' && (
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Notificações
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-700/40">
              <label className="flex items-center space-x-3 mb-3">
                <input
                  type="checkbox"
                  checked={config.notifications.emailEnabled}
                  onChange={(e) => updateConfig('notifications', 'emailEnabled', e.target.checked)}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span>Email</span>
              </label>
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={config.notifications.smsEnabled}
                  onChange={(e) => updateConfig('notifications', 'smsEnabled', e.target.checked)}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span>SMS</span>
              </label>
            </div>

            <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-700/40">
              <label className="flex items-center space-x-3 mb-3">
                <input
                  type="checkbox"
                  checked={config.notifications.webhookEnabled}
                  onChange={(e) => updateConfig('notifications', 'webhookEnabled', e.target.checked)}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span>Webhook</span>
              </label>
              <div className="mt-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  URL do Webhook
                </label>
                <input
                  type="url"
                  placeholder="https://seuservico.com/webhook"
                  value={config.notifications.webhookUrl || ''}
                  onChange={(e) => updateConfig('notifications', 'webhookUrl', e.target.value)}
                  className="input-field"
                  disabled={!config.notifications.webhookEnabled}
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Security Settings */}
      {activeTab === 'security' && (
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Configurações de Segurança
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Timeout de Sessão (segundos)
              </label>
              <input
                type="number"
                min="300"
                value={config.security.sessionTimeout}
                onChange={(e) => updateConfig('security', 'sessionTimeout', parseInt(e.target.value))}
                className="input-field"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Máximo de Tentativas de Login
              </label>
              <input
                type="number"
                min="1"
                max="10"
                value={config.security.maxLoginAttempts}
                onChange={(e) => updateConfig('security', 'maxLoginAttempts', parseInt(e.target.value))}
                className="input-field"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Rate Limit (requisições/minuto)
              </label>
              <input
                type="number"
                min="10"
                value={config.security.rateLimitRequests}
                onChange={(e) => updateConfig('security', 'rateLimitRequests', parseInt(e.target.value))}
                className="input-field"
              />
            </div>
          </div>
          
          <div className="mt-6 space-y-4">
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={config.security.requireTwoFactor}
                onChange={(e) => updateConfig('security', 'requireTwoFactor', e.target.checked)}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Exigir Autenticação de Dois Fatores
              </span>
            </label>
            
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={config.security.rateLimitEnabled}
                onChange={(e) => updateConfig('security', 'rateLimitEnabled', e.target.checked)}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Ativar Rate Limiting
              </span>
            </label>
          </div>
        </div>
      )}

      {/* Integrations Settings */}
      {activeTab === 'integrations' && (
        <div className="space-y-6">
          {/* Mercado Pago */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Mercado Pago
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Ambiente
                </label>
                <select
                  value={config.integrations.mercadoPago.environment}
                  onChange={(e) => updateNestedConfig('integrations', 'mercadoPago', 'environment', e.target.value)}
                  className="input-field"
                >
                  <option value="sandbox">Sandbox (Teste)</option>
                  <option value="production">Produção</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Webhook URL
                </label>
                <input
                  type="url"
                  value={config.integrations.mercadoPago.webhookUrl}
                  onChange={(e) => updateNestedConfig('integrations', 'mercadoPago', 'webhookUrl', e.target.value)}
                  className="input-field"
                />
              </div>
            </div>
            
            <div className="mt-4">
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={config.integrations.mercadoPago.enabled}
                  onChange={(e) => updateNestedConfig('integrations', 'mercadoPago', 'enabled', e.target.checked)}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Ativar Integração Mercado Pago
                </span>
              </label>
            </div>
          </div>

          {/* YouTube */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              YouTube API
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Limite de Quota da API
                </label>
                <input
                  type="number"
                  min="1000"
                  value={config.integrations.youtube.apiQuotaLimit}
                  onChange={(e) => updateNestedConfig('integrations', 'youtube', 'apiQuotaLimit', parseInt(e.target.value))}
                  className="input-field"
                />
              </div>
            </div>
            
            <div className="mt-4 space-y-4">
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={config.integrations.youtube.enabled}
                  onChange={(e) => updateNestedConfig('integrations', 'youtube', 'enabled', e.target.checked)}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Ativar Integração YouTube
                </span>
              </label>
              
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={config.integrations.youtube.cacheEnabled}
                  onChange={(e) => updateNestedConfig('integrations', 'youtube', 'cacheEnabled', e.target.checked)}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Ativar Cache de Dados
                </span>
              </label>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SystemSettings;
