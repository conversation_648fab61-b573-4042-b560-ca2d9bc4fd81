const validator = require("express-validator");
import { Request, Response, NextFunction } from "express";
import { ValidationError } from "./errors";

// Re-export validation functions from express-validator
export const { body, param, query, validationResult } = validator;

/**
 * Middleware to check validation errors and throw if any exist
 */
export function checkValidationErrors(
  req: Request,
  res: Response,
  next: NextFunction
) {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError("Validation failed", errors.array());
  }
  next();
}

/**
 * Simple function to check validation errors and throw if any exist
 */
export function validateRequest(req: Request) {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError("Validation failed", errors.array());
  }
}

/**
 * Higher-order function to wrap validation middleware
 */
export function validate(validations: any[]) {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Run all validations
    await Promise.all(validations.map((validation) => validation.run(req)));

    // Check for errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Validation failed", errors.array());
    }

    next();
  };
}
