# Teste do PIX - Sistema de Playlist do Restaurante
Write-Host "Testando sistema de pagamento PIX..." -ForegroundColor Green

# 1. Verificar configuração do Mercado Pago
Write-Host "`n1. Verificando configuração do Mercado Pago..." -ForegroundColor Yellow
try {
    $config = Invoke-RestMethod -Uri "http://localhost:8001/api/v1/payments/config" -Method GET
    Write-Host "✅ Configuração OK:" -ForegroundColor Green
    Write-Host "   - Access Token: $($config.config.accessTokenConfigured)" -ForegroundColor White
    Write-Host "   - Public Key: $($config.config.publicKeyConfigured)" -ForegroundColor White
    Write-Host "   - Environment: $($config.config.environment)" -ForegroundColor White
    Write-Host "   - Webhook URL: $($config.config.webhookUrl)" -ForegroundColor White
} catch {
    Write-Host "❌ Erro na configuração: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. Criar pagamento PIX
Write-Host "`n2. Criando pagamento PIX..." -ForegroundColor Yellow
$payload = @{
    restaurantId = "demo-restaurant"
    youtubeId = "xzvZfUwy2LQ"
    title = "Ta Tum Tum"
    artist = "MC Kevinho"
    clientName = "Cliente Teste PIX"
    clientMessage = "Teste de pagamento PIX"
    tableNumber = 15
    sessionId = "test-session-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    amount = 500
} | ConvertTo-Json -Depth 10

try {
    $response = Invoke-RestMethod -Uri "http://localhost:8001/api/v1/payments/pix/suggestion" -Method POST -ContentType "application/json" -Body $payload
    
    if ($response.success) {
        Write-Host "✅ PIX criado com sucesso!" -ForegroundColor Green
        Write-Host "   - Payment ID: $($response.payment.id)" -ForegroundColor White
        Write-Host "   - Status: $($response.payment.status)" -ForegroundColor White
        Write-Host "   - Valor: R$ $($response.payment.transaction_amount)" -ForegroundColor White
        Write-Host "   - PIX Code: $($response.pixCode.Substring(0, 50))..." -ForegroundColor White
        
        # Salvar QR Code se disponível
        if ($response.qrCodeData) {
            Write-Host "   - QR Code salvo em: qr_code_pix.png" -ForegroundColor White
        }
        
        Write-Host "`nPROXIMOS PASSOS:" -ForegroundColor Cyan
        Write-Host "1. Acesse seu painel do Mercado Pago (sandbox)" -ForegroundColor White
        Write-Host "2. Verifique se o pagamento apareceu na lista" -ForegroundColor White
        Write-Host "3. Use o PIX Code para simular o pagamento" -ForegroundColor White
        Write-Host "4. Aguarde o webhook confirmar o pagamento" -ForegroundColor White
        
    } else {
        Write-Host "❌ Erro ao criar PIX: $($response.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Erro na requisição: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Detalhes do erro: $responseBody" -ForegroundColor Red
    }
}

Write-Host "`nTeste concluido!" -ForegroundColor Green
