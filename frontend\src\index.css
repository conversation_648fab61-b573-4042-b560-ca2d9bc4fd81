@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* <PERSON>ontes */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap');

/* Variáveis CSS customizadas */
:root {
  --color-primary: theme('colors.primary.500');
  --color-secondary: theme('colors.secondary.500');
  --color-accent: theme('colors.accent.500');
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Tema escuro */
.dark {
  --color-primary: theme('colors.primary.400');
  --color-secondary: theme('colors.secondary.400');
  --color-accent: theme('colors.accent.400');
}

/* Reset e base styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Scrollbar customizada */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

/* Componentes customizados */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 dark:bg-primary-500 dark:hover:bg-primary-600;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500 dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600;
}

.btn-success {
  @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
}

.btn-danger {
  @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.btn-outline {
  @apply border border-gray-300 bg-transparent text-gray-700 hover:bg-gray-50 focus:ring-gray-500 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800;
}

.btn-sm {
  @apply px-3 py-1.5 text-xs;
}

.btn-lg {
  @apply px-6 py-3 text-base;
}

.card {
  @apply bg-white rounded-xl shadow-sm border border-gray-200 dark:bg-gray-800 dark:border-gray-700;
}

.card-hover {
  @apply transition-all duration-200 hover:shadow-md hover:-translate-y-0.5;
}

.input {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-500 dark:text-white;
}

.label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1;
}

/* Animações customizadas */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

.dark .shimmer {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200px 100%;
}

/* Efeitos de glassmorphism */
.glass {
  @apply backdrop-blur-md bg-white/80 dark:bg-gray-900/80;
}

.glass-card {
  @apply backdrop-blur-md bg-white/90 dark:bg-gray-800/90 border border-white/20 dark:border-gray-700/50;
}

/* Gradientes */
.gradient-primary {
  background: linear-gradient(135deg, theme('colors.primary.500'), theme('colors.primary.600'));
}

.gradient-secondary {
  background: linear-gradient(135deg, theme('colors.secondary.500'), theme('colors.secondary.600'));
}

.gradient-accent {
  background: linear-gradient(135deg, theme('colors.accent.500'), theme('colors.accent.600'));
}

/* Estados de loading */
.loading-dots {
  @apply inline-flex space-x-1;
}

.loading-dots > div {
  @apply w-2 h-2 bg-current rounded-full animate-pulse;
  animation-delay: calc(var(--i) * 0.2s);
}

/* Responsividade para texto */
.text-responsive {
  @apply text-sm sm:text-base lg:text-lg;
}

.heading-responsive {
  @apply text-lg sm:text-xl lg:text-2xl xl:text-3xl;
}

/* Utilitários para acessibilidade */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus-visible {
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800;
}

/* Estilos para componentes específicos */
.music-card {
  @apply card card-hover p-4 space-y-3;
}

.vote-button {
  @apply btn btn-sm transition-all duration-200 hover:scale-105 active:scale-95;
}

.queue-item {
  @apply flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200;
}

.suggestion-status {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.suggestion-status.pending {
  @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200;
}

.suggestion-status.approved {
  @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
}

.suggestion-status.rejected {
  @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
}

.suggestion-status.playing {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200;
}

/* Estilos para mobile */
@media (max-width: 768px) {
  .mobile-padding {
    @apply px-4 py-2;
  }
  
  .mobile-text {
    @apply text-sm;
  }
  
  .mobile-hidden {
    @apply hidden;
  }
}

/* Estilos para impressão */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
}

/* Estilos para modo de alto contraste */
@media (prefers-contrast: high) {
  .btn {
    @apply border-2;
  }
  
  .card {
    @apply border-2;
  }
}
