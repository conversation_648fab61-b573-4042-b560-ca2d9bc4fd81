import { Server as SocketIOServer, Socket } from "socket.io";
import jwt from "jsonwebtoken";
import { IsString, <PERSON>N<PERSON>ber, IsOptional, IsNotEmpty, Min, validate } from "class-validator";
import { plainToClass } from "class-transformer";
import { logger } from "../utils/logger";
import { redisClient } from "../config/redis";

/**
 * Interfaces para tipagem estrita dos dados WebSocket
 */
export interface ISuggestion {
  id: string;
  title: string;
  artist: string;
  youtubeVideoId: string;
  thumbnailUrl?: string;
  duration?: number;
  upvotes: number;
  downvotes: number;
  status: "pending" | "approved" | "rejected" | "playing" | "played";
  isPaid: boolean;
  paymentId?: string;
  sessionId: string;
  restaurantId: string;
  createdAt: Date;
}

export interface ITrack {
  id: string;
  title: string;
  artist: string;
  youtubeVideoId: string;
  duration: number;
  thumbnailUrl?: string;
  position?: number;
}

export interface IQueueItem {
  id: string;
  suggestionId: string;
  title: string;
  artist: string;
  duration: number;
  thumbnailUrl?: string;
  youtubeVideoId: string;
  isPaid: boolean;
  paymentAmount?: number;
  clientName?: string;
  tableName?: string;
  sessionId: string;
  addedAt: Date;
  estimatedPlayTime?: Date;
  position: number;
  priority: number;
}

export interface IConnectionStats {
  totalConnections: number;
  activeRooms: string[];
  restaurantConnections: Record<string, number>;
  adminConnections: Record<string, number>;
  tableConnections: Record<string, Record<string, number>>;
  timestamp: string;
}

export interface IWebSocketEvent {
  event: string;
  data: any;
  timestamp: string;
  restaurantId?: string;
  userId?: string;
  sessionId?: string;
}

/**
 * Classes de validação para parâmetros de entrada
 */
export class RestaurantEventDto {
  @IsString()
  @IsNotEmpty()
  restaurantId: string;

  @IsString()
  @IsNotEmpty()
  event: string;

  data: any;
}

export class TableEventDto extends RestaurantEventDto {
  @IsNumber()
  tableNumber: number;
}

export class VoteUpdateDto {
  @IsString()
  @IsNotEmpty()
  restaurantId: string;

  @IsString()
  @IsNotEmpty()
  suggestionId: string;

  @IsNumber()
  @Min(0)
  votes: number;
}

export class SuggestionStatusDto {
  @IsString()
  @IsNotEmpty()
  restaurantId: string;

  @IsString()
  @IsNotEmpty()
  suggestionId: string;

  @IsString()
  @IsNotEmpty()
  status: string;

  @IsOptional()
  @IsString()
  reason?: string;
}

/**
 * Classe de erro personalizada para WebSocket
 */
export class WebSocketError extends Error {
  public readonly code: string;
  public readonly statusCode: number;
  public readonly isOperational: boolean;

  constructor(
    message: string,
    code: string = "WEBSOCKET_ERROR",
    statusCode: number = 500,
    isOperational: boolean = true
  ) {
    super(message);
    this.name = "WebSocketError";
    this.code = code;
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Serviço WebSocket melhorado para gerenciar eventos em tempo real
 * da aplicação de playlist interativa para restaurantes.
 *
 * @class WebSocketService
 * @description Gerencia conexões WebSocket, autenticação, validação de dados,
 * tratamento de erros e estatísticas de conexão.
 *
 * @example
 * ```typescript
 * const wsService = WebSocketService.getInstance(io);
 * wsService.notifyNewSuggestion('restaurant-123', suggestionData);
 * ```
 */
export class WebSocketService {
  private static instance: WebSocketService;
  private io: SocketIOServer;
  private authenticatedSockets: Map<
    string,
    { userId: string; role: string; restaurantId?: string }
  > = new Map();
  private roomStats: Map<string, number> = new Map();
  private userSockets: Map<string, Set<string>> = new Map(); // Map userId -> Set of socket IDs

  constructor(io: SocketIOServer) {
    this.io = io;
    this.setupEventHandlers();
    this.startStatsCollection();
  }

  /**
   * Obtém a instância singleton do WebSocketService
   * @param io - Instância do Socket.IO Server
   * @returns Instância do WebSocketService
   */
  static getInstance(io?: SocketIOServer): WebSocketService {
    if (!WebSocketService.instance && io) {
      WebSocketService.instance = new WebSocketService(io);
    }
    return WebSocketService.instance;
  }

  /**
   * Configura os manipuladores de eventos WebSocket
   * @private
   */
  private setupEventHandlers(): void {
    this.io.on("connection", (socket: Socket) => {
      logger.info(`Nova conexão WebSocket: ${socket.id}`);

      // Evento de autenticação
      socket.on(
        "authenticate",
        async (data: { token: string; restaurantId?: string }) => {
          try {
            await this.authenticateSocket(socket, data);
          } catch (error) {
            this.handleSocketError(
              socket,
              error as Error,
              "AUTHENTICATION_FAILED"
            );
          }
        }
      );

      // Evento de junção a sala
      socket.on(
        "joinRoom",
        async (data: {
          restaurantId: string;
          tableNumber?: number;
          role?: string;
        }) => {
          try {
            await this.handleJoinRoom(socket, data);
          } catch (error) {
            this.handleSocketError(socket, error as Error, "JOIN_ROOM_FAILED");
          }
        }
      );

      // Evento de desconexão
      socket.on("disconnect", () => {
        this.handleDisconnect(socket);
      });

      // Evento de erro
      socket.on("error", (error: Error) => {
        this.handleSocketError(socket, error, "SOCKET_ERROR");
      });
    });
  }

  /**
   * Autentica um socket usando token JWT
   * @private
   */
  private async authenticateSocket(
    socket: Socket,
    data: { token: string; restaurantId?: string }
  ): Promise<void> {
    try {
      // Aqui você implementaria a verificação do JWT
      // Por simplicidade, vamos simular uma autenticação básica
      const { token, restaurantId } = data;

      if (!token) {
        throw new WebSocketError(
          "Token de autenticação obrigatório",
          "MISSING_TOKEN",
          401
        );
      }

      // Verificar JWT real
      const userData = this.verifyJwtToken(token);

      this.authenticatedSockets.set(socket.id, {
        userId: userData.userId,
        role: userData.role,
        restaurantId: restaurantId || userData.restaurantId,
      });

      socket.emit("authenticated", { success: true, userId: userData.userId });
      logger.info(
        `Socket ${socket.id} autenticado para usuário ${userData.userId}`
      );
    } catch (error) {
      socket.emit("authenticationError", {
        error:
          error instanceof WebSocketError
            ? error.message
            : "Falha na autenticação",
      });
      throw error;
    }
  }

  /**
   * Verifica token de autenticação (implementação simplificada)
   * @private
   */
  private verifyJwtToken(token: string): {
    userId: string;
    role: string;
    restaurantId?: string;
  } {
    try {
      const secret = process.env.JWT_SECRET;
      if (!secret) {
        throw new WebSocketError(
          "Configuração do servidor ausente (JWT_SECRET)",
          "SERVER_CONFIG_ERROR",
          500
        );
      }
      const decoded = jwt.verify(token, secret) as any;
      return {
        userId: decoded.userId || decoded.sub || "unknown-user",
        role: decoded.role || "client",
        restaurantId: decoded.restaurantId,
      };
    } catch (e) {
      throw new WebSocketError("Token inválido", "INVALID_TOKEN", 401);
    }
  }

  /**
   * Manipula a entrada de um socket em uma sala
   * @private
   */
  private async handleJoinRoom(
    socket: Socket,
    data: { restaurantId: string; tableNumber?: number; role?: string }
  ): Promise<void> {
    const { restaurantId, tableNumber } = data;

    // Validar dados de entrada (permite anônimo para sala pública)
    const validation = await this.validateRestaurantAccess(socket, restaurantId);
    if (!validation.isValid) {
      throw new WebSocketError(validation.error!, "ACCESS_DENIED", 403);
    }

    // Definir salas baseadas no papel do usuário (role derivada do token, não do payload do cliente)
    const authData = this.authenticatedSockets.get(socket.id);
    const rooms: string[] = [`restaurant-${restaurantId}`];

    if (authData && (authData.role === "admin" || authData.role === "staff")) {
      rooms.push(`restaurant-${restaurantId}-admins`);
    }

    if (tableNumber) {
      rooms.push(`restaurant-${restaurantId}-table-${tableNumber}`);
    }

    // Juntar-se às salas
    for (const room of rooms) {
      await socket.join(room);
      this.updateRoomStats(room, 1);
    }

    socket.emit("joinedRoom", { restaurantId, tableNumber, rooms });
    logger.info(`Socket ${socket.id} juntou-se às salas: ${rooms.join(", ")}`);
  }

  /**
   * Manipula a desconexão de um socket
   * @private
   */
  private handleDisconnect(socket: Socket): void {
    const authData = this.authenticatedSockets.get(socket.id);

    // Atualizar estatísticas das salas
    const rooms = Array.from(socket.rooms);
    rooms.forEach((room) => {
      if (room !== socket.id) {
        this.updateRoomStats(room, -1);
      }
    });

    this.authenticatedSockets.delete(socket.id);

    logger.info(`Socket ${socket.id} desconectado`, {
      userId: authData?.userId,
      rooms: rooms.filter((room) => room !== socket.id),
    });
  }

  /**
   * Manipula erros de socket
   * @private
   */
  private handleSocketError(socket: Socket, error: Error, code: string): void {
    const errorData = {
      code,
      message: error.message,
      timestamp: new Date().toISOString(),
      socketId: socket.id,
    };

    socket.emit("error", errorData);
    logger.error(`Erro no socket ${socket.id}:`, errorData);
  }

  /**
   * Valida se um socket tem acesso a um restaurante
   * @private
   */
  private async validateRestaurantAccess(
    socket: Socket,
    restaurantId: string
  ): Promise<{ isValid: boolean; error?: string }> {
    const authData = this.authenticatedSockets.get(socket.id);

    // Permitir acesso público (sem autenticação) à sala base do restaurante
    if (!authData) {
      return { isValid: true };
    }

    // Admins têm acesso a todos os restaurantes
    if (authData.role === "admin") {
      return { isValid: true };
    }

    // Verificar se o usuário tem acesso ao restaurante específico
    if (authData.restaurantId && authData.restaurantId !== restaurantId) {
      return { isValid: false, error: "Acesso negado ao restaurante" };
    }

    return { isValid: true };
  }

  /**
   * Atualiza estatísticas de salas
   * @private
   */
  private updateRoomStats(room: string, delta: number): void {
    const current = this.roomStats.get(room) || 0;
    const newCount = Math.max(0, current + delta);

    if (newCount === 0) {
      this.roomStats.delete(room);
    } else {
      this.roomStats.set(room, newCount);
    }
  }

  /**
   * Inicia coleta de estatísticas periódicas
   * @private
   */
  private startStatsCollection(): void {
    setInterval(() => {
      this.collectAndCacheStats();
    }, 30000); // A cada 30 segundos
  }

  /**
   * Coleta e armazena estatísticas no Redis
   * @private
   */
  private async collectAndCacheStats(): Promise<void> {
    try {
      const stats = this.getConnectionStats();
      await redisClient.getClient().setEx(
        "websocket:stats",
        300, // 5 minutos
        JSON.stringify(stats)
      );
    } catch (error) {
      logger.error("Erro ao coletar estatísticas WebSocket:", error);
    }
  }

  /**
   * Valida dados de entrada usando class-validator
   * @private
   */
  private async validateInput<T extends object>(
    dto: new () => T,
    data: any
  ): Promise<T> {
    const instance = plainToClass(dto, data);
    const errors = await validate(instance);

    if (errors.length > 0) {
      const errorMessages = errors
        .map((error) => Object.values(error.constraints || {}).join(", "))
        .join("; ");

      throw new WebSocketError(
        `Dados inválidos: ${errorMessages}`,
        "VALIDATION_ERROR",
        400
      );
    }

    return instance;
  }

  /**
   * Verifica se uma sala está ativa antes de emitir eventos
   * @private
   */
  private isRoomActive(room: string): boolean {
    return (this.roomStats.get(room) || 0) > 0;
  }

  /**
   * Emite evento para todos os clientes de um restaurante com validação
   * @param restaurantId - ID do restaurante
   * @param event - Nome do evento
   * @param data - Dados do evento
   */
  async emitToRestaurant(
    restaurantId: string,
    event: string,
    data: any
  ): Promise<void> {
    try {
      // Validar entrada
      await this.validateInput(RestaurantEventDto, {
        restaurantId,
        event,
        data,
      });

      const room = `restaurant-${restaurantId}`;

      // Verificar se a sala está ativa
      if (!this.isRoomActive(room)) {
        logger.warn(`Tentativa de emitir para sala inativa: ${room}`);
        return;
      }

      const eventData: IWebSocketEvent = {
        event,
        data,
        timestamp: new Date().toISOString(),
        restaurantId,
      };

      this.io.to(room).emit(event, eventData);

      logger.info(`Evento ${event} emitido para restaurante ${restaurantId}`, {
        room,
        dataSize: JSON.stringify(data).length,
        connections: this.roomStats.get(room) || 0,
      });
    } catch (error) {
      logger.error("Erro ao emitir evento para restaurante:", error);
      throw new WebSocketError(
        `Falha ao emitir evento: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
        "EMIT_ERROR"
      );
    }
  }

  /**
   * Alias para broadcastToRestaurant (usado no PlaybackService)
   * @param restaurantId - ID do restaurante
   * @param event - Nome do evento
   * @param data - Dados do evento
   */
  async broadcastToRestaurant(
    restaurantId: string,
    event: string,
    data: any
  ): Promise<void> {
    await this.emitToRestaurant(restaurantId, event, data);
  }

  /**
   * Emite evento para um usuário específico
   * @param userId - ID do usuário
   * @param event - Nome do evento
   * @param data - Dados do evento
   */
  async emitToUser(userId: string, event: string, data: any): Promise<void> {
    try {
      const socketIds = this.userSockets.get(userId);
      if (socketIds && socketIds.size > 0) {
        for (const socketId of socketIds) {
          this.io.to(socketId).emit(event, data);
        }
      }
    } catch (error) {
      logger.error(`Erro ao emitir evento para usuário ${userId}:`, error);
    }
  }

  /**
   * Broadcast global para todos os conectados
   * @param event - Nome do evento
   * @param data - Dados do evento
   */
  async broadcast(event: string, data: any): Promise<void> {
    try {
      this.io.emit(event, data);
    } catch (error) {
      logger.error("Erro ao fazer broadcast:", error);
    }
  }

  /**
   * Emite evento para uma mesa específica com validação
   * @param restaurantId - ID do restaurante
   * @param tableNumber - Número da mesa
   * @param event - Nome do evento
   * @param data - Dados do evento
   */
  async emitToTable(
    restaurantId: string,
    tableNumber: number,
    event: string,
    data: any
  ): Promise<void> {
    try {
      // Validar entrada
      await this.validateInput(TableEventDto, {
        restaurantId,
        tableNumber,
        event,
        data,
      });

      const room = `restaurant-${restaurantId}-table-${tableNumber}`;

      // Verificar se a sala está ativa
      if (!this.isRoomActive(room)) {
        logger.warn(`Tentativa de emitir para mesa inativa: ${room}`);
        return;
      }

      const eventData: IWebSocketEvent = {
        event,
        data,
        timestamp: new Date().toISOString(),
        restaurantId,
      };

      this.io.to(room).emit(event, eventData);

      logger.info(
        `Evento ${event} emitido para mesa ${tableNumber} do restaurante ${restaurantId}`,
        {
          room,
          dataSize: JSON.stringify(data).length,
          connections: this.roomStats.get(room) || 0,
        }
      );
    } catch (error) {
      logger.error("Erro ao emitir evento para mesa:", error);
      throw new WebSocketError(
        `Falha ao emitir evento para mesa: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
        "EMIT_TABLE_ERROR"
      );
    }
  }

  /**
   * Emite evento para administradores do restaurante com autenticação
   * @param restaurantId - ID do restaurante
   * @param event - Nome do evento
   * @param data - Dados do evento
   * @param requiredRole - Papel mínimo necessário (padrão: 'admin')
   */
  async emitToAdmins(
    restaurantId: string,
    event: string,
    data: any,
    requiredRole: string = "admin"
  ): Promise<void> {
    try {
      // Validar entrada
      await this.validateInput(RestaurantEventDto, {
        restaurantId,
        event,
        data,
      });

      const room = `restaurant-${restaurantId}-admins`;

      // Verificar se a sala está ativa
      if (!this.isRoomActive(room)) {
        logger.warn(`Tentativa de emitir para admins inativos: ${room}`);
        return;
      }

      // Verificar autorização dos sockets conectados
      const authorizedSockets = Array.from(this.authenticatedSockets.entries())
        .filter(([socketId, authData]) => {
          const socket = this.io.sockets.sockets.get(socketId);
          return (
            socket &&
            socket.rooms.has(room) &&
            this.hasRequiredRole(authData.role, requiredRole)
          );
        })
        .map(([socketId]) => socketId);

      if (authorizedSockets.length === 0) {
        logger.warn(`Nenhum admin autorizado encontrado para ${room}`);
        return;
      }

      const eventData: IWebSocketEvent = {
        event,
        data,
        timestamp: new Date().toISOString(),
        restaurantId,
      };

      // Emitir apenas para sockets autorizados
      authorizedSockets.forEach((socketId) => {
        this.io.to(socketId).emit(event, eventData);
      });

      logger.info(
        `Evento ${event} emitido para admins do restaurante ${restaurantId}`,
        {
          room,
          authorizedSockets: authorizedSockets.length,
          requiredRole,
          dataSize: JSON.stringify(data).length,
        }
      );
    } catch (error) {
      logger.error("Erro ao emitir evento para admins:", error);
      throw new WebSocketError(
        `Falha ao emitir evento para admins: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
        "EMIT_ADMIN_ERROR"
      );
    }
  }

  /**
   * Verifica se um papel tem a autorização necessária
   * @private
   */
  private hasRequiredRole(userRole: string, requiredRole: string): boolean {
    const roleHierarchy = ["client", "staff", "moderator", "admin"];
    const userLevel = roleHierarchy.indexOf(userRole);
    const requiredLevel = roleHierarchy.indexOf(requiredRole);

    return userLevel >= requiredLevel;
  }

  // Emitir evento global para todos os clientes conectados
  emitGlobal(event: string, data: any): void {
    try {
      this.io.emit(event, data);
      logger.info(`Evento global ${event} emitido`, { data });
    } catch (error) {
      logger.error("Erro ao emitir evento global:", error);
    }
  }

  /**
   * Notifica sobre uma nova sugestão musical
   * @param restaurantId - ID do restaurante
   * @param suggestion - Dados da sugestão
   */
  async notifyNewSuggestion(
    restaurantId: string,
    suggestion: ISuggestion
  ): Promise<void> {
    try {
      // Emitir para todos os clientes
      await this.emitToRestaurant(restaurantId, "newSuggestion", suggestion);

      // Emitir versão detalhada para admins
      const adminData = {
        ...suggestion,
        metadata: {
          clientIP: suggestion.sessionId,
          timestamp: suggestion.createdAt,
          requiresModeration: !suggestion.isPaid,
        },
      };

      await this.emitToAdmins(restaurantId, "newSuggestionAdmin", adminData);

      logger.info(
        `Nova sugestão notificada: ${suggestion.title} no restaurante ${restaurantId}`
      );
    } catch (error) {
      logger.error("Erro ao notificar nova sugestão:", error);
      throw new WebSocketError(
        "Falha ao notificar nova sugestão",
        "NOTIFY_SUGGESTION_ERROR"
      );
    }
  }

  /**
   * Notifica sobre sugestão prioritária (paga)
   * @param restaurantId - ID do restaurante
   * @param suggestion - Dados da sugestão prioritária
   */
  async notifyPrioritySuggestion(
    restaurantId: string,
    suggestion: ISuggestion
  ): Promise<void> {
    try {
      const priorityData = {
        ...suggestion,
        priority: true,
        paymentConfirmed: true,
        timestamp: new Date().toISOString(),
      };

      await this.emitToRestaurant(
        restaurantId,
        "prioritySuggestion",
        priorityData
      );
      await this.emitToAdmins(
        restaurantId,
        "adminPrioritySuggestion",
        priorityData
      );

      logger.info(
        `Sugestão prioritária notificada: ${suggestion.title} no restaurante ${restaurantId}`
      );
    } catch (error) {
      logger.error("Erro ao notificar sugestão prioritária:", error);
      throw new WebSocketError(
        "Falha ao notificar sugestão prioritária",
        "NOTIFY_PRIORITY_ERROR"
      );
    }
  }

  /**
   * Notifica sobre atualização de votos
   * @param restaurantId - ID do restaurante
   * @param suggestionId - ID da sugestão
   * @param votes - Dados dos votos atualizados
   */
  async notifyVoteUpdate(
    restaurantId: string,
    suggestionId: string,
    votes: { upvotes: number; downvotes: number; total: number }
  ): Promise<void> {
    try {
      // Validar entrada
      await this.validateInput(VoteUpdateDto, {
        restaurantId,
        suggestionId,
        votes: votes.total,
      });

      const voteData = {
        suggestionId,
        votes,
        timestamp: new Date().toISOString(),
      };

      await this.emitToRestaurant(restaurantId, "voteUpdate", voteData);

      logger.info(
        `Votos atualizados para sugestão ${suggestionId}: ${votes.total} votos`
      );
    } catch (error) {
      logger.error("Erro ao notificar atualização de votos:", error);
      throw new WebSocketError(
        "Falha ao notificar atualização de votos",
        "NOTIFY_VOTE_ERROR"
      );
    }
  }

  /**
   * Notifica sobre mudança de status de sugestão
   * @param restaurantId - ID do restaurante
   * @param suggestionId - ID da sugestão
   * @param status - Novo status
   * @param reason - Motivo da mudança (opcional)
   */
  async notifySuggestionStatusUpdate(
    restaurantId: string,
    suggestionId: string,
    status: string,
    reason?: string
  ): Promise<void> {
    try {
      // Validar entrada
      await this.validateInput(SuggestionStatusDto, {
        restaurantId,
        suggestionId,
        status,
        reason,
      });

      const statusData = {
        suggestionId,
        status,
        reason,
        timestamp: new Date().toISOString(),
      };

      await this.emitToRestaurant(
        restaurantId,
        "suggestionStatusUpdate",
        statusData
      );

      // Notificar admins com informações adicionais
      const adminStatusData = {
        ...statusData,
        metadata: {
          moderatedBy: "system", // Em produção, pegar do contexto de autenticação
          previousStatus: "pending", // Em produção, buscar do banco
        },
      };

      await this.emitToAdmins(
        restaurantId,
        "suggestionStatusUpdateAdmin",
        adminStatusData
      );

      logger.info(
        `Status da sugestão ${suggestionId} atualizado para ${status}`
      );
    } catch (error) {
      logger.error("Erro ao notificar atualização de status:", error);
      throw new WebSocketError(
        "Falha ao notificar atualização de status",
        "NOTIFY_STATUS_ERROR"
      );
    }
  }

  /**
   * Notifica sobre início de reprodução
   * @param restaurantId - ID do restaurante
   * @param track - Dados da faixa
   */
  async notifyPlaybackStart(
    restaurantId: string,
    track: ITrack
  ): Promise<void> {
    try {
      const playbackData = {
        track,
        event: "start",
        timestamp: new Date().toISOString(),
        serverTime: Date.now(),
      };

      await this.emitToRestaurant(restaurantId, "playbackStart", playbackData);

      logger.info(
        `Reprodução iniciada: ${track.title} no restaurante ${restaurantId}`
      );
    } catch (error) {
      logger.error("Erro ao notificar início de reprodução:", error);
      throw new WebSocketError(
        "Falha ao notificar início de reprodução",
        "NOTIFY_PLAYBACK_START_ERROR"
      );
    }
  }

  /**
   * Notifica sobre fim de reprodução
   * @param restaurantId - ID do restaurante
   * @param track - Dados da faixa
   */
  async notifyPlaybackEnd(restaurantId: string, track: ITrack): Promise<void> {
    try {
      const playbackData = {
        track,
        event: "end",
        timestamp: new Date().toISOString(),
        serverTime: Date.now(),
      };

      await this.emitToRestaurant(restaurantId, "playbackEnd", playbackData);

      logger.info(
        `Reprodução finalizada: ${track.title} no restaurante ${restaurantId}`
      );
    } catch (error) {
      logger.error("Erro ao notificar fim de reprodução:", error);
      throw new WebSocketError(
        "Falha ao notificar fim de reprodução",
        "NOTIFY_PLAYBACK_END_ERROR"
      );
    }
  }

  /**
   * Notifica sobre atualização da fila de reprodução
   * @param restaurantId - ID do restaurante
   * @param queue - Fila atualizada
   */
  async notifyQueueUpdate(
    restaurantId: string,
    queue: IQueueItem[]
  ): Promise<void> {
    try {
      const queueData = {
        queue,
        totalItems: queue.length,
        priorityItems: queue.filter((item) => item.isPaid).length,
        normalItems: queue.filter((item) => !item.isPaid).length,
        estimatedDuration: queue.reduce(
          (total, item) => total + item.duration,
          0
        ),
        timestamp: new Date().toISOString(),
      };

      await this.emitToRestaurant(restaurantId, "queueUpdate", queueData);

      logger.info(
        `Fila atualizada para restaurante ${restaurantId}: ${queue.length} itens`
      );
    } catch (error) {
      logger.error("Erro ao notificar atualização da fila:", error);
      throw new WebSocketError(
        "Falha ao notificar atualização da fila",
        "NOTIFY_QUEUE_ERROR"
      );
    }
  }

  /**
   * Notifica atualização de reprodução
   * @param restaurantId - ID do restaurante
   * @param data - Dados da atualização
   */
  async notifyPlaybackUpdate(restaurantId: string, data: any): Promise<void> {
    try {
      const updateData = {
        ...data,
        timestamp: new Date().toISOString(),
        serverTime: Date.now(),
      };

      await this.emitToRestaurant(restaurantId, "playbackUpdate", updateData);

      logger.info(
        `Atualização de reprodução notificada para restaurante ${restaurantId}`
      );
    } catch (error) {
      logger.error("Erro ao notificar atualização de reprodução:", error);
      throw new WebSocketError(
        "Falha ao notificar atualização de reprodução",
        "NOTIFY_PLAYBACK_UPDATE_ERROR"
      );
    }
  }

  /**
   * Notifica sobre pagamento PIX aprovado
   * @param restaurantId - ID do restaurante
   * @param paymentId - ID do pagamento
   * @param suggestionId - ID da sugestão
   */
  async notifyPaymentApproved(
    restaurantId: string,
    paymentId: string,
    suggestionId: string
  ): Promise<void> {
    try {
      const paymentData = {
        paymentId,
        suggestionId,
        status: "approved",
        timestamp: new Date().toISOString(),
      };

      await this.emitToRestaurant(restaurantId, "paymentApproved", paymentData);
      await this.emitToAdmins(
        restaurantId,
        "adminPaymentApproved",
        paymentData
      );

      logger.info(
        `Pagamento aprovado: ${paymentId} para sugestão ${suggestionId}`
      );
    } catch (error) {
      logger.error("Erro ao notificar pagamento aprovado:", error);
      throw new WebSocketError(
        "Falha ao notificar pagamento aprovado",
        "NOTIFY_PAYMENT_APPROVED_ERROR"
      );
    }
  }

  /**
   * Notifica sobre erro de pagamento
   * @param restaurantId - ID do restaurante
   * @param paymentId - ID do pagamento
   * @param error - Descrição do erro
   */
  async notifyPaymentError(
    restaurantId: string,
    paymentId: string,
    error: string
  ): Promise<void> {
    try {
      const errorData = {
        paymentId,
        error,
        status: "failed",
        timestamp: new Date().toISOString(),
      };

      await this.emitToRestaurant(restaurantId, "paymentError", errorData);
      await this.emitToAdmins(restaurantId, "adminPaymentError", errorData);

      logger.error(`Erro de pagamento: ${paymentId} - ${error}`);
    } catch (error) {
      logger.error("Erro ao notificar erro de pagamento:", error);
      throw new WebSocketError(
        "Falha ao notificar erro de pagamento",
        "NOTIFY_PAYMENT_ERROR_ERROR"
      );
    }
  }

  /**
   * Obtém estatísticas detalhadas de conexões
   * @returns Estatísticas completas das conexões WebSocket
   */
  getConnectionStats(): IConnectionStats {
    const sockets = this.io.sockets.sockets;
    const restaurantConnections: Record<string, number> = {};
    const adminConnections: Record<string, number> = {};
    const tableConnections: Record<string, Record<string, number>> = {};
    const activeRooms: string[] = [];

    // Analisar conexões por tipo
    this.authenticatedSockets.forEach((authData, socketId) => {
      const socket = sockets.get(socketId);
      if (!socket) return;

      const { restaurantId, role } = authData;

      if (restaurantId) {
        restaurantConnections[restaurantId] =
          (restaurantConnections[restaurantId] || 0) + 1;

        if (role === "admin" || role === "staff") {
          adminConnections[restaurantId] =
            (adminConnections[restaurantId] || 0) + 1;
        }
      }

      // Analisar salas de mesa
      socket.rooms.forEach((room) => {
        if (room.includes("-table-")) {
          const [, restaurantPart, , tableNumber] = room.split("-");
          const restId = restaurantPart;

          if (!tableConnections[restId]) {
            tableConnections[restId] = {};
          }

          tableConnections[restId][tableNumber] =
            (tableConnections[restId][tableNumber] || 0) + 1;
        }
      });
    });

    // Coletar salas ativas
    this.roomStats.forEach((count, room) => {
      if (count > 0) {
        activeRooms.push(room);
      }
    });

    return {
      totalConnections: sockets.size,
      activeRooms,
      restaurantConnections,
      adminConnections,
      tableConnections,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Obtém estatísticas de uma sala específica
   * @param room - Nome da sala
   * @returns Número de conexões na sala
   */
  getRoomStats(room: string): number {
    return this.roomStats.get(room) || 0;
  }

  /**
   * Limpa conexões inativas e atualiza estatísticas
   */
  async cleanupInactiveConnections(): Promise<void> {
    try {
      const sockets = this.io.sockets.sockets;

      // Remover sockets autenticados que não existem mais
      for (const [socketId] of this.authenticatedSockets) {
        if (!sockets.has(socketId)) {
          this.authenticatedSockets.delete(socketId);
        }
      }

      // Atualizar estatísticas das salas
      const activeRooms = new Map<string, number>();

      sockets.forEach((socket) => {
        socket.rooms.forEach((room) => {
          if (room !== socket.id) {
            activeRooms.set(room, (activeRooms.get(room) || 0) + 1);
          }
        });
      });

      this.roomStats = activeRooms;

      logger.info("Limpeza de conexões inativas concluída", {
        authenticatedSockets: this.authenticatedSockets.size,
        activeRooms: this.roomStats.size,
      });
    } catch (error) {
      logger.error("Erro na limpeza de conexões:", error);
    }
  }
}
