import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  Settings,
  Shield,
  ShieldOff,
  Play,
  Pause,
  Users,
  Music,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  XCircle,
  RefreshCw,
  ExternalLink,
} from "lucide-react";
import { toast } from "react-hot-toast";
import apiService from "@/services/api";

interface Restaurant {
  id: string;
  name: string;
  email: string;
  description: string;
  phone?: string;
  address?: string;
  isActive: boolean;
  status: 'active' | 'inactive' | 'suspended' | 'trial';
  createdAt: string;
  lastActivityAt?: string;
  // Dados do usuário admin
  adminUser?: {
    id: string;
    email: string;
    name: string;
    isActive: boolean;
  };
  settings?: {
    allowSuggestions: boolean;
  };
}

const RestaurantManagement: React.FC = () => {
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingRestaurant, setEditingRestaurant] = useState<Restaurant | null>(
    null
  );
  const [editingUser, setEditingUser] = useState({
    email: "",
    password: "",
  });
  const [newRestaurant, setNewRestaurant] = useState({
    name: "",
    email: "",
    description: "",
    address: "",
    phone: "",
    password: "",
  });

  useEffect(() => {
    loadRestaurants();
  }, []);

  const loadRestaurants = async (isManualRefresh = false) => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.client.get("/admin/restaurants");
      console.log("Resposta da API:", response.data);

      // Mapear os dados reais dos restaurantes
      const restaurantsData = response.data.restaurants?.map((restaurant: any) => ({
        ...restaurant,
        status: restaurant.isActive ? 'active' : 'inactive',
      })) || [];

      setRestaurants(restaurantsData);
      
      // Mostrar toast de sucesso apenas para recarregamentos manuais
      if (isManualRefresh) {
        toast.success("Lista de restaurantes atualizada com sucesso!");
      }
    } catch (error) {
      console.error("Erro ao carregar restaurantes:", error);
      setError("Erro ao carregar restaurantes");

      // Fallback com dados reais em caso de erro
      const fallbackRestaurants = [
        {
          id: "demo-restaurant",
          name: "Restaurante Demo",
          email: "<EMAIL>",
          description: "Restaurante de demonstração",
          phone: "(11) 99999-9999",
          address: "Rua Demo, 123",
          isActive: true,
          status: 'active' as const,
          createdAt: new Date().toISOString(),
          settings: {
            allowSuggestions: true,
          }
        }
      ];

      setRestaurants(fallbackRestaurants);
      toast.error("Erro ao carregar restaurantes - usando dados de demonstração");
    } finally {
      setLoading(false);
    }
  };

  const createRestaurant = async () => {
    try {
      const response = await apiService.client.post(
        "/admin/restaurants",
        newRestaurant
      );

      const data = response.data;
      toast.success("Restaurante criado com sucesso!");

      // Mostrar credenciais de login
      toast.success(
        `Login: ${data.credentials.email}\nSenha: ${data.credentials.password}\nURL: ${data.loginUrl}`,
        { duration: 10000 }
      );

      setShowCreateModal(false);
      setNewRestaurant({
        name: "",
        email: "",
        description: "",
        address: "",
        phone: "",
        password: "",
      });
      loadRestaurants();
    } catch (error: any) {
      console.error("Erro ao criar restaurante:", error);
      toast.error(error.response?.data?.error || "Erro ao criar restaurante");
    }
  };

  const updateRestaurant = async () => {
    if (!editingRestaurant) return;

    try {
      const updateData: any = {
        name: editingRestaurant.name,
        description: editingRestaurant.description,
        phone: editingRestaurant.phone,
        address: editingRestaurant.address,
      };

      // Incluir email e senha se foram alterados
      if (editingUser.email.trim()) {
        updateData.email = editingUser.email;
      }
      if (editingUser.password.trim()) {
        updateData.password = editingUser.password;
      }

      const response = await apiService.client.put(
        `/admin/restaurants/${editingRestaurant.id}`,
        updateData
      );

      toast.success("Restaurante atualizado com sucesso!");
      setEditingRestaurant(null);
      setEditingUser({ email: "", password: "" });
      loadRestaurants();
    } catch (error: any) {
      console.error("Erro ao atualizar restaurante:", error);
      toast.error(error.response?.data?.error || "Erro ao atualizar restaurante");
    }
  };

  const toggleRestaurantStatus = async (
    id: string,
    isActive: boolean
  ) => {
    try {
      const response = await apiService.client.patch(
        `/admin/restaurants/${id}/status`,
        { isActive }
      );

      const action = isActive ? "ativado" : "desativado";
      toast.success(`Restaurante ${action} com sucesso!`);
      loadRestaurants();
    } catch (error: any) {
      console.error("Erro ao atualizar restaurante:", error);
      toast.error(
        error.response?.data?.error || "Erro ao atualizar restaurante"
      );
    }
  };

  const suspendRestaurant = async (id: string) => {
    if (!confirm("Tem certeza que deseja suspender este restaurante?")) return;

    try {
      await apiService.client.patch(`/admin/restaurants/${id}/status`, { isActive: false });
      toast.success("Restaurante suspenso com sucesso!");
      loadRestaurants();
    } catch (error: any) {
      console.error("Erro ao suspender restaurante:", error);
      toast.error(error.response?.data?.error || "Erro ao suspender restaurante");
    }
  };

  const reactivateRestaurant = async (id: string) => {
    try {
      await apiService.client.patch(`/admin/restaurants/${id}/status`, { isActive: true });
      toast.success("Restaurante reativado com sucesso!");
      loadRestaurants();
    } catch (error: any) {
      console.error("Erro ao reativar restaurante:", error);
      toast.error(error.response?.data?.error || "Erro ao reativar restaurante");
    }
  };

  const deleteRestaurant = async (id: string) => {
    if (
      !confirm(
        "Tem certeza que deseja deletar este restaurante? Esta ação não pode ser desfeita."
      )
    ) {
      return;
    }

    try {
      await apiService.client.delete(`/admin/restaurants/${id}`);
      toast.success("Restaurante deletado com sucesso!");
      loadRestaurants();
    } catch (error: any) {
      console.error("Erro ao deletar restaurante:", error);
      toast.error(error.response?.data?.error || "Erro ao deletar restaurante");
    }
  };

  const getStatusColor = (restaurant: Restaurant) => {
    switch (restaurant.status) {
      case 'active': return "text-green-600 bg-green-100 dark:bg-green-900/20";
      case 'inactive': return "text-gray-600 bg-gray-100 dark:bg-gray-900/20";
      case 'suspended': return "text-red-600 bg-red-100 dark:bg-red-900/20";
      case 'trial': return "text-blue-600 bg-blue-100 dark:bg-blue-900/20";
      default: return "text-gray-600 bg-gray-100 dark:bg-gray-900/20";
    }
  };

  const getStatusText = (restaurant: Restaurant) => {
    switch (restaurant.status) {
      case 'active': return "Ativo";
      case 'inactive': return "Inativo";
      case 'suspended': return "Suspenso";
      case 'trial': return "Trial";
      default: return "Desconhecido";
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Gerenciamento de Restaurantes
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Gerencie todos os restaurantes da plataforma
            </p>
          </div>
        </div>
        
        <div className="flex flex-col justify-center items-center h-64 space-y-4">
          <div className="relative">
            <RefreshCw className="w-12 h-12 animate-spin text-blue-600" />
            <div className="absolute inset-0 w-12 h-12 border-4 border-blue-100 dark:border-blue-900/20 rounded-full animate-pulse"></div>
          </div>
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Carregando restaurantes
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Por favor, aguarde...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (error && restaurants.length === 0) {
    return (
      <div className="flex flex-col justify-center items-center h-64 space-y-4">
        <AlertCircle className="w-12 h-12 text-red-500" />
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Erro ao carregar restaurantes
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            {error}
          </p>
          <button
            onClick={() => loadRestaurants(true)}
            disabled={loading}
            className="mt-6 inline-flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <RefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
            <span>Tentar novamente</span>
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Gerenciamento de Restaurantes
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Gerencie todos os restaurantes da plataforma
          </p>
        </div>

        <div className="flex items-center space-x-3">
          <button
            onClick={() => loadRestaurants(true)}
            disabled={loading}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Recarregar lista de restaurantes"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span>Recarregar</span>
          </button>
          
          <button
            onClick={() => setShowCreateModal(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            <Plus className="w-4 h-4" />
            <span>Novo Restaurante</span>
          </button>
        </div>
      </div>

      {/* Lista de Restaurantes */}
      {restaurants.length === 0 ? (
        <div className="text-center py-12">
          <div className="mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
            <Users className="w-12 h-12 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Nenhum restaurante encontrado
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Comece criando seu primeiro restaurante na plataforma.
          </p>
          <button
            onClick={() => setShowCreateModal(true)}
            className="inline-flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="w-5 h-5" />
            <span>Criar Primeiro Restaurante</span>
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {restaurants.map((restaurant) => (
          <motion.div
            key={restaurant.id}
            layout
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-100 dark:border-gray-700 hover:shadow-xl transition-all duration-300"
          >
            {/* Header do Card */}
            <div className="flex justify-between items-start mb-6">
              <div className="flex-1">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                  {restaurant.name}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 flex items-center">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                  </svg>
                  {restaurant.email}
                </p>
              </div>

              <div
                className={`px-3 py-1 rounded-full text-xs font-semibold ${getStatusColor(
                  restaurant
                )}`}
              >
                {getStatusText(restaurant)}
              </div>
            </div>

            {/* Descrição */}
            {restaurant.description && (
              <div className="mb-4">
                <p className="text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg">
                  {restaurant.description}
                </p>
              </div>
            )}

            {/* Informações de Contato */}
            <div className="space-y-2 mb-6">
              {restaurant.phone && (
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                  </svg>
                  {restaurant.phone}
                </div>
              )}
              
              {restaurant.address && (
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                  {restaurant.address}
                </div>
              )}
            </div>

            {/* Informações de Sistema */}
            <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 mb-6 space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">Sugestões</span>
                <span className={`font-medium ${restaurant.settings?.allowSuggestions ? 'text-green-600' : 'text-red-600'}`}>
                  {restaurant.settings?.allowSuggestions ? 'Ativas' : 'Inativas'}
                </span>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">Criado em</span>
                <span className="text-gray-900 dark:text-white font-medium">
                  {new Date(restaurant.createdAt).toLocaleDateString('pt-BR')}
                </span>
              </div>
              
              {restaurant.lastActivityAt && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Última atividade</span>
                  <span className="text-gray-900 dark:text-white font-medium">
                    {new Date(restaurant.lastActivityAt).toLocaleDateString('pt-BR')}
                  </span>
                </div>
              )}
            </div>

            {/* Ações Administrativas */}
            <div className="flex flex-col gap-3">
              {/* Ação Principal */}
              <button
                onClick={() => window.open(`/restaurant/${restaurant.id}/dashboard`, "_blank")}
                className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium"
                title="Acessar Dashboard do Restaurante"
              >
                <ExternalLink className="w-4 h-4" />
                <span>Acessar Dashboard</span>
              </button>

              {/* Ações Secundárias */}
              <div className="grid grid-cols-2 gap-2">
                <button
                  onClick={() => {
                    setEditingRestaurant(restaurant);
                    // Pré-preencher dados do usuário se existirem
                    if ((restaurant as any).adminUser) {
                      setEditingUser({
                        email: (restaurant as any).adminUser.email || "",
                        password: "",
                      });
                    } else {
                      setEditingUser({ email: "", password: "" });
                    }
                  }}
                  className="flex items-center justify-center space-x-1 px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm"
                  title="Editar Restaurante"
                >
                  <Edit className="w-4 h-4" />
                  <span>Editar</span>
                </button>

                <button
                  onClick={() => toggleRestaurantStatus(restaurant.id, !restaurant.isActive)}
                  className={`flex items-center justify-center space-x-1 px-3 py-2 rounded-lg transition-colors text-sm font-medium ${
                    restaurant.isActive
                      ? "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400 hover:bg-yellow-200 dark:hover:bg-yellow-900/50"
                      : "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 hover:bg-green-200 dark:hover:bg-green-900/50"
                  }`}
                  title={restaurant.isActive ? "Desativar Restaurante" : "Ativar Restaurante"}
                >
                  {restaurant.isActive ? (
                    <Pause className="w-4 h-4" />
                  ) : (
                    <Play className="w-4 h-4" />
                  )}
                  <span>{restaurant.isActive ? "Pausar" : "Ativar"}</span>
                </button>
              </div>

              {/* Ações de Risco */}
              <div className="grid grid-cols-2 gap-2">
                {restaurant.status !== 'suspended' ? (
                  <button
                    onClick={() => suspendRestaurant(restaurant.id)}
                    className="flex items-center justify-center space-x-1 px-3 py-2 bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-400 rounded-lg hover:bg-orange-200 dark:hover:bg-orange-900/50 transition-colors text-sm"
                    title="Suspender Restaurante"
                  >
                    <Shield className="w-4 h-4" />
                    <span>Suspender</span>
                  </button>
                ) : (
                  <button
                    onClick={() => reactivateRestaurant(restaurant.id)}
                    className="flex items-center justify-center space-x-1 px-3 py-2 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors text-sm"
                    title="Reativar Restaurante"
                  >
                    <ShieldOff className="w-4 h-4" />
                    <span>Reativar</span>
                  </button>
                )}

                <button
                  onClick={() => deleteRestaurant(restaurant.id)}
                  className="flex items-center justify-center space-x-1 px-3 py-2 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 rounded-lg hover:bg-red-200 dark:hover:bg-red-900/50 transition-colors text-sm"
                  title="Deletar Restaurante"
                >
                  <Trash2 className="w-4 h-4" />
                  <span>Deletar</span>
                </button>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
      )}

      {/* Modal de Criação */}
      <AnimatePresence>
        {showCreateModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto"
          >
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Criar Novo Restaurante
            </h3>              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Nome do Restaurante *
                  </label>
                  <input
                    type="text"
                    value={newRestaurant.name}
                    onChange={(e) =>
                      setNewRestaurant({
                        ...newRestaurant,
                        name: e.target.value,
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="Ex: Restaurante do João"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Email de Login *
                  </label>
                  <input
                    type="email"
                    value={newRestaurant.email}
                    onChange={(e) =>
                      setNewRestaurant({
                        ...newRestaurant,
                        email: e.target.value,
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Senha *
                  </label>
                  <input
                    type="password"
                    value={newRestaurant.password}
                    onChange={(e) =>
                      setNewRestaurant({
                        ...newRestaurant,
                        password: e.target.value,
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="Senha segura"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Descrição
                  </label>
                  <textarea
                    value={newRestaurant.description}
                    onChange={(e) =>
                      setNewRestaurant({
                        ...newRestaurant,
                        description: e.target.value,
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    rows={2}
                    placeholder="Descrição do restaurante"
                  />
                </div>
              </div>

              <div className="flex space-x-3 mt-6">
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="flex-1 px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300"
                >
                  Cancelar
                </button>
                <button
                  onClick={createRestaurant}
                  disabled={
                    !newRestaurant.name ||
                    !newRestaurant.email ||
                    !newRestaurant.password
                  }
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  Criar
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}

        {/* Modal de Edição */}
        {editingRestaurant && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto"
          >
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Editar Restaurante
            </h3>              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Nome do Restaurante *
                  </label>
                  <input
                    type="text"
                    value={editingRestaurant.name}
                    onChange={(e) =>
                      setEditingRestaurant({
                        ...editingRestaurant,
                        name: e.target.value,
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="Ex: Restaurante do João"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Descrição
                  </label>
                  <textarea
                    value={editingRestaurant.description}
                    onChange={(e) =>
                      setEditingRestaurant({
                        ...editingRestaurant,
                        description: e.target.value,
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    rows={2}
                    placeholder="Descrição do restaurante"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Telefone
                  </label>
                  <input
                    type="text"
                    value={editingRestaurant.phone || ""}
                    onChange={(e) =>
                      setEditingRestaurant({
                        ...editingRestaurant,
                        phone: e.target.value,
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="(11) 99999-9999"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Endereço
                  </label>
                  <input
                    type="text"
                    value={editingRestaurant.address || ""}
                    onChange={(e) =>
                      setEditingRestaurant({
                        ...editingRestaurant,
                        address: e.target.value,
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="Rua, número, bairro"
                  />
                </div>

                {/* Campos de Login */}
                <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                    Credenciais de Login
                  </h4>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Email de Login
                      </label>
                      <input
                        type="email"
                        value={editingUser.email}
                        onChange={(e) =>
                          setEditingUser({
                            ...editingUser,
                            email: e.target.value,
                          })
                        }
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="<EMAIL>"
                      />
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Deixe em branco para manter o email atual
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Nova Senha
                      </label>
                      <input
                        type="password"
                        value={editingUser.password}
                        onChange={(e) =>
                          setEditingUser({
                            ...editingUser,
                            password: e.target.value,
                          })
                        }
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="Nova senha (opcional)"
                      />
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Deixe em branco para manter a senha atual
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex space-x-3 mt-6">
                <button
                  onClick={() => {
                    setEditingRestaurant(null);
                    setEditingUser({ email: "", password: "" });
                  }}
                  className="flex-1 px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300"
                >
                  Cancelar
                </button>
                <button
                  onClick={updateRestaurant}
                  disabled={!editingRestaurant.name}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  Salvar
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default RestaurantManagement;
