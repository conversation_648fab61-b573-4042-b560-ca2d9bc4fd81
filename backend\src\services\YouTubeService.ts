import axios, { AxiosResponse } from "axios";
import { IsString, IsN<PERSON>ber, IsO<PERSON><PERSON>, <PERSON>, <PERSON>, validate } from "class-validator";
import { plainToClass } from "class-transformer";
import { redisClient } from "../config/redis";
import { logger, logYouTubeAPI } from "../utils/logger";

/**
 * Classes de validação para entrada de dados
 */
export class SearchVideosDto {
  @IsString()
  query: string;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(50)
  maxResults?: number = 10;

  @IsOptional()
  @IsString()
  pageToken?: string;
}

export class GetVideoDetailsDto {
  @IsString()
  videoIds: string;
}

export class GetVideoInfoDto {
  @IsString()
  videoId: string;
}

/**
 * Classe de erro personalizada para YouTube API
 */
export class YouTubeError extends Error {
  public readonly code: string;
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly quotaUsed?: number;

  constructor(
    message: string,
    code: string = 'YOUTUBE_ERROR',
    statusCode: number = 500,
    isOperational: boolean = true,
    quotaUsed?: number
  ) {
    super(message);
    this.name = 'YouTubeError';
    this.code = code;
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.quotaUsed = quotaUsed;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Interfaces tipadas para resposta da API do YouTube
 */
interface YouTubeVideoSnippet {
  title: string;
  description: string;
  channelTitle: string;
  channelId: string;
  publishedAt: string;
  thumbnails: {
    default: { url: string; width: number; height: number };
    medium: { url: string; width: number; height: number };
    high: { url: string; width: number; height: number };
    standard?: { url: string; width: number; height: number };
    maxres?: { url: string; width: number; height: number };
  };
  tags?: string[];
  categoryId: string;
  defaultLanguage?: string;
  defaultAudioLanguage?: string;
  liveBroadcastContent?: string;
}

interface YouTubeVideoContentDetails {
  duration: string; // ISO 8601 format (PT4M13S)
  definition: string;
  caption: string;
  licensedContent: boolean;
  contentRating?: {
    ytRating?: string;
  };
}

interface YouTubeVideoStatistics {
  viewCount: string;
  likeCount?: string;
  dislikeCount?: string;
  favoriteCount: string;
  commentCount?: string;
}

interface YouTubeVideoStatus {
  uploadStatus: string;
  privacyStatus: string;
  license: string;
  embeddable: boolean;
  publicStatsViewable: boolean;
  madeForKids: boolean;
}

interface YouTubeVideo {
  id: string;
  snippet: YouTubeVideoSnippet;
  contentDetails: YouTubeVideoContentDetails;
  statistics: YouTubeVideoStatistics;
  status: YouTubeVideoStatus;
}

interface YouTubeSearchResult {
  id: {
    videoId: string;
  };
  snippet: YouTubeVideoSnippet;
}

interface YouTubeApiResponse<T> {
  items: T[];
  nextPageToken?: string;
  prevPageToken?: string;
  pageInfo: {
    totalResults: number;
    resultsPerPage: number;
  };
}

/**
 * Interfaces para resultados processados e cache
 */
export interface IProcessedVideoInfo {
  youtubeVideoId: string;
  title: string;
  artist: string;
  channelName: string;
  duration: number; // em segundos
  thumbnailUrl: string;
  description: string;
  metadata: {
    genre?: string[];
    mood?: string[];
    language?: string;
    explicit?: boolean;
    live?: boolean;
    publishedAt: string;
    viewCount: number;
    likeCount?: number;
    tags?: string[];
    categoryId: string;
    embeddable: boolean;
    madeForKids: boolean;
  };
}

export interface ISearchResult {
  videos: IProcessedVideoInfo[];
  nextPageToken?: string;
  totalResults: number;
}

export interface IQuotaInfo {
  used: number;
  limit: number;
  remaining: number;
  resetTime: Date;
}

export interface ICacheStats {
  hits: number;
  misses: number;
  hitRate: number;
  totalKeys: number;
}

export interface IYouTubeServiceConfig {
  apiKey: string;
  quotaLimit: number;
  cacheEnabled: boolean;
  cacheTTL: number;
}

// Manter AppError para compatibilidade (será removido gradualmente)
// AppError removido: padronizar todos os erros como YouTubeError

/**
 * Interfaces para parâmetros (compatibilidade)
 */
interface SearchVideosParams {
  query: string;
  maxResults?: number;
  pageToken?: string;
}

interface VideoIdParams {
  videoId: string;
}

/**
 * Serviço YouTube API com cache inteligente e gerenciamento de quota
 *
 * @class YouTubeService
 * @description Gerencia interações com a YouTube Data API v3, incluindo
 * busca de vídeos, cache otimizado, controle de quota e validação robusta.
 *
 * @example
 * ```typescript
 * const youtubeService = new YouTubeService();
 * const results = await youtubeService.searchVideos('rock music', 10);
 * ```
 */
export class YouTubeService {
  private readonly apiKey: string;
  private readonly baseUrl = "https://www.googleapis.com/youtube/v3";
  private readonly dailyQuotaLimit: number;
  private readonly cacheEnabled: boolean;
  private readonly cacheTTL: number;

  private quotaUsed = 0;
  private quotaResetTime: Date;
  private cacheStats: ICacheStats = {
    hits: 0,
    misses: 0,
    hitRate: 0,
    totalKeys: 0
  };

  constructor(config?: Partial<IYouTubeServiceConfig>) {
    this.apiKey = config?.apiKey || process.env.YOUTUBE_API_KEY || "";
    this.dailyQuotaLimit = config?.quotaLimit || parseInt(process.env.YOUTUBE_API_QUOTA_LIMIT || "10000");
  this.cacheEnabled = config?.cacheEnabled ?? true;
    this.cacheTTL = config?.cacheTTL || 3600; // 1 hora

    // Inicializar quota reset time
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    this.quotaResetTime = tomorrow;

    this.validateConfiguration();
    this.initializeQuotaTracking();
  }

  /**
   * Calcula o próximo reset de quota (meia-noite PST)
   * @private
   */
  private getNextQuotaReset(): Date {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0); // Meia-noite
    return tomorrow;
  }

  /**
   * Inicializa o rastreamento de quota
   * @private
   */
  private initializeQuotaTracking(): void {
    // Evitar timers durante testes para não manter handles abertos no Jest
    if (process.env.NODE_ENV === 'test') {
      return;
    }

    // Resetar quota diariamente
    const msUntilReset = this.quotaResetTime.getTime() - Date.now();
    setTimeout(() => {
      this.resetQuota();
      // Configurar reset diário
      setInterval(() => {
        this.resetQuota();
      }, 24 * 60 * 60 * 1000); // 24 horas
    }, msUntilReset);
  }

  /**
   * Valida a configuração do serviço
   * @private
   */
  private validateConfiguration(): void {
    if (!this.apiKey) {
      throw new YouTubeError(
        'YOUTUBE_API_KEY não configurada - configure a variável de ambiente',
        'MISSING_API_KEY',
        500
      );
    }

    if (this.dailyQuotaLimit <= 0) {
      throw new YouTubeError(
        'Limite de quota inválido',
        'INVALID_QUOTA_LIMIT',
        500
      );
    }

    // Verificar se Redis está disponível para cache
  if (this.cacheEnabled && !redisClient.isReady) {
      logger.warn("Redis não está conectado - cache desabilitado");
    }

    // Inicializar quota do Redis
    this.loadQuotaFromRedis();
  }

  // Validação manual de entrada para busca de vídeos
  private validateSearchParams(params: SearchVideosParams): void {
    if (!params.query || typeof params.query !== 'string' || params.query.trim().length === 0) {
      throw new YouTubeError("Query é obrigatória e deve ser uma string não vazia", 'INVALID_PARAMS', 400);
    }
    
    if (params.maxResults !== undefined) {
      if (!Number.isInteger(params.maxResults) || params.maxResults < 1 || params.maxResults > 50) {
        throw new YouTubeError("maxResults deve ser um número inteiro entre 1 e 50", 'INVALID_PARAMS', 400);
      }
    }
    
    if (params.pageToken !== undefined && (typeof params.pageToken !== 'string' || params.pageToken.trim().length === 0)) {
      throw new YouTubeError("pageToken deve ser uma string não vazia", 'INVALID_PARAMS', 400);
    }
  }

  // Validação manual de entrada para videoId
  private validateVideoId(params: VideoIdParams): void {
    if (!params.videoId || typeof params.videoId !== 'string' || params.videoId.trim().length === 0) {
      throw new YouTubeError("videoId é obrigatório e deve ser uma string não vazia", 'INVALID_PARAMS', 400);
    }
  }

  private async loadQuotaFromRedis(): Promise<void> {
    try {
      if (redisClient.isReady) {
        const storedQuota = await redisClient.getClient().get("youtube:quota_used");
        this.quotaUsed = storedQuota ? parseInt(storedQuota) : 0;
      }
    } catch (error) {
      logger.warn("Erro ao carregar quota do Redis:", error);
      this.quotaUsed = 0;
    }
  }

  private async incrementQuotaUsed(cost: number): Promise<void> {
    try {
      if (redisClient.isReady) {
        const client: any = redisClient.getClient();
        if (typeof client.incrBy === 'function') {
          await client.incrBy("youtube:quota_used", cost);
          this.quotaUsed = parseInt((await client.get("youtube:quota_used")) || "0");
        } else {
          // Fallback: atualiza localmente e tenta persistir se possível
          const current = parseInt((await client.get("youtube:quota_used")) || "0");
          const next = current + cost;
          if (typeof client.set === 'function') {
            await client.set("youtube:quota_used", String(next));
          }
          this.quotaUsed = next;
        }
      } else {
        this.quotaUsed += cost;
      }
    } catch (error) {
      logger.warn("Erro ao atualizar quota no Redis:", error);
      this.quotaUsed += cost;
    }
  }

  // Buscar vídeos por termo
  async searchVideos(
    query: string,
    maxResults: number = 10,
    pageToken?: string
  ): Promise<ISearchResult> {
    try {
      // Validar entrada
      this.validateSearchParams({ query, maxResults, pageToken });

      // Verificar cache primeiro
      const cacheKey = `search:${query}:${maxResults}:${pageToken || ""}`;
      const cached = await this.getCachedSearch(cacheKey);

      if (cached) {
        logYouTubeAPI("search_cache_hit", query);
        return cached;
      }

      // Verificar quota
      if (!this.hasQuotaFor("search")) {
        throw new YouTubeError("Quota diária da API do YouTube excedida", 'QUOTA_EXCEEDED', 429, true, this.quotaUsed);
      }

      // Buscar vídeos
      const searchResponse = await this.makeApiRequest<
        YouTubeApiResponse<YouTubeSearchResult>
      >("/search", {
        part: "snippet",
        q: query,
        type: "video",
        maxResults,
        pageToken,
        videoCategoryId: "10", // Música
        videoEmbeddable: "true",
        videoSyndicated: "true",
        order: "relevance",
      });

      await this.incrementQuotaUsed(100); // Search custa 100 unidades

      if (!searchResponse.data.items.length) {
        return {
          videos: [],
          totalResults: 0,
        };
      }

      // Obter detalhes dos vídeos
      const videoIds = searchResponse.data.items.map((item) => item.id.videoId);
      const videoDetails = await this.getVideoDetails(videoIds);

      // Processar resultados
      const processedVideos = videoDetails
        .filter((video) => this.isValidVideo(video))
        .map((video) => this.processVideoInfo(video));

      const result = {
        videos: processedVideos,
        nextPageToken: searchResponse.data.nextPageToken,
        totalResults: searchResponse.data.pageInfo.totalResults,
      };

      // Cachear resultado por 1 hora
      await this.cacheSearch(cacheKey, result, 3600);

      logYouTubeAPI("search_success", query, this.quotaUsed);
      return result;
    } catch (error) {
      logYouTubeAPI("search_error", query, this.quotaUsed, error);
      if (error instanceof YouTubeError) {
        throw error;
      }
      throw new YouTubeError("Erro ao buscar vídeos no YouTube", 'YOUTUBE_SEARCH_ERROR', 500, true, this.quotaUsed);
    }
  }

  // Obter detalhes de vídeos específicos
  async getVideoDetails(videoIds: string[]): Promise<YouTubeVideo[]> {
    try {
      if (videoIds.length === 0) return [];

      // Verificar cache em lote
      const cachedVideos: YouTubeVideo[] = [];
      const uncachedIds: string[] = [];

      if (redisClient.isReady) {
        const client: any = redisClient.getClient();
        let cachedResults: (string | null)[] | null = null;
        if (typeof client.mGet === 'function') {
          cachedResults = await client.mGet(videoIds.map((id: string) => `youtube:video:${id}`));
        }
        for (let i = 0; i < videoIds.length; i++) {
          if (cachedResults && cachedResults[i]) {
            try {
              cachedVideos.push(JSON.parse(cachedResults[i]));
              logYouTubeAPI("video_cache_hit", videoIds[i]);
            } catch (parseError) {
              uncachedIds.push(videoIds[i]);
            }
          } else {
            uncachedIds.push(videoIds[i]);
          }
        }
      } else {
        uncachedIds.push(...videoIds);
      }

      let apiVideos: YouTubeVideo[] = [];

      // Buscar vídeos não cacheados
      if (uncachedIds.length > 0) {
        if (!this.hasQuotaFor("video_details", uncachedIds.length)) {
          throw new YouTubeError("Quota diária da API do YouTube excedida", 'QUOTA_EXCEEDED', 429, true, this.quotaUsed);
        }

        const response = await this.makeApiRequest<
          YouTubeApiResponse<YouTubeVideo>
        >("/videos", {
          part: "snippet,contentDetails,statistics,status",
          id: uncachedIds.join(","),
          maxResults: 50,
        });

  await this.incrementQuotaUsed(1); // Videos.list custa 1 unidade
        apiVideos = response.data.items;

        // Cachear vídeos individuais por 24 horas
        if (redisClient.isReady) {
          const cachePromises = apiVideos.map((video) =>
            this.cacheVideo(video.id, video, 86400)
          );
          await Promise.all(cachePromises);
        }
      }

      return [...cachedVideos, ...apiVideos];
    } catch (error) {
      logYouTubeAPI("video_details_error", undefined, this.quotaUsed, error);
      if (error instanceof YouTubeError) {
        throw error;
      }
      throw new YouTubeError("Erro ao obter detalhes dos vídeos", 'YOUTUBE_VIDEO_DETAILS_ERROR', 500, true, this.quotaUsed);
    }
  }

  // Obter informações de um vídeo específico
  async getVideoInfo(videoId: string): Promise<IProcessedVideoInfo | null> {
    try {
      // Validar entrada
      this.validateVideoId({ videoId });

      const videos = await this.getVideoDetails([videoId]);

      if (videos.length === 0) {
        return null;
      }

  // Retorna o vídeo correspondente; se não encontrado, usa o primeiro item
  const video = videos.find(v => v.id === videoId) || videos[0];

      return this.processVideoInfo(video);
    } catch (error) {
      logYouTubeAPI("video_info_error", videoId, this.quotaUsed, error);
      if (error instanceof YouTubeError) {
        throw error;
      }
      // Repropaga erro original para inspecionar falha nos testes
      throw error;
    }
  }

  // Validar se o vídeo é adequado
  private isValidVideo(video: YouTubeVideo): boolean {
    // Verificar se é embeddable
  if (video.status && video.status.embeddable === false) {
      return false;
    }

    // Verificar se não é privado
  if (video.status && video.status.privacyStatus !== "public") {
      return false;
    }

    // Verificar se não é conteúdo infantil (pode ter restrições)
  if (video.status && video.status.madeForKids) {
      return false;
    }

    // Verificar duração (não deve ser muito curta ou muito longa)
    const duration = this.parseDuration(video.contentDetails.duration);
    if (duration < 10 || duration > 3600) {
      // 10 segundos a 1 hora
      return false;
    }

    return true;
  }

  // Processar informações do vídeo
  private processVideoInfo(video: YouTubeVideo): IProcessedVideoInfo {
    const snippet = video.snippet;
    const duration = this.parseDuration(video.contentDetails.duration);

    // Melhor extração de artista e título usando regex
    const titleRegex = /^(.*?)\s*[-–—|]\s*(.+)$/i;
  const match = snippet.title.match(titleRegex);
  const title = match ? match[1].trim() : snippet.title;
  const artist = match ? match[2].trim() : snippet.channelTitle;

    return {
      youtubeVideoId: video.id,
      title: title,
      artist: artist,
      channelName: snippet.channelTitle,
      duration: duration,
      thumbnailUrl:
        snippet.thumbnails.high?.url ||
        snippet.thumbnails.medium?.url ||
        snippet.thumbnails.default?.url ||
        "",
      description: snippet.description,
      metadata: {
        language:
          snippet.defaultAudioLanguage || snippet.defaultLanguage || "unknown",
        explicit: this.detectExplicitContent(snippet),
        live: this.detectLiveContent(snippet),
        publishedAt: snippet.publishedAt,
        viewCount: parseInt(video.statistics.viewCount) || 0,
        likeCount: video.statistics.likeCount
          ? parseInt(video.statistics.likeCount)
          : undefined,
        tags: snippet.tags || [],
        categoryId: snippet.categoryId,
        embeddable: video.status.embeddable,
        madeForKids: video.status.madeForKids,
      },
    };
  }

  private detectExplicitContent(snippet: YouTubeVideoSnippet): boolean {
    const explicitKeywords = ["explicit", "parental advisory", "18+", "nsfw"];
    return explicitKeywords.some(
      (keyword) =>
        snippet.title.toLowerCase().includes(keyword) ||
        snippet.description.toLowerCase().includes(keyword) ||
        (snippet.tags &&
          snippet.tags.some((tag) => tag.toLowerCase().includes(keyword)))
    );
  }

  private detectLiveContent(snippet: YouTubeVideoSnippet): boolean {
    return (
      snippet.title.toLowerCase().includes("live") ||
      snippet.description.toLowerCase().includes("ao vivo") ||
      snippet.liveBroadcastContent === "live"
    );
  }

  // Converter duração ISO 8601 para segundos
  private parseDuration(duration: string): number {
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
    if (!match) return 0;

    const hours = parseInt(match[1] || "0");
    const minutes = parseInt(match[2] || "0");
    const seconds = parseInt(match[3] || "0");

    return hours * 3600 + minutes * 60 + seconds;
  }

  // Fazer requisição para a API
  private async makeApiRequest<T>(
    endpoint: string,
    params: Record<string, any>
  ): Promise<AxiosResponse<T>> {
    try {
      const url = `${this.baseUrl}${endpoint}`;

      const response = await axios.get<T>(url, {
        params: {
          ...params,
          key: this.apiKey,
        },
        timeout: 10000,
        headers: {
          "User-Agent": "RestaurantPlaylistSystem/1.0",
        },
      });

      return response;
    } catch (error: any) {
      if (error?.response?.status === 403) {
        throw new YouTubeError("Quota da API do YouTube excedida ou chave inválida", 'API_FORBIDDEN', 403, true, this.quotaUsed);
      }
      if (error?.response?.status === 400) {
        throw new YouTubeError("Parâmetros inválidos na requisição à API do YouTube", 'INVALID_API_PARAMS', 400, true, this.quotaUsed);
      }
      throw new YouTubeError(`Erro na requisição à API do YouTube: ${error?.message || 'Unknown error'}`, 'API_REQUEST_FAILED', 500, true, this.quotaUsed);
    }
  }

  // Métodos de cache específicos
  private async getCachedSearch(cacheKey: string): Promise<any | null> {
    try {
  if (!this.cacheEnabled || !redisClient.isReady) return null;
      
      const cached = await redisClient.getClient().get(`youtube:search:${cacheKey}`);
      if (cached) {
        return JSON.parse(cached);
      }
      return null;
    } catch (error) {
      logger.warn("Erro ao acessar cache de busca:", error);
      return null;
    }
  }

  private async cacheSearch(cacheKey: string, data: any, ttl: number): Promise<void> {
    try {
  if (this.cacheEnabled && redisClient.isReady) {
        await redisClient.getClient().setEx(`youtube:search:${cacheKey}`, ttl, JSON.stringify(data));
      }
    } catch (error) {
      logger.warn("Erro ao cachear busca:", error);
    }
  }

  private async cacheVideo(videoId: string, data: YouTubeVideo, ttl: number): Promise<void> {
    try {
  if (this.cacheEnabled && redisClient.isReady) {
        await redisClient.getClient().setEx(`youtube:video:${videoId}`, ttl, JSON.stringify(data));
      }
    } catch (error) {
      logger.warn("Erro ao cachear vídeo:", error);
    }
  }

  // Obter quota usada hoje
  getQuotaUsed(): number {
    return this.quotaUsed;
  }

  // Obter quota restante
  getQuotaRemaining(): number {
    return Math.max(0, this.dailyQuotaLimit - this.quotaUsed);
  }

  // Verificar se há quota suficiente
  hasQuotaFor(operation: "search" | "video_details", count: number = 1): boolean {
    const cost = operation === "search" ? 100 : count;
    return this.quotaUsed + cost <= this.dailyQuotaLimit;
  }

  // Reset quota (chamado diariamente)
  async resetQuota(): Promise<void> {
    try {
      if (redisClient.isReady) {
        await redisClient.getClient().set("youtube:quota_used", "0");
      }
      this.quotaUsed = 0;
      logger.info("Quota da API do YouTube resetada");
    } catch (error) {
      logger.error("Erro ao resetar quota:", error);
      this.quotaUsed = 0;
    }
  }

  /**
   * Obtém informações de quota
   */
  getQuotaInfo(): IQuotaInfo {
    return {
      used: this.quotaUsed,
      limit: this.dailyQuotaLimit,
      remaining: this.dailyQuotaLimit - this.quotaUsed,
      resetTime: this.quotaResetTime
    };
  }

  /**
   * Obtém estatísticas de cache
   */
  getCacheStats(): ICacheStats {
    return { ...this.cacheStats };
  }

  /**
   * Limpa cache
   */
  async clearCache(): Promise<void> {
    if (this.cacheEnabled && redisClient.isReady) {
      try {
        const client: any = redisClient.getClient();
        const keys = typeof client.keys === 'function' ? await client.keys('youtube:*') : [];
        if (keys.length > 0) {
          await client.del(keys);
        }
        logger.info(`Cache do YouTube limpo: ${keys.length} chaves removidas`);
      } catch (error) {
        logger.error('Erro ao limpar cache do YouTube:', error);
      }
    }
  }
}
