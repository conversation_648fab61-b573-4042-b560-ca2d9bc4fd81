import { AppDataSource } from '../config/database';
import { Suggestion } from '../models/Suggestion';
import { Vote, VoteType } from '../models/Vote';
import { PlayHistory } from '../models/PlayHistory';
import { Restaurant } from '../models/Restaurant';

export interface TrackAnalytics {
  id: string;
  title: string;
  artist: string;
  youtubeVideoId: string;
  totalVotes: number;
  upvotes: number;
  downvotes: number;
  score: number;
  negativeVoteRatio: number;
  positiveVoteRatio: number;
  playCount: number;
  skipCount: number;
  completionRate: number;
  averagePlayDuration: number;
  lastPlayed?: Date;
  suggestedCount: number;
  performance: 'excellent' | 'good' | 'average' | 'poor' | 'terrible';
  recommendation: 'keep' | 'monitor' | 'remove' | 'blacklist';
}

export interface PlaylistHealth {
  totalTracks: number;
  excellentTracks: number;
  goodTracks: number;
  averageTracks: number;
  poorTracks: number;
  terribleTracks: number;
  overallScore: number;
  healthRating: 'excellent' | 'good' | 'needs_attention' | 'critical';
  recommendations: string[];
}

export class PlaylistAnalyticsService {
  private static instance: PlaylistAnalyticsService;

  static getInstance(): PlaylistAnalyticsService {
    if (!PlaylistAnalyticsService.instance) {
      PlaylistAnalyticsService.instance = new PlaylistAnalyticsService();
    }
    return PlaylistAnalyticsService.instance;
  }

  // Analisar performance de todas as músicas
  async analyzeTrackPerformance(restaurantId: string): Promise<TrackAnalytics[]> {
    try {
      const suggestionRepository = AppDataSource.getRepository(Suggestion);
      const playHistoryRepository = AppDataSource.getRepository(PlayHistory);

      // Buscar todas as sugestões com votos
      const suggestions = await suggestionRepository
        .createQueryBuilder('suggestion')
        .leftJoinAndSelect('suggestion.votes', 'vote')
        .where('suggestion.restaurant.id = :restaurantId', { restaurantId })
        .getMany();

      const analytics: TrackAnalytics[] = [];

      for (const suggestion of suggestions) {
        // Calcular estatísticas de votos
        const upvotes = suggestion.votes?.filter(v => v.voteType === VoteType.UP).length || 0;
        const downvotes = suggestion.votes?.filter(v => v.voteType === VoteType.DOWN).length || 0;
        const totalVotes = upvotes + downvotes;
        const score = upvotes - downvotes;
        const negativeVoteRatio = totalVotes > 0 ? downvotes / totalVotes : 0;
        const positiveVoteRatio = totalVotes > 0 ? upvotes / totalVotes : 0;

        // Buscar histórico de reprodução
        const playHistory = await playHistoryRepository
          .createQueryBuilder('history')
          .where('history.restaurant.id = :restaurantId', { restaurantId })
          .andWhere('history.youtubeVideoId = :videoId', { videoId: suggestion.youtubeVideoId })
          .getMany();

        const playCount = playHistory.length;
        const skipCount = playHistory.filter(h => h.status === 'skipped').length;
        const completedCount = playHistory.filter(h => h.status === 'completed').length;
        const completionRate = playCount > 0 ? completedCount / playCount : 0;
        
        const totalPlayDuration = playHistory.reduce((sum, h) => sum + (h.playDuration || 0), 0);
        const averagePlayDuration = playCount > 0 ? totalPlayDuration / playCount : 0;
        
        const lastPlayed = playHistory.length > 0 
          ? playHistory.sort((a, b) => b.playedAt.getTime() - a.playedAt.getTime())[0].playedAt
          : undefined;

        // Determinar performance e recomendação
        const performance = this.calculatePerformance(
          negativeVoteRatio, 
          completionRate, 
          totalVotes, 
          playCount
        );
        
        const recommendation = this.getRecommendation(performance, negativeVoteRatio, totalVotes);

        analytics.push({
          id: suggestion.id,
          title: suggestion.title,
          artist: suggestion.artist || 'Artista Desconhecido',
          youtubeVideoId: suggestion.youtubeVideoId,
          totalVotes,
          upvotes,
          downvotes,
          score,
          negativeVoteRatio,
          positiveVoteRatio,
          playCount,
          skipCount,
          completionRate,
          averagePlayDuration,
          lastPlayed,
          suggestedCount: 1, // Cada sugestão conta como 1
          performance,
          recommendation
        });
      }

      return analytics.sort((a, b) => b.negativeVoteRatio - a.negativeVoteRatio);
    } catch (error) {
      console.error('Erro ao analisar performance das músicas:', error);
      return [];
    }
  }

  // Obter músicas com pior votação
  async getWorstRatedTracks(restaurantId: string): Promise<TrackAnalytics[]> {
    const allTracks = await this.analyzeTrackPerformance(restaurantId);
    
    return allTracks.filter(track => 
      track.negativeVoteRatio > 0.7 && 
      track.totalVotes > 5
    );
  }

  // Obter músicas recomendadas para remoção
  async getTracksToRemove(restaurantId: string): Promise<TrackAnalytics[]> {
    const allTracks = await this.analyzeTrackPerformance(restaurantId);
    
    return allTracks.filter(track => 
      track.recommendation === 'remove' || track.recommendation === 'blacklist'
    );
  }

  // Analisar saúde geral da playlist
  async analyzePlaylistHealth(restaurantId: string): Promise<PlaylistHealth> {
    const tracks = await this.analyzeTrackPerformance(restaurantId);
    
    const totalTracks = tracks.length;
    const excellentTracks = tracks.filter(t => t.performance === 'excellent').length;
    const goodTracks = tracks.filter(t => t.performance === 'good').length;
    const averageTracks = tracks.filter(t => t.performance === 'average').length;
    const poorTracks = tracks.filter(t => t.performance === 'poor').length;
    const terribleTracks = tracks.filter(t => t.performance === 'terrible').length;

    // Calcular score geral (0-100)
    const overallScore = totalTracks > 0 
      ? Math.round(
          (excellentTracks * 100 + goodTracks * 80 + averageTracks * 60 + poorTracks * 40 + terribleTracks * 20) 
          / totalTracks
        )
      : 0;

    // Determinar rating de saúde
    let healthRating: PlaylistHealth['healthRating'];
    if (overallScore >= 80) healthRating = 'excellent';
    else if (overallScore >= 65) healthRating = 'good';
    else if (overallScore >= 50) healthRating = 'needs_attention';
    else healthRating = 'critical';

    // Gerar recomendações
    const recommendations: string[] = [];
    
    if (terribleTracks > 0) {
      recommendations.push(`Remover ${terribleTracks} música(s) com performance terrível`);
    }
    
    if (poorTracks > totalTracks * 0.2) {
      recommendations.push(`Monitorar ${poorTracks} música(s) com performance ruim`);
    }
    
    if (totalTracks < 20) {
      recommendations.push('Adicionar mais músicas à playlist para maior variedade');
    }
    
    if (excellentTracks < totalTracks * 0.3) {
      recommendations.push('Adicionar mais músicas populares para melhorar engajamento');
    }

    return {
      totalTracks,
      excellentTracks,
      goodTracks,
      averageTracks,
      poorTracks,
      terribleTracks,
      overallScore,
      healthRating,
      recommendations
    };
  }

  // Calcular performance baseada em métricas
  private calculatePerformance(
    negativeVoteRatio: number,
    completionRate: number,
    totalVotes: number,
    playCount: number
  ): TrackAnalytics['performance'] {
    // Música sem dados suficientes
    if (totalVotes < 3 && playCount < 2) {
      return 'average';
    }

    // Música terrível: alta rejeição e baixa conclusão
    if (negativeVoteRatio > 0.8 || (negativeVoteRatio > 0.6 && completionRate < 0.3)) {
      return 'terrible';
    }

    // Música ruim: moderada rejeição
    if (negativeVoteRatio > 0.6 || (negativeVoteRatio > 0.4 && completionRate < 0.5)) {
      return 'poor';
    }

    // Música média: balanceada
    if (negativeVoteRatio > 0.4 || completionRate < 0.7) {
      return 'average';
    }

    // Música boa: baixa rejeição e boa conclusão
    if (negativeVoteRatio < 0.2 && completionRate > 0.8) {
      return 'excellent';
    }

    return 'good';
  }

  // Obter recomendação baseada na performance
  private getRecommendation(
    performance: TrackAnalytics['performance'],
    negativeVoteRatio: number,
    totalVotes: number
  ): TrackAnalytics['recommendation'] {
    switch (performance) {
      case 'terrible':
        return negativeVoteRatio > 0.9 && totalVotes > 10 ? 'blacklist' : 'remove';
      case 'poor':
        return totalVotes > 15 ? 'remove' : 'monitor';
      case 'average':
        return 'monitor';
      case 'good':
      case 'excellent':
      default:
        return 'keep';
    }
  }

  // Obter relatório detalhado
  async getDetailedReport(restaurantId: string) {
    const [trackAnalytics, playlistHealth] = await Promise.all([
      this.analyzeTrackPerformance(restaurantId),
      this.analyzePlaylistHealth(restaurantId)
    ]);

    const worstTracks = trackAnalytics.filter(t => t.performance === 'terrible' || t.performance === 'poor');
    const bestTracks = trackAnalytics.filter(t => t.performance === 'excellent').slice(0, 10);
    const tracksToRemove = trackAnalytics.filter(t => t.recommendation === 'remove' || t.recommendation === 'blacklist');

    return {
      playlistHealth,
      trackAnalytics,
      worstTracks,
      bestTracks,
      tracksToRemove,
      summary: {
        totalAnalyzed: trackAnalytics.length,
        needsAttention: worstTracks.length,
        recommendedForRemoval: tracksToRemove.length,
        topPerformers: bestTracks.length
      }
    };
  }
}
