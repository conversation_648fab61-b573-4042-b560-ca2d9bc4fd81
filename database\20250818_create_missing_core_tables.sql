
-- Nota: não cria extensões aqui para evitar conflito com gen_random_uuid criada via init.sql.

-- analytics_daily
CREATE TABLE IF NOT EXISTS analytics_daily (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    date DATE NOT NULL,
    "totalSuggestions" INTEGER DEFAULT 0,
    "approvedSuggestions" INTEGER DEFAULT 0,
    "rejectedSuggestions" INTEGER DEFAULT 0,
    "pendingSuggestions" INTEGER DEFAULT 0,
    "totalVotes" INTEGER DEFAULT 0,
    upvotes INTEGER DEFAULT 0,
    downvotes INTEGER DEFAULT 0,
    "uniqueSessions" INTEGER DEFAULT 0,
    "totalPageViews" INTEGER DEFAULT 0,
    "totalPlayTime" INTEGER DEFAULT 0,
    "songsPlayed" INTEGER DEFAULT 0,
    "songsSkipped" INTEGER DEFAULT 0,
    "averageSessionDuration" DOUBLE PRECISION DEFAULT 0,
    "averageSongRating" DOUBLE PRECISION DEFAULT 0,
    "topGenres" JSON,
    "topArtists" JSON,
    "hourlyActivity" JSON,
    devicestats JSON,
    locationstats JSON,
    engagementstats JSON,
    moderationstats JSON,
    restaurant_id VARCHAR(255) NOT NULL,
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE constraint_type='FOREIGN KEY' AND constraint_name='fk_analytics_restaurant'
  ) THEN
    ALTER TABLE analytics_daily
      ADD CONSTRAINT fk_analytics_restaurant FOREIGN KEY (restaurant_id)
      REFERENCES restaurants(id) ON DELETE CASCADE;
  END IF;
END $$;

-- competitive_votes
CREATE TABLE IF NOT EXISTS competitive_votes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    voting_session_id VARCHAR(255) NOT NULL,
    suggestion_id UUID NOT NULL,
    voter_session_id UUID NOT NULL,
    voter_table_name VARCHAR(100),
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    voter_ip VARCHAR(100),
    voter_user_agent TEXT,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE constraint_type='FOREIGN KEY' AND constraint_name='fk_comp_votes_suggestion'
  ) THEN
    ALTER TABLE competitive_votes
      ADD CONSTRAINT fk_comp_votes_suggestion FOREIGN KEY (suggestion_id)
      REFERENCES suggestions(id) ON DELETE CASCADE;
  END IF;
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE constraint_type='FOREIGN KEY' AND constraint_name='fk_comp_votes_voter_session'
  ) THEN
    ALTER TABLE competitive_votes
      ADD CONSTRAINT fk_comp_votes_voter_session FOREIGN KEY (voter_session_id)
      REFERENCES client_sessions(id) ON DELETE CASCADE;
  END IF;
END $$;

CREATE INDEX IF NOT EXISTS idx_comp_votes_suggestion ON competitive_votes(suggestion_id);
CREATE INDEX IF NOT EXISTS idx_comp_votes_voter_session ON competitive_votes(voter_session_id);

-- suggestion_genres
CREATE TABLE IF NOT EXISTS suggestion_genres (
    suggestion_id UUID NOT NULL,
    genre_id UUID NOT NULL,
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (suggestion_id, genre_id)
);

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE constraint_type='FOREIGN KEY' AND constraint_name='fk_sugg_genres_suggestion'
  ) THEN
    ALTER TABLE suggestion_genres
      ADD CONSTRAINT fk_sugg_genres_suggestion FOREIGN KEY (suggestion_id)
      REFERENCES suggestions(id) ON DELETE CASCADE;
  END IF;

  -- Só cria a FK para genres se a tabela existir
  IF EXISTS (
    SELECT 1 FROM information_schema.tables WHERE table_schema='public' AND table_name='genres'
  ) AND NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE constraint_type='FOREIGN KEY' AND constraint_name='fk_sugg_genres_genre'
  ) THEN
    ALTER TABLE suggestion_genres
      ADD CONSTRAINT fk_sugg_genres_genre FOREIGN KEY (genre_id)
      REFERENCES genres(id) ON DELETE CASCADE;
  END IF;
END $$;

-- lyrics
CREATE TABLE IF NOT EXISTS lyrics (
    id VARCHAR(255) PRIMARY KEY,
    title VARCHAR(300) NOT NULL,
    artist VARCHAR(200) NOT NULL,
    album VARCHAR(200),
    duration INTEGER NOT NULL,
    language VARCHAR(10) DEFAULT 'pt',
    lines JSON NOT NULL,
    source VARCHAR(50) NOT NULL,
    copyright TEXT,
    is_explicit BOOLEAN DEFAULT FALSE,
    has_timestamps BOOLEAN DEFAULT FALSE,
    youtube_video_id VARCHAR(100),
    metadata JSON,
    view_count INTEGER DEFAULT 0,
    sing_along_count INTEGER DEFAULT 0,
    last_used TIMESTAMP WITHOUT TIME ZONE,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- moderation_rules
CREATE TABLE IF NOT EXISTS moderation_rules (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    "ruleType" rule_type NOT NULL,
    action rule_action DEFAULT 'flag_for_review',
    "ruleValue" JSON NOT NULL,
    "isActive" BOOLEAN DEFAULT TRUE,
    priority INTEGER DEFAULT 0,
    "matchCount" INTEGER DEFAULT 0,
    "lastMatchedAt" TIMESTAMP WITHOUT TIME ZONE,
    restaurant_id VARCHAR(255) NOT NULL,
    created_by UUID,
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_mod_rules_restaurant ON moderation_rules(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_mod_rules_active ON moderation_rules("isActive");

-- play_history
CREATE TABLE IF NOT EXISTS play_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    "youtubeVideoId" VARCHAR(100) NOT NULL,
    title VARCHAR(300) NOT NULL,
    artist VARCHAR(200),
    duration INTEGER NOT NULL,
    "playDuration" INTEGER NOT NULL,
    status play_status NOT NULL,
    "skipReason" TEXT,
    "playedAt" TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    "endedAt" TIMESTAMP WITHOUT TIME ZONE,
    "queuePosition" INTEGER,
    "voteCount" INTEGER DEFAULT 0,
    metadata JSON,
    restaurant_id VARCHAR(255) NOT NULL,
    suggestion_id UUID,
    played_by UUID,
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_play_history_restaurant ON play_history(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_play_history_suggestion ON play_history(suggestion_id);

-- rewards
CREATE TABLE IF NOT EXISTS rewards (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    restaurant_id VARCHAR(255) NOT NULL,
    session_id UUID NOT NULL,
    client_name VARCHAR(255),
    table_name VARCHAR(100),
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    reward_data JSON NOT NULL,
    awarded_for VARCHAR(50) NOT NULL,
    awarded_date TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    expires_at TIMESTAMP WITHOUT TIME ZONE,
    status VARCHAR(50) DEFAULT 'active',
    used_at TIMESTAMP WITHOUT TIME ZONE,
    usage_count INTEGER DEFAULT 0,
    performance_data JSON,
    social_sharing JSON,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_rewards_restaurant ON rewards(restaurant_id);
CREATE INDEX IF NOT EXISTS idx_rewards_session ON rewards(session_id);
