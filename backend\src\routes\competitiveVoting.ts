import { Router } from "express";
import {
  body,
  param,
  query,
  validationResult,
  validateRequest,
} from "../utils/validation";
import { AppDataSource } from "../config/database";
import { CompetitiveVote } from "../models/CompetitiveVote";
import { Suggestion } from "../models/Suggestion";
import asyncHandler from "../middleware/asyncHandler";
import { optionalAuth } from "../middleware/auth";
import { ValidationError, NotFoundError } from "../utils/errors";
import { redisClient } from "../config/redis";
import {
  notificationService,
  NotificationType,
  NotificationPriority,
} from "../services/NotificationService";

const router = Router();

/**
 * @swagger
 * /api/v1/competitive-voting/start/{suggestionId}:
 *   post:
 *     summary: Iniciar sessão de votação competitiva
 *     tags: [Competitive Voting]
 *     parameters:
 *       - in: path
 *         name: suggestionId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       201:
 *         description: Sessão de votação iniciada
 */
router.post(
  "/start/:suggestionId",
  [
    param("suggestionId")
      .notEmpty()
      .withMessage("ID da sugestão é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    validateRequest(req);

    const { suggestionId } = req.params;
    const suggestionRepository = AppDataSource.getRepository(Suggestion);

    const suggestion = await suggestionRepository.findOne({
      where: { id: suggestionId },
      relations: ["restaurant", "session"],
    });

    if (!suggestion) {
      throw new NotFoundError("Sugestão não encontrada");
    }

    if (!suggestion.isPaid) {
      throw new ValidationError(
        "Apenas músicas pagas podem ter votação competitiva"
      );
    }

    // Criar sessão de votação
    const sessionId = `voting_${suggestionId}_${Date.now()}`;
    const startTime = new Date();
    const endTime = new Date(
      startTime.getTime() + (suggestion.duration || 180) * 1000
    );

    const votingSession = {
      id: sessionId,
      suggestionId,
      restaurantId: suggestion.restaurant.id,
      performerSessionId: suggestion.clientSessionId,
      performerTableName: suggestion.tableNumber?.toString(),
      performerClientName: suggestion.clientName,
      songTitle: suggestion.title,
      artist: suggestion.artist,
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      isActive: true,
      totalVotes: 0,
      averageRating: 0,
      ratings: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
    };

    // Salvar no Redis com TTL
    const ttl =
      Math.ceil((endTime.getTime() - startTime.getTime()) / 1000) + 60;
    await redisClient
      .getClient()
      .setEx(`voting_session:${sessionId}`, ttl, JSON.stringify(votingSession));

    // Notificar todos os clientes do restaurante
    await notificationService.sendToRestaurant(suggestion.restaurant.id, {
      type: NotificationType.INFO,
      title: "🎤 Votação Ativa!",
      message: `Vote na performance de "${suggestion.title}" pela mesa ${
        suggestion.tableNumber || "?"
      }`,
      priority: NotificationPriority.HIGH,
      category: "competitive_voting",
      data: {
        votingSessionId: sessionId,
        suggestionId,
        songTitle: suggestion.title,
        artist: suggestion.artist,
        performerName: suggestion.clientName,
        performerTable: suggestion.tableNumber?.toString(),
        endTime: endTime.toISOString(),
      },
    });

    res.status(201).json({
      success: true,
      message: "Sessão de votação iniciada",
      votingSession,
    });
  })
);

/**
 * @swagger
 * /api/v1/competitive-voting/vote:
 *   post:
 *     summary: Submeter voto competitivo
 *     tags: [Competitive Voting]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - votingSessionId
 *               - suggestionId
 *               - voterSessionId
 *               - rating
 *             properties:
 *               votingSessionId:
 *                 type: string
 *               suggestionId:
 *                 type: string
 *               voterSessionId:
 *                 type: string
 *               rating:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 5
 *               comment:
 *                 type: string
 *               voterTableName:
 *                 type: string
 *     responses:
 *       201:
 *         description: Voto registrado com sucesso
 */
router.post(
  "/vote",
  [
    body("votingSessionId")
      .notEmpty()
      .withMessage("ID da sessão de votação é obrigatório"),
    body("suggestionId").notEmpty().withMessage("ID da sugestão é obrigatório"),
    body("voterSessionId")
      .notEmpty()
      .withMessage("ID da sessão do votante é obrigatório"),
    body("rating")
      .isInt({ min: 1, max: 5 })
      .withMessage("Rating deve ser entre 1 e 5"),
    body("comment").optional().isString(),
    body("voterTableName").optional().isString(),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    validateRequest(req);

    const {
      votingSessionId,
      suggestionId,
      voterSessionId,
      rating,
      comment,
      voterTableName,
    } = req.body;
    const voteRepository = AppDataSource.getRepository(CompetitiveVote);

    // Verificar se a sessão de votação está ativa
    const votingSessionData = await redisClient
      .getClient()
      .get(`voting_session:${votingSessionId}`);

    if (!votingSessionData) {
      throw new NotFoundError("Sessão de votação não encontrada ou expirada");
    }

    const votingSession = JSON.parse(votingSessionData);

    if (
      !votingSession.isActive ||
      new Date() > new Date(votingSession.endTime)
    ) {
      throw new ValidationError("Votação encerrada para esta música");
    }

    // Verificar se o usuário já votou
    const existingVote = await voteRepository.findOne({
      where: {
        votingSessionId,
        voterSessionId,
      },
    });

    if (existingVote) {
      throw new ValidationError("Você já votou nesta performance");
    }

    // Verificar se não é o próprio performer votando
    if (voterSessionId === votingSession.performerSessionId) {
      throw new ValidationError(
        "Você não pode votar na sua própria performance"
      );
    }

    // Criar voto
    const vote = CompetitiveVote.createVote({
      votingSessionId,
      suggestionId,
      voterSessionId,
      rating: rating as 1 | 2 | 3 | 4 | 5,
      comment,
      voterTableName,
      voterIp: req.ip,
      voterUserAgent: req.get("User-Agent"),
    });

    await voteRepository.save(vote);

    // Atualizar estatísticas da sessão
    const votes = await voteRepository.find({
      where: { votingSessionId },
    });

    const totalVotes = votes.length;
    const ratings = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
    let totalRating = 0;

    votes.forEach((v) => {
      ratings[v.rating]++;
      totalRating += v.rating;
    });

    const averageRating =
      totalVotes > 0 ? Math.round((totalRating / totalVotes) * 100) / 100 : 0;

    // Atualizar sessão no Redis
    const updatedSession = {
      ...votingSession,
      totalVotes,
      averageRating,
      ratings,
    };

    const ttl = Math.ceil(
      (new Date(votingSession.endTime).getTime() - Date.now()) / 1000
    );
    if (ttl > 0) {
      await redisClient
        .getClient()
        .setEx(
          `voting_session:${votingSessionId}`,
          ttl,
          JSON.stringify(updatedSession)
        );
    }

    // Notificar performer
    await notificationService.sendToSession(votingSession.performerSessionId, {
      type: NotificationType.SUCCESS,
      title: "🌟 Novo Voto!",
      message: `Alguém votou ${rating} estrelas na sua performance!`,
      priority: NotificationPriority.NORMAL,
      category: "competitive_voting",
      data: {
        rating,
        totalVotes,
        averageRating,
      },
    });

    // Notificar restaurante
    await notificationService.sendToRestaurant(votingSession.restaurantId, {
      type: NotificationType.INFO,
      title: "Votação Atualizada",
      message: `Nova avaliação: ${rating} estrelas`,
      priority: NotificationPriority.LOW,
      category: "competitive_voting",
      data: {
        votingSessionId,
        newRating: rating,
        updatedStats: updatedSession,
      },
    });

    res.status(201).json({
      success: true,
      message: "Voto registrado com sucesso!",
      vote: vote.toPublicJSON(),
      votingSession: updatedSession,
    });
  })
);

/**
 * @swagger
 * /api/v1/competitive-voting/session/{sessionId}:
 *   get:
 *     summary: Obter dados da sessão de votação
 *     tags: [Competitive Voting]
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Dados da sessão de votação
 */
router.get(
  "/session/:sessionId",
  [param("sessionId").notEmpty().withMessage("ID da sessão é obrigatório")],
  optionalAuth,
  asyncHandler(async (req, res) => {
    validateRequest(req);

    const { sessionId } = req.params;

    const votingSessionData = await redisClient
      .getClient()
      .get(`voting_session:${sessionId}`);

    if (!votingSessionData) {
      throw new NotFoundError("Sessão de votação não encontrada");
    }

    const votingSession = JSON.parse(votingSessionData);

    res.json({
      success: true,
      votingSession,
    });
  })
);

/**
 * @swagger
 * /api/v1/competitive-voting/active/{restaurantId}:
 *   get:
 *     summary: Obter sessões ativas de votação
 *     tags: [Competitive Voting]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Sessões ativas de votação
 */
router.get(
  "/active/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    validateRequest(req);

    const { restaurantId } = req.params;

    // Por simplicidade, retornar array vazio
    // Em produção, você manteria um índice das sessões ativas
    const activeSessions = [];

    res.json({
      success: true,
      activeSessions,
      total: activeSessions.length,
    });
  })
);

/**
 * @swagger
 * /api/v1/competitive-voting/leaderboard/{restaurantId}:
 *   get:
 *     summary: Obter leaderboard do dia
 *     tags: [Competitive Voting]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: date
 *         schema:
 *           type: string
 *           format: date
 *     responses:
 *       200:
 *         description: Leaderboard do dia
 */
router.get(
  "/leaderboard/:restaurantId",
  [
    param("restaurantId")
      .notEmpty()
      .withMessage("ID do restaurante é obrigatório"),
    query("date").optional().isISO8601(),
  ],
  optionalAuth,
  asyncHandler(async (req, res) => {
    validateRequest(req);

    const { restaurantId } = req.params;
    const { date } = req.query;

    const targetDate = date ? new Date(date as string) : new Date();
    const startOfDay = new Date(targetDate);
    startOfDay.setHours(0, 0, 0, 0);

    const endOfDay = new Date(targetDate);
    endOfDay.setHours(23, 59, 59, 999);

    const voteRepository = AppDataSource.getRepository(CompetitiveVote);

    // Buscar votos do dia
    const votes = await voteRepository
      .createQueryBuilder("vote")
      .leftJoinAndSelect("vote.suggestion", "suggestion")
      .leftJoinAndSelect("suggestion.restaurant", "restaurant")
      .where("restaurant.id = :restaurantId", { restaurantId })
      .andWhere("vote.createdAt BETWEEN :startOfDay AND :endOfDay", {
        startOfDay,
        endOfDay,
      })
      .getMany();

    // Agrupar por performance
    const performanceMap = new Map();

    votes.forEach((vote) => {
      const key = `${vote.suggestion.clientSessionId}_${vote.suggestionId}`;

      if (!performanceMap.has(key)) {
        performanceMap.set(key, {
          sessionId: vote.suggestion.clientSessionId,
          clientName: vote.suggestion.clientName || "Cliente",
          tableName: vote.suggestion.tableNumber?.toString() || "Mesa ?",
          songTitle: vote.suggestion.title,
          artist: vote.suggestion.artist,
          votes: [],
          performanceTime: vote.suggestion.playedAt || vote.createdAt,
        });
      }

      performanceMap.get(key).votes.push(vote.rating);
    });

    // Calcular leaderboard
    const leaderboard = Array.from(performanceMap.values())
      .map((performance) => ({
        sessionId: performance.sessionId,
        clientName: performance.clientName,
        tableName: performance.tableName,
        songTitle: performance.songTitle,
        artist: performance.artist,
        averageRating:
          performance.votes.reduce(
            (sum: number, rating: number) => sum + rating,
            0
          ) / performance.votes.length,
        totalVotes: performance.votes.length,
        performanceTime: performance.performanceTime,
        rank: 0,
      }))
      .sort((a, b) => {
        if (b.averageRating !== a.averageRating) {
          return b.averageRating - a.averageRating;
        }
        return b.totalVotes - a.totalVotes;
      })
      .map((entry, index) => ({
        ...entry,
        rank: index + 1,
        averageRating: Math.round(entry.averageRating * 100) / 100,
      }));

    res.json({
      success: true,
      leaderboard,
      date: targetDate.toISOString().split("T")[0],
      total: leaderboard.length,
    });
  })
);

export default router;
