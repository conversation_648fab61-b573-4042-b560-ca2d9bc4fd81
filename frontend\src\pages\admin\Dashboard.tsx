import React, { useState, useEffect } from "react";
import { Routes, Route, Link } from "react-router-dom";
import { motion } from "framer-motion";
import {
  BarChart3,
  Music,
  Users,
  Settings,
  PlayCircle,
  TrendingUp,
  Clock,
  Star,
  Plus,
  Trash2,
  Edit,
  Save,
  X,
  Youtube,
  Play,
  Pause,
  AlertCircle,
  QrCode,
  Building2,
  Home,
  LogOut,
  User,
  RefreshCw,
} from "lucide-react";
import { toast } from "react-hot-toast";
import { buildApiUrl } from "../../config/api";

// Componentes
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import RestaurantManagement from "@/components/admin/RestaurantManagement";

// Novos componentes para o admin reestruturado
import GlobalAnalytics from "@/components/admin/GlobalAnalytics";
import RevenueDashboard from "@/components/admin/RevenueDashboard";
import SystemSettings from "@/components/admin/SystemSettings";

// (Removido) Gerenciamento de playlist local não faz parte do escopo do Admin SaaS

// Dashboard principal com métricas globais
const DashboardHome: React.FC = () => {
  const [globalStats, setGlobalStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [revenueOverview, setRevenueOverview] = useState<{ totalRevenue: number } | null>(null);

  useEffect(() => {
    loadGlobalStats();
  }, []);

  const loadGlobalStats = async () => {
    try {
      setLoading(true);
      const [res, revRes] = await Promise.all([
        fetch('/api/v1/admin/analytics'),
        fetch('/api/v1/admin/revenue?period=30d'),
      ]);
      const [data, revData] = await Promise.all([res.json(), revRes.json()]);
      if (data?.success && data.analytics?.overview) {
        setGlobalStats({
          totalRestaurants: data.analytics.overview.totalRestaurants || 0,
          activeRestaurants: data.analytics.overview.activeRestaurants || 0,
          totalRevenue: data.analytics.overview.totalRevenue || 0,
          monthlyRevenue: data.analytics.overview.monthlyGrowth || 0,
          totalSuggestions: data.analytics.overview.totalSuggestions || 0,
          totalPayments: data.analytics.overview.totalPayments || 0,
          averageRating: data.analytics.overview.averageRating || 0,
          systemUptime: data.analytics.overview.systemUptime || '—',
        });
        if (revData?.success && revData.revenue?.totalRevenue != null) {
          setRevenueOverview({ totalRevenue: revData.revenue.totalRevenue });
        }
      } else {
        throw new Error('Resposta inválida de /admin/analytics');
      }
    } catch (error) {
      console.error('Erro ao carregar estatísticas globais:', error);
      toast.error('Erro ao carregar dados do dashboard');
    } finally {
      setLoading(false);
    }
  };

  const stats = [
    {
      title: "Restaurantes Ativos",
      value: globalStats?.activeRestaurants || "0",
      total: globalStats?.totalRestaurants || "0",
      change: "+2 este mês",
      icon: Building2,
      color: "text-blue-600 dark:text-blue-400",
      bgColor: "bg-blue-100 dark:bg-blue-900/20",
    },
    {
      title: "Receita Total",
      value: `R$ ${(revenueOverview?.totalRevenue ?? globalStats?.totalRevenue ?? 0).toFixed(2)}`,
      change: `+R$ ${globalStats?.monthlyRevenue?.toFixed(2) || "0,00"} este mês`,
      icon: TrendingUp,
      color: "text-green-600 dark:text-green-400",
      bgColor: "bg-green-100 dark:bg-green-900/20",
    },
    {
      title: "Pagamentos (Supervoto)",
      value: globalStats?.totalPayments || "0",
      change: "Últimos 30 dias",
      icon: TrendingUp,
      color: "text-purple-600 dark:text-purple-400",
      bgColor: "bg-purple-100 dark:bg-purple-900/20",
    },
    {
      title: "Uptime do Sistema",
      value: globalStats?.systemUptime || "—",
      change: "Últimas 24h",
      icon: RefreshCw,
      color: "text-yellow-600 dark:text-yellow-400",
      bgColor: "bg-yellow-100 dark:bg-yellow-900/20",
    },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Admin Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Visão geral do sistema e controle de receitas
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={loadGlobalStats}
            disabled={loading}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="Recarregar dados do dashboard"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span>Recarregar</span>
          </button>
          
          <button
            onClick={() => window.location.href = '/admin/analytics'}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            <BarChart3 className="w-4 h-4" />
            <span>Ver Analytics</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="card p-6"
          >
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                  {stat.title}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stat.value}
                  {stat.total && (
                    <span className="text-sm text-gray-500 ml-1">
                      / {stat.total}
                    </span>
                  )}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {stat.change} vs ontem
                </p>
              </div>
              <div
                className={`w-12 h-12 rounded-lg ${stat.bgColor} flex items-center justify-center`}
              >
                <stat.icon className={`w-6 h-6 ${stat.color}`} />
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Seções focadas em SaaS: Transações e Top Restaurantes */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Transações Recentes (Supervoto)
          </h3>
          <div className="space-y-3">
            {[1, 2, 3, 4].map((i) => (
              <div
                key={i}
                className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <div className="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                  <TrendingUp className="w-4 h-4 text-green-600 dark:text-green-400" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    R$ {(5 * i).toFixed(2)} • Restaurante #{i}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    há {i} min • Supervoto
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Top Restaurantes por Receita (30d)
          </h3>
          <div className="space-y-3">
            {[1, 2, 3, 4].map((i) => (
              <div
                key={i}
                className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/20 rounded text-xs flex items-center justify-center font-medium">
                    {i}
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      Restaurante #{i}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">ID: demo-{i}</p>
                  </div>
                </div>
                <div className="text-sm font-semibold text-gray-900 dark:text-white">
                  R$ {(1000 * i).toFixed(2)}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

const Dashboard: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link
              to="/admin"
              className="flex items-center space-x-3 hover:opacity-80 transition-opacity"
            >
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <Music className="w-5 h-5 text-white" />
              </div>
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                Restaurant Playlist Admin
              </h1>
            </Link>

            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Admin
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-gray-600 dark:text-gray-300" />
                </div>
                <Link
                  to="/"
                  className="flex items-center space-x-1 px-3 py-2 text-sm text-white bg-red-500 hover:bg-red-600 rounded-lg transition-colors font-medium"
                  title="Sair do Admin"
                >
                  <LogOut className="w-4 h-4" />
                  <span>Sair</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            {[
              { name: "Dashboard", icon: Home, path: "/admin/dashboard" },
              {
                name: "Restaurantes",
                icon: Building2,
                path: "/admin/restaurants",
              },
              {
                name: "Analytics Globais",
                icon: BarChart3,
                path: "/admin/analytics"
              },
              {
                name: "Receitas",
                icon: TrendingUp,
                path: "/admin/revenue",
              },
              {
                name: "Configurações",
                icon: Settings,
                path: "/admin/settings",
              },
            ].map((item) => (
              <Link
                key={item.name}
                to={item.path}
                className="flex items-center space-x-2 py-4 px-1 border-b-2 text-sm font-medium transition-colors border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"
              >
                <item.icon className="w-4 h-4 flex-shrink-0" />
                <span className="truncate">{item.name}</span>
              </Link>
            ))}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Routes>
          <Route path="/" element={<DashboardHome />} />
          <Route path="/dashboard" element={<DashboardHome />} />
          <Route path="/restaurants" element={<RestaurantManagement />} />
          <Route path="/analytics" element={<GlobalAnalytics />} />
          <Route path="/revenue" element={<RevenueDashboard />} />
          <Route path="/settings" element={<SystemSettings />} />
          <Route path="*" element={<DashboardHome />} />
        </Routes>
      </main>
    </div>
  );
};

export default Dashboard;
