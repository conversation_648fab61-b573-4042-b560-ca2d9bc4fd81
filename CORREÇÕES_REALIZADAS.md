# Correções Realizadas - 2025-08-18

- Migração SQL queue_items corrigida e tornada idempotente:
  - Substituição de blocos DO $$ por DDL com IF NOT EXISTS.
  - Ajuste do tipo de restaurant_id para VARCHAR (compatível com restaurants.id).
  - Criação/atualização idempotente da função e trigger de updated_at.
  - Índices criados com IF NOT EXISTS.
- backend/docker-entrypoint.sh:
  - Execução automática de arquivos .sql em backend/database/migrations no startup usando DATABASE_URL.
  - Logs claros de sucesso/aviso para cada arquivo.
- Validação:
  - Tabela public.queue_items presente no Postgres e com FKs/índices esperados.
  - Backend sobe saudável e reconhece a entidade QueueItem.

Impacto:
- Evita erro 500 por schema ausente e garante reprodutibilidade no Docker.
- Permite fila de reprodução persistente controlada pelo servidor.

Próximos passos sugeridos:
- Adicionar teste automatizado para inserir e avançar um QueueItem.
- Auditar rotas que ainda não usam o wrapper seguro do Redis.
