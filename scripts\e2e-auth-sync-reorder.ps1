Param(
  [string]$BaseUrl = 'http://localhost:8001/api/v1',
  [string]$RestaurantId = $env:E2E_RESTAURANT_ID,
  [string]$Email = $env:E2E_EMAIL,
  [string]$Password = $env:E2E_PASS
)

$ErrorActionPreference = 'Stop'

if ([string]::IsNullOrEmpty($RestaurantId)) { $RestaurantId = 'demo-restaurant' }
if ([string]::IsNullOrEmpty($Email)) { $Email = '<EMAIL>' }
if ([string]::IsNullOrEmpty($Password)) { $Password = 'demo123' }

Write-Host "🔐 Login ($Email)..."
$loginBody = @{ email = $Email; password = $Password } | ConvertTo-Json -Depth 3
$login = Invoke-RestMethod -Method Post -Uri "$BaseUrl/auth/login" -ContentType 'application/json' -Body $loginBody
$token = $login.token
if (-not $token) { throw 'Falha no login' }
Write-Host '✅ Login OK'

Write-Host "🔗 Gerando URL de autorização para $RestaurantId ..."
$auth = Invoke-RestMethod -Method Get -Uri "$BaseUrl/youtube-auth/$RestaurantId/authorize" -Headers @{ Authorization = "Bearer $token" }
$authUrl = $auth.authUrl
if ([string]::IsNullOrEmpty($authUrl)) { throw 'Falha ao obter authUrl (verifique YOUTUBE_CLIENT_ID/SECRET/REDIRECT_URI)' }
Write-Host "🌐 Abrindo navegador para autorizar: $authUrl"
Start-Process $authUrl | Out-Null

# Poll status até 3 minutos
$deadline = (Get-Date).AddMinutes(3)
$authenticated = $false
while ((Get-Date) -lt $deadline) {
  Start-Sleep -Seconds 5
  try {
    $status = Invoke-RestMethod -Method Get -Uri "$BaseUrl/youtube-auth/$RestaurantId/status" -Headers @{ Authorization = "Bearer $token" }
    if ($status.isAuthenticated -eq $true) { $authenticated = $true; break }
    Write-Host '⏳ Aguardando autorização no navegador...'
  } catch {
    Write-Warning $_
  }
}
if (-not $authenticated) {
  Write-Warning 'Não autenticado ainda. Conclua a autorização no navegador e reexecute este script para validar.'
}

Write-Host '🎵 Verificando playlists ativas...'
$pls = Invoke-RestMethod -Method Get -Uri "$BaseUrl/playlists/restaurant/$RestaurantId" -Headers @{ Authorization = "Bearer $token" }
$active = $null
if ($pls.playlists) {
  $active = $pls.playlists | Where-Object { $_.isActive -and $_.youtubePlaylistId } | Select-Object -First 1
}
if ($active) {
  Write-Host "✅ Playlist ativa: $($active.name) [$($active.youtubePlaylistId)]"
  try {
    Write-Host '🔄 Sincronizando playlist...'
    $sync = Invoke-RestMethod -Method Post -Uri "$BaseUrl/playlists/$($active.id)/sync" -Headers @{ Authorization = "Bearer $token" }
    Write-Host "✅ Sync: $($sync.message)"
  } catch {
    Write-Warning 'Falha ao sincronizar (verifique API key do YouTube). Prosseguindo.'
  }
} else {
  Write-Warning 'Nenhuma playlist ativa com youtubePlaylistId encontrada.'
}

try {
  Write-Host '🚀 Iniciando serviço de reordenação automática...'
  Invoke-RestMethod -Method Post -Uri "$BaseUrl/playlist-reorder/start" -Headers @{ Authorization = "Bearer $token" } | Out-Null
} catch {
  Write-Warning 'Não foi possível iniciar o serviço (já pode estar ativo).'
}

Write-Host '✅ Preparação concluída. Agora rode: npm run test:e2e -- --testPathPattern=05-supervote-pix-and-auto-reorder'
