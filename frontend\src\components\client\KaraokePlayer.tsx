import React, { useState, useEffect, useRef, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  Mic,
  X,
  RotateCcw,
  Settings,
  Maximize,
  Minimize,
  Heart,
  Users,
  Star,
} from "lucide-react";
import { toast } from "react-hot-toast";
import YouTube, { YouTubeProps } from "react-youtube";
import { buildApiUrl } from "@/config/api";

interface LyricsLine {
  time: number;
  text: string;
  duration?: number;
}

interface LyricsData {
  id: string;
  title: string;
  artist: string;
  duration: number;
  language: string;
  lines: LyricsLine[];
  hasTimestamps: boolean;
  // Campos opcionais usados em fallbacks/dados de teste
  source?: string;
  isExplicit?: boolean;
}

interface KaraokePlayerProps {
  isOpen: boolean;
  onClose: () => void;
  suggestion: {
    id: string;
    title: string;
    artist: string;
    thumbnailUrl?: string;
    duration?: number;
    youtubeVideoId: string;
  };
  sessionId: string;
  onVoteRequest?: () => void;
}

const KaraokePlayer: React.FC<KaraokePlayerProps> = ({
  isOpen,
  onClose,
  suggestion,
  sessionId,
  onVoteRequest,
}) => {
  const [lyrics, setLyrics] = useState<LyricsData | null>(null);
  const [currentTime, setCurrentTime] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [fontSize, setFontSize] = useState(28); // Increased default for mobile
  const [highlightColor, setHighlightColor] = useState("#3B82F6");
  const [loading, setLoading] = useState(false);
  const [currentLineIndex, setCurrentLineIndex] = useState(-1);
  const [progress, setProgress] = useState(0);

  const playerRef = useRef<YouTube>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const loadLyrics = useCallback(async () => {
    if (!suggestion.youtubeVideoId) {
      toast.error("ID do vídeo não fornecido");
      return;
    }

    try {
      setLoading(true);
      console.log(`🎵 Carregando letras para: ${suggestion.title} - ${suggestion.artist}`);

      // Primeiro, tentar buscar letras reais
      const response = await fetch(
        buildApiUrl(
          `/lyrics/search?title=${encodeURIComponent(
            suggestion.title
          )}&artist=${encodeURIComponent(suggestion.artist)}&youtubeVideoId=${
            suggestion.youtubeVideoId
          }`
        )
      );

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.lyrics) {
          setLyrics(data.lyrics);
          toast.success("Letras sincronizadas carregadas! 🎤");
          console.log(`✅ Letras carregadas com ${data.lyrics.lines?.length || 0} linhas`);
          return;
        }
      }

      // Fallback 1: Tentar buscar por YouTube ID
      try {
        const youtubeResponse = await fetch(
          buildApiUrl(`/lyrics/youtube/${suggestion.youtubeVideoId}`)
        );
        if (youtubeResponse.ok) {
          const youtubeData = await youtubeResponse.json();
          if (youtubeData.success && youtubeData.lyrics) {
            setLyrics(youtubeData.lyrics);
            toast.success("Letras encontradas pelo YouTube ID! 🎤");
            return;
          }
        }
      } catch (youtubeError) {
        console.warn("Fallback YouTube ID falhou:", youtubeError);
      }

      // Fallback 2: Letras de teste para demonstração
      const testResponse = await fetch(
        buildApiUrl(
          `/lyrics/test?title=${encodeURIComponent(
            suggestion.title
          )}&artist=${encodeURIComponent(suggestion.artist)}`
        )
      );

      if (testResponse.ok) {
        const testData = await testResponse.json();
        if (testData.success && testData.lyrics) {
          setLyrics(testData.lyrics);
          toast("Usando letras de demonstração 🎵", {
            icon: "ℹ️",
            duration: 4000
          });
          console.log("📝 Usando letras de teste");
          return;
        }
      }

      throw new Error("Nenhuma fonte de letras disponível");

    } catch (error) {
      console.error("Erro ao carregar letras:", error);
      toast.error("Não foi possível carregar as letras. Modo karaokê sem sincronização.");

      // Criar letras básicas para permitir karaokê sem sincronização
      setLyrics({
        id: `fallback_${suggestion.youtubeVideoId}`,
        title: suggestion.title,
        artist: suggestion.artist,
        duration: suggestion.duration || 180,
        language: "pt",
        lines: [
          { time: 0, text: "🎤 Modo Karaokê Ativo!" },
          { time: 5, text: "Cante junto com a música!" },
          { time: 10, text: "Letras sincronizadas não disponíveis" },
          { time: 15, text: "Mas você pode cantar do mesmo jeito! 🎵" }
        ],
        source: "fallback",
        isExplicit: false,
        hasTimestamps: false,
      });
    } finally {
      setLoading(false);
    }
  }, [suggestion.title, suggestion.artist, suggestion.youtubeVideoId, suggestion.duration]);

  const updateCurrentLine = useCallback(
    (time: number) => {
      if (!lyrics) return;

      let newIndex = -1;
      let newProgress = 0;

      for (let i = 0; i < lyrics.lines.length; i++) {
        if (time >= lyrics.lines[i].time) {
          newIndex = i;
        } else {
          break;
        }
      }

      if (newIndex >= 0 && newIndex < lyrics.lines.length - 1) {
        const currentLine = lyrics.lines[newIndex];
        const nextLine = lyrics.lines[newIndex + 1];
        const lineDuration = nextLine.time - currentLine.time;
        const elapsed = time - currentLine.time;
        newProgress = Math.min(1, Math.max(0, elapsed / lineDuration));
      }

      setCurrentLineIndex(newIndex);
      setProgress(newProgress);
    },
    [lyrics]
  );

  const syncLyrics = useCallback(() => {
    if (!playerRef.current || !lyrics) return;

    const player = playerRef.current.getInternalPlayer();
    if (player && typeof player.getCurrentTime === 'function') {
      player.getCurrentTime().then((time: number) => {
        setCurrentTime(time);
        updateCurrentLine(time);
      }).catch((error: any) => {
        console.warn('Erro ao obter tempo atual do player:', error);
      });
    }
  }, [lyrics, updateCurrentLine]);

  const togglePlay = useCallback(() => {
    const player = playerRef.current?.getInternalPlayer();
    if (player) {
      if (isPlaying) {
        player.pauseVideo();
      } else {
        player.playVideo();
      }
      setIsPlaying(!isPlaying);
    }
  }, [isPlaying]);

  const toggleMute = useCallback(() => {
    const player = playerRef.current?.getInternalPlayer();
    if (player) {
      if (isMuted) {
        player.unMute();
        player.setVolume(50);
      } else {
        player.mute();
      }
      setIsMuted(!isMuted);
    }
  }, [isMuted]);

  const restart = useCallback(() => {
    const player = playerRef.current?.getInternalPlayer();
    if (player) {
      player.seekTo(0);
      if (!isPlaying) {
        player.playVideo();
        setIsPlaying(true);
      }
    }
    setCurrentTime(0);
    setCurrentLineIndex(-1);
    setProgress(0);
  }, [isPlaying]);

  const toggleFullscreen = useCallback(() => {
    if (!containerRef.current) return;
    if (!isFullscreen) {
      containerRef.current.requestFullscreen?.();
    } else {
      document.exitFullscreen?.();
    }
    setIsFullscreen(!isFullscreen);
  }, [isFullscreen]);

  const handleYouTubeReady: YouTubeProps["onReady"] = useCallback(
    (event: Parameters<NonNullable<YouTubeProps["onReady"]>>[0]) => {
      event.target.setVolume(isMuted ? 0 : 50);
    },
    [isMuted]
  );

  const handleYouTubeStateChange: YouTubeProps["onStateChange"] = useCallback(
    (event: Parameters<NonNullable<YouTubeProps["onStateChange"]>>[0]) => {
      if (event.data === YouTube.PlayerState.PLAYING) {
        setIsPlaying(true);
      } else if (event.data === YouTube.PlayerState.PAUSED) {
        setIsPlaying(false);
      } else if (event.data === YouTube.PlayerState.ENDED) {
        setIsPlaying(false);
        setCurrentTime(0);
        setCurrentLineIndex(-1);
        setProgress(0);
      }
    },
    []
  );

  const formatTime = useCallback((seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  }, []);

  const getCurrentLine = useCallback((): LyricsLine | null => {
    if (!lyrics || currentLineIndex < 0) return null;
    return lyrics.lines[currentLineIndex] || null;
  }, [lyrics, currentLineIndex]);

  const getNextLine = useCallback((): LyricsLine | null => {
    if (
      !lyrics ||
      currentLineIndex < 0 ||
      currentLineIndex >= lyrics.lines.length - 1
    )
      return null;
    return lyrics.lines[currentLineIndex + 1] || null;
  }, [lyrics, currentLineIndex]);

  const getContextLines = useCallback((): LyricsLine[] => {
    if (!lyrics || currentLineIndex < 0) return [];
    const start = Math.max(0, currentLineIndex - 2);
    const end = Math.min(lyrics.lines.length, currentLineIndex + 3);
    return lyrics.lines.slice(start, end);
  }, [lyrics, currentLineIndex]);

  useEffect(() => {
    if (isOpen && suggestion.youtubeVideoId) {
      loadLyrics();
    }
    return () => {
      setLyrics(null);
      setCurrentTime(0);
      setCurrentLineIndex(-1);
      setProgress(0);
      setIsPlaying(false);
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [isOpen, suggestion.youtubeVideoId, loadLyrics]);

  useEffect(() => {
    if (isPlaying && lyrics) {
      // Sincronização mais frequente para melhor precisão
      timerRef.current = setInterval(syncLyrics, 100);
    } else if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [isPlaying, lyrics, syncLyrics]);

  // Handle fullscreen change for mobile
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex flex-col z-50 overflow-hidden"
        ref={containerRef}
        role="dialog"
        aria-modal="true"
        aria-labelledby="karaoke-title"
      >
        {/* Header Mobile-First */}
        <header className="flex items-center justify-between p-3 sm:p-4 bg-black/40 backdrop-blur-md border-b border-white/10">
          <div className="flex items-center gap-2 sm:gap-3 overflow-hidden flex-1 min-w-0">
            {suggestion.thumbnailUrl && (
              <img
                src={suggestion.thumbnailUrl}
                alt={`Capa de ${suggestion.title}`}
                className="w-8 h-8 sm:w-10 sm:h-10 rounded-lg object-cover flex-shrink-0 shadow-lg"
                aria-hidden="true"
              />
            )}
            <div className="min-w-0 flex-1">
              <h2
                id="karaoke-title"
                className="text-sm sm:text-lg font-bold text-white truncate leading-tight"
              >
                {suggestion.title}
              </h2>
              <p className="text-xs sm:text-sm text-purple-200 truncate">
                {suggestion.artist}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-1 sm:gap-2 flex-shrink-0">
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="p-2 text-white hover:bg-white/20 rounded-full transition-colors active:scale-95"
              aria-label={showSettings ? "Fechar configurações" : "Abrir configurações"}
            >
              <Settings className="w-4 h-4 sm:w-5 sm:h-5" aria-hidden="true" />
            </button>
            <button
              onClick={toggleFullscreen}
              className="p-2 text-white hover:bg-white/20 rounded-full transition-colors active:scale-95"
              aria-label={isFullscreen ? "Sair da tela cheia" : "Entrar na tela cheia"}
            >
              {isFullscreen ? (
                <Minimize className="w-4 h-4 sm:w-5 sm:h-5" aria-hidden="true" />
              ) : (
                <Maximize className="w-4 h-4 sm:w-5 sm:h-5" aria-hidden="true" />
              )}
            </button>
            <button
              onClick={onClose}
              className="p-2 text-white hover:bg-white/20 rounded-full transition-colors active:scale-95"
              aria-label="Fechar player de karaokê"
            >
              <X className="w-4 h-4 sm:w-5 sm:h-5" aria-hidden="true" />
            </button>
          </div>
        </header>

        {/* Settings Panel Mobile-First */}
        {showSettings && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-black/60 backdrop-blur-md p-3 sm:p-4 mx-3 sm:mx-4 rounded-xl border border-white/20 shadow-2xl"
          >
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
                <span className="text-white text-sm font-medium">Tamanho da fonte:</span>
                <div className="flex items-center gap-3 flex-1">
                  <input
                    type="range"
                    min="16"
                    max="40"
                    value={fontSize}
                    onChange={(e) => setFontSize(Number(e.target.value))}
                    className="flex-1 h-2 bg-white/20 rounded-lg appearance-none cursor-pointer"
                    aria-label="Ajustar tamanho da fonte"
                  />
                  <span className="text-white text-sm font-mono bg-white/10 px-2 py-1 rounded min-w-[50px] text-center">
                    {fontSize}px
                  </span>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
                <span className="text-white text-sm font-medium">Cor de destaque:</span>
                <div className="flex items-center gap-3">
                  <input
                    type="color"
                    value={highlightColor}
                    onChange={(e) => setHighlightColor(e.target.value)}
                    className="w-10 h-10 rounded-lg border-2 border-white/20 cursor-pointer"
                    aria-label="Escolher cor de destaque"
                  />
                  <span className="text-white text-sm font-mono bg-white/10 px-2 py-1 rounded">
                    {highlightColor}
                  </span>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Main Content Area - Mobile First */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* YouTube Player - Hidden on mobile, visible on larger screens */}
          {suggestion.youtubeVideoId && (
            <div className="hidden sm:block w-full px-3 sm:px-4 pt-2">
              <div className="w-full max-w-md mx-auto aspect-video">
                <YouTube
                  videoId={suggestion.youtubeVideoId}
                  opts={{
                    playerVars: {
                      autoplay: 0,
                      controls: 0,
                      rel: 0,
                      showinfo: 0,
                      modestbranding: 1,
                    },
                  }}
                  onReady={handleYouTubeReady}
                  onStateChange={handleYouTubeStateChange}
                  ref={playerRef}
                  className="w-full h-full rounded-xl overflow-hidden shadow-2xl border border-white/20"
                />
              </div>
            </div>
          )}

          {/* Lyrics Section - Main Focus */}
          <div className="flex-1 flex flex-col justify-center px-4 sm:px-6 py-4 overflow-hidden">
            {loading ? (
              <div className="text-center flex flex-col items-center justify-center h-full">
                <div className="relative">
                  <div className="w-16 h-16 border-4 border-purple-400 border-t-transparent rounded-full animate-spin mb-6"></div>
                  <div className="absolute inset-0 w-16 h-16 border-4 border-blue-400 border-b-transparent rounded-full animate-spin animate-reverse"></div>
                </div>
                <p className="text-white text-lg sm:text-xl font-medium">
                  Carregando letras...
                </p>
                <p className="text-purple-200 text-sm mt-2">
                  Preparando experiência de karaokê
                </p>
              </div>
            ) : lyrics ? (
              <div className="h-full flex flex-col justify-center text-center space-y-4 sm:space-y-6">
                {/* Linha atual - destaque principal */}
                <motion.div
                  key={`current-${currentLineIndex}`}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, ease: "easeOut" }}
                  className="flex-1 flex items-center justify-center px-2"
                  aria-live="polite"
                >
                  <div className="max-w-full">
                    <div
                      className="font-bold leading-tight text-center break-words"
                      style={{
                        fontSize: `${Math.max(fontSize * 0.8, 18)}px`,
                        color: getCurrentLine() ? highlightColor : "white",
                        textShadow: "2px 2px 8px rgba(0,0,0,0.9)",
                        filter: getCurrentLine() ? `drop-shadow(0 0 20px ${highlightColor}40)` : "none",
                        lineHeight: "1.2",
                      }}
                    >
                      {getCurrentLine()?.text || "🎵 Aguardando início..."}
                    </div>
                  </div>
                </motion.div>

                {/* Progresso da linha atual */}
                {getCurrentLine() && lyrics.hasTimestamps && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3 }}
                    className="px-6 sm:px-12"
                  >
                    <div className="w-full bg-white/20 rounded-full h-2 shadow-inner">
                      <motion.div
                        className="h-2 rounded-full bg-gradient-to-r from-purple-400 via-blue-400 to-cyan-400 shadow-lg"
                        style={{ width: `${progress * 100}%` }}
                        transition={{ duration: 0.1 }}
                      />
                    </div>
                    <div className="flex justify-between text-xs text-white/60 mt-1">
                      <span>Linha atual</span>
                      <span>{Math.round(progress * 100)}%</span>
                    </div>
                  </motion.div>
                )}

                {/* Próxima linha - preview */}
                {getNextLine() && (
                  <motion.div
                    key={`next-${currentLineIndex + 1}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 0.8, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.2 }}
                    className="px-4"
                  >
                    <div className="bg-white/5 rounded-xl p-3 sm:p-4 border border-white/10">
                      <div className="text-xs text-blue-300 font-medium mb-1">Próxima linha:</div>
                      <div
                        className="text-white/80 leading-relaxed break-words"
                        style={{
                          fontSize: `${Math.max(fontSize * 0.6, 14)}px`,
                          textShadow: "1px 1px 4px rgba(0,0,0,0.8)"
                        }}
                      >
                        {getNextLine()?.text}
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Contexto - linhas anteriores e seguintes (apenas em telas maiores) */}
                <div className="hidden sm:block space-y-1 opacity-30 max-h-24 overflow-y-auto">
                  {getContextLines().slice(0, 3).map((line, index) => {
                    const lineIndex = lyrics.lines.indexOf(line);
                    const isCurrent = lineIndex === currentLineIndex;
                    const isPrevious = lineIndex < currentLineIndex;

                    if (isCurrent || lineIndex === currentLineIndex + 1) return null;

                    return (
                      <motion.div
                        key={`context-${lineIndex}`}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: isPrevious ? 0.4 : 0.6 }}
                        transition={{ duration: 0.3 }}
                        className="text-white/50 text-sm px-4 truncate"
                      >
                        {line.text}
                      </motion.div>
                    );
                  })}
                </div>
              </div>
            ) : (
              <div className="h-full flex flex-col items-center justify-center text-center px-4">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.6, ease: "easeOut" }}
                  className="mb-6"
                >
                  <div className="relative">
                    <Mic className="w-16 h-16 sm:w-20 sm:h-20 text-purple-400 mx-auto mb-4" />
                    <div className="absolute inset-0 w-16 h-16 sm:w-20 sm:h-20 border-4 border-purple-400/30 rounded-full animate-ping"></div>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="space-y-3"
                >
                  <h3 className="text-white text-xl sm:text-2xl font-bold">
                    🎤 Modo Karaokê Ativo!
                  </h3>
                  <p className="text-purple-200 text-base sm:text-lg">
                    Letras sincronizadas não disponíveis
                  </p>
                  <p className="text-white/80 text-sm sm:text-base">
                    Mas você pode cantar junto com o vídeo! 🎵
                  </p>

                  <div className="mt-6 p-4 bg-white/5 rounded-xl border border-white/10">
                    <p className="text-white/70 text-sm">
                      💡 <strong>Dica:</strong> Use os controles abaixo para ajustar o volume e pausar a música
                    </p>
                  </div>
                </motion.div>
              </div>
            )}
          </div>
        </div>

        {/* Footer Controls - Mobile First */}
        <footer className="bg-black/50 backdrop-blur-md border-t border-white/10">
          {/* Progress Bar */}
          {lyrics && (
            <div className="px-4 pt-3">
              <div className="flex items-center justify-between text-white text-xs sm:text-sm mb-2">
                <span className="font-mono">{formatTime(currentTime)}</span>
                <span className="font-mono">
                  {formatTime(suggestion.duration || lyrics.duration)}
                </span>
              </div>
              <div className="w-full bg-white/20 rounded-full h-2 shadow-inner">
                <div
                  className="bg-gradient-to-r from-purple-400 to-blue-400 h-2 rounded-full transition-all duration-200 shadow-lg"
                  style={{
                    width: `${
                      (currentTime / (suggestion.duration || lyrics.duration)) * 100
                    }%`,
                  }}
                  role="progressbar"
                  aria-valuenow={
                    (currentTime / (suggestion.duration || lyrics.duration)) * 100
                  }
                  aria-valuemin={0}
                  aria-valuemax={100}
                />
              </div>
            </div>
          )}

          {/* Main Controls */}
          <div className="p-4">
            <div className="flex items-center justify-center gap-3 sm:gap-6">
              <button
                onClick={restart}
                className="p-3 bg-white/10 text-white rounded-full hover:bg-white/20 active:scale-95 transition-all border border-white/20"
                aria-label="Reiniciar música"
              >
                <RotateCcw className="w-5 h-5 sm:w-6 sm:h-6" aria-hidden="true" />
              </button>

              <button
                onClick={togglePlay}
                className="p-4 sm:p-5 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-full hover:from-purple-700 hover:to-blue-700 active:scale-95 transition-all shadow-lg border border-white/20"
                aria-label={isPlaying ? "Pausar música" : "Tocar música"}
              >
                {isPlaying ? (
                  <Pause className="w-6 h-6 sm:w-8 sm:h-8" aria-hidden="true" />
                ) : (
                  <Play className="w-6 h-6 sm:w-8 sm:h-8" aria-hidden="true" />
                )}
              </button>

              <button
                onClick={toggleMute}
                className="p-3 bg-white/10 text-white rounded-full hover:bg-white/20 active:scale-95 transition-all border border-white/20"
                aria-label={isMuted ? "Ativar som" : "Silenciar som"}
              >
                {isMuted ? (
                  <VolumeX className="w-5 h-5 sm:w-6 sm:h-6" aria-hidden="true" />
                ) : (
                  <Volume2 className="w-5 h-5 sm:w-6 sm:h-6" aria-hidden="true" />
                )}
              </button>

              {onVoteRequest && (
                <button
                  onClick={onVoteRequest}
                  className="p-3 bg-pink-600/80 text-white rounded-full hover:bg-pink-600 active:scale-95 transition-all border border-white/20"
                  aria-label="Solicitar votação da performance"
                >
                  <Heart className="w-5 h-5 sm:w-6 sm:h-6" aria-hidden="true" />
                </button>
              )}
            </div>

            {/* Status Indicators */}
            <div className="flex items-center justify-center gap-4 sm:gap-6 mt-4 text-white/70 text-xs sm:text-sm">
              <div className="flex items-center gap-1.5">
                <Mic className="w-4 h-4" aria-hidden="true" />
                <span className="hidden sm:inline">Cante Comigo</span>
                <span className="sm:hidden">Karaokê</span>
              </div>

              {lyrics?.hasTimestamps && (
                <div className="flex items-center gap-1.5">
                  <Star className="w-4 h-4 text-yellow-400" aria-hidden="true" />
                  <span className="hidden sm:inline">Sincronizado</span>
                  <span className="sm:hidden">Sync</span>
                </div>
              )}

              <div className="flex items-center gap-1.5">
                <Users className="w-4 h-4" aria-hidden="true" />
                <span className="hidden sm:inline">Interativo</span>
                <span className="sm:hidden">Live</span>
              </div>
            </div>
          </div>
        </footer>
      </motion.div>
    </AnimatePresence>
  );
};

export default KaraokePlayer;