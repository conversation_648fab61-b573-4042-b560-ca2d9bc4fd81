# ============================================
# SCRIPT DE ATUALIZAÇÃO DO BANCO DE DADOS (PowerShell)
# Aplica migração de execution_order em containers existentes
# ============================================

Write-Host "Iniciando atualização do banco de dados..." -ForegroundColor Cyan

# Verificar se o container do banco está rodando
$containerRunning = docker ps --format "table {{.Names}}" | Select-String "restaurant-playlist-db"
if (-not $containerRunning) {
    Write-Host "Container do banco de dados não está rodando!" -ForegroundColor Red
    Write-Host "Execute: docker-compose up -d" -ForegroundColor Yellow
    exit 1
}

Write-Host "Container do banco encontrado" -ForegroundColor Green

# Verificar se a coluna execution_order já existe
Write-Host "Verificando se a coluna execution_order já existe..." -ForegroundColor Cyan
$columnExists = docker exec restaurant-playlist-db psql -U restaurant_user -d restaurant_playlist -tAc "SELECT 1 FROM information_schema.columns WHERE table_name='playlists' AND column_name='execution_order';"

if ($columnExists -eq "1") {
    Write-Host "Coluna execution_order já existe" -ForegroundColor Green
} else {
    Write-Host "Adicionando coluna execution_order..." -ForegroundColor Yellow
    docker exec restaurant-playlist-db psql -U restaurant_user -d restaurant_playlist -c "ALTER TABLE playlists ADD COLUMN execution_order INTEGER DEFAULT NULL;"
    
    Write-Host "Criando índice para execution_order..." -ForegroundColor Yellow
    docker exec restaurant-playlist-db psql -U restaurant_user -d restaurant_playlist -c "CREATE INDEX IF NOT EXISTS idx_playlists_execution_order ON playlists(restaurant_id, execution_order);"
    
    Write-Host "Coluna e índice criados com sucesso" -ForegroundColor Green
}

# Executar migração dos dados
Write-Host "Executando migração de dados..." -ForegroundColor Cyan
Get-Content "database/migrate_execution_order.sql" | docker exec -i restaurant-playlist-db psql -U restaurant_user -d restaurant_playlist

if ($LASTEXITCODE -eq 0) {
    Write-Host "Migração concluída com sucesso!" -ForegroundColor Green
} else {
    Write-Host "Erro durante a migração" -ForegroundColor Red
    exit 1
}

# Verificar resultados
Write-Host "Verificando resultados da migração..." -ForegroundColor Cyan
$updatedCount = docker exec restaurant-playlist-db psql -U restaurant_user -d restaurant_playlist -tAc "SELECT COUNT(*) FROM playlists WHERE execution_order IS NOT NULL;"
Write-Host "Total de playlists com execution_order: $updatedCount" -ForegroundColor Green

Write-Host ""
Write-Host "Atualização do banco de dados concluída!" -ForegroundColor Green
Write-Host "Agora reinicie o backend para aplicar as mudanças:" -ForegroundColor Yellow
Write-Host "   docker-compose restart backend" -ForegroundColor Cyan
