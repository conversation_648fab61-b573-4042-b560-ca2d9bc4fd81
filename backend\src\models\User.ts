import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  BeforeInsert,
  BeforeUpdate
} from 'typeorm';
import * as bcrypt from 'bcryptjs';
import { Restaurant } from './Restaurant';

export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  MODERATOR = 'moderator',
  STAFF = 'staff'
}

@Entity('users')
@Index(['email'], { unique: true })
@Index(['restaurant', 'role'])
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar', unique: true })
  email: string;

  @Column({ type: 'varchar' })
  password: string;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.STAFF
  })
  role: UserRole;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'varchar', nullable: true })
  phone: string;

  @Column({ type: 'varchar', nullable: true })
  avatar: string;

  @Column({ type: 'timestamp', nullable: true })
  lastLoginAt: Date;

  @Column({ type: 'inet', nullable: true })
  lastLoginIp: string;

  @Column({ type: 'json', nullable: true })
  preferences: {
    language?: string;
    timezone?: string;
    notifications?: {
      email?: boolean;
      push?: boolean;
      newSuggestions?: boolean;
      moderationAlerts?: boolean;
    };
  };

  @Column({ type: 'timestamp', nullable: true })
  emailVerifiedAt: Date;

  @Column({ type: 'varchar', nullable: true })
  emailVerificationToken: string;

  @Column({ type: 'varchar', nullable: true })
  passwordResetToken: string;

  @Column({ type: 'timestamp', nullable: true })
  passwordResetExpiresAt: Date;

  @ManyToOne(() => Restaurant, restaurant => restaurant.users, {
    nullable: true,
    onDelete: 'SET NULL'
  })
  @JoinColumn({ name: 'restaurant_id' })
  restaurant: Restaurant;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Hooks para hash da senha
  @BeforeInsert()
  @BeforeUpdate()
  async hashPassword() {
    if (this.password && !this.password.startsWith('$2')) {
      const salt = await bcrypt.genSalt(12);
      this.password = await bcrypt.hash(this.password, salt);
    }
  }

  // Métodos da instância
  async comparePassword(candidatePassword: string): Promise<boolean> {
    return bcrypt.compare(candidatePassword, this.password);
  }

  toJSON() {
    const { password, emailVerificationToken, passwordResetToken, ...user } = this;
    return user;
  }

  // Verificar se o usuário tem permissão para uma ação
  hasPermission(action: string, resource?: string): boolean {
    switch (this.role) {
      case UserRole.SUPER_ADMIN:
        return true;
      
      case UserRole.ADMIN:
        // Admin pode fazer tudo no seu restaurante
        return true;
      
      case UserRole.MODERATOR:
        // Moderador pode moderar conteúdo e gerenciar playlists
        return ['moderate', 'playlist', 'analytics'].some(perm => action.includes(perm));
      
      case UserRole.STAFF:
        // Staff pode apenas visualizar
        return action.includes('read') || action.includes('view');
      
      default:
        return false;
    }
  }

  // Verificar se pode acessar restaurante
  canAccessRestaurant(restaurantId: string): boolean {
    if (this.role === UserRole.SUPER_ADMIN) {
      return true;
    }
    
    return this.restaurant?.id === restaurantId;
  }

  // Obter permissões como array
  getPermissions(): string[] {
    const permissions: string[] = [];

    switch (this.role) {
      case UserRole.SUPER_ADMIN:
        permissions.push(
          'restaurants:*',
          'users:*',
          'playlists:*',
          'suggestions:*',
          'analytics:*',
          'moderation:*'
        );
        break;

      case UserRole.ADMIN:
        permissions.push(
          'restaurant:read',
          'restaurant:update',
          'users:read',
          'users:create',
          'users:update',
          'playlists:*',
          'suggestions:*',
          'analytics:*',
          'moderation:*'
        );
        break;

      case UserRole.MODERATOR:
        permissions.push(
          'restaurant:read',
          'playlists:read',
          'playlists:create',
          'playlists:update',
          'suggestions:read',
          'suggestions:moderate',
          'analytics:read',
          'moderation:*'
        );
        break;

      case UserRole.STAFF:
        permissions.push(
          'restaurant:read',
          'playlists:read',
          'suggestions:read',
          'analytics:read'
        );
        break;
    }

    return permissions;
  }
}
