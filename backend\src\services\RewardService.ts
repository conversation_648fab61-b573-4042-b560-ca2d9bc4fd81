import { AppDataSource } from "../config/database";
import { Reward } from "../models/Reward";
import { CompetitiveVote } from "../models/CompetitiveVote";
import { Suggestion } from "../models/Suggestion";
import {
  notificationService,
  NotificationType,
  NotificationPriority,
} from "./NotificationService";
import { logger } from "../utils/logger";

export interface DailyWinner {
  sessionId: string;
  clientName: string;
  tableName: string;
  songTitle: string;
  artist: string;
  averageRating: number;
  totalVotes: number;
  rank: number;
}

class RewardService {
  private rewardRepository = AppDataSource.getRepository(Reward);
  private voteRepository = AppDataSource.getRepository(CompetitiveVote);
  private suggestionRepository = AppDataSource.getRepository(Suggestion);

  // Processar prêmios diários automaticamente
  async processDailyRewards(restaurantId: string): Promise<DailyWinner[]> {
    try {
      const today = new Date();
      const startOfDay = new Date(today);
      startOfDay.setHours(0, 0, 0, 0);

      const endOfDay = new Date(today);
      endOfDay.setHours(23, 59, 59, 999);

      // Buscar performances do dia
      const votes = await this.voteRepository
        .createQueryBuilder("vote")
        .leftJoinAndSelect("vote.suggestion", "suggestion")
        .leftJoinAndSelect("suggestion.restaurant", "restaurant")
        .leftJoinAndSelect("suggestion.session", "session")
        .where("restaurant.id = :restaurantId", { restaurantId })
        .andWhere("vote.createdAt BETWEEN :startOfDay AND :endOfDay", {
          startOfDay,
          endOfDay,
        })
        .getMany();

      // Agrupar por performance
      const performanceMap = new Map<
        string,
        {
          sessionId: string;
          clientName: string;
          tableName: string;
          songTitle: string;
          artist: string;
          votes: number[];
          suggestionId: string;
        }
      >();

      votes.forEach((vote) => {
        const key = `${vote.suggestion.clientSessionId}_${vote.suggestionId}`;

        if (!performanceMap.has(key)) {
          performanceMap.set(key, {
            sessionId: vote.suggestion.clientSessionId,
            clientName:
              vote.suggestion.clientSession?.clientName ||
              vote.suggestion.clientName ||
              "Cliente",
            tableName: `Mesa ${
              vote.suggestion.clientSession?.tableNumber ||
              vote.suggestion.tableNumber ||
              "?"
            }`,
            songTitle: vote.suggestion.title,
            artist: vote.suggestion.artist,
            votes: [],
            suggestionId: vote.suggestionId,
          });
        }

        performanceMap.get(key)!.votes.push(vote.rating);
      });

      // Calcular rankings
      const dailyWinners: DailyWinner[] = Array.from(performanceMap.values())
        .map((performance) => ({
          sessionId: performance.sessionId,
          clientName: performance.clientName,
          tableName: performance.tableName,
          songTitle: performance.songTitle,
          artist: performance.artist,
          averageRating:
            performance.votes.reduce((sum, rating) => sum + rating, 0) /
            performance.votes.length,
          totalVotes: performance.votes.length,
          rank: 0,
        }))
        .sort((a, b) => {
          if (b.averageRating !== a.averageRating) {
            return b.averageRating - a.averageRating;
          }
          return b.totalVotes - a.totalVotes;
        })
        .map((winner, index) => ({
          ...winner,
          rank: index + 1,
          averageRating: Math.round(winner.averageRating * 100) / 100,
        }));

      // Criar prêmios para os top 3
      for (let i = 0; i < Math.min(3, dailyWinners.length); i++) {
        const winner = dailyWinners[i];
        await this.createDailyWinnerReward(restaurantId, winner);
      }

      return dailyWinners;
    } catch (error) {
      logger.error("Erro ao processar prêmios diários:", error);
      return [];
    }
  }

  // Criar prêmio para vencedor diário
  private async createDailyWinnerReward(
    restaurantId: string,
    winner: DailyWinner
  ): Promise<void> {
    try {
      // Verificar se já existe prêmio para hoje
      const today = new Date();
      const startOfDay = new Date(today);
      startOfDay.setHours(0, 0, 0, 0);

      const existingReward = await this.rewardRepository.findOne({
        where: {
          restaurantId,
          sessionId: winner.sessionId,
          awardedFor: "daily_winner",
          awardedDate: { $gte: startOfDay } as any,
        },
      });

      if (existingReward) return; // Já tem prêmio hoje

      let rewardData: any;
      let title: string;
      let description: string;

      // Definir prêmio baseado na posição
      switch (winner.rank) {
        case 1:
          rewardData = {
            discountPercentage: 20,
            freeSongsCount: 3,
            priorityDuration: 120,
            usageLimit: 1,
            validUntil: new Date(
              Date.now() + 7 * 24 * 60 * 60 * 1000
            ).toISOString(), // 7 dias
          };
          title = "🥇 Campeão do Dia!";
          description = `Parabéns! Você foi o grande vencedor com "${winner.songTitle}" (${winner.averageRating}⭐)`;
          break;

        case 2:
          rewardData = {
            discountPercentage: 15,
            freeSongsCount: 2,
            priorityDuration: 90,
            usageLimit: 1,
            validUntil: new Date(
              Date.now() + 5 * 24 * 60 * 60 * 1000
            ).toISOString(), // 5 dias
          };
          title = "🥈 Vice-Campeão!";
          description = `Excelente performance com "${winner.songTitle}" (${winner.averageRating}⭐)`;
          break;

        case 3:
          rewardData = {
            discountPercentage: 10,
            freeSongsCount: 1,
            priorityDuration: 60,
            usageLimit: 1,
            validUntil: new Date(
              Date.now() + 3 * 24 * 60 * 60 * 1000
            ).toISOString(), // 3 dias
          };
          title = "🥉 Terceiro Lugar!";
          description = `Ótima apresentação com "${winner.songTitle}" (${winner.averageRating}⭐)`;
          break;

        default:
          return; // Não criar prêmio para posições além do top 3
      }

      // Criar prêmio
      const reward = Reward.createReward({
        restaurantId,
        sessionId: winner.sessionId,
        clientName: winner.clientName,
        tableName: winner.tableName,
        type: "custom",
        title,
        description,
        rewardData,
        awardedFor: "daily_winner",
        expiresAt: new Date(rewardData.validUntil),
        performanceData: {
          songTitle: winner.songTitle,
          artist: winner.artist,
          averageRating: winner.averageRating,
          totalVotes: winner.totalVotes,
          rank: winner.rank,
          competitionDate: new Date().toISOString().split("T")[0],
        },
      });

      await this.rewardRepository.save(reward);

      // Notificar vencedor
      await notificationService.sendToSession(winner.sessionId, {
        type: NotificationType.SUCCESS,
        title: `🏆 ${title}`,
        message: description,
        priority: NotificationPriority.HIGH,
        category: "reward",
        data: {
          rewardId: reward.id,
          reward: reward.toPublicJSON(),
        },
      });

      // Notificar restaurante
      await notificationService.sendToRestaurant(restaurantId, {
        type: NotificationType.INFO,
        title: "🏆 Prêmio Concedido",
        message: `${winner.tableName} ganhou: ${title}`,
        priority: NotificationPriority.NORMAL,
        category: "reward",
        data: {
          rewardId: reward.id,
          winner,
        },
      });
    } catch (error) {
      logger.error("Erro ao criar prêmio do vencedor diário:", error);
    }
  }

  // Obter prêmios de um cliente
  async getClientRewards(sessionId: string): Promise<Reward[]> {
    try {
      return await this.rewardRepository.find({
        where: { sessionId },
        order: { awardedDate: "DESC" },
      });
    } catch (error) {
      logger.error("Erro ao obter prêmios do cliente:", error);
      return [];
    }
  }

  // Obter prêmios de um restaurante
  async getRestaurantRewards(
    restaurantId: string,
    limit: number = 50
  ): Promise<Reward[]> {
    try {
      return await this.rewardRepository.find({
        where: { restaurantId },
        order: { awardedDate: "DESC" },
        take: limit,
      });
    } catch (error) {
      logger.error("Erro ao obter prêmios do restaurante:", error);
      return [];
    }
  }

  // Usar prêmio
  async useReward(
    rewardId: string,
    sessionId: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      const reward = await this.rewardRepository.findOne({
        where: { id: rewardId, sessionId },
      });

      if (!reward) {
        return { success: false, message: "Prêmio não encontrado" };
      }

      if (!reward.canBeUsed()) {
        return {
          success: false,
          message: "Prêmio não pode ser usado (expirado ou já utilizado)",
        };
      }

      reward.markAsUsed();
      await this.rewardRepository.save(reward);

      // Notificar uso do prêmio
      await notificationService.sendToSession(sessionId, {
        type: NotificationType.SUCCESS,
        title: "🎁 Prêmio Utilizado!",
        message: `Você usou: ${reward.title}`,
        priority: NotificationPriority.NORMAL,
        category: "reward",
        data: {
          rewardId: reward.id,
          rewardType: reward.type,
        },
      });

      return { success: true, message: "Prêmio utilizado com sucesso!" };
    } catch (error) {
      logger.error("Erro ao usar prêmio:", error);
      return { success: false, message: "Erro interno do servidor" };
    }
  }

  // Criar prêmio personalizado (admin)
  async createCustomReward(data: {
    restaurantId: string;
    sessionId: string;
    clientName?: string;
    tableName?: string;
    type: Reward["type"];
    title: string;
    description: string;
    rewardData: any;
    expiresAt?: Date;
  }): Promise<Reward> {
    const reward = Reward.createReward({
      ...data,
      awardedFor: "custom" as any,
    });

    await this.rewardRepository.save(reward);

    // Notificar cliente
    await notificationService.sendToSession(data.sessionId, {
      type: NotificationType.SUCCESS,
      title: "🎁 Novo Prêmio!",
      message: `Você ganhou: ${data.title}`,
      priority: NotificationPriority.HIGH,
      category: "reward",
      data: {
        rewardId: reward.id,
        reward: reward.toPublicJSON(),
      },
    });

    return reward;
  }

  // Obter estatísticas de prêmios
  async getRewardStats(restaurantId: string): Promise<{
    totalRewards: number;
    activeRewards: number;
    usedRewards: number;
    expiredRewards: number;
    rewardsByType: Record<string, number>;
    topWinners: Array<{
      clientName: string;
      tableName: string;
      rewardCount: number;
    }>;
  }> {
    try {
      const rewards = await this.rewardRepository.find({
        where: { restaurantId },
      });

      const stats = {
        totalRewards: rewards.length,
        activeRewards: rewards.filter((r) => r.isActive()).length,
        usedRewards: rewards.filter((r) => r.status === "used").length,
        expiredRewards: rewards.filter((r) => r.isExpired()).length,
        rewardsByType: {} as Record<string, number>,
        topWinners: [] as Array<{
          clientName: string;
          tableName: string;
          rewardCount: number;
        }>,
      };

      // Contar por tipo
      rewards.forEach((reward) => {
        stats.rewardsByType[reward.type] =
          (stats.rewardsByType[reward.type] || 0) + 1;
      });

      // Top vencedores
      const winnerMap = new Map<
        string,
        { clientName: string; tableName: string; count: number }
      >();

      rewards.forEach((reward) => {
        if (reward.clientName && reward.tableName) {
          const key = `${reward.clientName}_${reward.tableName}`;
          if (winnerMap.has(key)) {
            winnerMap.get(key)!.count++;
          } else {
            winnerMap.set(key, {
              clientName: reward.clientName,
              tableName: reward.tableName,
              count: 1,
            });
          }
        }
      });

      stats.topWinners = Array.from(winnerMap.values())
        .sort((a, b) => b.count - a.count)
        .slice(0, 10)
        .map((winner) => ({
          clientName: winner.clientName,
          tableName: winner.tableName,
          rewardCount: winner.count,
        }));

      return stats;
    } catch (error) {
      logger.error("Erro ao obter estatísticas de prêmios:", error);
      return {
        totalRewards: 0,
        activeRewards: 0,
        usedRewards: 0,
        expiredRewards: 0,
        rewardsByType: {},
        topWinners: [],
      };
    }
  }

  // Limpar prêmios expirados
  async cleanupExpiredRewards(restaurantId: string): Promise<number> {
    try {
      const result = await this.rewardRepository
        .createQueryBuilder()
        .update(Reward)
        .set({ status: "expired" })
        .where("restaurant_id = :restaurantId", { restaurantId })
        .andWhere("status = :status", { status: "active" })
        .andWhere("expiresAt < :now", { now: new Date() })
        .execute();

      return result.affected || 0;
    } catch (error) {
      logger.error("Erro ao limpar prêmios expirados:", error);
      return 0;
    }
  }
}

export const rewardService = new RewardService();
export default RewardService;
