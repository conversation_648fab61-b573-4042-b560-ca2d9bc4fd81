#!/usr/bin/env node

// Teste automatizado do fluxo de supervoto e reordenação
// - Consulta ranking
// - Aplica supervoto (R$20 => +8)
// - Consulta ranking novamente e valida
// - Dispara reordenação manual e valida retorno

const base = "http://localhost:8001/api/v1";

const sleep = (ms) => new Promise((r) => setTimeout(r, ms));

async function getJson(url, opts = {}) {
  const res = await fetch(url, opts);
  const text = await res.text();
  let json;
  try {
    json = JSON.parse(text);
  } catch {
    json = { raw: text };
  }
  return { ok: res.ok, status: res.status, json };
}

async function main() {
  console.log("\n=== 🔎 Ranking (antes) ===");
  let r1 = await getJson(
    `${base}/collaborative-playlist/demo-restaurant/ranking`
  );
  console.log(JSON.stringify(r1.json, null, 2));

  if (!r1.ok) {
    console.error("❌ Falha ao obter ranking inicial");
    process.exit(1);
  }

  const first = r1.json?.data?.[0];
  if (!first) {
    console.error(
      "⚠️ Ranking vazio; criando um caso de teste exigiria criar sugestão antes."
    );
    process.exit(1);
  }

  const videoId = first.youtubeVideoId;
  const beforeVotes = Number(first.voteCount || 0);
  const beforeRevenue = Number(first.totalRevenue || "0");

  console.log(`\nAplicando supervoto de R$20 no vídeo ${videoId}...`);
  const payload = {
    youtubeVideoId: videoId,
    paymentAmount: 20,
    paymentId: `pix_test_${Date.now()}`,
    tableNumber: 7,
    clientSessionId: `cli_${Date.now()}`,
  };

  const sv = await getJson(
    `${base}/collaborative-playlist/demo-restaurant/supervote`,
    {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(payload),
    }
  );
  console.log("\n=== 🎟️ Supervote response ===");
  console.log(JSON.stringify(sv.json, null, 2));
  if (!sv.ok) {
    console.error("❌ Falha ao aplicar supervoto");
    process.exit(1);
  }

  // Pequeno intervalo para processamento/agg
  await sleep(800);

  console.log("\n=== 🔎 Ranking (depois) ===");
  let r2 = await getJson(
    `${base}/collaborative-playlist/demo-restaurant/ranking`
  );
  console.log(JSON.stringify(r2.json, null, 2));
  if (!r2.ok) {
    console.error("❌ Falha ao obter ranking após supervoto");
    process.exit(1);
  }

  const after =
    r2.json?.data?.find((d) => d.youtubeVideoId === videoId) ||
    r2.json?.data?.[0];
  const afterVotes = Number(after?.voteCount || 0);
  const afterRevenue = Number(after?.totalRevenue || "0");

  const expectedWeight = 8; // R$20 => +8
  const voteDelta = afterVotes - beforeVotes;
  const revenueDelta = afterRevenue - beforeRevenue;

  console.log(`\n📊 Validações:`);
  console.log(
    `- Votos antes: ${beforeVotes}, depois: ${afterVotes}, delta: ${voteDelta}`
  );
  console.log(
    `- Receita antes: ${beforeRevenue.toFixed(
      2
    )}, depois: ${afterRevenue.toFixed(2)}, delta: ${revenueDelta.toFixed(2)}`
  );

  if (voteDelta < expectedWeight) {
    console.error(
      `⚠️ Peso inesperado. Esperado >= ${expectedWeight}, obtido ${voteDelta}`
    );
  } else {
    console.log(`✅ Peso de supervoto compatível (>= ${expectedWeight})`);
  }

  if (revenueDelta < 20) {
    console.error(
      `⚠️ Receita não agregada corretamente. Esperado >= 20, obtido ${revenueDelta}`
    );
  } else {
    console.log("✅ Receita agregada corretamente (>= 20)");
  }

  console.log("\n=== ♻️ Reordenação manual ===");
  const mr = await getJson(`${base}/playlist-reorder/manual/demo-restaurant`, {
    method: "POST",
  });
  console.log(JSON.stringify(mr.json, null, 2));
  if (!mr.ok || !mr.json?.success) {
    console.error("❌ Falha ao reordenar manualmente");
    process.exit(1);
  } else {
    console.log("✅ Reordenação manual executada");
  }

  console.log("\n=== 📈 Status do serviço de reordenação ===");
  const st = await getJson(`${base}/playlist-reorder/status`);
  console.log(JSON.stringify(st.json, null, 2));

  console.log("\n✅ Teste concluído.");
}

main().catch((e) => {
  console.error("❌ Erro no teste:", e);
  process.exit(1);
});
