import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";
import { Restaurant } from "./Restaurant";

export interface TimeSlot {
  playlistId: string;
  playlistName: string;
  startTime: string; // HH:MM format
  endTime: string; // HH:MM format
  days: number[]; // 0=Sunday, 1=Monday, ..., 6=Saturday
  priority: number; // Higher number = higher priority
  isActive: boolean;
  description?: string;
}

@Entity("playlist_schedules")
export class PlaylistSchedule {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @ManyToOne(() => Restaurant, { onDelete: "CASCADE" })
  @JoinColumn({ name: "restaurant_id" })
  restaurant: Restaurant;

  @Column({ name: "restaurant_id" })
  restaurantId: string;

  @Column({ type: "varchar" })
  name: string;

  @Column({ type: "text", nullable: true })
  description: string;

  @Column({ type: "json" })
  timeSlots: TimeSlot[];

  @Column({ type: "boolean", default: true })
  isActive: boolean;

  @Column({ type: "varchar", default: "normal" })
  mode: "normal" | "shuffle" | "repeat" | "smart"; // Modos de reprodução

  @Column({ type: "json", nullable: true })
  settings: any;

  @CreateDateColumn({ name: "createdAt" })
  createdAt: Date;

  @UpdateDateColumn({ name: "updatedAt" })
  updatedAt: Date;

  // Método para obter playlist ativa no momento atual
  getCurrentActivePlaylist(): TimeSlot | null {
    const now = new Date();
    const currentDay = now.getDay(); // 0=Sunday, 1=Monday, etc.
    const currentTime = now.toTimeString().slice(0, 5); // HH:MM

    // Filtrar slots ativos para o dia atual
    const todaySlots = this.timeSlots.filter(
      (slot) => slot.isActive && slot.days.includes(currentDay)
    );

    // Encontrar slot que está ativo no horário atual
    const activeSlots = todaySlots.filter((slot) => {
      const startTime = slot.startTime;
      const endTime = slot.endTime;

      // Lidar com horários que passam da meia-noite (ex: 23:00 - 02:00)
      if (startTime <= endTime) {
        return currentTime >= startTime && currentTime <= endTime;
      } else {
        return currentTime >= startTime || currentTime <= endTime;
      }
    });

    // Se múltiplos slots estão ativos, usar o de maior prioridade
    if (activeSlots.length > 0) {
      return activeSlots.reduce((prev, current) =>
        current.priority > prev.priority ? current : prev
      );
    }

    return null;
  }

  // Método para obter próxima playlist agendada
  getNextScheduledPlaylist(): { slot: TimeSlot; startsIn: number } | null {
    const now = new Date();
    const currentDay = now.getDay();
    const currentTime = now.toTimeString().slice(0, 5);

    let nextSlot: TimeSlot | null = null;
    let minTimeUntilStart = Infinity;

    for (const slot of this.timeSlots) {
      if (!slot.isActive) continue;

      for (const day of slot.days) {
        let daysUntil = (day - currentDay + 7) % 7;

        // Se é hoje, verificar se ainda não passou o horário
        if (daysUntil === 0 && slot.startTime <= currentTime) {
          daysUntil = 7; // Próxima semana
        }

        const timeUntilStart =
          daysUntil * 24 * 60 +
          this.timeToMinutes(slot.startTime) -
          this.timeToMinutes(currentTime);

        if (timeUntilStart < minTimeUntilStart && timeUntilStart > 0) {
          minTimeUntilStart = timeUntilStart;
          nextSlot = slot;
        }
      }
    }

    return nextSlot ? { slot: nextSlot, startsIn: minTimeUntilStart } : null;
  }

  private timeToMinutes(time: string): number {
    const [hours, minutes] = time.split(":").map(Number);
    return hours * 60 + minutes;
  }

  // Validar se não há conflitos de horário
  validateTimeSlots(): { isValid: boolean; conflicts: string[] } {
    const conflicts: string[] = [];

    for (let i = 0; i < this.timeSlots.length; i++) {
      for (let j = i + 1; j < this.timeSlots.length; j++) {
        const slot1 = this.timeSlots[i];
        const slot2 = this.timeSlots[j];

        // Verificar se há dias em comum
        const commonDays = slot1.days.filter((day) => slot2.days.includes(day));

        if (commonDays.length > 0) {
          // Verificar sobreposição de horários
          const overlap = this.checkTimeOverlap(
            slot1.startTime,
            slot1.endTime,
            slot2.startTime,
            slot2.endTime
          );

          if (overlap) {
            conflicts.push(
              `Conflito entre "${slot1.playlistName}" e "${
                slot2.playlistName
              }" nos dias ${commonDays.join(", ")}`
            );
          }
        }
      }
    }

    return {
      isValid: conflicts.length === 0,
      conflicts,
    };
  }

  private checkTimeOverlap(
    start1: string,
    end1: string,
    start2: string,
    end2: string
  ): boolean {
    const s1 = this.timeToMinutes(start1);
    const e1 = this.timeToMinutes(end1);
    const s2 = this.timeToMinutes(start2);
    const e2 = this.timeToMinutes(end2);

    // Lidar com horários que passam da meia-noite
    if (s1 > e1) {
      // slot1 passa da meia-noite
      if (s2 > e2) {
        // slot2 também passa da meia-noite
        return true; // Sempre há sobreposição
      } else {
        return s2 <= e1 || e2 >= s1;
      }
    } else if (s2 > e2) {
      // apenas slot2 passa da meia-noite
      return s1 <= e2 || e1 >= s2;
    } else {
      // nenhum passa da meia-noite
      return Math.max(s1, s2) < Math.min(e1, e2);
    }
  }
}
