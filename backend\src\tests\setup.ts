/**
 * Setup global para testes Jest
 */

// Mock do logger para evitar logs durante os testes
jest.mock('../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn()
  },
  logYouTubeAPI: jest.fn()
}));

// Mock do Redis client
jest.mock('../config/redis', () => ({
  redisClient: {
    isReady: jest.fn().mockReturnValue(true),
    getClient: jest.fn().mockReturnValue({
      get: jest.fn(),
      set: jest.fn(),
      setEx: jest.fn(),
      del: jest.fn(),
      exists: jest.fn(),
      expire: jest.fn(),
      flushall: jest.fn()
    }),
    getCachedYouTubeSearch: jest.fn(),
    setCachedYouTubeSearch: jest.fn()
  }
}));

// Mock do AppDataSource
jest.mock('../config/database', () => ({
  AppDataSource: {
    getRepository: jest.fn(),
    initialize: jest.fn(),
    isInitialized: true,
    createQueryBuilder: jest.fn(),
    manager: {
      query: jest.fn(),
      save: jest.fn(),
      find: jest.fn(),
      findOne: jest.fn(),
      count: jest.fn(),
      delete: jest.fn(),
      update: jest.fn()
    }
  }
}));

// Mock do axios para testes de API
jest.mock('axios', () => ({
  default: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    create: jest.fn(() => ({
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn()
    }))
  },
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn()
}));

// Mock do QRCode
jest.mock('qrcode', () => ({
  toDataURL: jest.fn().mockResolvedValue('data:image/png;base64,mock-qr-code')
}));

// Mock das variáveis de ambiente para testes
process.env.NODE_ENV = 'test';
process.env.YOUTUBE_API_KEY = 'test-youtube-api-key';
process.env.YOUTUBE_API_QUOTA_LIMIT = '10000';
process.env.MERCADO_PAGO_ACCESS_TOKEN = 'test-mp-token';
process.env.MERCADO_PAGO_PUBLIC_KEY = 'test-mp-public-key';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.REDIS_URL = 'redis://localhost:6379';

// Configurar timeout global para testes
jest.setTimeout(10000);

// Limpar mocks após cada teste
afterEach(() => {
  jest.clearAllMocks();
});

// Setup global antes de todos os testes
beforeAll(() => {
  // Suprimir console.log durante os testes
  jest.spyOn(console, 'log').mockImplementation(() => {});
  jest.spyOn(console, 'warn').mockImplementation(() => {});
  jest.spyOn(console, 'error').mockImplementation(() => {});
});

// Cleanup após todos os testes
afterAll(() => {
  // Restaurar console
  jest.restoreAllMocks();
});
