import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Play,
  Pause,
  SkipForward,
  SkipBack,
  Volume2,
  VolumeX,
  Clock,
  Music,
  Users,
  MoreVertical,
  Trash2,
  MoveUp,
  MoveDown,
  RefreshCw,
  History,
  Settings,
} from "lucide-react";
import { toast } from "react-hot-toast";
import { apiService } from "../../services/api";
import { PlayQueue } from "@/types";

interface QueueItem {
  id: string;
  title: string;
  artist: string;
  duration: string;
  thumbnailUrl: string;
  addedBy: string;
  addedAt: string;
  votes: number;
  isPlaying?: boolean;
  position: number;
}

interface PlaybackState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  isMuted: boolean;
  currentTrack?: QueueItem;
}

const QueueManager: React.FC = () => {
  const [queue, setQueue] = useState<QueueItem[]>([]);
  const [playHistory, setPlayHistory] = useState<QueueItem[]>([]);
  const [playbackState, setPlaybackState] = useState<PlaybackState>({
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    volume: 75,
    isMuted: false,
  });
  const [loading, setLoading] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [selectedItem, setSelectedItem] = useState<QueueItem | null>(null);

  const restaurantId = "demo-restaurant";

  // Carregar dados da fila
  useEffect(() => {
    loadQueueData();

    // Atualizar a cada 5 segundos
    const interval = setInterval(loadQueueData, 5000);
    return () => clearInterval(interval);
  }, []);

  // Simular progresso da música
  useEffect(() => {
    if (playbackState.isPlaying && playbackState.currentTrack) {
      const interval = setInterval(() => {
        setPlaybackState((prev) => {
          const newTime = prev.currentTime + 1;
          // Se chegou ao fim da música, pular para próxima
          if (newTime >= prev.duration) {
            skipToNext();
            return { ...prev, currentTime: 0 };
          }
          return { ...prev, currentTime: newTime };
        });
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [playbackState.isPlaying, playbackState.currentTrack]);

  const toQueueItem = (item: any): QueueItem => ({
    id: item.id || item.youtubeVideoId || item.videoId || String(item.title),
    title: item.title || item.name || "",
    artist: item.artist || item.channelName || "",
    duration: typeof item.duration === "number" ? `${Math.floor(item.duration/60)}:${String(Math.floor(item.duration%60)).padStart(2,"0")}` : (item.duration || item.formattedDuration || "0:00"),
    thumbnailUrl: item.thumbnailUrl,
    addedBy: item.addedBy || item.requestedBy,
    addedAt: item.addedAt || item.createdAt,
    votes: item.votes ?? item.voteCount ?? 0,
    isPlaying: item.isPlaying,
    position: item.position ?? item.queuePosition ?? 0,
  });

  const loadQueueData = async () => {
    try {
      const data: PlayQueue = await apiService.getPlayQueue(restaurantId);

      if (data?.queue) {
        const mappedQueue = (data.queue || []).map(toQueueItem);
        setQueue(mappedQueue);
        const hist = (data.history || []).map(toQueueItem);
        setPlayHistory(hist);
        setPlaybackState((prev) => ({
          ...prev,
          currentTrack: data.currentlyPlaying ? toQueueItem(data.currentlyPlaying) : mappedQueue[0],
          isPlaying: data.playbackState?.isPlaying || false,
          currentTime: data.playbackState?.currentTime || 0,
          duration: data.playbackState?.duration || 0,
          volume: data.playbackState?.volume || 75,
          isMuted: data.playbackState?.isMuted || false,
        }));
      }
    } catch (error) {
      console.error("Erro ao carregar fila:", error);
      // Fallback para dados mock em caso de erro
      loadMockData();
    }
  };

  const loadMockData = () => {
    const mockQueue: QueueItem[] = [
      {
        id: "1",
        title: "Bohemian Rhapsody",
        artist: "Queen",
        duration: "5:55",
        thumbnailUrl: "https://i.ytimg.com/vi/fJ9rUzIMcZQ/mqdefault.jpg",
        addedBy: "Mesa 5",
        addedAt: "2024-01-15T14:30:00Z",
        votes: 15,
        isPlaying: true,
        position: 1,
      },
      {
        id: "2",
        title: "Hotel California",
        artist: "Eagles",
        duration: "6:30",
        thumbnailUrl: "https://i.ytimg.com/vi/BciS5krYL80/mqdefault.jpg",
        addedBy: "Mesa 2",
        addedAt: "2024-01-15T14:25:00Z",
        votes: 12,
        position: 2,
      },
      {
        id: "3",
        title: "Imagine",
        artist: "John Lennon",
        duration: "3:07",
        thumbnailUrl: "https://i.ytimg.com/vi/YkgkThdzX-8/mqdefault.jpg",
        addedBy: "Mesa 8",
        addedAt: "2024-01-15T14:20:00Z",
        votes: 8,
        position: 3,
      },
    ];

    const mockHistory: QueueItem[] = [
      {
        id: "h1",
        title: "Yesterday",
        artist: "The Beatles",
        duration: "2:05",
        thumbnailUrl: "https://i.ytimg.com/vi/NrgmdOz227I/mqdefault.jpg",
        addedBy: "Mesa 3",
        addedAt: "2024-01-15T13:45:00Z",
        votes: 20,
        position: 0,
      },
    ];

    setQueue(mockQueue);
    setPlayHistory(mockHistory);
    setPlaybackState((prev) => ({ ...prev, currentTrack: mockQueue[0] }));
  };

  const togglePlayback = async () => {
    const action = playbackState.isPlaying ? "pause" : "play";

    try {
      const response = await fetch(
        `http://localhost:8001/api/v1/queue/${restaurantId}/playback`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ action }),
        }
      );

      if (response.ok) {
        setPlaybackState((prev) => ({ ...prev, isPlaying: !prev.isPlaying }));
        toast.success(
          playbackState.isPlaying ? "Música pausada" : "Música reproduzindo"
        );
      }
    } catch (error) {
      console.error("Erro ao controlar reprodução:", error);
      toast.error("Erro ao controlar reprodução");
    }
  };

  const skipToNext = () => {
    if (queue.length > 1) {
      const currentTrack = queue[0];
      const newQueue = queue.slice(1);
      const newHistory = [currentTrack, ...playHistory];

      setQueue(newQueue);
      setPlayHistory(newHistory);
      setPlaybackState((prev) => ({
        ...prev,
        currentTrack: newQueue[0],
        currentTime: 0,
      }));

      toast.success("Próxima música");
    }
  };

  const skipToPrevious = () => {
    if (playHistory.length > 0) {
      const previousTrack = playHistory[0];
      const newHistory = playHistory.slice(1);
      const newQueue = [previousTrack, ...queue];

      setQueue(newQueue);
      setPlayHistory(newHistory);
      setPlaybackState((prev) => ({
        ...prev,
        currentTrack: previousTrack,
        currentTime: 0,
      }));

      toast.success("Música anterior");
    }
  };

  const adjustVolume = (volume: number) => {
    setPlaybackState((prev) => ({ ...prev, volume, isMuted: volume === 0 }));
  };

  const toggleMute = () => {
    setPlaybackState((prev) => ({
      ...prev,
      isMuted: !prev.isMuted,
      volume: prev.isMuted ? 75 : 0,
    }));
  };

  const removeFromQueue = async (itemId: string) => {
    try {
      const response = await fetch(
        `http://localhost:8001/api/v1/queue/${restaurantId}/song/${itemId}`,
        {
          method: "DELETE",
        }
      );

      if (response.ok) {
        setQueue((prev) => prev.filter((item) => item.id !== itemId));
        toast.success("Música removida da fila");
      }
    } catch (error) {
      console.error("Erro ao remover música:", error);
      toast.error("Erro ao remover música");
    }
  };

  const moveItemUp = (itemId: string) => {
    setQueue((prev) => {
      const index = prev.findIndex((item) => item.id === itemId);
      if (index > 0) {
        const newQueue = [...prev];
        [newQueue[index], newQueue[index - 1]] = [
          newQueue[index - 1],
          newQueue[index],
        ];
        return newQueue;
      }
      return prev;
    });
  };

  const moveItemDown = (itemId: string) => {
    setQueue((prev) => {
      const index = prev.findIndex((item) => item.id === itemId);
      if (index < prev.length - 1) {
        const newQueue = [...prev];
        [newQueue[index], newQueue[index + 1]] = [
          newQueue[index + 1],
          newQueue[index],
        ];
        return newQueue;
      }
      return prev;
    });
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("pt-BR", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Gestão de Fila
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Controle a reprodução e gerencie a fila de músicas
          </p>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowHistory(!showHistory)}
            className={`px-4 py-2 rounded-lg flex items-center space-x-2 ${
              showHistory
                ? "bg-blue-600 text-white"
                : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
            }`}
          >
            <History className="w-4 h-4" />
            <span>Histórico</span>
          </button>

          <button className="p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg">
            <Settings className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Player de controle */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Player de Controle</h3>
          <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
            <Music className="w-4 h-4" />
            <span>{queue.length} na fila</span>
          </div>
        </div>

        {playbackState.currentTrack ? (
          <div className="space-y-4">
            {/* Música atual */}
            <div className="flex items-center space-x-4">
              <img
                src={playbackState.currentTrack.thumbnailUrl}
                alt={playbackState.currentTrack.title}
                className="w-16 h-12 object-cover rounded"
              />
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-gray-900 dark:text-white truncate">
                  {playbackState.currentTrack.title}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                  {playbackState.currentTrack.artist}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-500">
                  Adicionada por {playbackState.currentTrack.addedBy}
                </p>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {playbackState.currentTrack.votes} votos
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-500">
                  {playbackState.currentTrack.duration}
                </p>
              </div>
            </div>

            {/* Barra de progresso */}
            <div className="space-y-2">
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{
                    width: `${
                      (playbackState.currentTime / playbackState.duration) * 100
                    }%`,
                  }}
                />
              </div>
              <div className="flex justify-between text-xs text-gray-500 dark:text-gray-500">
                <span>{formatTime(playbackState.currentTime)}</span>
                <span>{formatTime(playbackState.duration)}</span>
              </div>
            </div>

            {/* Controles */}
            <div className="flex items-center justify-center space-x-4">
              <button
                onClick={skipToPrevious}
                disabled={playHistory.length === 0}
                className="p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg disabled:opacity-50"
              >
                <SkipBack className="w-5 h-5" />
              </button>

              <button
                onClick={togglePlayback}
                className="p-3 bg-blue-600 text-white rounded-full hover:bg-blue-700"
              >
                {playbackState.isPlaying ? (
                  <Pause className="w-6 h-6" />
                ) : (
                  <Play className="w-6 h-6" />
                )}
              </button>

              <button
                onClick={skipToNext}
                disabled={queue.length <= 1}
                className="p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg disabled:opacity-50"
              >
                <SkipForward className="w-5 h-5" />
              </button>
            </div>

            {/* Controle de volume */}
            <div className="flex items-center space-x-3">
              <button
                onClick={toggleMute}
                className="text-gray-600 dark:text-gray-400"
              >
                {playbackState.isMuted ? (
                  <VolumeX className="w-4 h-4" />
                ) : (
                  <Volume2 className="w-4 h-4" />
                )}
              </button>
              <div className="flex-1">
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={playbackState.isMuted ? 0 : playbackState.volume}
                  onChange={(e) => adjustVolume(parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer"
                />
              </div>
              <span className="text-sm text-gray-600 dark:text-gray-400 w-8">
                {playbackState.isMuted ? 0 : playbackState.volume}
              </span>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <Music className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">
              Nenhuma música na fila
            </p>
          </div>
        )}
      </div>

      {/* Fila de reprodução */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">
              {showHistory ? "Histórico de Reprodução" : "Fila de Reprodução"}
            </h3>
            <button
              onClick={() => window.location.reload()}
              className="text-blue-600 hover:text-blue-700 flex items-center space-x-1"
            >
              <RefreshCw className="w-4 h-4" />
              <span>Atualizar</span>
            </button>
          </div>
        </div>

        <div className="p-6">
          <AnimatePresence>
            {(showHistory ? playHistory : queue).map((item, index) => (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className={`flex items-center justify-between p-4 rounded-lg mb-3 ${
                  item.isPlaying
                    ? "bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800"
                    : "bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700"
                }`}
              >
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full text-sm font-medium">
                    {showHistory ? <History className="w-4 h-4" /> : index + 1}
                  </div>

                  <img
                    src={item.thumbnailUrl}
                    alt={item.title}
                    className="w-12 h-9 object-cover rounded"
                  />

                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-gray-900 dark:text-white truncate">
                      {item.title}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                      {item.artist} • {item.duration}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-500">
                      {item.addedBy} • {formatDate(item.addedAt)}
                    </p>
                  </div>

                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {item.votes} votos
                    </p>
                    {item.isPlaying && (
                      <p className="text-xs text-blue-600 dark:text-blue-400">
                        Tocando agora
                      </p>
                    )}
                  </div>
                </div>

                {!showHistory && !item.isPlaying && (
                  <div className="flex items-center space-x-1">
                    <button
                      onClick={() => moveItemUp(item.id)}
                      disabled={index === 0}
                      className="p-1 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 rounded disabled:opacity-50"
                      title="Mover para cima"
                    >
                      <MoveUp className="w-4 h-4" />
                    </button>

                    <button
                      onClick={() => moveItemDown(item.id)}
                      disabled={index === queue.length - 1}
                      className="p-1 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 rounded disabled:opacity-50"
                      title="Mover para baixo"
                    >
                      <MoveDown className="w-4 h-4" />
                    </button>

                    <button
                      onClick={() => removeFromQueue(item.id)}
                      className="p-1 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/20 rounded"
                      title="Remover da fila"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                )}
              </motion.div>
            ))}
          </AnimatePresence>

          {(showHistory ? playHistory : queue).length === 0 && (
            <div className="text-center py-12">
              {showHistory ? (
                <>
                  <History className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 dark:text-gray-400">
                    Nenhuma música no histórico
                  </p>
                </>
              ) : (
                <>
                  <Music className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 dark:text-gray-400">
                    Fila vazia. As sugestões aprovadas aparecerão aqui.
                  </p>
                </>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default QueueManager;
