import { AppDataSource } from "../config/database";
import { Playlist } from "../models/Playlist";
import { Suggestion } from "../models/Suggestion";
import { youtubeOAuthService } from "./YouTubeOAuthService";
import {
  NotificationService,
  NotificationType,
  NotificationPriority,
} from "./NotificationService";
import { WebSocketService } from "./WebSocketService";

const notificationService = NotificationService.getInstance();

interface VotedTrack {
  youtubeVideoId: string;
  title: string;
  artist: string;
  voteScore: number;
  upvotes: number;
  downvotes: number;
  currentPosition: number;
  newPosition: number;
}

interface PlaylistReorderResult {
  success: boolean;
  tracksReordered: number;
  newOrder: VotedTrack[];
  message: string;
}

export class VotingPlaylistService {
  private playlistRepository = AppDataSource.getRepository(Playlist);
  private suggestionRepository = AppDataSource.getRepository(Suggestion);

  /**
   * Atualizar ordem da playlist do YouTube baseada nas votações
   */
  async updatePlaylistOrderByVotes(
    restaurantId: string,
    playlistId: string
  ): Promise<PlaylistReorderResult> {
    try {
      // Verificar se o restaurante está autenticado com YouTube
      const isAuthenticated = await youtubeOAuthService.isAuthenticated(
        restaurantId
      );
      if (!isAuthenticated) {
        return {
          success: false,
          tracksReordered: 0,
          newOrder: [],
          message:
            "Restaurante não autenticado com YouTube. Configure OAuth primeiro.",
        };
      }

      // Buscar playlist local
      const playlist = await this.playlistRepository.findOne({
        where: { id: playlistId, restaurant: { id: restaurantId } },
      });

      if (!playlist || !playlist.youtubePlaylistId) {
        return {
          success: false,
          tracksReordered: 0,
          newOrder: [],
          message: "Playlist não encontrada ou não vinculada ao YouTube",
        };
      }

      // Buscar todas as sugestões/votos para músicas desta playlist
      const suggestions = await this.suggestionRepository
        .createQueryBuilder("suggestion")
        .where("suggestion.restaurant.id = :restaurantId", { restaurantId })
        .andWhere("suggestion.status IN (:...statuses)", {
          statuses: ["approved", "playing", "played"],
        })
        .getMany();

      // Calcular scores de votação para cada música
      const trackVotes = new Map<
        string,
        { upvotes: number; downvotes: number; score: number }
      >();

      suggestions.forEach((suggestion) => {
        if (suggestion.youtubeVideoId) {
          const existing = trackVotes.get(suggestion.youtubeVideoId) || {
            upvotes: 0,
            downvotes: 0,
            score: 0,
          };
          existing.upvotes += suggestion.upvotes;
          existing.downvotes += suggestion.downvotes;
          existing.score = existing.upvotes - existing.downvotes;
          trackVotes.set(suggestion.youtubeVideoId, existing);
        }
      });

      // Preparar lista de músicas com votos
      const votedTracks: VotedTrack[] = playlist.tracks
        .map((track, index) => {
          const votes = trackVotes.get(track.youtubeVideoId) || {
            upvotes: 0,
            downvotes: 0,
            score: 0,
          };
          return {
            youtubeVideoId: track.youtubeVideoId,
            title: track.title,
            artist: track.artist,
            voteScore: votes.score,
            upvotes: votes.upvotes,
            downvotes: votes.downvotes,
            currentPosition: index,
            newPosition: index, // Será calculado abaixo
          };
        })
        .filter((track) => track !== undefined);

      // Ordenar por score de votos (maior score = posição mais alta)
      const sortedTracks = [...votedTracks].sort((a, b) => {
        // Primeiro critério: score de votos
        if (b.voteScore !== a.voteScore) {
          return b.voteScore - a.voteScore;
        }
        // Segundo critério: manter ordem original se empate
        return a.currentPosition - b.currentPosition;
      });

      // Atualizar novas posições
      sortedTracks.forEach((track, index) => {
        track.newPosition = index;
      });

      // Verificar se houve mudanças significativas
      const hasSignificantChanges = sortedTracks.some(
        (track) => Math.abs(track.newPosition - track.currentPosition) > 0
      );

      if (!hasSignificantChanges) {
        return {
          success: true,
          tracksReordered: 0,
          newOrder: sortedTracks,
          message: "Nenhuma mudança necessária na ordem da playlist",
        };
      }

      // Preparar dados para reordenação no YouTube
      const playlistItems = sortedTracks.map((track) => ({
        videoId: track.youtubeVideoId,
        position: track.newPosition,
      }));

      // Reordenar playlist no YouTube
      const reorderSuccess = await youtubeOAuthService.reorderPlaylist(
        restaurantId,
        playlist.youtubePlaylistId,
        playlistItems
      );

      if (!reorderSuccess) {
        return {
          success: false,
          tracksReordered: 0,
          newOrder: sortedTracks,
          message: "Falha ao reordenar playlist no YouTube",
        };
      }

      // Atualizar ordem local na base de dados
      playlist.tracks = sortedTracks.map((track) => {
        const originalTrack = playlist.tracks.find(
          (t) => t.youtubeVideoId === track.youtubeVideoId
        );
        return {
          ...originalTrack!,
          position: track.newPosition,
        };
      });

      await this.playlistRepository.save(playlist);

      // Contar quantas músicas mudaram de posição
      const tracksReordered = sortedTracks.filter(
        (track) => track.newPosition !== track.currentPosition
      ).length;

      // Notificar mudanças via WebSocket
      await this.notifyPlaylistReorder(restaurantId, playlistId, sortedTracks);

      console.log(
        `🔄 Playlist reordenada por votos: ${tracksReordered} músicas movidas`
      );

      return {
        success: true,
        tracksReordered,
        newOrder: sortedTracks,
        message: `Playlist reordenada com sucesso! ${tracksReordered} músicas mudaram de posição.`,
      };
    } catch (error) {
      console.error("Erro ao atualizar ordem da playlist:", error);
      return {
        success: false,
        tracksReordered: 0,
        newOrder: [],
        message: `Erro interno: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`,
      };
    }
  }

  /**
   * Processar voto e atualizar playlist automaticamente
   */
  async processVoteAndUpdatePlaylist(
    restaurantId: string,
    suggestionId: string,
    voteType: "up" | "down"
  ): Promise<void> {
    try {
      // Buscar a sugestão
      const suggestion = await this.suggestionRepository.findOne({
        where: { id: suggestionId },
        relations: ["restaurant", "playlist"],
      });

      if (!suggestion || !suggestion.playlist) {
        return;
      }

      // Verificar se a playlist permite votação
      if (!suggestion.playlist.settings?.allowVoting) {
        return;
      }

      // Aguardar um pouco para permitir que outros votos sejam processados
      setTimeout(async () => {
        await this.updatePlaylistOrderByVotes(
          restaurantId,
          suggestion.playlist.id
        );
      }, 2000); // 2 segundos de delay
    } catch (error) {
      console.error("Erro ao processar voto e atualizar playlist:", error);
    }
  }

  /**
   * Configurar atualização automática da playlist
   */
  async enableAutoReorder(
    restaurantId: string,
    playlistId: string
  ): Promise<boolean> {
    try {
      const playlist = await this.playlistRepository.findOne({
        where: { id: playlistId, restaurant: { id: restaurantId } },
      });

      if (!playlist) {
        return false;
      }

      // Ativar reordenação automática nas configurações
      playlist.settings = {
        ...playlist.settings,
        allowVoting: true,
        autoReorderByVotes: true, // Nova configuração
        reorderThreshold: 5, // Reordenar a cada 5 votos
      };

      await this.playlistRepository.save(playlist);

      console.log(`✅ Auto-reordenação ativada para playlist: ${playlistId}`);
      return true;
    } catch (error) {
      console.error("Erro ao ativar auto-reordenação:", error);
      return false;
    }
  }

  /**
   * Notificar mudanças na ordem da playlist
   */
  private async notifyPlaylistReorder(
    restaurantId: string,
    playlistId: string,
    newOrder: VotedTrack[]
  ): Promise<void> {
    try {
      // Notificar via WebSocket
      await notificationService.sendToRestaurant(restaurantId, {
        type: NotificationType.INFO,
        title: "🔄 Playlist Reordenada",
        message:
          "A ordem da playlist foi atualizada baseada nas votações dos clientes",
        priority: NotificationPriority.NORMAL,
        category: "playlist",
        data: {
          playlistId,
          newOrder: newOrder.map((track) => ({
            videoId: track.youtubeVideoId,
            title: track.title,
            oldPosition: track.currentPosition,
            newPosition: track.newPosition,
            voteScore: track.voteScore,
          })),
        },
      });

      // Notificar clientes também
      await notificationService.sendToClients(restaurantId, {
        type: NotificationType.PLAYLIST_UPDATE,
        title: "🔄 Playlist Atualizada",
        message: "A ordem da playlist foi atualizada pelos votos!",
        data: {
          playlistId,
          topTracks: newOrder.slice(0, 3).map((track) => ({
            title: track.title,
            artist: track.artist,
            voteScore: track.voteScore,
          })),
        },
      });
    } catch (error) {
      console.error("Erro ao notificar reordenação:", error);
    }
  }

  /**
   * Obter estatísticas de votação da playlist
   */
  async getPlaylistVotingStats(
    restaurantId: string,
    playlistId: string
  ): Promise<any> {
    try {
      const playlist = await this.playlistRepository.findOne({
        where: { id: playlistId, restaurant: { id: restaurantId } },
      });

      if (!playlist) {
        return null;
      }

      const suggestions = await this.suggestionRepository
        .createQueryBuilder("suggestion")
        .where("suggestion.restaurant.id = :restaurantId", { restaurantId })
        .andWhere("suggestion.playlist.id = :playlistId", { playlistId })
        .getMany();

      const totalVotes = suggestions.reduce(
        (sum, s) => sum + s.upvotes + s.downvotes,
        0
      );
      const totalUpvotes = suggestions.reduce((sum, s) => sum + s.upvotes, 0);
      const totalDownvotes = suggestions.reduce(
        (sum, s) => sum + s.downvotes,
        0
      );

      return {
        totalVotes,
        totalUpvotes,
        totalDownvotes,
        tracksWithVotes: suggestions.filter(
          (s) => s.upvotes > 0 || s.downvotes > 0
        ).length,
        mostVotedTrack: suggestions.reduce(
          (max, s) =>
            s.upvotes + s.downvotes > max.upvotes + max.downvotes ? s : max,
          suggestions[0]
        ),
      };
    } catch (error) {
      console.error("Erro ao obter estatísticas:", error);
      return null;
    }
  }
}

export const votingPlaylistService = new VotingPlaylistService();
