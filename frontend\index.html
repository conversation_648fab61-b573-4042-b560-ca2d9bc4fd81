<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Meta tags para SEO -->
    <title>Restaurant Playlist - Sistema de Playlist Interativa</title>
    <meta
      name="description"
      content="Sistema interativo de playlist para restaurantes. Clientes podem sugerir músicas e votar em tempo real."
    />
    <meta
      name="keywords"
      content="playlist, restaurante, música, interativo, sugestões, votação"
    />
    <meta name="author" content="Restaurant Playlist System" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://restaurantplaylist.com/" />
    <meta
      property="og:title"
      content="Restaurant Playlist - Sistema de Playlist Interativa"
    />
    <meta
      property="og:description"
      content="Sistema interativo de playlist para restaurantes. Clientes podem sugerir músicas e votar em tempo real."
    />
    <meta property="og:image" content="/og-image.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://restaurantplaylist.com/" />
    <meta
      property="twitter:title"
      content="Restaurant Playlist - Sistema de Playlist Interativa"
    />
    <meta
      property="twitter:description"
      content="Sistema interativo de playlist para restaurantes. Clientes podem sugerir músicas e votar em tempo real."
    />
    <meta property="twitter:image" content="/twitter-image.png" />

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#3B82F6" />
    <meta name="background-color" content="#ffffff" />
    <meta name="display" content="standalone" />
    <meta name="orientation" content="portrait" />

    <!-- Apple Touch Icon -->
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Restaurant Playlist" />

    <!-- Microsoft Tiles -->
    <meta name="msapplication-TileColor" content="#3B82F6" />
    <meta name="msapplication-TileImage" content="/mstile-144x144.png" />
    <meta name="msapplication-config" content="/browserconfig.xml" />

    <!-- Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- Preconnect para performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://i.ytimg.com" />

    <!-- DNS Prefetch -->
    <link rel="dns-prefetch" href="//www.youtube.com" />
    <link rel="dns-prefetch" href="//youtube.com" />

    <!-- Preload critical resources apenas se necessário -->
    <link
      rel="preload"
      href="/fonts/inter-var.woff2"
      as="font"
      type="font/woff2"
      crossorigin="anonymous"
      media="print"
      onload="this.media='all'"
    />
    <noscript>
      <link
        rel="stylesheet"
        href="/fonts/inter-var.woff2"
        media="all"
      />
    </noscript>

    <!-- Critical CSS inline (será injetado pelo Vite) -->
    <style>
      /* Critical CSS para evitar FOUC */
      html {
        line-height: 1.15;
        -webkit-text-size-adjust: 100%;
      }

      body {
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
          "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans",
          "Helvetica Neue", sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: #f9fafb;
      }

      /* Loading spinner inicial */
      .initial-loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f9fafb;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }

      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e5e7eb;
        border-top: 4px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      /* Dark mode support */
      @media (prefers-color-scheme: dark) {
        body {
          background-color: #111827;
          color: #f9fafb;
        }

        .initial-loading {
          background: #111827;
        }

        .loading-spinner {
          border-color: #374151;
          border-top-color: #60a5fa;
        }
      }

      /* Hide loading when app is ready */
      .app-ready .initial-loading {
        display: none;
      }
    </style>

    <!-- Schema.org structured data -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "Restaurant Playlist",
        "description": "Sistema interativo de playlist para restaurantes",
        "url": "https://restaurantplaylist.com",
        "applicationCategory": "Entertainment",
        "operatingSystem": "Web",
        "offers": {
          "@type": "Offer",
          "price": "0",
          "priceCurrency": "BRL"
        },
        "author": {
          "@type": "Organization",
          "name": "Restaurant Playlist System"
        }
      }
    </script>
  </head>

  <body>
    <!-- Loading inicial -->
    <div class="initial-loading">
      <div class="loading-spinner"></div>
    </div>

    <!-- Container principal da aplicação React -->
    <div id="root"></div>

    <!-- Fallback para JavaScript desabilitado -->
    <noscript>
      <div
        style="
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: #f9fafb;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          text-align: center;
          padding: 20px;
          box-sizing: border-box;
        "
      >
        <h1 style="color: #1f2937; margin-bottom: 16px">
          JavaScript Necessário
        </h1>
        <p style="color: #6b7280; max-width: 400px; line-height: 1.5">
          Este aplicativo requer JavaScript para funcionar. Por favor, habilite
          JavaScript no seu navegador e recarregue a página.
        </p>
      </div>
    </noscript>

    <!-- Script principal -->
    <script type="module" src="/src/main.tsx"></script>

    <!-- App bootstrap helpers -->
    <script>
      // Remover loading inicial quando app estiver pronto
      window.addEventListener("DOMContentLoaded", () => {
        setTimeout(() => {
          document.body.classList.add("app-ready");
        }, 100);
      });

      // Fallback global para imagens quebradas (placeholders de demo)
      window.addEventListener(
        "error",
        (e) => {
          const target = e.target;
          if (target && target.tagName === "IMG") {
            const img = target;
            const src = img.getAttribute("src") || "";

            // Evitar loop infinito
            if (img.hasAttribute("data-fallback-applied")) {
              return;
            }

            console.warn("🖼️ Imagem falhou ao carregar:", src);

            // Corrige host antigo via.placeholder.com
            if (src.includes("via.placeholder.com")) {
              const fixed = src.replace("via.placeholder.com", "placehold.co");
              console.log("🔄 Tentando corrigir URL:", fixed);
              img.setAttribute("src", fixed);
              img.setAttribute("data-fallback-applied", "true");
              return;
            }

            // Fallback final para qualquer imagem quebrada
            console.log("🔄 Aplicando fallback final");
            img.setAttribute("src", "https://placehold.co/300x300/e5e7eb/6b7280?text=Imagem");
            img.setAttribute("data-fallback-applied", "true");
          }
        },
        true
      );

      // Detectar instalação da PWA
      let deferredPrompt;
      window.addEventListener("beforeinstallprompt", (e) => {
        // Não cancelar o evento globalmente; guardar referência e exibir UI opcional
        deferredPrompt = e;
        const installButton = document.getElementById("install-button");
        if (installButton) {
          installButton.style.display = "block";
          installButton.addEventListener("click", () => {
            try {
              if (deferredPrompt && typeof deferredPrompt.prompt === "function") {
                deferredPrompt.prompt();
                deferredPrompt.userChoice?.then?.((choiceResult) => {
                  if (choiceResult?.outcome === "accepted") {
                    console.log("User accepted the install prompt");
                  }
                  deferredPrompt = null;
                });
              }
            } catch (_) {
              // Se prompt não estiver disponível, apenas oculta o botão
              deferredPrompt = null;
            }
          }, { passive: true });
        }
      });

      // Analytics de instalação da PWA
      window.addEventListener("appinstalled", (evt) => {
        console.log("PWA was installed");
        // Enviar evento para analytics se configurado
        if (typeof gtag !== "undefined") {
          gtag("event", "pwa_install", {
            event_category: "engagement",
            event_label: "PWA Installation",
          });
        }
      });
    </script>

    <!-- Google Analytics (opcional) -->
    <!-- 
    <script async src="https://www.googletagmanager.com/gtag/js?id=GA_TRACKING_ID"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'GA_TRACKING_ID');
    </script>
    -->
  </body>
</html>
