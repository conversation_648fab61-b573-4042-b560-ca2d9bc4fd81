import { AppDataSource } from "../config/database";
import { Restaurant } from "../models/Restaurant";
import { QRCode, QRCodeType } from "../models/QRCode";
import * as QRCodeLib from "qrcode";

/**
 * Service utilitário para garantir o QRCode do painel do músico (Couvert)
 */
export class CouvertService {
  private getFrontendBase(): string {
    // Preferir URL pública do frontend, com fallback para ambiente local padrão
    return (
      process.env.PUBLIC_FRONTEND_URL ||
      process.env.FRONTEND_URL ||
      "http://localhost:8000"
    ).replace(/\/$/, "");
  }

  /**
   * Garante que exista um QRCode do tipo "Couvert" para o restaurante.
   * Não lança erro: retorna false em caso de problemas para não quebrar o fluxo principal.
   */
  async ensureCouvertQRCode(restaurantId: string): Promise<boolean> {
    try {
      if (!restaurantId) return false;

      const restaurantRepo = AppDataSource.getRepository(Restaurant);
      const qrRepo = AppDataSource.getRepository(QRCode);

      const restaurant = await restaurantRepo.findOne({ where: { id: restaurantId } });
      if (!restaurant) return false;

      const base = this.getFrontendBase();
      const couvertUrl = `${base}/couvert/${restaurantId}`;

      // Verificar se já existe (por URL) para evitar duplicidade
      const existing = await qrRepo.findOne({
        where: { restaurant: { id: restaurantId }, url: couvertUrl, isActive: true },
      });
      if (existing) return true;

      // Gerar imagem do QR
      const qrCodeDataURL = await QRCodeLib.toDataURL(couvertUrl, {
        errorCorrectionLevel: "M",
        type: "image/png",
        margin: 1,
        color: { dark: "#000000", light: "#FFFFFF" },
      });

      const entity = qrRepo.create({
        restaurant,
        type: QRCodeType.RESTAURANT,
        name: "Couvert",
        url: couvertUrl,
        qrCodeData: qrCodeDataURL,
        isActive: true,
      });
      await qrRepo.save(entity);
      return true;
    } catch (e) {
      // Não propagar falhas de geração de QR para não quebrar criação de playlist
      return false;
    }
  }
}

export const couvertService = new CouvertService();
