# ============================================
# SCRIPT DE VERIFICAÇÃO DO BANCO DE DADOS
# Sistema de Playlist Interativa para Restaurantes
# ============================================

Write-Host "🔍 Verificando estado do banco de dados..." -ForegroundColor Cyan

# Verificar se o container do PostgreSQL está rodando
Write-Host "`n📦 Verificando containers..." -ForegroundColor Yellow
docker ps --filter "name=restaurant-playlist-db" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Verificar se o banco está acessível
Write-Host "`n🔌 Testando conexão com o banco..." -ForegroundColor Yellow
$connectionTest = docker exec restaurant-playlist-db pg_isready -U restaurant_user -d restaurant_playlist
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Conexão com o banco estabelecida com sucesso!" -ForegroundColor Green
} else {
    Write-Host "❌ Falha na conexão com o banco!" -ForegroundColor Red
    exit 1
}

# Executar script de verificação de tabelas
Write-Host "`n📋 Verificando estrutura das tabelas..." -ForegroundColor Yellow
docker exec -i restaurant-playlist-db psql -U restaurant_user -d restaurant_playlist -f /docker-entrypoint-initdb.d/05-verify-tables.sql

# Verificar logs do container do banco
Write-Host "`n📝 Últimos logs do banco de dados:" -ForegroundColor Yellow
docker logs restaurant-playlist-db --tail=20

# Verificar se o backend consegue conectar
Write-Host "`n🔗 Verificando conexão do backend..." -ForegroundColor Yellow
$backendLogs = docker logs restaurant-playlist-api --tail=10 2>&1
if ($backendLogs -match "Database connected successfully" -or $backendLogs -match "Server running") {
    Write-Host "✅ Backend conectado ao banco com sucesso!" -ForegroundColor Green
} else {
    Write-Host "⚠️  Verificar logs do backend para possíveis problemas de conexão" -ForegroundColor Yellow
    Write-Host "Últimos logs do backend:" -ForegroundColor Gray
    docker logs restaurant-playlist-api --tail=15
}

# Testar uma consulta simples
Write-Host "`n🧪 Testando consulta simples..." -ForegroundColor Yellow
$queryResult = docker exec -i restaurant-playlist-db psql -U restaurant_user -d restaurant_playlist -c "SELECT COUNT(*) as total_restaurants FROM restaurants;" 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Consulta executada com sucesso!" -ForegroundColor Green
    Write-Host $queryResult
} else {
    Write-Host "❌ Erro na consulta!" -ForegroundColor Red
    Write-Host $queryResult
}

# Verificar APIs do backend
Write-Host "`n🌐 Testando APIs do backend..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8001/api/v1/health" -Method GET -TimeoutSec 5
    Write-Host "✅ API de health funcionando!" -ForegroundColor Green
    Write-Host "Status: $($response.status)" -ForegroundColor Gray
} catch {
    Write-Host "❌ API de health não está respondendo!" -ForegroundColor Red
    Write-Host "Erro: $($_.Exception.Message)" -ForegroundColor Gray
}

try {
    $response = Invoke-RestMethod -Uri "http://localhost:8001/api/v1/admin/restaurants" -Method GET -TimeoutSec 5
    Write-Host "✅ API de restaurantes funcionando!" -ForegroundColor Green
    Write-Host "Total de restaurantes: $($response.restaurants.Count)" -ForegroundColor Gray
} catch {
    Write-Host "❌ API de restaurantes não está respondendo!" -ForegroundColor Red
    Write-Host "Erro: $($_.Exception.Message)" -ForegroundColor Gray
}

# Verificar frontend
Write-Host "`n🎨 Testando frontend..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000" -Method GET -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Frontend acessível!" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Frontend retornou status: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Frontend não está acessível!" -ForegroundColor Red
    Write-Host "Erro: $($_.Exception.Message)" -ForegroundColor Gray
}

Write-Host "`n🎉 Verificação concluída!" -ForegroundColor Cyan
Write-Host "Para mais detalhes, acesse:" -ForegroundColor Gray
Write-Host "  - Frontend: http://localhost:8000" -ForegroundColor Gray
Write-Host "  - API: http://localhost:8001" -ForegroundColor Gray
Write-Host "  - Admin: http://localhost:8000/admin" -ForegroundColor Gray
