import { Router, Request, Response } from "express";
import { body, validationResult, query } from "../utils/validation";
import bcrypt from "bcryptjs";
import { AppDataSource } from "../config/database";
import { User } from "../models/User";
import { Restaurant } from "../models/Restaurant";
import { generateToken, authMiddleware } from "../middleware/auth";
import asyncHandler from "../middleware/asyncHandler";
import { ValidationError, UnauthorizedError } from "../utils/errors";
import { logger } from "../utils/logger";
import { YouTubeOAuthService } from "../services/YouTubeOAuthService";

const router = Router();
const youtubeOAuth = new YouTubeOAuthService();

/**
 * @swagger
 * /api/v1/auth/login:
 *   post:
 *     summary: Fazer login no sistema
 *     tags: [Autenticação]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email do usuário
 *               password:
 *                 type: string
 *                 minLength: 6
 *                 description: Senha do usuário
 *     responses:
 *       200:
 *         description: Login realizado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 token:
 *                   type: string
 *                   description: Token JWT
 *                 user:
 *                   $ref: '#/components/schemas/User'
 *                 restaurant:
 *                   $ref: '#/components/schemas/Restaurant'
 *       401:
 *         description: Credenciais inválidas
 *       400:
 *         description: Dados de entrada inválidos
 */
router.post(
  "/login",
  [
    body("email")
      .isEmail()
      .withMessage("Email deve ser válido")
      .normalizeEmail(),
    body("password")
      .isLength({ min: 6 })
      .withMessage("Senha deve ter pelo menos 6 caracteres"),
  ],
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { email, password } = req.body;

    // Buscar usuário
    const userRepository = AppDataSource.getRepository(User);
    const user = await userRepository.findOne({
      where: { email },
      relations: ["restaurant"],
    });

    if (!user) {
      throw new UnauthorizedError("Credenciais inválidas");
    }

    // Verificar se usuário está ativo
    if (!user.isActive) {
      throw new UnauthorizedError("Conta desativada");
    }

    // Verificar senha
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      throw new UnauthorizedError("Credenciais inválidas");
    }

    // Verificar se restaurante está ativo (se aplicável)
    if (user.restaurant && !user.restaurant.isActive) {
      throw new UnauthorizedError("Restaurante desativado");
    }

    // Atualizar último login
    user.lastLoginAt = new Date();
    user.lastLoginIp = req.ip || "unknown";
    await userRepository.save(user);

    // Gerar token
    const token = generateToken(user);

    logger.info("Login realizado", {
      userId: user.id,
      email: user.email,
      ip: req.ip,
      userAgent: req.get("User-Agent"),
    });

    res.json({
      token,
      user: user.toJSON(),
      restaurant: user.restaurant?.toJSON() || null,
    });
  })
);

/**
 * @swagger
 * /api/v1/auth/register:
 *   post:
 *     summary: Registrar novo usuário (apenas super admin)
 *     tags: [Autenticação]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - email
 *               - password
 *               - role
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 2
 *                 description: Nome do usuário
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email do usuário
 *               password:
 *                 type: string
 *                 minLength: 6
 *                 description: Senha do usuário
 *               role:
 *                 type: string
 *                 enum: [admin, moderator, staff]
 *                 description: Papel do usuário
 *               restaurantId:
 *                 type: string
 *                 format: uuid
 *                 description: ID do restaurante (obrigatório para não super admin)
 *               phone:
 *                 type: string
 *                 description: Telefone do usuário
 *     responses:
 *       201:
 *         description: Usuário criado com sucesso
 *       400:
 *         description: Dados inválidos
 *       409:
 *         description: Email já existe
 */
router.post(
  "/register",
  [
    body("name")
      .isLength({ min: 2, max: 255 })
      .withMessage("Nome deve ter entre 2 e 255 caracteres"),
    body("email")
      .isEmail()
      .withMessage("Email deve ser válido")
      .normalizeEmail(),
    body("password")
      .isLength({ min: 6 })
      .withMessage("Senha deve ter pelo menos 6 caracteres"),
    body("role")
      .isIn(["admin", "moderator", "staff"])
      .withMessage("Papel deve ser admin, moderator ou staff"),
    body("restaurantId")
      .optional()
      .isUUID()
      .withMessage("ID do restaurante deve ser um UUID válido"),
    body("phone")
      .optional()
      .isMobilePhone("pt-BR")
      .withMessage("Telefone deve ser válido"),
  ],
  authMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    // Apenas super admin pode criar usuários
    if (req.user!.role !== "super_admin") {
      throw new UnauthorizedError(
        "Apenas super administradores podem criar usuários"
      );
    }

    const { name, email, password, role, restaurantId, phone } = req.body;

    const userRepository = AppDataSource.getRepository(User);
    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    // Verificar se email já existe
    const existingUser = await userRepository.findOne({ where: { email } });
    if (existingUser) {
      return res.status(409).json({
        error: "Email já está em uso",
        code: "EMAIL_ALREADY_EXISTS",
      });
    }

    // Verificar se restaurante existe (se fornecido)
    let restaurant = null;
    if (restaurantId) {
      restaurant = await restaurantRepository.findOne({
        where: { id: restaurantId },
      });
      if (!restaurant) {
        return res.status(400).json({
          error: "Restaurante não encontrado",
          code: "RESTAURANT_NOT_FOUND",
        });
      }
    }

    // Criar usuário
    const user = userRepository.create({
      name,
      email,
      password,
      role,
      phone,
      restaurant: restaurant || undefined,
      isActive: true,
      emailVerifiedAt: new Date(), // Auto-verificar para usuários criados por admin
    });

    await userRepository.save(user);

    logger.info("Usuário criado", {
      userId: user.id,
      email: user.email,
      role: user.role,
      createdBy: req.user!.id,
    });

    res.status(201).json({
      message: "Usuário criado com sucesso",
      user: user.toJSON(),
    });
  })
);

/**
 * @swagger
 * /api/v1/auth/me:
 *   get:
 *     summary: Obter informações do usuário logado
 *     tags: [Autenticação]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Informações do usuário
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 user:
 *                   $ref: '#/components/schemas/User'
 *                 restaurant:
 *                   $ref: '#/components/schemas/Restaurant'
 *                 permissions:
 *                   type: array
 *                   items:
 *                     type: string
 *       401:
 *         description: Token inválido
 */
router.get(
  "/me",
  authMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const user = req.user!;

    res.json({
      user: user.toJSON(),
      restaurant: user.restaurant?.toJSON() || null,
      permissions: user.getPermissions(),
    });
  })
);

/**
 * @swagger
 * /api/v1/auth/change-password:
 *   put:
 *     summary: Alterar senha do usuário
 *     tags: [Autenticação]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - currentPassword
 *               - newPassword
 *             properties:
 *               currentPassword:
 *                 type: string
 *                 description: Senha atual
 *               newPassword:
 *                 type: string
 *                 minLength: 6
 *                 description: Nova senha
 *     responses:
 *       200:
 *         description: Senha alterada com sucesso
 *       400:
 *         description: Dados inválidos
 *       401:
 *         description: Senha atual incorreta
 */
router.put(
  "/change-password",
  [
    body("currentPassword").notEmpty().withMessage("Senha atual é obrigatória"),
    body("newPassword")
      .isLength({ min: 6 })
      .withMessage("Nova senha deve ter pelo menos 6 caracteres"),
  ],
  authMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError("Parâmetros inválidos", errors.array());
    }

    const { currentPassword, newPassword } = req.body;
    const user = req.user!;

    // Verificar senha atual
    const isCurrentPasswordValid = await user.comparePassword(currentPassword);
    if (!isCurrentPasswordValid) {
      throw new UnauthorizedError("Senha atual incorreta");
    }

    // Atualizar senha
    const userRepository = AppDataSource.getRepository(User);
    user.password = newPassword;
    await userRepository.save(user);

    logger.info("Senha alterada", {
      userId: user.id,
      email: user.email,
      ip: req.ip,
    });

    res.json({
      message: "Senha alterada com sucesso",
    });
  })
);

/**
 * @swagger
 * /api/v1/auth/refresh:
 *   post:
 *     summary: Renovar token de acesso
 *     tags: [Autenticação]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Token renovado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 token:
 *                   type: string
 *                   description: Novo token JWT
 *       401:
 *         description: Token inválido
 */
router.post(
  "/refresh",
  authMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const user = req.user!;

    // Gerar novo token
    const token = generateToken(user);

    res.json({
      token,
    });
  })
);

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         name:
 *           type: string
 *         email:
 *           type: string
 *           format: email
 *         role:
 *           type: string
 *           enum: [super_admin, admin, moderator, staff]
 *         isActive:
 *           type: boolean
 *         phone:
 *           type: string
 *         avatar:
 *           type: string
 *         lastLoginAt:
 *           type: string
 *           format: date-time
 *         preferences:
 *           type: object
 *         emailVerifiedAt:
 *           type: string
 *           format: date-time
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *   securitySchemes:
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 */

/**
 * GET /auth/youtube
 * Iniciar processo de autenticação OAuth do YouTube
 */
router.get("/youtube", [
  query("restaurantId")
    .notEmpty()
    .withMessage("restaurantId é obrigatório")
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError("Dados inválidos", errors.array());
  }

  const { restaurantId } = req.query as { restaurantId: string };

  try {
    const authUrl = youtubeOAuth.generateAuthUrl(restaurantId);
    logger.info(`🔐 Redirecionando para autenticação YouTube: ${restaurantId}`);

    // Redirecionar para URL de autorização do Google
    res.redirect(authUrl);
  } catch (error) {
    logger.error("❌ Erro ao gerar URL de autenticação:", error);
    res.status(500).json({
      success: false,
      error: "Erro interno do servidor",
      message: "Não foi possível iniciar autenticação YouTube"
    });
  }
}));

/**
 * GET /auth/youtube/callback
 * Callback da autenticação OAuth do YouTube
 */
router.get("/youtube/callback", [
  query("code").notEmpty().withMessage("Código de autorização é obrigatório"),
  query("state").notEmpty().withMessage("State (restaurantId) é obrigatório")
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError("Dados inválidos", errors.array());
  }

  const { code, state: restaurantId } = req.query as { code: string; state: string };

  try {
  // Trocar o código de autorização por tokens e salvar credenciais
  const credentials = await youtubeOAuth.exchangeCodeForTokens(code, restaurantId);
  const success = !!credentials;

    if (success) {
      logger.info(`✅ Autenticação YouTube concluída: ${restaurantId}`);
      res.send(`
        <html>
          <head><title>Autenticação Concluída</title></head>
          <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
            <h1 style="color: green;">✅ Autenticação Concluída!</h1>
            <p>O restaurante <strong>${restaurantId}</strong> foi autorizado com sucesso.</p>
            <p>Agora as playlists serão reordenadas automaticamente baseado nas votações.</p>
            <p><a href="http://localhost:8000/restaurant/${restaurantId}/dashboard">Voltar ao Dashboard</a></p>
          </body>
        </html>
      `);
    } else {
      throw new Error("Falha na autenticação");
    }
  } catch (error) {
    logger.error("❌ Erro no callback YouTube:", error);
    res.status(500).send(`
      <html>
        <head><title>Erro na Autenticação</title></head>
        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
          <h1 style="color: red;">❌ Erro na Autenticação</h1>
          <p>Não foi possível completar a autenticação do YouTube.</p>
          <p>Erro: ${(error as any).message || 'Erro desconhecido'}</p>
          <p><a href="http://localhost:8000/restaurant/${restaurantId}/dashboard">Voltar ao Dashboard</a></p>
        </body>
      </html>
    `);
  }
}));

export default router;
