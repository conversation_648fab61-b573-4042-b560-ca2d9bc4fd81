export class ValidationError extends <PERSON>rror {
  public statusCode: number;
  public errors: any[];

  constructor(message: string, errors: any[] = []) {
    super(message);
    this.name = "ValidationError";
    this.statusCode = 400;
    this.errors = errors;
  }
}

/**
 * Helper function to create validation error from express-validator results
 */
export function createValidationError(errors: any[]): ValidationError {
  return new ValidationError("Validation failed", errors);
}

export class NotFoundError extends Error {
  public statusCode: number;

  constructor(message: string = "Resource not found") {
    super(message);
    this.name = "NotFoundError";
    this.statusCode = 404;
  }
}

export class UnauthorizedError extends Error {
  public statusCode: number;

  constructor(message: string = "Unauthorized") {
    super(message);
    this.name = "UnauthorizedError";
    this.statusCode = 401;
  }
}

export class ForbiddenError extends Error {
  public statusCode: number;

  constructor(message: string = "Forbidden") {
    super(message);
    this.name = "ForbiddenError";
    this.statusCode = 403;
  }
}

export class ConflictError extends Error {
  public statusCode: number;

  constructor(message: string = "Conflict") {
    super(message);
    this.name = "ConflictError";
    this.statusCode = 409;
  }
}

export class InternalServerError extends Error {
  public statusCode: number;

  constructor(message: string = "Internal Server Error") {
    super(message);
    this.name = "InternalServerError";
    this.statusCode = 500;
  }
}
