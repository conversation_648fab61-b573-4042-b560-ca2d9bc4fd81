-- <PERSON><PERSON><PERSON> tabela de gêneros musicais
-- Sistema de Playlist Interativa para Restaurantes

-- Tabela principal de gêneros
CREATE TABLE IF NOT EXISTS genres (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    "displayName" VARCHAR(100) NOT NULL,
    description VARCHAR(200),
    category VARCHAR(50) DEFAULT 'music' CHECK (category IN ('music', 'mood', 'energy', 'time', 'custom')),
    color VARCHAR(7) DEFAULT '#3B82F6',
    icon VARCHAR(50),
    metadata JSON,
    priority INTEGER DEFAULT 0,
    "isActive" BOOLEAN DEFAULT true,
    "isDefault" BOOLEAN DEFAULT false,
    "usageCount" INTEGER DEFAULT 0,
    "lastUsedAt" TIMESTAMP WITHOUT TIME ZONE,
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_genres_name ON genres(name);
CREATE INDEX IF NOT EXISTS idx_genres_category ON genres(category);
CREATE INDEX IF NOT EXISTS idx_genres_active ON genres("isActive");
CREATE INDEX IF NOT EXISTS idx_genres_priority ON genres(priority);
CREATE INDEX IF NOT EXISTS idx_genres_usage ON genres("usageCount" DESC);

-- Tabela de relacionamento entre sugestões e gêneros
CREATE TABLE IF NOT EXISTS suggestion_genres (
    suggestion_id UUID NOT NULL REFERENCES suggestions(id) ON DELETE CASCADE,
    genre_id UUID NOT NULL REFERENCES genres(id) ON DELETE CASCADE,
    "createdAt" TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (suggestion_id, genre_id)
);

-- Índices para a tabela de relacionamento
CREATE INDEX IF NOT EXISTS idx_suggestion_genres_suggestion ON suggestion_genres(suggestion_id);
CREATE INDEX IF NOT EXISTS idx_suggestion_genres_genre ON suggestion_genres(genre_id);

-- Trigger para atualizar updatedAt automaticamente
CREATE OR REPLACE FUNCTION update_genres_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updatedAt" = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_genres_updated_at
    BEFORE UPDATE ON genres
    FOR EACH ROW
    EXECUTE FUNCTION update_genres_updated_at();

-- Inserir gêneros padrão do sistema
INSERT INTO genres (name, "displayName", description, category, color, icon, "isDefault", priority) VALUES
-- Gêneros Musicais
('rock', 'Rock', 'Rock clássico e moderno', 'music', '#DC2626', 'Music2', true, 1),
('pop', 'Pop', 'Musica popular contemporanea', 'music', '#EC4899', 'Mic', true, 2),
('sertanejo', 'Sertanejo', 'Musica sertaneja brasileira', 'music', '#D97706', 'Music', true, 3),
('mpb', 'MPB', 'Musica Popular Brasileira', 'music', '#059669', 'Heart', true, 4),
('funk', 'Funk', 'Funk brasileiro e internacional', 'music', '#EA580C', 'Volume2', true, 5),
('eletronica', 'Eletronica', 'Musica eletronica e EDM', 'music', '#7C3AED', 'Zap', true, 6),
('jazz', 'Jazz', 'Jazz classico e contemporaneo', 'music', '#1F2937', 'Music3', true, 7),
('reggae', 'Reggae', 'Reggae e musica jamaicana', 'music', '#16A34A', 'Music4', true, 8),
('hip-hop', 'Hip Hop', 'Hip hop e rap', 'music', '#374151', 'Mic', true, 9),
('country', 'Country', 'Musica country', 'music', '#92400E', 'Music', true, 10),
('blues', 'Blues', 'Blues tradicional e moderno', 'music', '#1E40AF', 'Music2', true, 11),
('classical', 'Classica', 'Musica classica', 'music', '#7C2D12', 'Music3', true, 12),

-- Humores
('happy', 'Alegre', 'Musicas alegres e animadas', 'mood', '#FCD34D', 'Smile', true, 10),
('sad', 'Melancolico', 'Musicas tristes e melancolicas', 'mood', '#6B7280', 'CloudRain', true, 11),
('romantic', 'Romantico', 'Musicas romanticas', 'mood', '#F472B6', 'Heart', true, 12),
('energetic', 'Energetico', 'Musicas energeticas', 'mood', '#EF4444', 'Zap', true, 13),
('calm', 'Calmo', 'Musicas calmas e relaxantes', 'mood', '#06B6D4', 'Waves', true, 14),
('nostalgic', 'Nostalgico', 'Musicas nostalgicas', 'mood', '#8B5CF6', 'Heart', true, 15),
('motivational', 'Motivacional', 'Musicas motivacionais', 'mood', '#10B981', 'Zap', true, 16),

-- Energia
('high_energy', 'Alta Energia', 'Músicas de alta energia', 'energy', '#DC2626', 'Zap', true, 20),
('medium_energy', 'Média Energia', 'Músicas de energia moderada', 'energy', '#F59E0B', 'Activity', true, 21),
('low_energy', 'Baixa Energia', 'Músicas de baixa energia', 'energy', '#3B82F6', 'Headphones', true, 22),

-- Horário
('morning', 'Manhã', 'Músicas para manhã', 'time', '#FCD34D', 'Sun', true, 30),
('afternoon', 'Tarde', 'Músicas para tarde', 'time', '#F97316', 'Coffee', true, 31),
('evening', 'Noite', 'Músicas para noite', 'time', '#6366F1', 'Moon', true, 32),
('late_night', 'Madrugada', 'Músicas para madrugada', 'time', '#4C1D95', 'Moon', true, 33)

ON CONFLICT (name) DO NOTHING;

-- Função para incrementar uso de gênero
CREATE OR REPLACE FUNCTION increment_genre_usage(genre_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE genres 
    SET "usageCount" = "usageCount" + 1,
        "lastUsedAt" = NOW()
    WHERE id = genre_id;
END;
$$ LANGUAGE plpgsql;

-- Função para obter gêneros por categoria
CREATE OR REPLACE FUNCTION get_genres_by_category(category_filter VARCHAR DEFAULT NULL)
RETURNS TABLE (
    id UUID,
    name VARCHAR,
    "displayName" VARCHAR,
    description VARCHAR,
    category VARCHAR,
    color VARCHAR,
    icon VARCHAR,
    priority INTEGER,
    "isActive" BOOLEAN,
    "usageCount" INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        g.id,
        g.name,
        g."displayName",
        g.description,
        g.category,
        g.color,
        g.icon,
        g.priority,
        g."isActive",
        g."usageCount"
    FROM genres g
    WHERE (category_filter IS NULL OR g.category = category_filter)
      AND g."isActive" = true
    ORDER BY g.priority ASC, g."displayName" ASC;
END;
$$ LANGUAGE plpgsql;

-- Função para buscar gêneros por texto
CREATE OR REPLACE FUNCTION search_genres(search_text VARCHAR)
RETURNS TABLE (
    id UUID,
    name VARCHAR,
    "displayName" VARCHAR,
    description VARCHAR,
    category VARCHAR,
    color VARCHAR,
    icon VARCHAR,
    priority INTEGER,
    "isActive" BOOLEAN,
    "usageCount" INTEGER,
    relevance FLOAT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        g.id,
        g.name,
        g."displayName",
        g.description,
        g.category,
        g.color,
        g.icon,
        g.priority,
        g."isActive",
        g."usageCount",
        (
            CASE 
                WHEN LOWER(g."displayName") = LOWER(search_text) THEN 1.0
                WHEN LOWER(g.name) = LOWER(search_text) THEN 0.9
                WHEN LOWER(g."displayName") LIKE LOWER(search_text || '%') THEN 0.8
                WHEN LOWER(g.name) LIKE LOWER(search_text || '%') THEN 0.7
                WHEN LOWER(g."displayName") LIKE LOWER('%' || search_text || '%') THEN 0.6
                WHEN LOWER(g.name) LIKE LOWER('%' || search_text || '%') THEN 0.5
                WHEN LOWER(g.description) LIKE LOWER('%' || search_text || '%') THEN 0.3
                ELSE 0.1
            END
        ) as relevance
    FROM genres g
    WHERE g."isActive" = true
      AND (
        LOWER(g."displayName") LIKE LOWER('%' || search_text || '%') OR
        LOWER(g.name) LIKE LOWER('%' || search_text || '%') OR
        LOWER(g.description) LIKE LOWER('%' || search_text || '%')
      )
    ORDER BY relevance DESC, g.priority ASC, g."displayName" ASC;
END;
$$ LANGUAGE plpgsql;

-- View para estatísticas de gêneros
CREATE OR REPLACE VIEW genre_stats AS
SELECT 
    g.id,
    g.name,
    g."displayName",
    g.category,
    g."usageCount",
    g."lastUsedAt",
    COUNT(sg.suggestion_id) as suggestions_count,
    COUNT(DISTINCT sg.suggestion_id) as unique_suggestions
FROM genres g
LEFT JOIN suggestion_genres sg ON g.id = sg.genre_id
GROUP BY g.id, g.name, g."displayName", g.category, g."usageCount", g."lastUsedAt"
ORDER BY g."usageCount" DESC, g."displayName" ASC;

-- Comentários para documentação
COMMENT ON TABLE genres IS 'Tabela de gêneros musicais e categorias';
COMMENT ON COLUMN genres.name IS 'Nome único do gênero (usado como ID)';
COMMENT ON COLUMN genres."displayName" IS 'Nome de exibição do gênero';
COMMENT ON COLUMN genres.category IS 'Categoria do gênero: music, mood, energy, time, custom';
COMMENT ON COLUMN genres.color IS 'Cor hexadecimal para exibição';
COMMENT ON COLUMN genres.icon IS 'Nome do ícone para exibição';
COMMENT ON COLUMN genres.metadata IS 'Metadados adicionais em JSON';
COMMENT ON COLUMN genres.priority IS 'Prioridade para ordenação (menor = maior prioridade)';
COMMENT ON COLUMN genres."isActive" IS 'Se o gênero está ativo para uso';
COMMENT ON COLUMN genres."isDefault" IS 'Se é um gênero padrão do sistema';
COMMENT ON COLUMN genres."usageCount" IS 'Número de vezes que foi usado';
COMMENT ON COLUMN genres."lastUsedAt" IS 'Última vez que foi usado';

COMMENT ON TABLE suggestion_genres IS 'Relacionamento many-to-many entre sugestões e gêneros';
COMMENT ON VIEW genre_stats IS 'Estatísticas de uso dos gêneros';

-- Conceder permissões (ajustar conforme necessário)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON genres TO playlist_user;
-- GRANT SELECT, INSERT, DELETE ON suggestion_genres TO playlist_user;
-- GRANT SELECT ON genre_stats TO playlist_user;
